{"text": "# Full Accounting for Verifiable Outsourcing  \n\nRiad S. Wahby⋆ Ye Ji◦ Andrew <PERSON>† a<PERSON><PERSON>‡ <PERSON> Thaler△ <PERSON> Walfish◦ <PERSON> Wies◦  \n\n⋆Stanford ◦NYU †UT Austin ‡Northeastern △Georgetown  \n\n# ABSTRACT  \n\nSystems for verifiable outsourcing incur costs for a prover, a verifier, and precomputation; outsourcing makes sense when the combination of these costs is cheaper than not outsourcing. Yet, when prior works impose quantitative thresholds to analyze whether outsourcing is justified, they generally ignore prover costs. Verifiable ASICs (VA)—in which the prover is a custom chip—is the other way around: its cost calculations ignore precomputation.  \n\nThis paper describes a new VA system, called Giraffe; charges <PERSON>iraffe for all three costs; and identifies regimes where outsourcing is worthwhile. Giraffe’s base is an interactive proof geared to dataparallel computation. <PERSON>iraffe makes this protocol asymptotically optimal for the prover and improves the verifier’s main bottleneck by almost $3\\times$ , both of which are of independent interest. Giraffe also develops a design template that produces hardware designs automatically for a wide range of parameters, introduces hardware primitives molded to the protocol’s data flows, and incorporates program analyses that expand applicability. <PERSON><PERSON><PERSON> wins even when outsourcing several tens of sub-computations, scales to $500\\times$ larger computations than prior work, and can profitably outsource parts of programs that are not worthwhile to outsource in full.  \n\n# 1 INTRODUCTION  \n\nIn probabilistic proofs—Interactive Proofs (IPs) [12, 49, 50, 58, 76], arguments [30, 52, 54, 62], SNARGs [48], SNARKs [26, 47], and PCPs [9, 10]—a prover efficiently convinces a verifier of a claim, in such a way that the verifier is highly likely to reject a false claim. These protocols are foundational in complexity theory and cryptography. There has also been substantial progress in implementations over the last six years [14, 15, 17–19, 21, 22, 32, 34, 36, 37, 39, 40, 42, 45, 47, 56, 64, 66, 72–75, 77, 79, 81, 82, 84, 88] (for a survey, see [85]), based on theoretical refinements and systems work.  \n\nA central application example is verifiable outsourcing. The verifier specifies a computation and input; the prover returns the (purported) output and proves the claim that “the returned output equals the computation applied to the input.” The essential property here is that the verifier’s probabilistic checks are asymptotically less expensive than executing the computation; as a result, outsourcing can be worthwhile for the verifier. This picture partially motivated the original theory [13, 46, 49, 62] and has reappeared in tales of outsourcing to the cloud. But to validate these stories, one must consider three kinds of costs:  \n\n• Prover overhead. Even in the best general-purpose probabilistic proof protocols, the prover has enormous overhead in running the protocol versus simply executing the underlying computation: the ratio between these is typically at least $10^{7}$ [85, Fig. 5]. • Precomputation. Many of the implemented protocols require a setup phase, performed by the verifier or a party that the verifier trusts. This phase is required for each computation and can be reused over different input-output instances. Its costs are usually proportional to the time to run the computation. (Precomputation can be asymptotically suppressed or even eliminated, but at vastly higher concrete cost [17, 21, 22, 34]; see $\\S10.$ .) • Verifier overhead. Separate from precomputation, there are inherent costs that the verifier incurs for each input-output instance. These costs are at least linear in the input and output lengths.  \n\nMore or less tacitly, “practical” work in this area has bundled in assumptions about the regimes in which these costs are reasonable for the operator of the verifier. For example, one way to tame the costs is not to charge the operator for precomputation. This is the approach taken in Pinocchio, which focuses on per-instance verifier overhead [66, 67]. This choice can be justified if there is a trusted third party with extremely inexpensive cycles.  \n\nAnother possibility is to target data-parallel computations, meaning identical sub-computations on different inputs. Here, one can charge the operator of the verifier for the precomputation, which amortizes, and then identify cross-over points where the verifier saves work from outsourcing [32, 37, 66, 73–75, 81, 84, 88].  \n\nIn both of these cases, prover overhead is measured but in some sense ignored (when considering whether outsourcing is worthwhile). This would make sense if the prover’s cycles were vastly cheaper than the verifier’s—the required ratio is approximately the prover’s overhead: $10^{7}{\\times}{-}\\mathrm{or}$ if the outsourced computation could not be executed in any other way.  \n\nRecently, Zebra [82] used a different justification by observing that one can gain high-assurance execution of custom chips (ASICs) by using trusted slow chips to verify the outputs of untrusted fast chips. In this Verifiable ASICs (VA) domain (§2.3), one can charge the operator for both verifier and prover and still identify regimes where their combination outperforms a baseline of executing the functionality in a trusted slow chip. However, Zebra does not charge for precomputation—and worse, introduces a preposterous assumption about daily delivery of hard drives to handle the problem.  \n\nThe work of this paper is to create a system, Giraffe; to charge the operator for all three costs; and to seek out regimes where this combined cost is superior to the baseline. Giraffe builds on Zebra and likewise targets the VA setting. However, some of Giraffe’s results and techniques apply to verifiable outsourcing more generally.  \n\nGiraffe has two high-level aspects. The first is a new probabilistic proof built on a protocol that we call T13 [77, §7]. As with all work in this area, T13 requires computations to be expressed as arithmetic circuits, or ACs (§2.1). T13 has three key advantages: (a) T13 is a variant of CMT [36, 49], which is Zebra’s base, and thus promises amenability to hardware implementation; (b) in the VA context, T13 can in principle pay for precomputation and break even, because it is geared to the aforementioned data-parallel model: precomputation is proportional to one sub-computation, and amortizes over $N$ subcomputations; and (c) T13 ought to permit breaking even for small $N$ : CMT has low overhead compared to alternatives [85]. From this starting point, Giraffe does the following (§3):  \n\n• Giraffe improves T13. Most significantly, Giraffe makes the prover asymptotically time-optimal: for sufficiently large $N$ , the prover’s work is now only a multiple $(\\approx10\\times)$ of executing the AC (§3.1). This can save an order of magnitude or more for any implementation of T13 in any context (for example, vSQL [88]), and is of independent interest. • Giraffe develops a design template that automatically instantiates physically realizable, efficient, high-throughput ASIC designs for the prover and verifier. The basic challenge is that, consistent with our search for applicable regimes, there are variables with wide ranges: small and large $N$ , different hardware substrates, etc. As a result, the optimal architectures are diverse. For example, large ACs (large sub-computations and/or large $N$ ) must iteratively reuse the underlying hardware whereas small ACs call for high parallelism. Giraffe responds with the RWSR: a new hardware structure that, when applied to the data flows in T13, both runs efficiently in serial execution and parallelizes easily. • Giraffe demonstrates algorithmic improvements that apply to all CMT-based systems [36, 77, 79, 81, 82, 88]. This includes reducing the verifier’s main bottleneck by ${\\approx}3\\times(\\S3.3)$ , eliminating a log factor from one of the verifier’s other computations by shifting additional work to the prover, and other optimizations that result in constant-factor improvements ([83, Appx. B.2]).  \n\nThe second aspect of Giraffe is motivated by our search for applicable regimes. In existing systems, protocol overhead limits the maximum size of a computation that can be outsourced. Worse, outsourcing really makes sense only if the computation is naturally expressed as an AC; otherwise, the asymptotic savings do not apply until program sizes are well beyond the aforementioned maximum. While these systems differ in the particulars, their restrictions are qualitatively similar—and there has been no fundamental progress on the expressivity issue over the last six years. As a consequence, it seems imperative to adapt to this situation. Two possible approaches are to handle these constraints by outsourcing amenable pieces of a given computation and to apply program transformations to increase the range of suitable computations. These ideas have of course appeared in the literature on compiling cryptographic protocols [43, 45, 59, 87], but previous efforts in the context of verifiable outsourcing have been very limited [37, 84].  \n\nWe study techniques for each of these approaches, adapted to this setting (§4). Giraffe employs slicing, which takes as input an abstract cost model and a program, automatically identifies amenable subregions of the program, and generates glue code to sew the outsourced pieces into the rest of the program. Slicing is a very general technique that can work with all probabilistic proof implementations. Giraffe also uses squashing, which transforms sequential ACs into parallel ACs and adjusts the verifier to link these computations; this is relevant to CMT and T13, which require parallel ACs.  \n\nOur implementation of Giraffe (§5) applies the above transformations to programs written in a subset of C, producing one or more ACs. Giraffe’s design template uses these ACs, along with several physical parameters (hardware substrates, chip area, etc.), and automatically generates concrete hardware designs for the prover and verifier, built in SystemVerilog, that can be used for cycle-accurate simulation or synthesized (i.e., compiled to a chip).  \n\nWe evaluate using detailed simulation and modeling of these generated hardware designs. Accounting for all costs (prover, precomputation, verifier), Giraffe saves compared to native execution across a wide range of computation sizes and hardware substrates (§6.2). Giraffe breaks even on operating costs for $N{\\approx}30$ parallel sub-computations; this value is essentially independent of the size of each sub-computation (§6.1). Compared to prior work in the VA setting, Giraffe scales to $500\\times$ larger computation sizes, holding all else constant (§8.1). A disadvantage of Giraffe is that its verifier is costlier than Zebra’s, and thus Giraffe’s break-even point is higher than Zebra’s. This is not because Zebra is fundamentally cheaper, but rather because it assumes away precomputation and thus does not have to pay for it. Furthermore, Giraffe’s program analysis techniques expand applicability beyond Zebra; our experiments demonstrate that slicing enables an image-matching application that neither Zebra nor Giraffe could otherwise handle (§8.2).  \n\nNevertheless, Giraffe has limitations (some of which reflect the research area itself (§9)). Breaking even requires data-parallel computations (to amortize precomputation), requires that the computation be naturally expressed as a layered AC, and requires a large gap between the hardware technologies used for the verifier and prover (which holds in some concrete settings; see [82, §1]). Moreover, the absolute cost of verifiability is still very high. Finally, the program transformation techniques have taken only a small first step.  \n\nDespite these limitations, we think that Giraffe has a substantial claim to significance: it adopts the most stringent cost regime in the verifiable outsourcing literature and (to our knowledge) is the only system that can profitably outsource under this accounting.  \n\nDebts and contributions. Giraffe builds on the T13 protocol [77, $\\S7]$ and an optimization [78] (§2.2). It also generalizes a prior technique [1–3, 77, 82] (§3.2, “Algorithm”). Finally, Giraffe borrows from Zebra [82], specifically: the setting (§2.3), how to evaluate in that setting (§2.3, §6.2), a high-level design strategy (implicit in this paper), a design for a module within the prover (footnote 5), and the application to Curve25519 (§8.1). Giraffe’s contributions are:  \n\n• Algorithmic refinements of the T13 interactive proof, yielding an asymptotically optimal prover (§3.1) and a ${\\approx}3\\times$ reduction in the verifier’s main bottleneck (§3.3).   \n• Hardware design templates for prover and verifier chips (§3.2, “Computing in hardware”; $\\S3.3)$ . We note that automatically generating a wide variety of optimized hardware designs is a significant technical challenge; it is achieved here via the introduction of the RWSR (and other hardware primitives), and the observation that RWSRs service a wide range of possible designs.   \n• Techniques for compiling from a subset of C to ACs while automatically optimizing for outsourcing based on cost models (§4).   \n• An implemented pipeline that takes as input a program in a subset of C and physical parameters, and produces hardware designs automatically (§5).   \n• Evaluation of the whole system (§6–§8) and a new application of verifiable outsourcing: image matching using a pyramid (§8.2).   \n• The first explicit consideration of the stringent, tripartite cost regime, and—for all of Giraffe’s limitations—being the first that can at least sometimes outsource profitably in that regime.  \n\n# 2 BACKGROUND  \n\n# 2.1 Probabilistic proofs for verifiability  \n\nThe description below is intended to give necessary terminology;   \nit does not cover all variations in the literature.  \n\nSystems for verifiable outsourcing enable the following. A verifier $_\\mathcal{V}$ specifies a computation $\\Psi$ (often expressed in a high-level language) to a prover $\\mathcal{P}.\\mathcal{V}$ determines input $x$ ; $\\mathcal{P}$ returns $y$ , which is purportedly $\\Psi(x)$ . A protocol between $_\\mathcal{V}$ and $\\mathcal{P}$ allows $_\\mathcal{V}$ to check whether $y=\\Psi(x)$ but without executing $\\Psi$ . There are few (and sometimes no) assumptions about the scope of $\\mathcal{P}$ ’s misbehavior.  \n\nThese systems typically have a front-end and a back-end. The interface between them is an arithmetic circuit (AC). In an AC, the domain is a finite field $\\mathbb{\\ F}$ , usually $\\mathbb{F}_{p}$ (the integers mod a prime $p$ ); “gates” are field operations (add or multiply), and “wires” are field elements. The front-end transforms $\\Psi$ from its original expression to an AC, denoted $c$ ; this step often uses a compiler [19, 22, 31, 32, 37, 45, 66, 73, 75, 81, 84], though is sometimes done manually [18, 36, 77]. The back-end is a probabilistic proof protocol, targeting the assertion ${}^{\\omega}y=C(x){}^{,}$ ; this step incorporates tools from complexity theory and sometimes cryptography.  \n\n# 2.2 Starting point for Giraffe’s back-end: T13  \n\nGiraffe’s back-end builds on a line of interactive proofs [12, 49, 50, 58, 76]: GKR [49], as refined and implemented by CMT [36], Allspice [81], Thaler [77], and Zebra [82]. Our description below sometimes borrows from [81, 82].  \n\nIn these works, the AC $c$ must be layered: the gates are partitioned, and there are wires only between adjacent partitions (layers). Giraffe’s specific base is T13 [77, §7], with an optimization [78]. T13 requires data parallelism: $c$ must have $N$ identical sub-circuit copies, each with its own inputs and outputs $x$ and $y$ now denote the aggregate inputs and outputs). We call each copy a sub-AC. Each sub-AC has $d$ layers. For simplicity, we assume that every sub-AC layer has the same width, $G$ (this implies that $|x|=|y|=N\\cdot G $ . The properties of T13 are given below; probabilities are over $\\phi_{\\mathrm{~s~}}^{*}$ random choices ([83, Appx. A] justifies these properties, by proof and reference to the literature):  \n\n• Completeness. If $y=C(x)$ , and if $\\mathcal{P}$ follows the protocol, then $\\operatorname*{Pr}\\{{\\mathcal{V}}\\operatorname{accepts}\\}=1$ .   \n• Soundness. If $y\\neq C(x)$ , then $\\mathrm{Pr}\\{\\mathcal{V}\\mathrm{accepts}\\}<\\epsilon$ , where $\\epsilon=$ $(\\lceil\\log|y|\\rceil+6d\\log{(G\\cdot N)})/|\\mathbb{F}|$ . This holds unconditionally (no assumptions about $\\mathcal{P}$ ). Typically, $\\left\\lvert\\mathbb{F}\\right\\rvert$ is astronomical, making this error probability tiny.   \n• Verifier’s running time. $_\\mathcal{V}$ requires precomputation that is proportional to executing one sub-AC: $O(d\\cdot G)$ . Then, to validate all inputs and outputs, $_\\mathcal{V}$ incurs cost $O(d\\cdot\\log\\left(N\\cdot G\\right)+|x|+$ $\\left|y\\right|)$ (which, under our “same-size-layer assumption”, is $O(d\\cdot$ $\\log\\left(N\\cdot G\\right)+N\\cdot G))$ . Notice that the total cost to verify $c$ , $O(d\\cdot$ $G+d\\cdot\\log N+N\\cdot G)$ , is less than the cost to execute $c$ directly, which is $O(d\\cdot G\\cdot N)$ .   \n• Prover’s running time. $\\mathcal{P}$ ’s running time is $O(d\\cdot G\\cdot N\\cdot\\log G);$ we improve this later (§3.1).  \n\nDetails. Within a layer of $c$ , each gate is labeled with a pair $(n,g)\\in$ $\\{0,1\\}^{b_{N}}\\times\\{0,1\\}^{b_{G}}$ , where $b_{N}\\triangleq\\log N$ and $b_{G}\\triangleq\\log G$ . (We assume for simplicity that $N$ and $G$ are powers of 2.) We also view labels numerically, as elements in $\\left\\{0,\\dots,N-1\\right\\}\\times\\left\\{0,\\dots,G-1\\right\\}$ . In either case, $n$ (a gate label’s upper bits) selects a sub-AC, and $g$ (a gate label’s lower bits) indexes a gate within the sub-AC.  \n\nEach layer i has an evaluator function $V_{i}:\\{0,1\\}^{b_{N}}{\\times}\\{0,1\\}^{b_{G}}\\rightarrow\\mathbb{R}$ that maps a gate’s label to the output of that gate;3 implicitly, $V_{i}$ depends on the input $x$ . By convention, the layers are numbered in reverse execution order. Thus, $V_{0}$ refers to the output layer, and $V_{d}$ refers to the inputs. For example, $V_{0}(n,j_{1})$ is the correct $j_{1}$ th output in sub-AC $n$ ; likewise, $V_{d}(n,j_{2})$ is the $j_{2}\\mathrm{th}$ input in sub-AC $n$ .  \n\nNotice that $_\\mathcal{V}$ wants to be convinced that $y$ , the purported outputs, matches the correct outputs, as given by $V_{0}$ . However, $_\\mathcal{V}$ cannot check this directly: evaluating $V_{0}$ would require re-executing C. Instead, $\\mathcal{P}$ combines all $V_{0}(\\cdot)$ values into a digest. Then, the protocol reduces this digest to another digest, this one (purportedly) corresponding to all of the values $V_{1}(\\cdot)$ . The protocol proceeds in this fashion, layer by layer, until $_\\mathcal{V}$ is left with a purported digest of the input $x$ , which $_\\mathcal{V}$ can then check itself.  \n\nInstantiating the preceding sketch requires some machinery. A key element is the sum-check protocol [58], which we will return to later (§3.1). For now, let $P\\colon\\mathbb{F}^{m}\\to\\mathbb{F}$ be an $m$ -variate polynomial. In a sum-check invocation, $\\mathcal{P}$ interactively establishes for $_\\mathcal{V}$ a claim about the sum of the evaluations of $P$ over the Boolean hypercube $\\{0,1\\}^{m}$ ; the number of protocol rounds is $m$ .  \n\nAnother key element is extensions. Technically, an extension $\\tilde{f}$ of a function $f$ is a polynomial that is defined over a domain that encloses the domain of $f$ and equals $f$ at all points where $f$ is defined. Informally, one can think of $\\tilde{f}$ as encoding the function table of $f$ . In this paper, extensions will always be multilinear extensions: the polynomial has degree at most one in each of its variables. We notate multilinear extensions with tildes.  \n\nBased on the earlier sketch, we are motivated to express $\\tilde{V}_{i-1}$ in terms of $\\tilde{V}_{i}$ . To that end, we define several predicates. The functions add(·) and $\\mathrm{mult}(\\cdot)$ are wiring predicates; they have signatures $\\{0,1\\}^{3b_{G}}\\rightarrow\\{0,1\\}$ , and implicitly describe the structure of a subAC. $\\mathrm{add}_{i}(g,h_{0},h_{1})$ returns 1 iff (a) within a sub-circuit, gate $g$ at layer $i\\mathrm{~-~}1$ is an add gate and (b) the left and right inputs of $g$ are, respectively, $h_{0}$ and $h_{1}$ at layer $i.\\mathrm{mult}_{i}$ is defined analogously. Note that these predicates ignore the “top bits” (the $n$ component) because all sub-ACs are identical. We also define the equality predicate eq : $\\{0,1\\}^{2b_{N}}\\rightarrow\\{0,1\\}$ with $\\operatorname{eq}(a,b)=1$ iff $a$ equals $^b$ . Notice that these predicates admit extensions: add, mult: $\\mathbb{F}^{3b_{G}}\\to\\mathbb{F}$ and $\\widetilde{\\mathbf e q}:\\mathbb{F}^{2b_{N}}\\bar{\\mathbf\\Sigma}\\rightarrow\\mathbb{F}$ . (We give explicit expressions in [83, Appx. A].) We can now express $\\tilde{V}_{i-1}$ in terms of a polynomial $P_{q,i}$ :  \n\n$$\n\\begin{array}{r l}&{P_{q,i}(\\boldsymbol{r}_{0},\\boldsymbol{r}_{1},\\boldsymbol{r}^{\\prime})\\triangleq\\widetilde{\\mathrm{eq}}(q^{\\prime},\\boldsymbol{r}^{\\prime})}\\ &{\\qquad\\cdot\\left[\\mathrm{a}\\tilde{\\mathrm{d}}\\mathrm{d}_{i}(q,\\boldsymbol{r}_{0},\\boldsymbol{r}_{1})\\cdot\\Big(\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{0})+\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{1})\\Big)\\right.}\\ &{\\qquad\\left.+\\operatorname*{mit}_{i}(q,\\boldsymbol{r}_{0},\\boldsymbol{r}_{1})\\cdot\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{0})\\cdot\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{1})\\right].}\\ &{\\qquad\\tilde{V}_{i-1}(q^{\\prime},q)=\\displaystyle\\sum_{h_{0},h_{1}\\in\\{0,1\\}^{b_{G}}}\\sum_{n\\in\\{0,1\\}^{b_{N}}}P_{q,i}(h_{0},h_{1},n).}\\end{array}\n$$  \n\nThe signatures are $P_{q,i}\\colon\\mathbb{R}^{2b_{G}+b_{N}}\\to\\mathbb{F}$ and $\\tilde{V}_{i-1},\\tilde{V}_{i}\\colon\\mathbb{F}^{b_{N}}\\times\\mathbb{F}^{b_{G}}\\rightarrow$ F. Equation (2) follows from an observation of [78] applied to a claim in [77, §7]. For intuition, notice that (i) $P_{q,i}$ is being summed only at points where its variables are 0-1, and (ii) at these points, if $(q^{\\prime},q)$ is a gate label (rather than an arbitrary value in $\\mathbb{F}^{b_{N}}\\times\\mathbb{F}^{b_{G}})$ , then the extensions of the predicates take on 0-1 values and in particular eliminate all summands except the one that contains the inputs to the gate $(q^{\\prime},q)$ .  \n\nAn excerpt of the protocol appears in Figure 1; the remainder appears in [83, Appx. A]. It begins with $_\\mathcal{V}$ wanting to be convinced that $\\tilde{V}_{0}$ (which is the extension of the correct $C(x))$ is the same polynomial as $\\tilde{V}_{y}$ (which denotes the extension of the purported output $y$ ). $_\\mathcal{V}$ thus chooses a random point in both polynomials’ domain, $(q_{0}^{\\prime},q_{0})$ , and wants to be convinced that $\\tilde{V}_{0}(q_{0}^{\\prime},q_{0})=\\tilde{V}_{y}(q_{0}^{\\prime},q_{0})\\stackrel{\\triangle}{=}a_{0}$ . Notice that (i) $\\tilde{V}_{0}(q_{0}^{\\prime},q_{0})$ can be expressed as the sum over a Boolean hypercube of the polynomial $P_{q_{0},1}$ (Equation (2)), and (ii) $P_{q_{0},1}$ itself is expressed in terms of $\\tilde{V}_{1}$ (Equation (1)). Using a sum-check invocation, the protocol exploits these facts to reduce $\\tilde{V}_{0}(q_{0}^{\\prime},q_{0})=a_{0}$ to a claim: $\\tilde{V}_{1}(\\bar{q}_{1}^{\\prime},q_{1})=a_{1}$ . This continues layer by layer until $_\\mathcal{V}$ obtains the claim: $\\bar{\\tilde{V}}_{d}(q_{d}^{\\prime},q_{d})=a_{d}$ $_\\mathcal{V}$ checks that assertion directly.  \n\nT13 incorporates one sum-check invocation—each of which is 2bG + bN rounds—for each polynomial Pq0,1, . . . , Pqd 1,d .  \n\n# 2.3 Verifiable ASICs  \n\nGiraffe’s back-end works in the Verifiable ASICs (VA) setting [82]. Giraffe also borrows evaluation metrics and some design elements from [82]; we summarize below.  \n\nConsider some principal (a government, fabless semiconductor company, etc.) that wants high-assurance execution of a custom chip (known as an ASIC) [82, $\\S1,\\S2.1]$ . The ASIC must be manufactured at a trustworthy foundry, for example one that is onshore. However, for many principals, high-assurance manufacture means an orders-of-magnitude sacrifice in price and performance, relative  \n\n1: function Verify(ArithCircuit c, input $x$ , output $y$ )   \n2: (q, qo)  FlogN x FlogG   \n3: $a_{0}\\gets\\tilde{V}_{y}(q_{0}^{\\prime},q_{0})//\\tilde{V}_{y}$ is the multilin. ext. of the output $y$   \n4: SendToProver $(q_{0}^{\\prime},q_{0})$   \n5: $d\\gets c$ .depth   \n6:   \n7: for $i=1,\\ldots,d$ do   \n8: // Reduce $\\tilde{V}_{i-1}(q_{i-1}^{\\prime},q_{i-1})\\stackrel{?}{=}a_{i-1}$ to $P_{q,i}(r_{0},r_{1},r^{\\prime})\\stackrel{?}{=}e$   \n9: $(e,r^{\\prime},r_{0},r_{1})\\gets\\mathrm{SumCHECKV}(i,a_{i-1})$   \n10:   \n11: // Below, $\\mathcal{P}$ describes a univariate polynomial $H(t)$ ,   \n12: $//\\protect$ of degree $\\log G$ , claimed to be $\\tilde{V}_{i}\\left(r^{\\prime},\\left(r_{1}-r_{0}\\right)t+r_{0}\\right)$   \n13: $H\\gets$ Receive from $\\mathcal{P}$ // see [83, Fig. 14, line 47]   \n14: $\\begin{array}{c}{{v_{0}\\gets H(0)}}\\ {{v_{1}\\gets H(1)}}\\end{array}$   \n15:   \n16:   \n17: // Reduce $P_{q,i}(r_{0},r_{1},r^{\\prime})\\stackrel{?}{=}e$ to two questions:   \n18: // $\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{0})\\stackrel{?}{=}v_{0}$ and $\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{1})\\stackrel{?}{=}v_{1}$   \n19:   \n20: $\\begin{array}{r}{\\mathrm{i}\\textbf{f}e\\neq\\widetilde{\\mathrm{eq}}(q_{i-1}^{\\prime},r^{\\prime})\\cdot\\left[\\mathrm{a}\\widetilde{\\mathrm{d}}\\mathrm{d}_{i}(q_{i-1},r_{0},r_{1})\\cdot(v_{0}+v_{1})\\right.}\\ {\\left.+\\mathrm{m}\\widetilde{\\mathrm{u}}\\mathrm{lt}_{i}(q_{i-1},r_{0},r_{1})\\cdot v_{0}\\cdot v_{1}\\right]}\\end{array}$   \n21: then   \n22: return reject   \n23:   \n24: // Reduce the two $v_{0},v_{1}$ questions to $\\tilde{V}_{i}(q_{i}^{\\prime},q_{i})\\overset{?}{=}a_{i}$   \n25: $\\begin{array}{l}{\\tau_{i}\\overset{R}{\\leftarrow}\\mathbb{F}}\\ {a_{i}\\gets H(\\tau_{i})}\\ {(q_{i}^{\\prime},q_{i})\\gets(r^{\\prime},(r_{1}-r_{0})\\cdot\\tau_{i}+r_{0})}\\end{array}$   \n26:   \n27:   \n28:   \n29: SendToProver $\\left(\\tau_{i}\\right)$   \n30:   \n31: // $\\tilde{V}_{d}(\\cdot)$ is the multilinear extension of the input $x$   \n32: if $\\tilde{V}_{d}(q_{d}^{\\prime},q_{d})=a_{d}$ then   \n33: return accept   \n34: return reject  \n\nto an advanced but untrusted foundry. This owes to the economics and scaling behavior of semiconductor technology. In the VA setup, one manufactures a prover in a state-of-the-art but untrusted foundry (we refer to the manufacturing process and hardware substrate as the untrusted technology node) and a verifier in a trusted foundry (the trusted technology node). A trusted integrator combines the two ASICs. This arrangement makes sense if their combined cost is cheaper than the native baseline: an ASIC manufactured in the trusted technology node.  \n\nVA is instantiated in a system called Zebra, which implements an optimized variant of CMT [36, 78, 81]. Zebra is evaluated with two metrics [82, $\\S2.3]$ . The first is energy ( $E_{\\mathrm{:}}$ , in joules/run), which is a proxy for operating cost. Energy tracks asymptotic (serial) running time: it captures the number of operations and the efficiency of their implementation. The second is area/throughput $(A/T$ , in $\\mathrm{mm}^{2}/(\\mathrm{ops}/\\mathrm{sec}))$ . Area is a proxy for manufacturing cost; normalizing by throughput reflects cost at a given performance level.  \n\nFurthermore, Zebra is designed to respect two physical constraints. The first is a maximum area, to reflect manufacturability (larger chips have more frequent defects and hence lower yields). The second is a maximum power dissipation, to limit heat. The first constraint limits $A$ (and thus the hardware design space) and the second limits the product of energy and throughput, $E\\cdot T$ .  \n\nZebra’s prover architecture consists of a collection of pipelined sub-provers, each one doing the execution and proving work for one layer of an AC [82, $\\S3.1\\substack{-3.2}]$ . Within a sub-prover, there is dedicated hardware for each AC gate in a layer. Zebra’s verifier is also organized into layers [82, $\\S3.5]$ . Giraffe incorporates this overall picture, including some integration details $[82,\\S4]$ . However, Giraffe requires a different architecture, as we explain next.  \n\n# 3 PROTOCOL AND HARDWARE DESIGN  \n\nThree goals drive Giraffe’s hardware back-end:  \n\nG1: Scale to large $N$ without sacrificing G. V’s precomputation scales with the size of one sub-AC (§2.2); it needs to amortize this over multiple sub-AC copies, $N$ . Further, we have an interest in handling large computations (sub-ACs and ACs). This implies that Giraffe’s design must reuse underlying hardware modules: for large $N$ and sub-AC width $G$ , requiring a number of modules proportional to $N\\cdot G$ is too costly. Zebra’s design is not suitable, since it requires logic proportional to the amount of work in an AC layer [82, Fig. 5].  \n\nG2: Be efficient. In this context, good efficiency implies lower cross-over points on the metrics of merit (§2.3). This in turn means custom hardware, which is expected in ASIC designs but, for us, is in tension with the next goal.  \n\nG3: Produce designs automatically. Ideally, the goal is to produce a compiler that takes as input a high-level description of the computation along with physical parameters (technology nodes, chip area, etc.; $\\S2.3)$ and produces synthesizable hardware (§5). This goes beyond convenience: a goal of this work is to understand where, in terms of computations $(G,N$ , etc.) and physical parameters (technology nodes, chip area, etc.), an abstract algorithm (T13) applies. To do this, we need to be able to optimize hardware for both the computations and the physical parameters, which poses a significant challenge: for different computations and physical parameters, different hardware designs make sense. For example, if $N$ and $G$ are small, iteratively reusing hardware might not consume all available chip area; one would prefer to spend this area to gain parallelism and thus increase throughput.  \n\nGiraffe answers this challenge by developing a design template that takes as input a description of the desired computation and a set of physical parameters, and produces as output an optimized hardware design. The template’s “primitives” are custom hardware structures that enable efficient reuse (serial execution) when there are few of them, but can be automatically parallelized. To use the design template, the designer simply specifies its inputs; design generation is fully automatic.  \n\nIn the rest of the section, we modify T13 to obtain an asymptotic improvement in $\\mathcal{P}$ ’s work (§3.1); this contributes to Giraffe’s scalability, and is of independent interest. We also describe aspects of the hardware design template for $\\mathcal{P}$ (§3.2). Finally, we do the same for $_\\mathcal{V}$ , and also describe optimizations that help offset the cost of precomputation (§3.3). Compared to prior work, these optimizations reduce $_\\mathcal{V}$ ’s primary cost by nearly $3\\times$ and eliminate a log factor from one of $_\\mathcal{V}$ ’s secondary costs; since $_\\mathcal{V}$ ’s costs dominate, these optimizations have a direct effect on end-to-end performance.  \n\nNotation. $[a,b]$ denotes $\\{a,a+1,\\ldots,b\\}$ . For a vector $u$ , $u[\\ell]$ denotes the $\\ell$ th entry, indexed from 1; $u[\\ell_{1}..\\ell_{2}]$ denotes the sub-vector between indices $\\ell_{1}$ and $\\ell_{2}$ , inclusive. Arrays are accessed similarly, but are indexed from 0. Vectors are indicated with lower-case letters, arrays with upper-case. Define $\\chi_{0},\\chi_{1}:\\mathbb{F}\\to\\mathbb{F}$ as $\\chi_{1}(w)=w$ , $\\chi_{0}(w)=1-w$ . Similarly, if s $\\in\\{0,1\\}^{\\gamma}$ and $u\\in\\mathbb{F}^{\\gamma}$ , $\\chi_{s}(u)\\triangleq\\Pi_{\\ell=1}^{\\gamma}\\chi_{s[\\ell]}(u[\\ell]).$ Notice that when $u$ comprises 0-1 values, $\\chi_{s}(u)$ returns 1 if $u=s$ and 0 otherwise.  \n\n# 3.1 Making $\\mathcal{P}$ time-optimal  \n\nThis section describes an algorithmic refinement that, by restructuring the application of the sum-check protocol, slashes $\\mathcal{P}$ ’s overhead. Specifically, $\\mathcal{P}$ ’s running time drops from $O(d\\cdot N\\cdot G\\cdot\\log G)$ to $O(d\\cdot(N\\cdot G+G\\cdot\\log G))$ . If $N\\gg\\log G,\\mathcal{P}$ ’s new running time is linear in the number of total gates in the AC—that is, the prover has no asymptotic overhead! Prior work [77, §5] achieved time-optimality in special cases (if the AC’s structure met an ad hoc and restrictive condition); the present refinement applies in general, whenever there are repeated sub-ACs.  \n\nThe $O(\\log G)$ reduction translates to concrete double digit factors ([83, Appx. D]). For example, software provers in this research area [36, 77, 79, 81, 88] typically run with $G$ at least $2^{18}$ ; thus, a software T13 prover’s running time improves by at least $18\\times$ . For a hardware prover, the $A/T$ metric improves by approximately $\\log G$ , as computation is the main source of area cost ([83, Appx. C], [82, Fig. 6 and 7]). The gain is less pronounced for the $E$ metric: storage and communication are large energy consumers but are unaffected by the refinement ([83, Appx. C]).  \n\nBefore describing the refinement, we give some background on sum-check protocols; for details, see [8, §8.3; 49, $\\S2.5$ ; 58; 76]. Consider a polynomial $P$ in $m$ variables and a claim that  \n\n$\\textstyle\\sum_{(t_{1},\\dots,t_{m})\\in\\{0,1\\}^{m}}P(t_{1},\\dots,t_{m})=L.$ . In round $j$ of the sum-check protocol, $\\mathcal{P}$ must describe to $_\\mathcal{V}$ a degree- $\\alpha$ univariate polynomial $F_{j}(t^{*})$ , where $\\alpha$ depends on $P$ and $j$ :  \n\n$$\nF_{j}(t^{*})=\\sum_{\\scriptstyle(t_{j+1},\\ldots,t_{m}){\\in}\\{0,1\\}^{m-j}}P(\\rho_{1},\\ldots,\\rho_{j-1},t^{*},t_{j+1},\\ldots,t_{m}).\n$$  \n\nTo discharge this obligation, $\\mathcal{P}$ computes evaluations $F_{j}(k)$ , for $\\alpha{+}1$ different values of $k$ . Then, at the end of round j, $_\\mathcal{V}$ sends $\\rho_{j}$ , for use in the next round. Notice the abstract pattern: in every round $j$ $\\mathcal{P}$ computes $\\alpha{+}1$ sums over a Boolean hypercube of dimension $m{-}j$ The number of hypercube vertices shrinks as $j$ increases: variables that were previously summed become set, or bound, to a $\\rho_{j}$ .  \n\nLet us map this picture to our context. There is one sum-check run for each layer $i\\in[1,d];P$ is the per-layer polynomial $P_{q,i}$ defined in Equation (1); $m=2b_{G}+b_{N}$ ; the $\\rho_{j}$ are aliases for the components of $r_{0},r_{1},r^{\\prime}$ ; likewise, the $t_{j}$ alias the components of $h_{0},h_{1},n$ . Also, $\\alpha$ is 2 or 3; this follows from Equation (1), recalling that each multilinear extension (eq, add, etc.) by definition has degree one in its variables.  \n\nThere are now two interrelated questions: In what order should the variables be bound? How does $\\mathcal{P}$ compute the $\\alpha{+}1$ sums per round? In T13, the order is $h_{0},h_{1},n_{:}$ , as in Equation (2). This enables $\\mathcal{P}$ to compute the needed sums in time $O(N\\cdot G\\cdot\\log G)$ per-layer [77, $\\S7]$ . $\\mathcal{P}$ ’s total running time is thus $O(d\\cdot N\\cdot G\\cdot\\log G)$ .  \n\nGiraffe’s refinement changes the order in which variables are bound, and exploits that order to simplify $\\mathcal{P}$ ’s work. Giraffe’s order is $n,h_{0},h_{1}$ . From here on, we write $P_{q,i}(h_{0},h_{1},n)$ as $P_{q,i}^{*}(n,h_{0},h_{1})$ ; $P_{q,i}\\equiv P_{q,i}^{*}$ except for argument order. Below, we describe the structure of $\\mathcal{P}$ ’s per-round obligations, fixing a layer i. This serves as background for the hardware design (§3.2) and as a sketch of the argument for the claimed running time. A proof, theorem statement, and pseudocode are in [83, Appx. B].  \n\nThe rounds decompose into two phases. Phase 1 is rounds $j\\in$ [1, $b_{N}]$ . Observe that in this phase, $\\mathcal{P}$ ’s sums seemingly have the form: $\\begin{array}{r}{F_{j}(k)=\\sum_{n[j+1..b_{N}]}\\sum_{h_{0},h_{1}}P_{q,i}^{*}(r^{\\prime}[1..j-1],k,n[j+1..b_{N}],h_{0},h_{1}),}\\end{array}$ where the outer sum is over all $n[j{+}1..b_{N}]\\in\\{0,1\\}^{b_{N}-j}$ . However, many $(h_{0},h_{1})$ combinations cause $P_{q,i}^{*}(...,h_{0},h_{1})$ to evaluate to 0.4 As a result, there is a more convenient form for the inner sum. Define $S_{\\mathrm{all},i}\\subseteq\\{0,1\\}^{3b_{G}}$ as all layer- $(i{-}1)$ gates with their layer-i neighbors, and $\\mathrm{OP}_{g}$ as $^{\\circ}+{}^{,}$ if $g$ is an addition gate and $\\begin{array}{c}{{\\epsilon\\circ\\circ}}\\ {{\\cdot\\circ}}\\end{array}$ if $g$ is a multiplication gate. Then $F_{j}(k)$ can be written as:  \n\n$$\n\\begin{array}{r l}{{}}&{{\\displaystyle F_{j}(k)=\\sum_{n[j+1...b_{N}](g,g_{L},g_{R})\\in S_{\\mathrm{all},i}}\\mathrm{term}\\mathrm{P}1_{j,n,k}\\cdot\\mathrm{term}\\mathrm{P}2_{g}}}\\ {{}}&{{\\displaystyle\\qquad\\cdot\\mathrm{OP}_{g}(\\mathrm{termL}_{j,n,g_{L},k},\\mathrm{term}\\mathrm{R}_{j,n,g_{R},k}),}}\\end{array}\n$$  \n\nwhere termP1 depends on $j,n,k$ ; termP2 depends on $g$ , and so forth; these also depend on values of $\\rho$ from prior rounds and prior layers. Section 3.2 makes some of these terms explicit ([83, Appx. B] fully specifies).  \n\nPhase 2 is the remaining $2b_{G}$ rounds. Here, there is only a single sum, over increasingly bound components of $h_{0},h_{1}$ . As with phase 1, it is convenient to express the sum “gatewise”. Specifically, for rounds $j\\in\\left[b_{N}+1,b_{N}+2b_{G}\\right]$ , one can write $F_{j}(k)=$ $\\sum_{(g,g_{L},g_{R})\\in S_{\\mathrm{all},i}}$ $\\mathrm{termP}_{j,g,k}\\cdot\\mathrm{OP}_{g}(\\mathrm{termL}_{j,g_{L},k},\\mathrm{termR}_{j,g_{R},k})$ .  \n\nIn both phases, $\\mathcal{P}$ can compute each sum over $S_{\\mathrm{all},i}$ with $O(G)$ work. Thus, per-layer, the running time for phase 1 is $O(G\\cdot N/2)+$ $O(G\\cdot N/4)+\\cdot\\cdot\\cdot+O(G)=O(G\\cdot N)$ , and for phase 2 it is $O(G\\cdot\\log G)$ , yielding the earlier claim of $O(d\\cdot(N\\cdot G+G\\cdot\\log G))$ .  \n\n# 3.2 Design of $\\mathcal{P}$  \n\nConsider $\\mathcal{P}$ ’s obligations in layer $i$ , summarized at the end of the previous section. Notice that $\\mathcal{P}$ ’s phase-2 obligations are independent of $N$ . This is a consequence of Section 3.1; there is no such independence in the original variable order [77, §7]. In the current variable order, the bulk of $\\mathcal{P}$ ’s work occurs in phase 1, and so our description below focuses on phase 1.  \n\nWithin phase 1, the heaviest work item is computing termL, termR in each round. The rest of this section describes the obligation, the algorithm by which $\\mathcal{P}$ discharges it, and the hardware design that computes the algorithm. $\\mathcal{P}$ ’s other obligations (computing $\\mathrm{termP1}_{j,n,k}$ , etc.) and algorithms for discharging them are described in [83, Appx. B].  \n\nAlgorithm for computing termL,termR. Fixing a layer $i$ , in round $j$ , termL and termR are:  \n\n$$\n\\begin{array}{r l}&{\\mathrm{termL}_{j,n,g_{L},k}\\triangleq\\tilde{V}_{i}\\left(r^{\\prime}[1..j-1],k,n[j+1..b_{N}],g_{L}\\right)}\\ &{\\mathrm{termR}_{j,n,g_{R},k}\\triangleq\\tilde{V}_{i}\\left(r^{\\prime}[1..j-1],k,n[j+1..b_{N}],g_{R}\\right)}\\end{array}\n$$  \n\nNotice that for each $k$ , Equation (4) refers to $G\\cdot N/2^{j}$ values of $\\tilde{V}(\\cdot)$ Figure 2 depicts an algorithm, EvalTermLR, that computes these values in time $O(G\\cdot N/2^{j})$ for round $j_{:}$ , by adapting a prior technique [77, §5.4; 82, §3.3] (see also [1–3]). EvalTermLR is oriented around a recurrence. Let $h$ be a bottom-bit gate label at layer $i$ Then for all $\\sigma\\in\\{0,1\\}^{b_{N}-j}$ , the following holds (derived in [83, Appx. B.1]):  \n\n$$\n\\begin{array}{r}{\\tilde{V}_{i}\\left(r^{\\prime}[1..j],\\sigma,h\\right)=\\left(1-r^{\\prime}[j]\\right)\\cdot\\tilde{V}_{i}\\left(r^{\\prime}[1..j-1],0,\\sigma,h\\right)}\\ {+r^{\\prime}[j]\\cdot\\tilde{V}_{i}\\left(r^{\\prime}[1..j-1],1,\\sigma,h\\right).}\\end{array}\n$$  \n\nEvalTermLR relies on a two-dimensional array $W$ , and maintains the following invariant, justified shortly: at the beginning of every round j, $W[h][\\sigma]$ stores $\\tilde{V}_{i}(r^{\\prime}[1..j-1],\\sigma,h)$ , for $h\\in[0,G{-}1]$ and $\\sigma\\in[0,N/2^{j-1}-1]$ .  \n\nGiven this invariant, $\\mathcal{P}$ obtains all of the termL, termR values from $W$ (in line 7), as follows. We focus on termL. Write $n[j{+}1..b_{N}]$ as $n_{j+1}$ . Then, for $k=\\{0,1\\}$ , $\\mathrm{termL}_{j,n,g_{L},k}$ is $W[g_{L}][k+2\\cdot n_{j+1}]$ this follows from Equation (4) plus the invariant. Meanwhile, for $k=-1$ , $\\mathrm{termL}_{j,n,g_{L},-1}=2\\cdot\\mathrm{termL}_{j,n,g_{L},0}+(-1)\\cdot\\mathrm{termL}_{j,n,g_{L},1}$ This follows from Equations (4) and (5); $k=2$ is similar. termR is the same, except $g_{R}$ replaces $g_{L}$ . The total time cost is $O(G\\cdot N/2^{j})$ in round $j$ : Collapse performs $(N/2^{j-1})/2$ iterations, and there are $G$ calls to Collapse.  \n\nThe invariant holds for $j=1$ because $\\tilde{V}_{i}(r^{\\prime}[1..j-1],\\sigma,h)=$ $\\tilde{V}_{i}(\\sigma,h)=V_{i}(\\sigma,h),$ , which initializes $W[h][\\sigma]$ (line 3); the latter equality holds because functions equal their extensions when evaluated on bit vectors. Now, at the end of $j$ , line 16 applies Equation (5) to all $\\sigma\\in[0,N/2^{j}-1]$ , thereby setting $W[h][\\sigma]$ to $\\tilde{V}_{i}(r[\\bar{1}..j],\\sigma,h)$ This is the required invariant at the start of round $j+1$ .  \n\nComputing EvalTermLR in hardware. To produce a design template for $\\mathcal{P}$ consistent with Giraffe’s goals, we must answer three questions. First, what breakdown of $\\mathcal{P}$ ’s work makes sense: which portions are parallelized, and what hardware is iteratively reused in a round (G1)? Second, for iterative parts of the computation, how does $\\mathcal{P}$ load operands and store results (G2)? Finally, how can this design be adapted to a range of computations and physical parameters (G3)?  \n\nA convenient top-level breakdown is already implied by the prior formulation of $W$ : since Collapse operates on each $W[h]$ array independently, it is natural to parallelize work across these arrays. Giraffe allocates separate storage structures and logic implementing Collapse for each $W[h]$ array (and, of course, reuses this hardware from round to round for each array). We therefore focus on the design of one of these modules.  \n\nTo answer the second question, we first consider two straw men. The first is to imitate a software design: instantiate one module for field arithmetic and a RAM to store the $W[h]$ array, then iterate through the $\\sigma$ loop sequentially, loading needed values, computing  \n\n1: // initialize $W$ : array of $G$ arrays of $N$ values   \n2: for $h=0,\\ldots,G-1$ and $\\sigma=0,\\dots,N-1$ do   \n3: $W[h][\\sigma]\\leftarrow V_{i}(\\sigma,h)$   \n4:   \n5: function EvalTermLR(Array-of-arrays W )   \n6: for $j=1,\\ldots,b_{N}$ do   \n7: look up all termL, termR in $W$ (see text)   \n8:   \n9: r ′[j] ← Receive from V // see [83, Fig. 15, line 19]   \n10:   \n11: for $h=0,\\ldots,G-1$ do   \n12: $\\mathrm{Collapse}(W[h],N/2^{j-1},r^{\\prime}[j])$   \n13:   \n14: function Collapse(Array $A_{:}$ , size len, $r\\in\\mathbb{F}.$ )   \n15: for $\\sigma=0,\\ldots,\\mathrm{len}/2-1$ do   \n16: $A[\\sigma]\\gets(1-r)\\cdot A[2\\sigma]+r\\cdot A[2\\sigma+1]$  \n\n# RWSR specification  \n\n• Power-of-two storage locations, $K$   \n• Only locations 0 and 1 can be read   \n• The only write operation is $\\xleftarrow{s}$ . It is specified below. Informally, it updates one location, and causes all the “even” locations to behave like a distinct shift register (location 6 shifts to 4, etc.), and likewise with all of the “odd” locations.   \n1: operator $\\operatorname{RWSR}[a]\\xleftarrow{s}{v}$ is   \n2: // Note that all updates happen simultaneously   \n3: ${\\mathrm{RWSR}}[a]\\leftarrow v$   \n4: for $\\ell<K,\\ell\\neq a$ do   \n5: $\\mathrm{RWSR}[\\ell]\\longleftarrow\\mathrm{RWSR}[\\ell+2]$   \n6:   \n7: function RWSRCollapse(RWSR $R_{:}$ , size len, $r\\in\\mathbb{F}$ )   \n8: for $\\sigma=0,\\ldots,\\mathrm{len}/2-1$ do   \n9: $R[\\ln-2-\\sigma]\\stackrel{s}{\\gets}(1-r)\\cdot R[0]+r\\cdot R[1]$  \n\nover them, and storing the results. In practice, however, ASIC designers often prefer to avoid building RAM circuits. This is because generality has a price (e.g., address decoding imposes overheads in area and energy), RAM often creates a throughput bottleneck, and RAM is a frequent cause of manufacturability and reliability issues. (Of course, RAMs are a dominant cost in many modern ASICs, but that doesn’t mean that designers prefer RAM: often there is simply no alternative. For example, an unpredictable memory access pattern often necessitates RAM.)  \n\nThe second straw man is essentially the opposite: instantiate a bank of registers to hold values in $W[h]$ , along with two field multipliers and one adder per pair of adjacent registers, then create a wiring pattern such that the adder for registers $2\\sigma$ and $2\\sigma+1$ connects to the input of register $\\sigma$ . This arrangement computes the entire $\\sigma$ loop in parallel. This is similar to prior work [82, $\\S3.3]$ but in Giraffe $\\operatorname{O}(N G)$ multipliers is extremely expensive when $N$ and $G$ are large. It is also inflexible: in this design, the number of multipliers is fixed after selecting $N$ and $G$ .  \n\nGiraffe’s solution is a hybrid of these approaches; we first explain a serial version, then describe how to parallelize. Giraffe instantiates two multipliers and one adder that together compute one step of the $\\sigma$ loop. The remaining challenge is to get operands to the multipliers and store the result from the adder. Giraffe does so using a custom hardware structure that is tailored to the access pattern of the $W[h]$ arrays: for each $W[h]$ , read two values, write one value, read two values, and so on. Giraffe uses RWSRs, (“random-write shift registers”), one for each $W[h]$ . Figure 3 specifies the RWSR and shows its use for Collapse.  \n\nCompared to the first straw man, Giraffe’s design has several advantages. First, an RWSR only allows two locations to be read; compared to a general-purpose RAM, this eliminates the need for most logic to handle read operations. Second, Giraffe’s RWSR need not be “random-write”: its $\\xleftarrow{s}$ operator (Fig. 3, line 1) can be specialized to the address sequence of the RWSRCollapse algorithm (Fig. 3, line 9), making its write logic far simpler than a RAM’s, too. This means that an RWSR can be implemented in almost the same way as a standard shift register, and at comparable cost. Alternatively, an RWSR can be implemented like a RAM, using the same data storage circuits but dramatically simplified addressing logic. The latter approach might reduce energy consumption compared to implementing like a standard shift register, and it would still cost less than using a general-purpose RAM; but it would potentially re-introduce the above-mentioned manufacturaility and reliability concerns associated with RAM circuits.  \n\nThe remaining question is how this design can be efficiently and automatically parallelized. Notice that the loop over $\\sigma$ is serialized (because RWSRs allow only one write at a time); but what if the designer allocates enough chip area to accommodate four multipliers for $W[h]$ instead of two? In other words, how can Giraffe’s design template automatically improve RWSRCollapse’s throughput by using more chip area?  \n\nTo demonstrate the approach, we refer to the pseudocode of Figure 2. First, split each $W[h]$ array into two arrays, $W1[h]$ and $W2[h]$ . In place of the Collapse invocation (line 12), run two parallel invocations on $W1[h]$ and $W2[h]$ , each of half the length. Notice that each array has increasing “empty” space as the rounds go on. In round $j$ , the “live values” are the first $\\bar{N}/2^{j}$ elements in each of $W1[h]$ and $W2[h]$ ; regard $W[h]$ as their concatenation.  \n\nTo see why this gives the correct result, notice that each Collapse invocation combines neighboring values of its input array. We can thus regard the values of $W[h]$ as the leaves of a binary tree, and Collapse as reducing the height of the tree by one, combining leaves into their parents. In this view, $W1[h]$ and $W2[h]$ represent the left and right subtrees corresponding to $W[h]$ . As a result, in round $j=$ $b_{N},W1[h]$ and $W2[h]$ each have one value; to obtain the final value of the Collapse operation, compute $(1-r)\\cdot W1[h][0]+r\\cdot W2[h][0]$  \n\nTo implement this picture in hardware, Giraffe instantiates two RWSRs, each of half the size. For even more parallelism, observe that each RWSR corresponds to a subtree of the full computation, and thus its work can be recursively split into two even smaller  \n\nRWSRs, each handling a correspondingly smaller subtree. Because of this structure, different choices of parallelism do not require the designer to do any additional design work (§5).  \n\n# 3.3 Scaling and optimizing $_\\mathcal{V}$  \n\nIn this section, we explain how $_\\mathcal{V}$ meets the starting design goals of scalability, efficiency, and automation. We do so by walking through three main costs for $_\\mathcal{V}$ , and how Giraffe handles them. Some of the optimizations apply to any CMT-based back-end [36, 77, 79, 81, 82, 88].  \n\nMultilinear extensions of I/O. $_\\mathcal{V}$ ’s principal bottleneck is computing the multilinear extension of its input $x$ and output $y$ (Figure 1, lines 3 and 32). Recall (§2.2) that $|x|=|y|=N\\cdot G;\\mathcal{V}$ ’s computation has at least this cost. When $N$ and $G$ are large, this is expensive and must be broken into parallel and serial portions. We show below that this work has a similar form to $\\mathcal{P}$ ’s (termL, termR; $\\S3.2_{,}$ ).  \n\nConsider the input $x$ and $\\tilde{V}_{d}$ $(y$ and $\\tilde{V}_{y}$ are similar). $_\\mathcal{V}$ must compute $\\tilde{V}_{d}(q_{d}^{\\prime},q_{d})$ . For $\\sigma\\in[0,N\\cdot G-1]$ , $\\tilde{V}_{d}(\\sigma)=V_{d}(\\sigma)$ , the $\\sigma$ th component of the input (§2.2). For $\\sigma\\in\\{0,1\\}^{b_{N}+b_{G}-\\ell}$ , we have  \n\n$$\n\\begin{array}{r}{\\tilde{V}_{d}\\left(r[1..\\ell],\\sigma\\right)=\\left(1-r[\\ell]\\right)\\cdot\\tilde{V}_{d}\\left(r[1..\\ell-1],0,\\sigma\\right)}\\ {+r[\\ell]\\cdot\\tilde{V}_{d}\\left(r[1..\\ell-1],1,\\sigma\\right).}\\end{array}\n$$  \n\nThis form is very close to Equation (5) (its derivation is similar to [83, Appx. B.1]). It follows that $_\\mathcal{V}$ can use $\\mathcal{P}$ ’s EvalTermLR to evaluate $\\tilde{V}_{d}(q_{d}^{\\prime},q_{d})\\colon\\mathcal{V}$ initializes an array $A$ , setting $A[\\sigma]$ to the $\\sigma$ th input value, for $\\sigma\\in\\left[0,N\\cdot G-1\\right]$ (cf. line 3, Fig. 2). $_\\mathcal{V}$ then invokes the algorithm MultiCollapse shown in Figure 4 on $A_{:}$ , setting $r$ to $(q_{d}^{\\prime},q_{d})$ . In total, MultiCollapse costs $2\\cdot N\\cdot G-2$ multiplications. To see how, notice that the initial size of $A$ is $N\\cdot G$ , so the first Collapse invocation costs $N\\cdot G$ multiplications; in each successive invocation, the cost is reduced by half. Summing gives the claimed cost.  \n\nMultiCollapse also applies to related systems, improving their constant factors; this is significant because in practice computing the multilinear extensions of $x$ and $y$ dominates $_\\mathcal{V}$ ’s costs. Allspice’s approach to this computation has leading constant 4 [81, $\\S5.1]$ Zebra [82] reduces the constant to 3 using a hand-tuned hardware structure; this does not meet Giraffe’s goal of producing designs automatically. MultiCollapse reduces this constant to 2. We now show how to reduce the constant to 1, leaving $_\\mathcal{V}$ with a $4{\\sqrt{N\\cdot G}}$ additive overhead. For the smallest problem sizes on which Giraffe breaks even (§6–8) this additive overhead is less than $25\\%$ ; on large computations it is negligible.  \n\nNotice that MultiCollapse describes a binary computation tree of depth $b_{\\mathrm{tot}}=b_{N}+b_{G}$ whose leaves are the inputs and whose root is the result $\\tilde{V}_{d}(q_{d}^{\\prime},q_{d})$ . Each node in this tree corresponds to a single step of Collapse, namely two multiplications and one addition (Fig. 2, line 16). The $b_{\\mathrm{tot}}/2$ layers of the computation tree closest to the leaves comprise $2^{b_{\\mathrm{tot}}/2}=\\sqrt{N\\cdot G}$ subtrees, each of which computes the dot product between $2^{b_{\\mathrm{tot}}/2}$ input values and an array $B[\\gamma]=\\chi_{\\gamma}(r[1..b_{\\mathrm{tot/2}}]),\\gamma\\in\\{0,1\\}^{b_{\\mathrm{tot/2}}}$ . This can be seen by expanding the recurrence of Equation (6) for $b_{\\mathrm{tot}}/2$ steps.  \n\nThe key observation is that MultiCollapse repeatedly recomputes the values of the array $B$ when computing these dot products. This means that each subtree costs $2\\cdot2^{b_{\\mathrm{tot}}/2}-2$ multiplications, whereas  \n\n1: // MultiCollapse costs 2 $|A|-2$ multiplications; see text   \n2: function MultiCollapse(Array $A$ , $\\bar{r}\\in\\mathbb{F}^{\\log|A|}$ )   \n3: loglen $\\leftarrow\\log\\vert A\\vert$   \n4: for $j=1,\\ldots,\\log\\mathrm{len}\\mathbf{d}$ o   \n5: $\\mathrm{Collapse}(A,2^{\\mathrm{loglen}+1-j},r[j])$ // see Figure 2, line 14   \n6: return A[0]   \n7:   \n8: function DotPMultiCollapse(Array $A_{:}$ , $r\\in\\mathbb{F}^{\\log\\left|A\\right|}.$ )   \n9: loglen $\\leftarrow\\log\\vert A\\vert$   \n10:   \n11: // $B$ is computed using the algorithm of [83, Fig. 13, Appx. A];   \n12: $//\\protect$ this costs $2\\cdot2^{b_{\\mathrm{tot}}/2}-2=2\\sqrt{N\\cdot G}-2$ multiplications.   \n13: B ← χγ (r [1..btot/2]) , γ ∈ {0, 1}btot/2   \n14:   \n15: // compute $2^{b_{\\mathrm{tot}}/2}$ dot products of length $2^{b_{\\mathrm{tot}}/2}$ ;   \n16: // this costs $N\\cdot G$ multiplications   \n17: for $j=0,\\ldots,2^{b_{\\mathrm{tot}}/2}-1$ do   \n18: $A^{\\prime}[j]\\leftarrow\\langle B,A[j\\cdot2^{b_{\\mathrm{tot}}/2}..(j+1)\\cdot2^{b_{\\mathrm{tot}}/2}]\\rangle$ // dot product   \n19:   \n20: // MultiCollapse on $A^{\\prime}$ costs $2\\cdot2^{b_{\\mathrm{tot}}/2}-2$ multiplications   \n21: return MultiCollapse $\\cdot(A^{\\prime},2^{b_{\\mathrm{tot}}/2},r[b_{\\mathrm{tot}}/2+1..b_{\\mathrm{tot}}])$  \n\nthe dot product costs just $2^{b_{\\mathrm{tot}}/2}$ multiplications once $B$ has been computed. DotPMultiCollapse (Fig. 4) improves on MultiCollapse’s running time by precomputing $B$ once and amortizing that cost over all $2^{b_{\\mathrm{tot}}{\\bar{/}}_{2}}$ subtrees. To see that two algorithms are equivalent, notice that the $b_{\\mathrm{tot}}/2$ layers of the MultiCollapse computation tree closest to the leaves (which correspond to the first $b_{\\mathrm{tot}}/2$ Collapse invocations) compute the same $2^{b_{\\mathrm{tot}}/2}$ dot products that DotPMultiCollapse stores in $A^{\\prime}$ (for the reasons described above), and that the two algorithms proceed identically thereafter. But DotPMultiCollapse costs just $N\\cdot G+4\\cdot2^{b_{\\mathrm{tot}}/2}-4=N\\cdot G+4\\sqrt{N\\cdot G}-4$ multiplications in total (see comments in Fig. 4).  \n\nDotPMultiCollapse’s hardware design uses primitives from other parts of $_\\mathcal{V}$ and $\\mathcal{P}$ . MultiCollapse reuses the same design that $\\mathcal{P}$ uses for EvalTermLR. The hardware for computing the array $B$ shares its design with $_\\mathcal{V}$ ’s precomputation hardware (“Precomputation,” below; [83, Appx. B.2]). The dot product computations are independent of one another and thus easily parallelized using separate multiply-and-accumulate units, which are standard.  \n\nPolynomial evaluation. The protocol requires $_\\mathcal{V}$ to evaluate polynomials (specified by $\\mathcal{P}$ ) at randomly chosen points (specified by $_\\mathcal{V}$ ). This occurs after the sum-check invocation (Fig. 1, line 26) and in each round of the sum-check protocol ([83, Appx. B]; [83, Fig. 11, line 21]). Our description here focuses on the former: the degree- $\\cdot b_{G}$ polynomial $H$ , evaluated at $\\tau$ . Giraffe applies the same technique to the latter, namely computing $F(r_{j})$ , but those polynomials are degree-2 or 3, and thus the savings are less pronounced.  \n\nIn the baseline approach [36, 77, 81, 82] to computing $H(\\tau)$ , $\\mathcal{P}$ sends evaluations (meaning $H(0),\\ldots,H(b_{G}))$ , and $_\\mathcal{V}$ uses Lagrange interpolation. (Lagrange interpolation expresses $H(\\tau)$ as $\\textstyle\\sum_{j=0}^{b_{G}}H(j)\\cdot f_{j}(\\tau)$ ; the $\\{f_{j}(\\cdot)\\}$ are basis polynomials.) But interpolation costs $O(b_{G}^{2})$ [55] for each polynomial (one per layer), making it $O(d\\log^{2}G)$ overall. Prior work [81, 82] cut this to $0(d\\log G)$ , by precomputing $\\{f_{j}(\\tau)\\}$ , and not charging for that.  \n\nGiraffe observes that the protocol works the same if $\\mathcal{P}$ describes $H$ in terms of its coefficients; this is because coefficients and evaluations are informationally equivalent. Thus, in Giraffe, $\\mathcal{P}$ recovers the coefficients by interpolating the evaluations of $H$ , incurring cost $O(d\\log^{2}G)$ . $_\\mathcal{V}$ uses the coefficients to evaluate $H(\\tau)$ via Horner’s rule [55]. The cost to $_\\mathcal{V}$ is now $\\mathrm{O}(b_{G})$ per layer, or $\\operatorname{O}(d\\log G)$ in total, without relying on precomputation.  \n\nSummarizing, $_\\mathcal{V}$ shifts its burden to $\\mathcal{P}$ , and in return saves a factor $\\log G$ . This refinement is sensible if the same operation at $\\mathcal{P}$ is substantially cheaper (by at least a $\\log G$ factor) than at $_\\mathcal{V}$ . This easily holds in the VA context. But it also holds in other contexts in which one would use a CMT-based back-end: if cycles at $\\mathcal{P}$ were not substantially cheaper than at $_\\mathcal{V}$ , the latter would not be outsourcing to the former in the first place.  \n\nPrecomputation. $_\\mathcal{V}$ must compute $P_{q,i}^{*}(r^{\\prime},r_{0},r_{1},)$ , given claimed $\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{0})$ and $\\tilde{V}_{i}(\\boldsymbol{r}^{\\prime},\\boldsymbol{r}_{1})$ : Figure 1, lines 20–21. The main costs are computing $\\tilde{\\mathrm{add}_{i}}(q,r_{0},r_{1})$ , $\\tilde{\\bf m u l t}_{i}(q,r_{0},r_{1})$ , and $\\widetilde{\\mathrm{eq}}(q^{\\prime},r^{\\prime})$ . This costs $O(G)$ per layer [81], and hence $O(d\\cdot G)$ ove reall. ([83, Appx. A] describes the approach; [83, Appx. B.2] briefly discusses the hardware design.) This is the “precomputation” in our context, and what was not charged in prior work in the VA setting [82, §4]. We note that this is not precomputation per se—it’s done alongside the rest of the protocol—but we retain the vocabulary because of the cost profile: the work is proportional to executing one sub-AC, is input-independent, and is incurred once per sum-check invocation, thereby amortizing over all $N$ sub-ACs.  \n\n# 4 FRONT-END DESIGN  \n\nGiraffe’s front-end compiles a C program into one or more pieces, each of which can be outsourced using the back-end machinery. The front-end incorporates two program transformation techniques that broaden the scope of computations amenable to outsourcing: • Slicing breaks up computations that are too large to be outsourced as a whole or contain parts that cannot be profitably outsourced. • Squashing rearranges repeated, serial computations like loops to produce data-parallel computations. While squashing makes some sequential computations amenable to execution in Giraffe’s data-parallel setting $(\\S2.2,\\S3.1)$ , slicing does not yield data-parallel ACs; thus, outsourcing a sliced computation requires executing multiple copies of the computation in parallel. Slicing. One approach to handling large outsourced computations is to break the computation into smaller pieces and then to either outsource each piece or to execute it locally at the verifier. This approach works as follows: a compiler breaks an input program into slices and decides, for each slice, whether to outsource or to execute locally (we describe this process below). The compiler converts each slice to be outsourced into an AC whose inputs are the program state prior to executing the slice and whose outputs are the program state after execution. To execute a sliced computation, the verifier runs glue code that passes inputs and outputs between slices, executes non-outsourced slices, and orchestrates the backend machinery. We call this glue code the computation’s manifest.  \n\nGiraffe’s slicing algorithm takes one parameter, a cost model for the target back-end. The algorithm’s input is a C program with the following restrictions (commonly imposed by the most efficient front-ends [37, 66, 84, 85]): loop bounds are statically computable, no recursive functions, and no function pointers.  \n\nThe algorithm first inlines all function calls. It then considers candidate slices comprising consecutive subsequences of top-level program statements. The algorithm transforms each candidate into an AC and uses the back-end cost model to determine the cost to outsource. Then, using a greedy heuristic, the algorithm chooses for outsourcing a set of non-overlapping slices, aiming to maximize savings. Finally, the algorithm handles parts of the program not in any of the outsourced slices: it adds atomic statements (e.g., assignments) to the manifest for local execution, and recursively invokes itself on non-atomic statements (e.g., the branches of if-else statements) to identify more outsourcing opportunities.  \n\nGiraffe assumes that the same back-end is used for all sliced subcomputations, but this approach generalizes to considering multiple back-ends simultaneously [45, 81].  \n\nSquashing. Giraffe’s second technique, squashing, turns a deep but narrow computation (for example, a loop) into a data-parallel one by laying identical chunks of the computation (e.g., iterations of a loop) side by side. The result is a squashed AC. The intermediate values at the output of each chunk in the original computation become additional inputs and outputs of the squashed AC. $\\mathcal{P}$ communicates these to $_\\mathcal{V}$ , which uses them to construct the input and output vectors for the squashed AC. This technique also generalizes to the case of code “between” the chunks.  \n\nGiraffe’s squashing transformation takes C code as input and applies a simple heuristic: the analysis assumes that chunks start and end at loop boundaries and comprise one or more loop iterations. Consider a loop with $I$ dependent iterations of a computation $F$ , where $F$ corresponds to an AC of depth $d$ and uniform width $G$ . The squasher chooses $N$ such that each chunk contains $I/N$ unrolled iterations, and generates a sub-AC of width $G$ and depth $d^{\\prime}=I\\cdot d/N$ , subject to a supplied cost model.  \n\nPutting it together. Giraffe’s front-end compiles C programs by combining slicing and squashing. In particular, Giraffe’s front-end applies the slicing algorithm as described above except that, when estimating the cost of candidate slices, the front-end also tries to apply the squashing transformation. If a candidate slice can be squashed, the slicer uses the squashed version of the slice instead.  \n\n# 5 IMPLEMENTATION  \n\nFront-end. The front-end produces an executable manifest in Python plus a high-level AC description for each outsourced slice (these are similar to the one used by Allspice [81] and Zebra). Outsourced slices in the manifest are executed using the simulation framework (below). The front-end comprises about 6100 lines of Scala and 300 lines of miscellaneous glue.  \n\nBack-end. Giraffe’s back-end has two components. The first is a compiler that takes high-level AC descriptions from the frontend along with technology node specifications, chooses $\\mathcal{P}$ ’s and $_\\mathcal{V}$ ’s hardware parallelism (§3.2; [83, Fig. 16, Appx. C]) to optimize throughput and chip area (§6.2), and automatically produces $\\mathcal{P}$ and $_\\mathcal{V}$ designs in fully synthesizable SystemVerilog, The second is a cycle-accurate simulation framework built on Icarus Verilog [86]. The back-end comprises 14 600 lines of SystemVerilog, 6800 lines of $\\mathrm{C}/\\mathrm{C}++$ , 3300 lines of Python, and 600 lines of miscellaneous glue. The SystemVerilog and $\\mathrm{C}/\\mathrm{C}++$ borrow primitives from Zebra [4].  \n\n# 6 BACK-END EVALUATION  \n\nWe evaluate Giraffe’s back-end by answering:  \n\n(1) When does Giraffe beat “native” (§2.3)? (2) What is the largest computation Giraffe supports? (3) How does Giraffe’s performance vary with computation and physical parameters?  \n\nIn [83, Appx. D] we also measure the effect of Giraffe’s protocol improvements (§3.1). In sum, they reduce $\\mathcal{P}$ ’s cost by a $\\log G$ factor.  \n\nThroughout the evaluation we assume that Giraffe is applied to computations most efficiently expressed as a sequence of field operations; we discuss this applicability limitation in Section 9.  \n\n# 6.1 Cross-over and scaling  \n\nMethod. We consider a generic computation in the form of an arithmetic circuit $C$ with depth $d$ , sub-AC width $G$ , number of parallel copies $N$ , and fraction of multipliers $\\delta$ . The baseline is direct evaluation of $c$ on the same technology node as $_\\mathcal{V}$ . To measure the energy cost for the baseline, we sum the total cost of field operations plus the energy associated with receiving inputs and transmitting outputs of the computation.  \n\nFor Giraffe’s energy costs, we use a combination of simulation and modeling. The simulations are cycle-accurate Verilog simulations of Giraffe’s execution. From these simulations we extract a model for energy costs, parameterized by technology node9 (a simplified model is given in [83, Fig. 16, Appx. C]), and we spot check with additional simulations to check this model. Practical considerations demand this approach: simulating Giraffe over a broad range of parameters would be prohibitively time consuming.  \n\nWe account for all costs for both $_\\mathcal{V}$ and $\\mathcal{P}$ : protocol execution, $\\mathcal{V}_{-}\\mathcal{P}$ communication, storage, random number generation, and the cost to receive inputs and transmit outputs. We simplify the accounting of the protocol execution’s energy cost by counting just the energy consumed by field operations. This approximation neglects the energy consumed by control logic and miscellaneous circuitry associated with protocol execution. As in prior work [82, $\\S7.2]$ , we expect these costs to be negligible; confirming this is future work. Computations in this section are over $\\mathbb{F}_{p},p=2^{61}-1$ Costs for trusted and untrusted technology nodes (arithmetic, communication, storage, random number generation, and $\\mathrm{I/O}$ circuits) are from prior work [82, Figs. 6–7].  \n\n![](/tmp/output/180_20250326215320/images/d43a39894950b2016b2d2b6930684c816a8deb7fdaee68dfc3741cf8c47b3a7c.jpg)  \nFigure 5: Evaluation of Giraffe’s back-end. We compare Giraffe’s costs to the native baseline, varying $N$ . Giraffe beats native for $N\\approx30.$ . Fixed AC parameters are: depth $d=20.$ ; width of sub-AC $G=2^{8}$ ; fraction of multipliers $\\delta=0.5$ ; trusted technology $=350\\mathrm{nm}$ ; untrusted technology $=7\\mathrm{nm}$ ; maximum chip area $\\ensuremath{A_{\\mathrm{max}}}=200\\ensuremath{\\mathbf{mm}}^{2}$ ; maximum power dissipation $P_{\\mathrm{max}}=150~\\mathbf{W}$ . Per-gate energy costs for trusted and untrusted nodes are the same as in prior work [82, Figs. 6–7]. In Section 6.2 we consider manufacturing costs; there, Giraffe is less competitive with native.  \n\nResults. Figure 5 compares Giraffe with the baseline. Giraffe’s total cost is dominated by $_\\mathcal{V}$ ; $\\mathcal{P}$ ’s cost is at most a few percent of the total. For small $N$ , $_\\mathcal{V}$ ’s precomputation (§3.3) dominates. As $N$ increases, $_\\mathcal{V}$ ’s multilinear extension evaluation (§3.3) dominates.  \n\nThe cross-over point for savings versus native in Figure 5 is roughly 30 copies. This value is relatively insensitive to $G$ because both precomputation cost and per-sub-AC savings are proportional to $G$ , and they offset. Varying $G$ and otherwise fixing the parameters as in Figure 5, we find crossover $N$ ranges from an extreme of ${\\approx}40$ at G = 8 to ≈20 for any G > 210.  \n\nFor the concrete costs we consider here, Giraffe can handle about $2^{16}$ parallel executions of a sub-AC with $G=2^{8}$ , $d=20$ , or about 80 million gates total. It can also handle sub-ACs as large as ${\\approx}1.5\\mathrm{mil}$ lion gates. For a given hardware substrate, the maximum $N\\cdot G$ product is nearly fixed. $\\mathcal{P}$ ’s costs increase with $d$ ([83, Fig. 16, Appx. C]), so maximum size shrinks as $d$ increases. On $A/T$ (§2.3), Giraffe is not as competitive with the baseline (§6.2).  \n\n# 6.2 Parameter variation  \n\nWe now explore Giraffe’s performance compared to the native baseline on generic arithmetic circuits characterized by $d,G,N$ and $\\delta$ , and on different technology nodes.  \n\nMethod. In addition to energy, we now consider manufacturing cost for a given performance level. Our metric is $A_{s}/T$ [82]. $T$ is throughput. $A_{s}=A_{\\mathcal{V}}+A_{\\mathcal{P}}/s$ , a weighted sum of $_\\mathcal{V}$ ’s and $\\mathcal{P}$ ’s chip area; s accounts for the difference between untrusted and trusted manufacturing costs. We do not know the exact value of s, as this depends on the specifics of the technology nodes being used; thus, we evaluate manufacturing cost over a range of values, $s\\in\\{1/3,1,3,10\\}$ , consistent with prior work [82].  \n\n![](/tmp/output/180_20250326215320/images/0e747431d347a3a9eebd11c52549748eef0116e29ac4b77c07e178779e02a1db.jpg)  \nFigure 6: Giraffe’s overall performance ( $^\\mathcal{N}$ and $\\mathcal{P}$ costs) compared to native baseline on $E$ and $A_{s}/T$ metrics (§6.2), varying AC parameters and technology nodes. In each case, we vary one parameter and fix the rest. Fixed parameters are: depth of $c$ , $d=20$ ; width of subcircuit $G=2^{8}$ ; number of sub-AC copies $N=2^{10}$ ; fraction of multipliers $\\delta=0.5$ ; trusted technology node $\\mathbf{\\lambda}=350~\\mathbf{nm}$ ; untrusted technology node $=7~\\mathrm{{nm}}$ ; maximum chip area $\\ensuremath{A_{\\mathrm{max}}}=200\\ensuremath{\\mathbf{mm}}^{2}$ ; maximum power dissipation $P_{\\operatorname*{max}}=150~\\mathbf{W}$ .  \n\nWe use the same simulations and detailed cost modeling as in Section 6.1 to compute costs for Giraffe. As a proxy for chip area dedicated to protocol execution, we use the area occupied by field adder and multiplier circuits. This neglects area dedicated to control logic and miscellaneous circuitry associated with protocol execution, but as in prior work [82, $\\S7.2]$ we expect these costs to be negligible; confirming this is future work.  \n\nFor throughput, we use cycle-accurate Verilog simulations to measure the delay of each stage of the execution and proving pipeline ([83, Appx. C]). End-to-end throughput is given by the inverse of the maximum delay in any stage of the computation. Concrete costs are the same as in Section 6.1. For each experiment we vary one parameter and fix the others; fixed parameters are $d=20,G=2^{\\bar{8}},N=2^{10},\\delta=0$ , trusted technology node $=350\\mathrm{nm}$ and untrusted technology node $=7~\\mathrm{{nm}}$ .  \n\nFor the native baseline, we optimize $A/T$ given $A_{\\mathrm{max}}$ subject to the arithmetic circuit’s layering constraints.  \n\nOptimizing $A_{s}/T$ in Giraffe. We optimize Giraffe’s $A_{s}/T$ by controlling the amount of hardware parallelism ([83, Appx. C]). First, we fix $_\\mathcal{V}$ ’s area equal to native baseline, which is no more than $A_{\\mathrm{max}}$ . We also limit $\\mathcal{P}$ ’s area to no more than $A_{\\mathrm{max}}$ and fix $n\\varphi_{,\\mathrm{pl}}=d$ Then we optimize $n_{\\mathcal V,\\mathrm{io}}$ and $n_{\\mathrm{\\Omega}}{}_{\\mathcal{V},\\mathrm{sc}}$ based on available area and relative delay of sum-check computations and multilinear extensions of inputs and outputs. Finally, given $_\\mathcal{V}$ ’s optimal delay value, we search for settings of $n_{\\mathcal{P},\\mathrm{ea}},n_{\\mathcal{P},\\tilde{\\mathrm{V}}}$ , and $n_{\\mathcal{P},\\mathrm{sc}}$ that optimize $A_{s}/T$ .  \n\nResults. Figure 6 summarizes results. Giraffe’s operating cost (i.e., energy consumption) beats the baseline’s over a wide range of AC parameters and hardware substrates.  \n\nAs in Section 6.1, energy cost is dominated by $_\\mathcal{V}$ . Savings increase with $d$ (Fig. 6a) because V’s per-layer work is much less than the native baseline’s. Similarly, as $\\delta$ increases (Fig. 6d), the native baseline’s costs increase but V’s do not. $_\\mathcal{V}$ ’s savings are insensitive to $G$ (Fig. 6b): the cost of multilinear extensions of I/O scales with $G$ , balancing the increased savings in per-layer work.  \n\nManufacturing costs are often dominated by $\\mathcal{P}$ . As $G$ increases (Fig. 6b), $\\mathcal{P}$ ’s area also increases (§3.2). As $N$ increases (Fig. 6c), $\\mathcal{P}$ ’s storage costs increase ([83, Fig. 16, Appx. C]). In these cases, even if Giraffe’s operating costs are better than the native baseline’s, its manufacturing costs at a given performance level may be worse.  \n\nFinally, as the gap between the trusted and untrusted technology nodes shrinks (Figs. 6e and 6f), $\\mathcal{P}$ ’s energy cost increases relative to $_\\mathcal{V}$ ’s, reducing overall performance versus the native baseline. As the trusted technology node gets more advanced (i.e., smaller, Fig. 6f), $_\\mathcal{V}$ ’s throughput increases and thus $\\mathcal{P}$ ’s size must increase to avoid becoming a bottleneck. As the untrusted technology node gets less advanced (i.e., bigger, Fig. 6e), $\\mathcal{P}$ ’s area grows and throughput decreases, making $A_{s}/T$ worse.  \n\n![](/tmp/output/180_20250326215320/images/382c0740ec826f3dddf19d4df5928c381f12b20029d5165acc6370ffa49faadf.jpg)  \nFigure 7: Evaluation of Giraffe’s front-end. Higher is better. F1 and F2 are computations corresponding to arithmetic circuits with $N=2^{10}$ , $G=2^{8}$ , $d=20$ . $\\delta_{1}$ and $\\delta_{2}$ are the fraction of multipliers in F1 and F2, respectively; we fix $\\delta_{2}=0.05$ . Figures 7a and 7c show inputs to Giraffe’s slicing transformation. In Figures 7b and 7d, we vary $\\delta_{1}$ , which changes whether F1 is amenable to outsourcing. We compare the efficacy of outsourcing the full computation and of first applying the slicing transform; when outsourcing would not result in savings, Giraffe executes the computation natively. Figure 7e is a deep loop with dependent iterations. Giraffe converts this to a data-parallel computation that can be outsourced, saving compared to native execution.  \n\n# 7 FRONT-END EVALUATION  \n\nThis section answers the following questions:  \n\n(1) How does slicing result in savings compared to full outsourcing and native execution?   \n(2) For deep loops with dependent iterations, how effective is squashing at extracting parallelism?  \n\nSetup and method. We create a sequence of programs written in C, each containing two generic blocks, F1 and F2, consisting of purely arithmetic computations. Among the programs, these blocks vary in the fraction $\\delta_{1}$ and $\\delta_{2}$ of multipliers, width of computation $(G_{1},G_{2}$ respectively), the depth of the computation $(d_{1},d_{2}$ respectively), and number of parallel instances $N$ . Unless specified, we fix $N=2^{10}$ , $G_{1}=G_{2}=2^{8}$ , $d_{1}=d_{2}=20$ , $\\delta_{2}=0.05$ .  \n\nWe consider two baselines: native execution and full outsourcing. The cost of native execution is defined as in the prior section: the cost of computation in the same technology node as $_\\mathcal{V}$ . We estimate the cost of full outsourcing by applying Giraffe’s back-end to the raw program, without Giraffe’s front-end transformations (§4).  \n\nTo compute costs for Giraffe, we apply the selected transformation to produce a manifest (§4), then evaluate the total cost of execution, as dictated by that manifest. We use the model of Section 6 to determine the cost of the outsourced portions of the manifest. For local computations, we sum the cost of all field operations, as in the native baseline.  \n\nSlicing. We consider a simple case and then conditionals.  \n\nWarmup. Consider the computation of Figure 7a. We vary $\\delta_{1}$ from 0 to 1. The front-end decides for F1 and F2 either to outsource or to execute locally. Note that F1’s amenability to outsourcing depends on $\\delta_{1}$ : native execution cost increases with $\\delta_{1}$ (multiplies are more expensive than adds) while $_\\mathcal{V}$ ’s protocol costs depend only on AC size. Because $\\delta_{2}=0.05$ , F2 is not amenable to outsourcing: it native execution cost is less than the cost to outsource. For full outsourcing we generate a sub-AC that combines F1 and F2, which is conservative because it saves on precomputation.  \n\nFigure 7b plots the performance of executing the slicer’s manifest and of outsourcing the entire computation, normalized to native execution. Giraffe’s front-end never outsources F2 because native execution is cheaper. F1 is amenable to outsourcing when $\\delta_{1}>0.2$ In contrast, full outsourcing pays extra costs for F2 compared to native execution. Thus, slicing always beats full outsourcing.  \n\nConditionals. In Figure 7c we consider a similar setup, but with a conditional. We assume that pred evaluates to true, so F1 is the desired branch. Naively converting this program to an AC results in a computation that materializes both F1 and F2, and selects the result based on pred. In essence, part of the work is useless.  \n\nFigure 7d plots the performance of executing the slicer’s manifest and the performance of outsourcing the entire computation, normalized to the performance of native execution. The manifest never invokes F2 because that branch is never taken. When $\\delta_{1}>0.2$ , F1 is amenable to outsourcing and Giraffe’s performance is better than native. Full outsourcing, meanwhile, evaluates an AC that incurs the cost for both branches. For large enough $\\delta_{1}$ , though, the savings from F1 offsets the useless work and full outsourcing beats native.  \n\nSquashing. We also experiment with a loop comprising $I$ iterations of F1 (Fig. 7e). Parameters are as above, $\\delta_{1}~=~0.5$ , and we vary I . This is deep $(I\\cdot d_{1})$ and narrow $\\left(G_{1}\\right)$ , and not data parallel. The squasher (§4) chooses $N$ . Effective depth is $d^{\\prime}=I\\cdot d/N$ for each chunk, balancing V’s I/O cost against the per-layer cost. This happens when depth and $|x|+|y|$ are within a constant factor, i.e., $N\\cdot G=d^{\\prime}=\\operatorname{O}\\left({\\sqrt{I}}\\right)$ (overall cost is the sum). Figure 7f shows the results: as $I$ goes from $2^{11}$ to $2^{14}$ , performance improves by $\\approx3\\times$ .  \n\n![](/tmp/output/180_20250326215320/images/e202d6904ef1a688db42a5b0591a53e779839a85aa29f1378af13de9ebc7eb84.jpg)  \nFigure 8: Energy cost of Giraffe, native baseline (§8.1), and Zebra [82, $\\S8.2]$ versus number of copies of Curve25519 subcircuit. Each subcircuit computes 20 parallel evaluations of five sequential double-andadd steps. Untrusted technology node $=350\\mathrm{nm}$ ; trusted technology node $=7\\mathrm{nm}$ ; $\\ensuremath{A_{\\mathrm{max}}}=200\\mathrm{mm}^{2}$ ; $P_{\\mathrm{max}}=150$ W. Zebra’s scaling is limited to about 1150 parallel evaluations. Giraffe scales to more than $500\\times$ more parallel computations for the same chip area. Because of Giraffe’s refinements (§3), its improvement versus native is greater than Zebra’s. But Giraffe must amortize precomputation, while $\\mathbf{Ze}$ bra assumes it is free; thus, Giraffe needs larger $N$ to break even.  \n\n# 8 APPLICATIONS  \n\n# 8.1 Curve25519  \n\nCurve25519 is a high-performance elliptic curve used in cryptographic protocols [5, 25]. This section compares three implementations of the point multiplication operation on this curve: a baseline, Zebra, and Giraffe. This operation takes as inputs a 255-bit scalar value $\\boldsymbol{v}$ and a curve point $Q$ , and computes the point $R=[v]Q$ via 255 double-and-add steps [11], one for each bit of $\\boldsymbol{v}$ . Our algorithm employs a Montgomery ladder, as is standard [11, 25, 63]. Doubleand-add is naturally expressed as an AC over $\\mathbb{F}_{p},p=2^{255}-19$ with $d=7$ and $G\\approx8$ .  \n\nZebra. This implementation [82, $\\S8.2]$ groups 5 Montgomery ladder steps into a block and requires 51 $\\left(=255/5\\right)$ iterations of this block per operation. Zebra uses a special mux gate for efficiency, requiring all double-and-add operations in a protocol run to use the same scalar input $\\boldsymbol{v}$ . This restriction is acceptable in some applications [82, $\\S8.2]$ .  \n\nBaseline implementation. Consistent with published hardware implementations of point multiplication on Curve25519 [70, 71] and the implementation from Zebra, our baseline directly executes 5 Montgomery ladder steps.  \n\nGiraffe. In Giraffe there are two degrees of freedom: $L$ , the number of parallel double-and-add steps in a sub-AC (which determines $G$ ); and $N$ . Each copy of the sub-AC uses the same $L$ scalars, $\\{v_{1},\\ldots,v_{L}\\}$ ; this is because wiring predicates are reused across the $N$ sub-ACs. In our experiment, we fix $L=20$ , and vary $N$ ; larger values of $L$ are also possible.  \n\nResults. We compute energy for Giraffe and the native baseline as in Section 6.1. For Zebra, we use published results [82, $\\S8.2]$ . We set the untrusted technology node $=350~\\mathrm{nm}$ , the trusted technology node $=7~\\mathrm{{nm}}$ , and $\\ensuremath{A_{\\mathrm{max}}}=200\\ensuremath{\\mathrm{mm}^{2}}$ , the same as in Zebra.  \n\nFigure 8 shows the results. Giraffe breaks even when $N\\approx30$ , or at about 600 parallel double-and-add operations, while Zebra breaks even for about 100 such operations. But Giraffe pays the cost of precomputation, whereas Zebra assumes that precomputation is free. Meanwhile, Zebra handles at most 1150 parallel copies for the given chip area, whereas Giraffe can accommodate roughly 32,000 parallel operations corresponding to roughly 100M AC gates, about $500\\times$ more than Zebra, for the same technology nodes.  \n\n![](/tmp/output/180_20250326215320/images/d5f16f8faffef8de7d651cecb2eb72be613525d298345d34313cbaf3b760314c.jpg)  \nFigure 9: Energy cost of Giraffe and native baseline (§8.2) versus number of parallel image pyramid matching evaluations. Each evaluation matches 16-word needles against 128-word haystacks for a two-level image pyramid (§8.2). Words are represented as elements in $\\mathbb{F}_{p}$ , $p=2^{61}+2^{19}+1.$ Untrusted technology node $\\mathbf{\\lambda}=35\\mathbf{0}\\mathbf{nm}$ ; trusted technology node $=7~\\mathbf{nm}$ ; $\\ensuremath{A_{\\mathrm{max}}}=200~\\mathrm{mm}^{2}$ , $P_{\\mathrm{max}}=150~\\mathbf{W}$ . Giraffe breaks even for $\\approx30$ parallel searches.  \n\n# 8.2 Image pyramid  \n\nAn image pyramid is a data structure for image processing [6] that holds multiple copies of an image at different resolutions. The “base” of the pyramid is a full resolution image and higher “layers” summarize the image at progressively lower resolutions. One application of an image pyramid is fast pattern matching. In the first step, a coarse pattern is matched against the coarsest layer (top) of the pyramid. Guided by the results, a finer pattern is matched against a small part of the next layer until eventually a portion of the full resolution image is matched against the finest pattern.  \n\nWe use a convolution-based matching algorithm [35] where the pattern may contain “don’t care” symbols that match any input. If the text is $t~=~t_{0}t_{1}\\ldots t_{n}$ and the pattern is $\\ensuremath{p}=\\ensuremath{p}_{0}\\ensuremath{p}_{1}\\dots\\ensuremath{p}_{m}$ , then the matching algorithm uses convolutions to compute $c_{i}=$ $\\textstyle\\sum_{j=0}^{m}p_{j}(p_{j}-t_{i+j})^{2},i\\in\\{1,\\dotsc,n\\},$ , and reports a match at $i$ if $c_{i}=0$  \n\nIn our implementation, the input consists of a two-layer image pyramid, a coarse pattern, and a fine pattern. The bottom layer of the pyramid has $2^{7}\\times2^{7}$ words, and the top layer has $1\\times2^{7}$ words. Both patterns comprise $2^{4}$ words. Words are represented over $\\mathbb{F}_{p},p=2^{61}+2^{19}+1$ , and we implement convolution using the number theoretic transform over $\\mathbb{F}_{p}$ . The entire application processes $N$ instances in parallel; each instance specifies its own input and pattern. The application is written as a C program.  \n\nBaseline implementation. In our baseline implementation, each convolution is implemented using the direct iterative implementation of the number theoretic transform (NTT) and its inverse. Energy costs are accounted as in the baseline of Section 6.  \n\nGiraffe. We apply Giraffe’s front-end to process our C program into a manifest; the local computation selects the needed portion of the next layer. We compute energy consumption of the resulting manifest as in Section 7. Our results reflect fully automated compilation on a realistic application with no hand optimizations.  \n\nResults. Figure 9 compares the cost of executing the manifest to the cost of the native baseline. Trusted and untrusted technology nodes and $A_{\\mathrm{max}}$ are as in Section 8.1. Giraffe needs roughly 30 parallel evaluations to break even, after which it uses $5\\times$ less energy than the baseline. Giraffe can scale to handle 32,000 parallel instances within the area constraint, or about 100 million AC gates.  \n\n# 9 DISCUSSION AND LIMITATIONS  \n\nTo understand Giraffe’s results, it is useful to provide context about the limitations of other implemented systems for verifiable outsourcing. All such systems (including Giraffe) are reasonable only when the computation to be outsourced is naturally expressed as an AC. Otherwise, translating the computation to an AC entails such steep overheads compared to native execution that (asymptotic) savings do not kick in for reasonable computation sizes [82, §9]. Moreover, all systems built on the interactive proofs of GKR and CMT (including Giraffe) require the AC to be layered.  \n\nAnother limitation of all built systems concerns their overheads and break-even points. As an example we consider SNARKs [26, 47]. Before continuing, we note that SNARKs apply more widely, for example to cryptocurrencies [18, 38] and beyond [39, 64]. In those contexts, which exploit SNARKs’ zero knowledge property, overheads are less relevant. Thus, the following discussion should be understood to cover only the proposed application of SNARKs to verifiable outsourcing [27, 47, 66].  \n\nCareful examination of the SNARK literature indicates that verifier overhead is so high that very large computations are required to break even: millions of AC gates per instance [21; 66, §5.3; 84, §2; 85]. Furthermore, the required number of instances is large: even on the best-case problem of matrix multiplication, Pinocchio [66] requires more than 6,000 instances, and BCTV [20, 22] requires more than 90,000 instances to break even [85, Fig.4]. (Note that we have not even discussed keeping track of prover overhead; even for small ACs, these provers take minutes on stock hardware.)  \n\nIn contrast, Giraffe’s performance (keeping track of prover costs) has only a weak dependence on computation size, even for ACs of only a few hundred gates (Figs. 6a and 6b, $\\S6.2_{,}$ . Moreover, the number of parallel copies required to amortize is small, $\\approx30$ (§6, $\\S8_{,}$ ). The maximum instance size for a Giraffe sub-AC is around 1.5 million gates (§6.1); this is largely a function of the constraints imposed by hardware. These numbers are very encouraging (although we note that Allspice [81] achieves similar break-even points).  \n\nOf course, SNARKs have distinct advantages:10 precomputation amortizes indefinitely in the non-interactive setting (eliminating the requirement for data-parallel computations), the communication costs are much lower, and a broader class of computations can be handled (there is no requirement that ACs be layered, and computations can accept non-deterministic input).  \n\nSince Giraffe is largely focused on the hardware setting, it is also worthwhile to contrast with Zebra [82]. On the one hand, Zebra does not impose the requirement for data-parallel computations (to amortize precomputation), and its break-even points are lower. On the other hand, Zebra achieves these things by not paying for computation (and making a fanciful assumption about daily delivery of trusted precomputations [82, §4]). Giraffe, by contrast, can break even despite paying for precomputation. Furthermore, Zebra is limited to approximately 500,000 AC gates total, whereas Giraffe supports 1.5 million gates per sub-AC and $N$ scales to about 50 in this case (§6.1); Giraffe is thus two orders of magnitude better than Zebra in total size (this is also demonstrated in $\\S8.1$ ). And, as the image pyramid example (§8.2) shows, Giraffe can be practical in situations where Zebra or Giraffe simply cannot outsource the entire computation.  \n\nTo be sure, Giraffe has serious limitations. The price of verification remains high; evaluation shows that the overall win is ${\\approx}3{-}5{\\times}$ (Fig. 5, §6; Figs. 8–9, §8). Given the prover overhead, Giraffe still requires a large technology gap between the $\\mathcal{P}$ and $_\\mathcal{V}$ technology nodes to be practical (§6.2), though there are scenarios when this holds [82, §1]. And finally, the regime of applicability is fundamentally narrow (as noted in the introduction).  \n\n# 10 RELATED WORK  \n\nProbabilistic proofs. Giraffe relates to the extensive body of recent work on verifiable outsourced computation [14, 15, 17–19, 21, 22, 32, 34, 36, 37, 39, 40, 42, 45, 47, 56, 64, 66, 72–75, 77, 79, 81, 82, 84, 88]; see [85] for a comparatively recent survey. Specifically, Giraffe descends from the GKR [49] interactive proof line [36, 77, 79, 81, 82]. This line has historically imposed certain limitations: a more restricted class of computations, and deterministic ACs. In work done concurrently with and independently of Giraffe, vSQL [88] has demonstrated how to support non-deterministic ACs, by composing GKR-derived protocols with polynomial commitment schemes [24, 41, 53, 65]. The result is exciting, and for some application lowers prover costs relative to the argument protocols, discussed below. However, the resulting prover is still much more costly than native computation; applying Giraffe’s protocol refinements (§3.1) would reduce this overhead. Indeed, vSQL and Giraffe are complementary: future work is to combine them, and thereby handle non-deterministic ACs in Giraffe’s operating model.  \n\nAnother line of work uses argument protocols, both interactive [73–75] and non-interactive [14, 21, 26, 47, 56, 66] (“SNARK” refers to the latter). However, these protocols seem incompatible with hardware implementation (as discussed in prior work [82, $\\S^{9]}$ ) and impose cryptographic assumptions (strong ones in the non-interactive setting). These protocols also have higher precomputation costs (which can be tamed asymptotically, but at very high concrete cost [21, 22, 34]—for example, the prover is two [22] to six [21, 34] orders of magnitude worse). On the other hand, non-interactive arguments can support zero knowledge (zkSNARK) protocols; this enables applications that are not possible otherwise.  \n\nMuch of the work in the area fits into the cost framework outlined in the introduction: precomputation, verifier overhead, and prover overhead, with native execution as a sensible baseline. There are a few exceptions. In the zkSNARK setting, the cost assessment depends on the premium that one is willing to pay for otherwise unachievable functionality [14, 18, 39]. Also, two works in the verifiable outsourcing setting do not require precomputation. The first is CMT [36, 79] (and [77]) when applied to highly regular wiring patterns; however, such wiring patterns substantially limit applicability. The second is SCI [17], which aims to be general purpose. SCI is, roughly speaking, an argument protocol built atop “short PCPs” [23], and is an exciting development. But inspection of SCI reveals that some of its costs, most notably the verifier’s, are orders of magnitude higher than in other works.  \n\nPL techniques in cryptographic protocols. Squashing (§4) is related to but distinct from Geppetto’s [37] optimizations for loops. At a high level the goals are similar (use loop transformations to adapt a computation to a protocol), but they differ in particulars because each technique leverages features of its respective back-end. In settings where they are both relevant, we believe the two approaches are complementary. (Giraffe pursues automatic inference for this optimization, which is discussed but not explored in [37].)  \n\nOur work on slicing is in the tradition of a great deal of work adapting PL techniques to implementing cryptographic protocols. In the verifiable outsourcing literature, there are a handful of examples (e.g., Buffet [84] uses sophisticated loop unrolling techniques to optimize loop handling, and Geppetto analyzes conditionals to minimize evaluation of “dead code”).  \n\nMore generally, the secure multi-party computation literature has seen a great deal of work using program analysis and transformation techniques to produce optimized protocols, starting with Fairplay [59] and notably including the line of work represented by [87]. There has also been relevant work in the Oblivious RAM community, for example [57] uses PL techniques to partition variables to ensure obliviousness. Another area in which these techniques are used is in automatic compilation for secure distributed programming [44]. Perhaps most similar to our slicing protocol are the various compilers for zero knowledge proofs of knowledge [7, 16, 61], most notably ZQL and ZØ [43, 45]. The latter weaves together explicitly annotated zero knowledge regions with ordinary code, and does automatic inference for assigning functionality to tiers in client-server applications (see also [60] for automatic tier partitioning). Giraffe is distinguished by performing automatic inference for slicing using a cost model, without explicit annotation.  \n\n# 11 CONCLUSION  \n\nWe have described a number of techniques that are relevant to verifiable outsourcing generally: an improvement to the T13 proof protocol that yields an asymptotically optimal prover and reduces concrete costs by an order of magnitude or more; algorithmic refinements for the verifier that improve its main bottleneck by ${\\approx}3\\times$ and apply to any CMT-based protocol; and two program transformations that increase the range of suitable applications: squashing, which applies to any CMT-based protocol, and slicing, which applies throughout the research area. We have also developed hardware primitives tailored to the proof protocol and a design template that automatically generates optimized hardware designs across a wide range of computations and physical substrates. And we have used all of these to build Giraffe, a system for Verifiable ASICs. For a range of computations, Giraffe meets or exceeds the performance of a chip built by a trusted manufacturer, while accounting for all costs: prover, verifier, and precomputation. Although the regime in which Giraffe applies is narrow, it is the first system to apply such a strict cost accounting—and win.  \n\n# Acknowledgments  \n\nWe thank Fraser Brown, Ioanna Tzialla, and Keith Winstein for helpful comments. The authors were supported by NSF grants CNS1423249, CNS-1514422, and CNS-1646671; AFOSR grant FA9550-15- 1-0302; ONR grant N00014-16-1-2154; DARPA grant HR0011-15-2- 0047; and a Google Research Award.  \n\nGiraffe’s source code is available at: http://www.pepper-project.org/  \n\n# REFERENCES  \n\n[1] https://github.com/pepper-project/releases/blob/master/ginger-allspice.tar.gz.   \n[2] http://people.cs.georgetown.edu/jthaler/code/code.htm.   \n[3] http://people.cs.georgetown.edu/jthaler/TRMPcode.htm.   \n[4] https://github.com/pepper-project.   \n[5] Things that use Curve25519. https://ianix.com/pub/curve25519-deployment.html.   \n[6] E. H. Adelson, C. H. Anderson, J. R. Bergen, P. J. Burt, and J. M. Ogden. Pyramid method in image processing. RCA Engineer, 29(6):33–41, Nov. 1984.   \n[7] J. B. Almeida, M. Barbosa, E. Bangerter, G. Barthe, S. Krenn, and S. Z. Béguelin. Full proof cryptography: verifiable compilation of efficient zero-knowledge protocols. In ACM CCS, 2012.   \n[8] S. Arora and B. Barak. Computational Complexity: A modern approach. Cambridge University Press, 2009.   \n[9] S. Arora, C. Lund, R. Motwani, M. Sudan, and M. Szegedy. Proof verification and the hardness of approximation problems. J. ACM, 45(3):501–555, May 1998.   \n[10] S. Arora and S. Safra. Probabilistic checking of proofs: a new characterization of NP. J. ACM, 45(1):70–122, Jan. 1998.   \n[11] R. M. Avanzi, H. Cohen, C. Doche, G. Frey, T. Lange, K. Nguyen, and F. Vercauteren. Handbook of Elliptic and Hyperelliptic Curve Cryptography. Chapman & Hall/CRC, 2005.   \n[12] L. Babai. Trading group theory for randomness. In STOC, May 1985.   \n[13] L. Babai, L. Fortnow, L. A. Levin, and M. Szegedy. Checking computations in polylogarithmic time. In STOC, May 1991.   \n[14] M. Backes, M. Barbosa, D. Fiore, and R. M. Reischuk. ADSNARK: Nearly practical and privacy-preserving proofs on authenticated data. In IEEE S&P, May 2015.   \n[15] M. Backes, D. Fiore, and R. M. Reischuk. Verifiable delegation of computation on outsourced data. In ACM CCS, Nov. 2013.   \n[16] E. Bangerter, J. Camenisch, S. Krenn, A. Sadeghi, and T. Schneider. Automatic generation of sound zero-knowledge protocols. IACR Cryptology ePrint Archive, 2008. http://eprint.iacr.org/2008/471.   \n[17] E. Ben-Sasson, I. Ben-Tov, A. Chiesa, A. Gabizon, D. Genkin, M. Hamilis, E. Pergament, M. Riabzev, M. Silberstein, E. Tromer, and M. Virza. Computational integrity with a public random string from quasi-linear PCPs. In EUROCRYPT, 2017.   \n[18] E. Ben-Sasson, A. Chiesa, C. Garman, M. Green, I. Miers, E. Tromer, and M. Virza. Decentralized anonymous payments from Bitcoin. In IEEE S&P, May 2014.   \n[19] E. Ben-Sasson, A. Chiesa, D. Genkin, E. Tromer, and M. Virza. SNARKs for C: Verifying program executions succinctly and in zero knowledge. In CRYPTO, Aug. 2013.   \n[20] E. Ben-Sasson, A. Chiesa, D. Genkin, E. Tromer, and M. Virza. TinyRAM architecture specification, v0.991. http://www.scipr-lab.org/system/files/TinyRAM-spec-0.991.pdf, 2013.   \n[21] E. Ben-Sasson, A. Chiesa, E. Tromer, and M. Virza. Scalable zero knowledge via cycles of elliptic curves. In CRYPTO, Aug. 2014.   \n[22] E. Ben-Sasson, A. Chiesa, E. Tromer, and M. Virza. Succinct non-interactive zero knowledge for a von Neumann architecture. In USENIX Security, Aug. 2014.   \n[23] E. Ben-Sasson and M. Sudan. Short PCPs with polylog query complexity. SIAM J. on Comp., 38(2):551–607, May 2008.   \n[24] S. Benabbas, R. Gennaro, and Y. Vahlis. Verifiable delegation of computation over large datasets. In CRYPTO, Aug. 2011.   \n[25] D. J. Bernstein. Curve25519: new Diffie-Hellman speed records. In PKC, Apr. 2006.   \n[26] N. Bitansky, R. Canetti, A. Chiesa, and E. Tromer. From extractable collision resistance to succinct non-interactive arguments of knowledge, and back again. In ITCS, Jan. 2012.   \n[27] N. Bitansky, R. Canetti, A. Chiesa, and E. Tromer. Recursive composition and bootstrapping for SNARKs and proof-carrying data. In STOC, 2013.   \n[28] U. Bondhugula, A. Hartono, J. Ramanujam, and P. Sadayappan. A practical automatic polyhedral parallelizer and locality optimizer. In PLDI, June 2008.   \n[29] P. Boulet, A. Darte, G. Silber, and F. Vivien. Loop parallelization algorithms: From parallelism extraction to code generation. Parallel Computing, 24(3-4):421–444, 1998.   \n[30] G. Brassard, D. Chaum, and C. Crépeau. Minimum disclosure proofs of knowledge. J. of Comp. and Sys. Sciences, 37(2):156–189, Oct. 1988.   \n[31] B. Braun. Compiling computations to constraints for verified computation. UT Austin Honors thesis HR-12-10, Dec. 2012.   \n[32] B. Braun, A. J. Feldman, Z. Ren, S. Setty, A. J. Blumberg, and M. Walfish. Verifying computations with state. In SOSP, Nov. 2013.   \n[33] S. Campanoni, K. Brownell, S. Kanev, T. M. Jones, G.-Y. Wei, and D. Brooks. HELIX-RC: an architecture-compiler co-design for automatic parallelization of irregular programs. In ISCA, June 2014.   \n[34] A. Chiesa, E. Tromer, and M. Virza. Cluster computing in zero knowledge. In EUROCRYPT, Apr. 2015.   \n[35] P. Clifford and R. Clifford. Simple deterministic wildcard matching. Information Processing Letters, 101(2):53 – 54, 2007.   \n[36] G. Cormode, M. Mitzenmacher, and J. Thaler. Practical verified computation with streaming interactive proofs. In ITCS, Jan. 2012.   \n[37] C. Costello, C. Fournet, J. Howell, M. Kohlweiss, B. Kreuter, M. Naehrig, B. Parno, and S. Zahur. Geppetto: Versatile verifiable computation. In IEEE S&P, May 2015.   \n[38] G. Danezis, C. Fournet, M. Kohlweiss, and B. Parno. Pinocchio coin: Building zerocoin from a succinct pairing-based proof system. In Workshop on Language Support for Privacy-enhancing Technologies, Nov. 2013.   \n[39] A. Delignat-Lavaud, C. Fournet, M. Kohlweiss, and B. Parno. Cinderella: Turning shabby X.509 certificates into elegant anonymous credentials with the magic of verifiable computation. In IEEE S&P, May 2016.   \n[40] D. Fiore, C. Fournet, E. Ghosh, M. Kohlweiss, O. Ohrimenko, and B. Parno. Hash first, argue later: Adaptive verifiable computations on outsourced data. In ACM CCS, Oct. 2016.   \n[41] D. Fiore and R. Gennaro. Publicly verifiable delegation of large polynomials and matrix computations, with applications. In ACM CCS, Oct. 2012.   \n[42] D. Fiore, R. Gennaro, and V. Pastro. Efficiently verifiable computation on encrypted data. In ACM CCS, Nov. 2014.   \n[43] C. Fournet, M. Kohlweiss, G. Danezis, and Z. Luo. ZQL: A compiler for privacy-preserving data processing. In USENIX Security, Aug. 2013.   \n[44] C. Fournet, G. Le Guernic, and T. Rezk. A security-preserving compiler for distributed programs: From information-flow policies to cryptographic mechanisms. In ACM CCS, 2009.   \n[45] M. Fredrikson and B. Livshits. ZØ: An optimizing distributing zero-knowledge compiler. In USENIX Security, Aug. 2014.   \n[46] R. Gennaro, C. Gentry, and B. Parno. Non-interactive verifiable computing: Outsourcing computation to untrusted workers. In CRYPTO, Aug. 2010.   \n[47] R. Gennaro, C. Gentry, B. Parno, and M. Raykova. Quadratic span programs and succinct NIZKs without PCPs. In EUROCRYPT, 2013.   \n[48] C. Gentry and D. Wichs. Separating succinct non-interactive arguments from all falsifiable assumptions. In STOC, June 2011.   \n[49] S. Goldwasser, Y. T. Kalai, and G. N. Rothblum. Delegating computation: Interactive proofs for muggles. J. ACM, 62(4):27:1–27:64, Aug. 2015. Prelim version STOC 2008.   \n[50] S. Goldwasser, S. Micali, and C. Rackoff. The knowledge complexity of interactive proof systems. SIAM J. on Comp., 18(1):186–208, 1989.   \n[51] B. Hoefflinger. ITRS: The international technology roadmap for semiconductors. In Chips 2020. Springer, 2012.   \n[52] Y. Ishai, E. Kushilevitz, and R. Ostrovsky. Efficient arguments without short PCPs. In IEEE CCC, June 2007.   \n[53] A. Kate, G. M. Zaverucha, and I. Goldberg. Constant-size commitments to polynomials and their applications. In ASIACRYPT, Dec. 2010.   \n[54] J. Kilian. A note on efficient zero-knowledge proofs and arguments (extended abstract). In STOC, May 1992.   \n[55] D. E. Knuth. Seminumerical Algorithms, volume 2 of The Art of Computer Programming, chapter 4.6.4. Addison-Wesley, third edition, 1997.   \n[56] A. E. Kosba, D. Papadopoulos, C. Papamanthou, M. F. Sayed, E. Shi, and N. Triandopoulos. TrueSet: Faster verifiable set computations. In USENIX Security, Aug. 2014.   \n[57] C. Liu, M. Hicks, and E. Shi. Memory trace oblivious program execution. In Computer Security Foundations Symposium (CSF), June 2013.   \n[58] C. Lund, L. Fortnow, H. J. Karloff, and N. Nisan. Algebraic methods for interactive proof systems. J. ACM, 39(4):859–868, Oct. 1992.   \n[59] D. Malkhi, N. Nisan, B. Pinkas, and Y. Sella. Fairplay—a secure two-party computation system. In USENIX Security, Aug. 2004.   \n[60] D. Manolescu, B. Beckman, and B. Livshits. Volta: Developing distributed applications by recompiling. IEEE Software, 2008.   \n[61] S. Meiklejohn, C. C. Erway, A. Küpçü, T. Hinkle, and A. Lysyanskaya. ZKPDL: a language-based system for efficient zero-knowledge proofs and electronic cash. In USENIX Security, 2010.   \n[62] S. Micali. Computationally sound proofs. SIAM J. on Comp., 30(4):1253–1298, 2000.   \n[63] P. L. Montgomery. Speeding the Pollard and elliptic curve methods of factorization. Math. of Computation, 48(177):243–264, Jan. 1987.   \n[64] A. Naveh and E. Tromer. PhotoProof: Cryptographic image authentication for any set of permissible transformations. In IEEE S&P, May 2016.   \n[65] C. Papamanthou, E. Shi, and R. Tamassia. Signatures of correct computation. In IACR TCC, Mar. 2013.   \n[66] B. Parno, C. Gentry, J. Howell, and M. Raykova. Pinocchio: Nearly practical verifiable computation. In IEEE S&P, May 2013.   \n[67] B. Parno, C. Gentry, J. Howell, and M. Raykova. Pinocchio: Nearly practical verifiable computation. In Communications of the ACM, volume 59, pages 103–112, Feb. 2016.   \n[68] J. M. Rabaey, A. P. Chandrakasan, and B. Nikolic. Digital integrated circuits, volume 2. Prentice Hall Englewood Cliffs, 2002.   \n[69] G. N. Rothblum. Delegating Computation Reliably: Paradigms and Constructions. PhD thesis, Massachusetts Institute of Technology, 2009.   \n[70] P. Sasdrich and T. Güneysu. Efficient elliptic-curve cryptography using Curve25519 on reconfigurable devices. In ARC, Apr. 2014.   \n[71] P. Sasdrich and T. Güneysu. Implementing Curve25519 for side-channel–protected elliptic curve cryptography. ACM TRETS, 9(1):3:1–3:15, Nov. 2015.   \n[72] S. Setty, A. J. Blumberg, and M. Walfish. Toward practical and unconditional verification of remote computations. In HotOS, May 2011.   \n[73] S. Setty, B. Braun, V. Vu, A. J. Blumberg, B. Parno, and M. Walfish. Resolving the conflict between generality and plausibility in verified computation. In EuroSys, Apr. 2013.   \n[74] S. Setty, R. McPherson, A. J. Blumberg, and M. Walfish. Making argument systems for outsourced computation practical (sometimes). In NDSS, Feb. 2012.   \n[75] S. Setty, V. Vu, N. Panpalia, B. Braun, A. J. Blumberg, and M. Walfish. Taking proof-based verified computation a few steps closer to practicality. In USENIX Security, Aug. 2012.   \n[76] A. Shamir. $\\mathrm{IP}={}$ PSPACE. J. ACM, 39(4):869–877, Oct. 1992.   \n[77] J. Thaler. Time-optimal interactive proofs for circuit evaluation. In CRYPTO, Aug. 2013. Citations refer to full version: https://arxiv.org/abs/1304.3812.   \n[78] J. Thaler. A note on the GKR protocol. http://people.seas.harvard.edu/ jthaler/GKRNote.pdf, 2015.   \n[79] J. Thaler, M. Roberts, M. Mitzenmacher, and H. Pfister. Verifiable computation with massively parallel interactive proofs. In USENIX HotCloud Workshop, June 2012.   \n[80] Victor Vu. Personal communication, 2013.   \n[81] V. Vu, S. Setty, A. J. Blumberg, and M. Walfish. A hybrid architecture for interactive verifiable computation. In IEEE S&P, May 2013.   \n[82] R. S. Wahby, M. Howald, S. Garg, abhi shelat, and M. Walfish. Verifiable ASICs. In IEEE S&P, May 2016.   \n[83] R. S. Wahby, Y. Ji, A. J. Blumberg, abhi shelat, J. Thaler, M. Walfish, and T. Wies. Full accounting for verifiable outsourcing (extended version). Cryptology ePrint Archive, Report 2017/242, 2017.   \n[84] R. S. Wahby, S. Setty, Z. Ren, A. J. Blumberg, and M. Walfish. Efficient RAM and control flow in verifiable outsourced computation. In NDSS, Feb. 2015.   \n[85] M. Walfish and A. J. Blumberg. Verifying computations without reexecuting them: from theoretical possibility to near practicality. Communications of the ACM, 58(2):74–84, Feb. 2015.   \n[86] S. Williams. Icarus Verilog. http://iverilog.icarus.com.   \n[87] S. Zahur and D. Evans. Circuit structures for improved efficiency of security and privacy tools. In IEEE S&P, pages 493–507, May 2013.   \n[88] Y. Zhang, D. Genkin, J. Katz, D. Papadopoulos, and C. Papamanthou. vSQL: Verifying arbitrary SQL queries over dynamic outsourced databases. In IEEE S&P, May 2017.  "}