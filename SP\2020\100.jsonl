{"text": "# Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof  \n\n<PERSON>, Tian<PERSON>∗, <PERSON><PERSON><PERSON>† and <PERSON>∗ ∗University of California, Berkeley, † Texas A&M University Emails: {jiaheng zhang, tianc.x, dawnsong}@berkeley.edu, zhang<PERSON><PERSON>@tamu.edu  \n\n# Abstract  \n\nWe present a new succinct zero knowledge argument scheme for layered arithmetic circuits without trusted setup. The prover time is $O(C+n\\log n)$ and the proof size is $O(\\hat{D}\\log C+\\log^{2}n)$ for a $D$ -depth circuit with $n$ inputs and $C$ gates. The verification time is also succinct, ${\\cal{O}}(D\\log C+\\log^{2}n)$ , if the circuit is structured. Our scheme only uses lightweight cryptographic primitives such as collision-resistant hash functions and is plausibly post-quantum secure. We implement a zero knowledge argument system, Virgo, based on our new scheme and compare its performance to existing schemes. Experiments show that it only takes 53 seconds to generate a proof for a circuit computing a Merkle tree with 256 leaves, at least an order of magnitude faster than all other succinct zero knowledge argument schemes. The verification time is $50\\mathrm{ms}$ , and the proof size is 253KB, both competitive to existing systems.  \n\nUnderlying Virgo is a new transparent zero knowledge verifiable polynomial delegation scheme with logarithmic proof size and verification time. The scheme is in the interactive oracle proof model and may be of independent interest.  \n\n# I. INTRODUCTION  \n\nZero knowledge proof (ZKP) allows a powerful prover to convince a weak verifier that a statement is true, without leaking any extra information about the statement beyond its validity. In recent years, significant progress has been made to bring ZKP protocols from purely theoretical interest to practical implementations, leading to its numerous applications in delegation of computations, anonymous credentials, privacypreserving cryptocurrencies and smart contracts.  \n\nDespite of these great success, there are still some limitations of existing ZKP systems. In SNARK [60], the most commonly adopted ZKP protocol in practice, though the proof sizes are of just hundreds of bytes and the verification times are of several milliseconds regardless of the size of the statements, it requires a trusted setup phase to generate structured reference string (SRS) and the security will be broken if the trapdoor is leaked.  \n\nTo address this problem, many ZKP protocols based on different techniques have been proposed recently to remove the trusted setup, which are referred as transparent ZKP protocols. Among these techniques, ZKP schemes based on the doubly efficient interactive proof proposed by Goldwasser et al. in [42] (referred as GKR protocol in this paper) are particularly interesting due to their efficient prover time and sublinear verification time for statements represented as structured arithmetic circuits, making it promising to scale to large statements. Unfortunately, as of today we are yet to construct an efficient transparent ZKP system based on the GKR protocol with succinct1 proof size and verification time. The transparent scheme in [69] has square-root proof size and verification time, while the succinct scheme in [70] requires a one-time trusted setup. See Section I-B for more details.  \n\nOur contributions. In this paper, we advance this line of research by proposing a transparent ZKP protocol based on GKR with succinct proof size and verification time, when the arithmetic circuit representing the statement is structured. The prover time of our scheme is particularly efficient, at least an order of magnitude faster than existing ZKP systems, and the verification time is merely tens of milliseconds. Our concrete contributions are:  \n\n• Transparent zero knowledge verifiable polynomial delegation. We propose a new zero knowledge verifiable polynomial delegation (zkVPD) scheme without trusted setup. Compared to existing pairing-based zkVPD schemes [59], [72], [73], our new scheme does not require a trapdoor and linear-size public keys, and eliminates heavy cryptographic operations such as modular exponentiation and bilinear pairing. Our scheme may be of independent interest, as polynomial delegation/commitment has various applications in areas such as verifiable secret sharing [6], proof of retrievability [71] and other constructions of ZKP [55]. • Transparent zero knowledge argument. Following the framework proposed in [73], we combine our new zkVPD protocol with the GKR protocol efficiently to get a transparent ZKP scheme. Our scheme only uses light-weight cryptographic primitives such as collision-resistant hash functions and is plausibly post-quantum secure. • Implementation and evaluation. We implement a ZKP system, Virgo, based on our new scheme. We develop optimizations such that our system can take arithmetic circuits on the field generated by Mersenne primes, the operations on which can be implemented efficiently using integer additions, multiplications and bit operations in $\\mathrm{C}{+}{+}$ . We plan to open source our system.  \n\nOur main technical contribution in this paper is a new transparent zkVPD scheme with $O(N\\log N)$ prover time, ${\\cal O}(\\log^{2}N)$ proof size and verification time, where $N$ is the size of the polynomial. We summarize the key ideas behind our construction. We first model the polynomial evaluation as the inner product between two vectors of size $N$ : one defined by the coefficients of the polynomial and the other defined by the evaluation point computed on each monomial of the polynomial. The former is committed by the prover (or delegated to the prover after preprocessing in the case of delegation of computation), and the later is publicly known to both the verifier and the prover. We then develop a protocol that allows the prover to convince the verifier the correctness of the inner product between a committed vector and a public vector with proof size $O(\\log^{2}N)$ , based on the univariate sumcheck protocol recently proposed by Ben-Sasson et al. in [14] (See Section II-D). To ensure security, the verifier needs to access the two vectors at some locations randomly chosen by the verifier during the protocol. For the first vector, the prover opens it at these locations using standard commitment schemes such as Merkle hash tree. For the second vector, however, it takes $O(N)$ time for the verifier to compute its values at these locations locally. In order to improve the verification time, we observe that the second vector is defined by the evaluation point of size only $\\ell$ for a $\\ell$ -variate polynomial, which is ${\\cal O}(\\log N)$ if the polynomial is dense. Therefore, this computation can be viewed as a function that takes $\\ell$ inputs, expands them to a vector of $N$ monomials and outputs some locations of the vector. It is a perfect case for the verifier to use the GKR protocol to delegate the computation to the prover and validate the output, instead of computing locally. With proper design of the GKR protocol, the verification time is reduced to ${\\bar{O}}(\\log^{2}N)$ and the total prover time is $O(N\\log N)$ . We then turn the basic protocol into zero knowledge using similar techniques proposed in [5], [14]. The detailed protocols are presented in Section III.  \n\n# B. Related Work  \n\nZero knowledge proof. Zero knowledge proof was introduced by Goldwasser et al. in [43] and generic constructions based on probabilistically checkable proofs (PCPs) were proposed in the seminal work of Kilian [51] and Micali [58] in the early days. In recent years there has been significant progress in efficient ZKP protocols and systems. Following earlier work of Ishai [48], Groth [45] and Lipmaa [53], Gennaro et al. [40] introduced quadratic arithmetic programs (QAPs), which leads to efficient implementations of SNARKs [12], [17], [24], [35], [38], [60], [68]. The proof size and verification time of SNARK are constant, which is particularly useful for realworld applications such as cryptocurrencies [11] and smart contract [23], [52]. However, SNARKs require a per-statement trusted setup, and incurs a high overhead in the prover running time and memory consumption, making it hard to scale to large statements. There has been great research for generating the SRS through multi-parity secure computations [13] and making the SRS universal and updatable [46], [55].  \n\nMany recent works attempt to remove the trusted setup and construct transparent ZKP schemes. Based on “(MPC)- in-the-head” introduced in [31], [41], [49], Ames et al. [5] proposed a ZKP scheme called Ligero. It only uses symmetric key operations and the prover time is fast in practice, but the proof size is $O({\\sqrt{C}})$ and the verification time is quasi-linear to the size of the circuit. Later, it is categorized as interactive oracle proofs (IOPs), and in the same model Ben-Sasson et al. built Stark [9], transparent ZKP in the RAM model of computation. Their verification time is only linear to the description of the RAM program, and succinct (logarithmic) in the time required for program execution. Recently, BenSasson et al. [14] proposed Aurora, a new ZKP system in the IOP model with the proof size of $O(\\log^{2}C)$ . Our new zkVPD and ZKP schemes fall in the IOP model.  \n\nIn the seminal work of [42], Goldwasser et al. proposed an efficient interactive proof for layered arithmetic circuits, which was extended to an arugment system by Zhang et al. in [74] using a protocol for verifiable polynomial delegation. Later, Zhang et al. [75], Wahby et al. [69] and Xie et al. [70] made the argument system zero knowledge by Cramer and Damgard transformation [36] and random masking polynomials [32]. The scheme of [69], Hyrax, is transparent, yet the proof size and verification time are $O(\\sqrt{n})$ where $n$ is the input size of the circuit; the schemes of [72] and [70] are succinct for structured circuits, but require one-time trusted setup. The prover time of the GKR protocol is substantially improved in [34], [64], [67], [69], [75], and recently Xie et al. [70] proposed a variant with $O(C)$ prover time for arbitrary circuits.  \n\nOther transparent ZKP schemes based on different techniques include discrete-log-based schemes [8], [21], [28], [44], hash-based schemes [22] and lattice-based schemes [7]. See Section V-C for detailed asymptotic complexity and practical performance of state-of-the-art systems with implementations.  \n\nVerifiable polynomial delegation. Verifiable polynomial delegation (VPD) allows a verifier to delegate the computation of polynomial evaluations to a powerful prover, and validates the result in time that is constant or logarithmic to the size of the polynomial. Earlier works in the literature include [18], [39], [50]. Based on [50], Papamanthou et al. [59] propose a protocol for multivariate polynomials. Later in [73], Zhang et al. extend the scheme to an argument of knowledge using powers of exponent assumptions, allowing a prover to commit to a multivariate polynomial, and open to evaluations at points queried by the verifier. In [72], Zhang et al. further make the scheme zero knowledge. These schemes are based on bilinear maps and require a trusted setup phase that generates linearsize public keys with a trapdoor.  \n\nIn a concurrent work, Binz et al. [26] propose another transparent polynomial commitment scheme without trusted setup. The scheme utilizes groups of unknown order and the techniques are different from our construction. The prover and verifier time are $O(N)$ and ${\\cal O}(\\log N)$ modulo exponentiation in the group and the proof size is ${\\cal O}(\\log N)$ group elements.  \n\nConcretely, the proof size is 10-20KB for a circuit with $2^{20}$ gates when compiled to different ZKP systems [26, Section 6], and the prover time and the verification time are not reported. Comparing to our scheme, we expect the prover and verifier time in our scheme are faster, while our proof size is larger, which gives an interesting trade-off.  \n\n# II. PRELIMINARIES  \n\nWe use $\\lambda$ to denote the security parameter, and $\\mathsf{n e g}|(\\lambda)$ to denote the negligible function in $\\lambda$ . “PPT” stands for probabilistic polynomial time. For a multivariate polynomial $f$ , its ”variable-degree” is the maximum degree of $f$ in any of its variables. We often rely on polynomial arithmetic, which can be efficiently performed via fast Fourier tranforms and their inverses. In particular, polynomial evaluation and interpolation over a multiplicative coset of size $n$ of a finite field can be performed in $O(n\\log n)$ field operations via the standard FFT protocol, which is based on the divide-and-conquer algorthim.  \n\nA. Interactive Proofs and Zero-knowledge Arguments  \n\nInteractive proofs. An interactive proof allows a prover $\\mathcal{P}$ to convince a verifier $\\nu$ the validity of some statement through several rounds of interaction. We say that an interactive proof is public coin if $\\nu$ ’s challenge in each round is independent of $\\mathcal{P}$ ’s messages in previous rounds. The proof system is interesting when the running time of $\\nu$ is less than the time of directly computing the function $f$ . We formalize interactive proofs in the following:  \n\nDefinition 1. Let $f$ be a Boolean function. A pair of interactive machines $\\langle\\mathcal{P},\\mathcal{V}\\rangle$ is an interactive proof for $f$ with soundness $\\epsilon$ if the following holds:  \n\n• Completeness. For every $x$ such that $f(x)=1$ it holds that $\\operatorname*{Pr}[\\langle{\\mathcal{P}},{\\mathcal{V}}\\rangle(x)=1]=1$ .   \n• $\\epsilon$ -Soundness. For any $x$ with $f(x)\\neq1$ and any $\\mathcal{P}^{*}$ it holds that $\\operatorname*{Pr}[\\langle{\\mathcal{P}}^{*},{\\mathcal{V}}\\rangle=1]\\leq\\epsilon$  \n\nZero-knowledge arguments. An argument system for an NP relationship $\\mathcal{R}$ is a protocol between a computationallybounded prover $\\mathcal{P}$ and a verifier $\\nu$ . At the end of the protocol, $\\nu$ is convinced by $\\mathcal{P}$ that there exists a witness $w$ such that $(x;w)\\in R$ for some input $x$ . We focus on arguments of knowledge which have the stronger property that if the prover convinces the verifier of the statement validity, then the prover must know $w$ . We use $\\mathcal{G}$ to represent the generation phase of the public parameters pp. Formally, consider the definition below, where we assume $R$ is known to $\\mathcal{P}$ and $\\nu$ .  \n\nDefinition 2. Let $\\mathcal{R}$ be an NP relation. A tuple of algorithm $(\\mathcal{G},\\mathcal{P},\\mathcal{V})$ is a zero-knowledge argument of knowledge for $\\mathcal{R}$ if the following holds.  \n\n• Correctness. For every pp output by $\\mathcal{G}(1^{\\lambda})$ and $(x,w)\\in R,$ $\\langle\\mathcal{P}(\\mathsf{p p},w),\\mathcal{V}(\\mathsf{p p})\\rangle(x)=1$  \n\nSoundness. For any PPT prover $\\mathcal{P}$ , there exists a PPT extractor $\\varepsilon$ such that for every pp output by $\\mathcal{G}(1^{\\lambda})$ and any $x$ , the following probability is $\\mathsf{n e g}|(\\lambda)$ : $\\operatorname*{Pr}[\\langle\\mathcal{P}(\\mathfrak{p}\\mathfrak{p}),\\mathcal{V}(\\mathfrak{p}\\mathfrak{p})\\rangle(x)=1\\wedge(x,w)\\notin\\mathcal{R}|w\\leftarrow\\varepsilon(\\mathfrak{p}\\mathfrak{p},x)]$ Zero knowledge. There exists a PPT simulator $\\boldsymbol{S}$ such that for any PPT algorithm $\\nu^{*}$ , auxiliary input $z\\in\\{0,1\\}^{*}$ , $(x;w)\\in{\\mathcal{R}},$ , pp output by $\\mathcal{G}(1^{\\lambda})$ , it holds that  \n\n$$\n\\mathsf{V i e w}(\\langle\\mathcal{P}(\\mathsf{p p},w),\\mathcal{V}^{*}(z,\\mathsf{p p})\\rangle(x))\\approx S^{\\mathcal{V}^{*}}(x,z)\n$$  \n\nWe say that $(\\mathcal{G},\\mathcal{P},\\mathcal{V})$ is a succinct argument system if the running time of $\\nu$ and the total communication between $\\mathcal{P}$ and $\\nu$ (proof size) are $\\mathsf{p o l y}(\\lambda,|x|,\\log|w|)$ .  \n\nIn the definition of zero knowledge, $S^{\\nu^{*}}$ denotes that the simulator $\\boldsymbol{S}$ is given the randomness of $\\nu^{*}$ sampled from polynomial-size space. This definition is commonly used in existing transparent zero knowledge proof schemes [5], [14], [28], [69].  \n\n# B. Zero-Knowledge Verifiable Polynomial Delegation  \n\nLet $\\mathbb{F}$ be a finite field, $\\mathcal{F}$ be a family of $\\ell$ -variate polynomial over $\\mathbb{F}$ , and $d$ be a variable-degree parameter. We use $\\mathcal{W}_{\\ell,d}$ to denote the collection of all monomials in $\\mathcal{F}$ and $N=|\\mathcal{W}_{\\ell,d}|=(d+1)^{\\ell}$ . A zero-knowledge verifiable polynomial delegation scheme (zkVPD) for $f\\in{\\mathcal{F}}$ and $t\\in\\mathbb{F}^{\\ell}$ consists of the following algorithms:  \n\n$$\n\\begin{array}{r l}&{\\bullet\\mathsf{p p}\\qquad\\mathsf{z k V P D.K e y G e n}(1^{\\lambda}),}\\ &{\\bullet\\mathsf{c o m}\\qquad\\mathsf{z k V P D.C o m m i t}(f,r_{f},\\mathsf{p p}),}\\ &{\\bullet\\left((y,\\pi);\\{0,1\\}\\right)}\\ &{\\quad\\langle\\mathsf{z k V P D.O p e n}(f,r_{f}),\\mathsf{z k V P D.V e r i f y}(\\mathsf{c o m})\\rangle(t,\\mathsf{p p})}\\end{array}\n$$  \n\nNote that unlike the zkVPD in [59], [72], [73], our definition is transparent and does not have a trapdoor in zkVPD.KeyGen. $\\pi$ denotes the transcript seen by the verifier during the interaction with zkVPD.Open, which is similar to the proof in noninteractive schemes in [59], [72], [73].  \n\nDefinition 3. A zkVPD scheme satisfies the following properties:  \n\n• Completeness. For any polynomial $\\textit{f}\\in\\mathcal{F}$ and value $\\begin{array}{r l r l r}{t}&{{}\\in}&{\\mathbb{F}^{\\ell},\\quad\\mathsf{p p}}&{\\gets}&{{}z\\mathsf{k V P D.K e y G e n}(1^{\\lambda})}\\end{array}$ , com $z k V P D.C o m m i t(f,r_{f}p p)$ , it holds that  \n\n$$\n\\operatorname*{Pr}\\left[\\langle z\\mathsf{k V P D}.0\\mathsf{p e n}(f,r_{f}),z\\mathsf{k V P D}.\\mathsf{V e r i f y}(\\mathsf{c o m})\\rangle(t,\\mathsf{p p})=1\\right]=1\n$$  \n\n• Soundness. For any PPT adversary $\\mathcal{A}$ , pp $z k\\mathsf{V P D.K e y{G e n(1^{\\lambda})}}$ , the following probability is negligible of $\\lambda$ :  \n\n$$\n\\operatorname*{Pr}{\\left[\\begin{array}{l}{(f^{*},\\mathsf{c o m}^{*},t)\\quad A(1^{\\lambda},\\mathsf{p p})}\\ {((y^{*},\\pi^{*});1)\\leftarrow\\langle A(),z\\mathsf{k V P D}.\\mathsf{V e r i f y}(\\mathsf{c o m}^{*})\\rangle(t,\\mathsf{p p})}\\ {\\mathsf{c o m}^{*}=z\\mathsf{k V P D}.\\mathsf{C o m m i t}(f^{*},\\mathsf{p p})}\\ {f^{*}(t)\\neq y^{*}}\\end{array}\\right]}\n$$  \n\n• Zero Knowledge. For security parameter $\\lambda,$ polynomial $f\\in{\\mathcal{F}}$ , $\\mathsf{p p}\\gets\\mathsf{z k V P D.K e y G e n}(1^{\\lambda})$ , PPT algorithm $\\mathcal{A}$ , and simulator ${\\cal S}~=~(S_{1},S_{2})$ , consider the following two experiments:  \n\n<html><body><table><tr><td>RealA,f(pp): 1）com←zkVPD.Commit(f,rf,pp) 2)t A(com,pp) 3）(y,π)←(zkVPD.Open(f,rf),A)(t,pp) 4)b A(com,y,T,pp) 5）Output b</td></tr><tr><td>Ideal A,sA (pp): 1）com S1(1^,pp) 2)t A(com, pp) 3) (y,π) (S2,A)(ti,pp),given oracle access to y =f(t). 4)b A(com,y,π,pp)</td></tr><tr><td>5）Output b</td></tr></table></body></html>\n\nFor any PPT algorithm and all polynomial , there exists simulator $\\boldsymbol{S}$ such that  \n\n$$\n\\vert\\operatorname*{Pr}[\\mathsf{R e a l}_{\\mathcal{A},f}(\\mathsf{p p})=1]-\\operatorname*{Pr}[\\mathsf{l d e a l}_{\\mathcal{A},S^{\\mathcal{A}}}(\\mathsf{p p})=1]\\vert\\leq\\mathsf{n e g l}(\\lambda).\n$$  \n\nC. Zero Knowledge Argument Based on GKR  \n\nIn [70], Xie et al. proposed an efficient zero knowledge argument scheme named Libra. The scheme extends the interactive proof protocol for layered arithmetic circuits proposed by Goldwasser et al. [42] (referred as the $G K R$ protocol) to a zero knowledge argument using multiple instances of $\\mathrm{_{zkVPD}}$ schemes. Our scheme follows this framework and we review the detailed protocols here.  \n\nSumcheck protocol. The sumcheck protocol is a fundamental protocol in the literature of interactive proof that has various applications. The problem is to sum a polynomial $f:\\mathbb{F}^{\\ell}\\to$ $\\mathbb{F}$ on the binary hypercube $\\begin{array}{r}{\\sum_{b_{1},b_{2},...,b_{\\ell}\\in\\{0,1\\}}f\\big(b_{1},b_{2},...,b_{\\ell}\\big)}\\end{array}$ . Directly computing the su m requires exponential time in $\\ell$ , as there are $2^{\\ell}$ combinations of $b_{1},\\dots,b_{\\ell}$ . Lund et al. [54] proposed a sumcheck protocol that allows a verifier $\\nu$ to delegate the computation to a computationally unbounded prover $\\mathcal{P}$ , who can convince $\\nu$ the correctness of the sum. At the end of the sumcheck protocol, $\\nu$ needs an oracle access to the evaluation of $f$ at a random point $r\\in\\mathbb{F}^{\\ell}$ chosen by $\\nu$ . The proof size of the sumcheck protocol is $O(d\\ell)$ , where $d$ is the variable-degree of $f$ , and the verification time of the protocol is $O(d\\ell)$ . The sumcheck protocol is complete and sound with $\\begin{array}{r}{\\epsilon=\\frac{d\\ell}{|\\mathbb{F}|}}\\end{array}$ .  \n\nGKR protocol. Let $C$ be a layered arithmetic circuit with depth $D$ over a finite field $\\mathbb{F}$ . Each gate in the $i$ -th layer takes inputs from two gates in the $(i+1)$ -th layer; layer 0 is the output layer and layer $D$ is the input layer. The GKR protocol proceeds layer by layer. Upon receiving the claimed output from $\\mathcal{P}$ , in the first round, $\\nu$ and $\\mathcal{P}$ run a sumcheck protocol to reduce the claim about the output to a claim about the values in the layer above. In the $i$ -th round, both parties reduce a claim about layer $i-1$ to a claim about layer $i$ through sumcheck. Finally, the protocol terminates with a claim about the input layer $D$ , which can be checked directly by $\\nu$ . If the check passes, $\\nu$ accepts the claimed output.  \n\nFormally speaking, we denote the number of gates in the $i$ -th layer as $S_{i}$ and let $s_{i}~=~\\lceil\\log S_{i}\\rceil$ . We then define a function $V_{i}:\\{0,1\\}^{s_{i}}\\to\\mathbb{F}$ that takes a binary string $b\\in\\{0,1\\}^{s_{i}}$ and returns the output of gate $b$ in layer $i$ , where $b$ is called the gate label. With this definition, $V_{0}$ corresponds to the output of the circuit, and $V_{D}$ corresponds to the input. As the sumcheck protocol works on $\\mathbb{F}$ , we then extend $V_{i}$ to its multilinear extension, the unique polynomial $\\tilde{V}_{i}:\\mathbb{F}^{s_{i}}\\to\\mathbb{F}$ such that $\\tilde{V}_{i}(x_{1},x_{2},...,x_{s_{i}})=V_{i}(x_{1},x_{2},...,x_{s_{i}})$ for all $x_{1},x_{2},...,x_{s_{\\tilde{\\imath}}}\\in\\{0,1\\}^{s_{i}}$ . As shown in prior work [34], the closed form of $V_{i}$ can be computed as:  \n\n$$\n\\begin{array}{r l}&{\\tilde{V}_{i}(x_{1},x_{2},...,x_{s_{i}})}\\ &{=\\displaystyle\\sum_{b\\in\\{0,1\\}^{s_{i}}}\\prod_{i=1}^{s_{i}}[((1-x_{i})(1-b_{i})+x_{i}b_{i})\\cdot V_{i}(b)],}\\end{array}\n$$  \n\nwhere $b_{i}$ is $i$ -th bit of $\\mathbf{b}$ .  \n\nWith these definitions, we can express the evaluations of $\\ddot{V_{i}}$ as a summation of evaluations of $\\tilde{V}_{i+1}$ :  \n\n$$\n\\begin{array}{r l}&{\\quad\\alpha_{i}\\tilde{V}_{i}(u^{(i)})+\\beta_{i}\\tilde{V}_{i}(v^{(i)})}\\ &{=\\sum_{x,y\\in\\{0,1\\}^{s_{i+1}}}f_{i}\\big(\\tilde{V}_{i+1}(x),\\tilde{V}_{i+1}(y)\\big),}\\end{array}\n$$  \n\nwhere $u^{(i)},v^{(i)}\\in\\mathbb{F}^{s_{i}}$ are random vectors and $\\alpha_{i},\\beta_{i}\\in\\mathbb{F}$ are random values. Note here that $f_{i}$ depends on $\\alpha_{i},\\beta_{i},u^{(i)},v^{(i)}$ and we omit the subscripts for easy interpretation.  \n\nWith Equation 2, the GKR protocol proceeds as follows. The prover $\\mathcal{P}$ first sends the claimed output of the circuit to $\\nu$ . From the claimed output, $\\nu$ defines polynomial $\\tilde{V}_{0}$ and computes $\\tilde{V}_{0}(u^{(0)})$ and $\\tilde{V_{0}}(v^{(0)})$ for random $u^{(0)},v^{(0)}\\in\\mathbb{F}^{s_{0}}$ . $\\nu$ then picks two random values $\\alpha_{0},\\beta_{0}$ and invokes a sumcheck protocol on Equation 2 with $\\mathcal{P}$ for $i=0$ . As described before, at the end of the sumcheck, $\\nu$ needs an oracle access to the evaluation of $f_{0}$ at $u^{(1)},v^{(1)}$ randomly selected in $\\mathbb{\\overline{{\\mathbb{F}}}}^{s_{1}}$ . To compute this value, $\\nu$ asks $\\mathcal{P}$ to send $\\tilde{V}_{1}(u^{(1)})$ and $\\tilde{V}_{1}(v^{(1)})$ . Other than these two values, $f_{0}$ only depends on $\\alpha_{0},\\beta_{0},\\dot{u}^{(0)},v^{(0)}$ and the gates and wiring in layer 0, which are all known to $\\nu$ and can be computed by $\\nu$ directly. In this way, $\\nu$ and $\\mathcal{P}$ reduces two evaluations of $\\tilde{V_{0}}$ to two evaluations of $\\tilde{V}_{1}$ in layer 1. $\\nu$ and $\\mathcal{P}$ then repeat the protocol recursively layer by layer. Eventually, $\\nu$ receives two claimed evaluations $\\tilde{V}_{D}^{\\alpha}(u^{(\\dot{D})})$ and $\\tilde{V}_{D}(v^{(D)})$ . $\\nu$ then checks the correctness of these two claims directly by evaluating $\\tilde{V}_{D}$ , which is defined by the input of the circuit. Let $\\mathsf{G K R.P}$ and $\\mathsf{G K R.}\\mathcal{V}$ be the algorithms for the GKR prover and verifier, we have the following theorem:  \n\nLemma 1. [34], [42], [64], [70]. Let $C:\\mathbb{F}^{n}\\to\\mathbb{F}$ be a depth- $D$ layered arithmetic circuit. $\\langle\\mathsf{G K R.P,G K R.}\\mathcal{V}\\rangle(C,x)$ is an interactive proof per Definition 1 for the function computed by $C$ on input $x$ with soundness $O(D\\log|C|/|\\mathbb{F}|)$ . The total communication is $O(D\\log|C|)$ and the running time of the prover $\\mathcal{P}$ is $O(|C|)$ . When $C$ has regular wiring pattern2, the running time of the verifier $\\nu$ is $O(n+D\\log|C|)$ .  \n\nExtending GKR to Zero Knowledge Argument. There are two limitations of the GKR protocol: (1) It is not an argument system supporting witness from $\\mathcal{P}$ , as $\\nu$ needs to evaluate $\\check{V}_{D}$ locally in the last round; (2) It is not zero knowledge,  \n\n2“Regular” circuits is defined in [34, Theorem A.1]. Roughly speaking, it means the mutilinear extension of its wiring predicates can be evaluated at a random point in time $O(\\log|C|)$ .  \n\nsecurity parameter, $\\mathbb{F}$ be a prime field. Let $C:\\mathbb{F}^{n}\\to\\mathbb{F}$ be a layered   \narithmetic circuit over $\\mathbb{F}$ with $D$ layers, input in and witness $w$ such   \nthat $|\\mathbf{i}\\mathbf{n}|+|w|\\le n$ and $1=C(\\dot{\\mathsf{m}};w)$ .   \n• $\\mathcal{G}(1^{\\lambda})$ : set pp as pp $z\\mathsf{k V P D.K e y G e n(1}^{\\lambda}).$ .   \n• $\\langle\\mathcal{P}(\\mathsf{p p},w),\\mathcal{V}(\\mathsf{p p})\\rangle(\\mathsf{i n}$ ): 1) $\\mathcal{P}$ selects a random bivariate polynomial $R_{D}$ . $\\mathcal{P}$ commits to the witness of $C$ by sending ${\\mathsf{c o m}}_{\\mathscr{D}}$ zkVPD.Commit $({\\dot{V}}_{D},r_{V_{D}}$ , pp) to $\\nu$ , where $\\dot{V}_{D}$ is defined by Equation 3.   \n2) $\\mathcal{P}$ randomly selects polynomials $R_{i}:\\mathbb{F}^{2}\\rightarrow\\mathbb{F}$ and $\\delta_{i}$ : $\\mathbb{F}^{2s_{i+1}+1}\\rightarrow\\mathbb{F}$ for $i=0,\\dots,D-1$ . $\\mathcal{P}$ commits to these polynomials by sending $\\mathsf{c o m}_{i,1}\\gets\\mathsf{z k V P D.C o m m i t}(R_{i},r_{R_{i}},\\mathsf{p p})$ and $\\mathsf{c o m}_{i,2}\\gets\\mathsf{z k V P D.C o m m i t}(\\delta_{i},r_{\\delta_{i}},\\mathsf{p p})$ to $\\nu$ . $\\mathcal{P}$ also reveals $R_{0}$ to $\\nu.$ , as $V_{0}$ is known to $\\nu$ . 3) $\\nu$ evaluates $\\dot{V}_{0}(u^{(0)})$ and $\\dot{V}_{0}(v^{(0)})$ for randomly chosen ${\\boldsymbol u}^{(0)},{\\boldsymbol v}^{(0)}\\in\\mathbb{F}^{s_{0}}$ . 4) For $i=0,\\dots,D-1$ : a) $\\mathcal{P}$ sends $\\begin{array}{r}{H_{i}=\\sum_{x,y\\in\\{0,1\\}^{s_{i}+1}\\_\\in\\{0,1\\}}\\delta_{i}(x,y,z)}\\end{array}$ to $\\nu$ . ) $\\nu$ picks $\\alpha_{i},\\beta_{i},\\gamma_{i}$ randomly in $\\mathbb{\\textrm{H}}$ . $c$ ) $\\nu$ and $\\mathcal{P}$ execute a sumcheck protocol on Equation 4. $A t$ the end of the sumcheck, $\\nu$ receives a claim of $f_{i}^{\\prime}$ at point $u^{(i+1)},v^{(i+1)}\\in\\mathbb{F}^{s_{i+1}}$ , $g_{i}\\in\\mathbb{F}$ selected randomlyi by $\\nu$ . $^d$ ) $\\mathcal{P}$ opens $R_{i}(u^{(i)},g_{i})$ , $R_{i}(v^{(i)},g_{i})$ and $\\delta_{i}\\big(u^{(i+1)},v^{(i+1)},g_{i}\\big)$ using zkVPD.Open. $\\mathcal{P}$ sends $\\dot{V}_{0}(u^{(i+1)})$ and $\\dot{V}_{0}(v^{(i+1)})$ to $\\nu$ . $e$ ) $\\nu$ $\\delta_{i}\\big(\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}\\big)$ validates $R_{i}(u^{(i)},g_{i})$ using zkVPD.Verify. If any of , $R_{i}(v^{(i)},g_{i})$ and them outputs $O_{i}$ , abort and output 0. $f)\\nu$ checks the claim of $f_{i}^{\\prime}$ using $R_{i}(u^{(i)},g_{i})$ , $R_{i}(v^{(i)},g_{i})$ , $\\delta_{i}\\big(\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}\\big)$ , $\\dot{V}_{0}(u^{(i+1)})$ and $\\dot{V}_{0}(v^{(i+1)})$ . If $i t$ fails, output 0. 5) $\\mathcal{P}$ runs $\\begin{array}{r l r}{(y_{1},\\pi_{1})}&{{}\\leftarrow}&{\\mathsf{z k V P D.O p e n}(\\dot{V}_{D},r_{V_{D}},u^{(D)},\\mathsf{p p}).}\\end{array}$ , $\\begin{array}{r l r}{(y_{2},\\pi_{2})}&{{}\\leftarrow}&{\\mathsf{z k V P D.O p e n}(\\dot{V}_{D},r_{V_{D}},v^{(D)},\\mathsf{p p})}\\end{array}$ and sends $y_{1},\\pi_{1},y_{2},\\pi_{2}$ to $\\nu$ . 6) $\\nu$ runs $\\mathsf{V e r i f y}(\\pi_{1},y_{1},\\mathsf{c o m}_{D},u^{(D)},\\mathsf{p p})$ and VVerify $\\cdot(\\pi_{2},y_{2},\\mathsf{c o m}_{D},v^{(D)},\\mathsf{p p})$ and output 0 if either check fails. Otherwise, $\\nu$ checks $\\dot{V}_{D}(u^{(D)})~=~y_{1}$ and $\\dot{V}_{D}(v^{(\\bar{D})})=y_{2}$ , and rejects if either fails. If all checks above pass, $\\nu$ output 1.  \n\nas in each round, both the sumcheck protocol and the two evaluations of $\\tilde{V}_{i}$ leak information about the values in layer $i$ .  \n\nTo extend the GKR protocol to a zero knowledge argument, Xie et al. [70] address both of the problems using zero knowledge polynomial delegation. Following the approach of [69], [72], [73], to support witness $w$ as the input to the circuit, $\\mathcal{P}$ commits to $\\tilde{V}_{D}$ using zkVPD before running the GKR protocol. In the last round of GKR, instead of evaluating $\\tilde{V}_{D}$ locally, $\\nu$ asks $\\mathcal{P}$ to open $\\tilde{V}_{D}$ at two random points $\\boldsymbol u^{(D)},\\dot{\\boldsymbol v}^{(D)}$ selected by $\\nu$ and validates them using zkVPD.Verify. In this way, $\\nu$ does not need to access $w$ directly and the soundness still holds because of the soundness guarantee of zkVPD.  \n\nTo ensure zero knowledge, using the techniques proposed by Chiesa et al. in [32], the prover $\\mathcal{P}$ masks the polynomial $\\tilde{V_{i}}$ and the sumcheck protocol by random polynomials so that the proof does not leak information. For correctness and soundness purposes, these random polynomials are committed using the zkVPD protocol and opened at random points chosen by $\\nu$ . In particular, for layer $i$ , the prover selects a random bivariate polynomial $R_{i}(x_{1},z)$ and defines  \n\n$$\n\\begin{array}{c}{{\\dot{V}_{i}(x_{1},\\ldots,x_{s_{i}})}}\\ {{\\stackrel{d e f}{=}}{\\tilde{V}}_{i}(x_{1},\\ldots,x_{s_{i}})+Z_{i}(x_{1},\\ldots,x_{s_{i}})\\displaystyle\\sum_{z\\in\\{0,1\\}}R_{i}(x_{1},z),}}\\end{array}\n$$  \n\nwhere $\\begin{array}{r}{Z_{i}(x)=\\prod_{i=1}^{s_{i}}x_{i}(1-x_{i})}\\end{array}$ , i.e., $Z_{i}(x)~=~0$ for all $x\\in\\{0,1\\}^{s_{i}}$ . $\\dot{V_{i}}$ is known as the low degree extension of $V_{i}$ , as $\\acute{V}_{i}(x)~=~\\tilde{V}_{i}(x)~=~V_{i}(x)$ for all $\\boldsymbol{x}\\in\\{0,1\\}^{s_{i}}$ . As $R_{i}$ is randomly selected by $\\mathcal{P}$ , revealing evaluations of $\\dot{V}_{i}$ does not leak information about $V_{i}$ , thus the values in the circuit. Additionally, $\\mathcal{P}$ selects another random polynomial $\\delta_{i}(x_{1},\\dots,x_{s_{i+1}},y_{1},\\dots,y_{s_{i+1}},z)$ to mask the sumcheck protocol. Let $\\begin{array}{r l}{H_{i}}&{{}=}\\end{array}$ $\\begin{array}{r}{\\sum_{x,y\\in\\{0,1\\}^{s_{i+1}},z\\in\\{0,1\\}}\\delta_{i}(x_{1},\\ldots,x_{s_{i+1}},y_{1},\\ldots,y_{s_{i+1}},z)}\\end{array}$ , Equation 2 to run sumcheck on becomes  \n\n$$\n\\begin{array}{r l}&{\\quad\\alpha_{i}\\dot{V}_{i}(u^{(i)})+\\beta_{i}\\dot{V}_{i}(v^{(i)})+\\gamma_{i}H_{i}}\\ &{=\\displaystyle\\sum_{x,y\\in\\{0,1\\}^{s_{i+1}},z\\in\\{0,1\\}}f_{i}^{\\prime}(\\dot{V}_{i+1}(x),\\dot{V}_{i+1}(y),}\\ &{\\quad R_{i}(u_{1}^{(i)},z),R_{i}(v_{1}^{(i)},z),\\delta_{i}(x,y,z)),}\\end{array}\n$$  \n\nwhere $\\gamma_{i}~\\in~\\mathbb{F}$ is randomly selected by $\\nu$ , and $f_{i}^{\\prime}$ is defined by $\\alpha_{i},\\beta_{i},\\gamma_{i},u^{(i)},v^{(i)},Z_{i}(u^{(i)}),Z_{i}(v^{(i)})^{3}$ . Now $\\nu$ and $\\mathcal{P}$ can execute the sumcheck and GKR protocol on Equation 4. In each round, $\\mathcal{P}$ additionally opens $R_{i}$ and $\\delta_{i}$ at $R_{i}(u_{1}^{(i)},g^{(i)}),R_{i}(v_{1}^{(i)},g^{(i)}),\\delta_{i}(u^{(i+1)},\\bar{v}^{(i+\\dot{1})},g^{(i)})$ for $g^{(i)}\\in\\mathbf{\\Gamma}$ $\\mathbb{F}$ randomly selected by $\\nu$ . With these values, $\\nu$ reduces the correctness of two evaluations $\\dot{V}_{i}(u^{(i)}),\\dot{V}_{i}(v^{(i)})$ to two evaluations $\\dot{V}_{i}\\big(u^{(i+1)}\\big),\\dot{V}_{i}\\big(v^{(i+1)}\\big)$ on one layer above like before. In addition, as $f_{i}$ is masked by $\\delta_{i}$ , the sumcheck protocol is zero knowledge; as $\\tilde{V}_{i}$ is masked by $R_{i}$ , the two evaluations of $\\dot{V_{i}}$ do not leak information. The full zero knowledge argument protocol in [70] is given in Protocol 1. We have the following theorem:  \n\nLemma 2. [70]. Let $C:\\mathbb{F}^{n}\\to\\mathbb{F}$ be a layered arithmetic circuit with $D$ layers, input in and witness $w$ . Protocol $I$ is $a$ zero knowledge argument of knowledge under Definition 2 for the relation defined by $1=C(\\dot{\\mathsf{m}};w)$ .  \n\nThe variable degree of $R_{i}$ is $O(1)$ . $\\delta_{i}(x,y,z)=\\delta_{i,1}(x_{1})+$ $\\ldots+\\delta_{i,s_{i+1}}(x_{s_{i+1}})+\\delta_{i,s_{i+1}+1}(y_{1})+\\ldots+\\delta_{i,2s_{i+1}}(y_{s_{i+1}})+ $ $\\delta_{i,2s_{i+1}+1}(z)$ is the summation of $2s_{i+1}+1$ univariate polynomials of degree $O(1)$ . Other than the $z k V P D$ instantiations, the proof size is $O(D\\log|C|)$ and the prover time is $O(|C|)$ . When $C$ is regular, the verification time is $O(n+D\\log|C|)$ .  \n\n$D$ . Univariate Sumcheck  \n\nOur transparent zkVPD protocol is inspired by the univariate sumcheck protocol recently proposed by Ben-Sasson et al.in [14]. As the name indicates, the univariate sumcheck protocol allows the verifier to validate the result of the sum  \n\n3Formally, $f_{i}^{\\prime}$ is $I(0,z)f_{i}(\\dot{V}_{i+1}(x),\\dot{V}_{i+1}(y))\\qquad+\\qquad$ $I((x,y),0)(\\alpha_{i}Z_{i}(u^{(i)})R(u_{1}^{(i)},z)+\\beta_{i}Z_{i}(v^{(i)})R(v_{1}^{(i)},z))+\\gamma_{i}\\delta_{i}(x,y,z),$ where $I(a,b)$ is an identity polynomial $I(a,b)=0$ iff $a=b$ . We will not use $f_{i}^{\\prime}$ explicitly in our constructions later.  \n\nof a univariate polynomial on a subset $\\mathbb{H}$ of the field $\\mathbb{F}$ : $\\begin{array}{r}{\\mu=\\sum_{a\\in\\mathbb{H}}f(a)}\\end{array}$ . The key idea of the protocol relies on the follo wing lemma:  \n\nLemma 3. [27]. Let H be a multiplicative coset4 of $\\mathbb{F}$ , and let $g(x)$ be a univariate polynomial over $\\mathbb{F}$ of degree strictly less that $\\left\\vert\\mathbb{H}\\right\\vert$ . Then $\\begin{array}{r}{\\sum_{a\\in\\mathbb{H}}g(a)=g(0)\\cdot|\\mathbb{H}|}\\end{array}$ .  \n\nBecause of Lemma 3, to test the result of $\\textstyle\\sum_{a\\in\\mathbb{H}}f(a)$ for $f$ with degree less than $k$ , we can decompose $f$ into two parts $f(x)=g(x)+Z_{\\mathbb{H}}(x)\\cdot h(x)$ , where $\\begin{array}{r}{Z_{\\mathbb{H}}(x)=\\prod_{a\\in\\mathbb{H}}(x-a)}\\end{array}$ (i.e., $Z_{\\mathbb{H}}(a)=0$ for all $a\\in\\mathbb{H},$ ), and the deg rees of $g$ and $h$ are strictly less than $|\\mathbb{H}|$ and $k-|\\mathbb{H}|$ . This decomposition is unique for every $f$ . As $Z_{\\mathbb{H}}(a)$ is always 0 for $a\\in\\mathbb{H}$ , $\\begin{array}{r}{\\mu=\\sum_{a\\in\\mathbb{H}}f(a)=\\sum_{a\\in\\mathbb{H}}g(a)=g(0)\\cdot|\\mathbb{H}|}\\end{array}$ by Lemma 3. Ther efore, if the clai med sum $\\mu$ sent by the prover is correct, $f(x)-Z_{\\mathbb{H}}(x)\\cdot h(x)-\\mu/|\\mathbb{H}|$ must be a polynomial of degree less than $\\left\\vert\\mathbb{H}\\right\\vert$ with constant term 0, or equivalently polynomial  \n\n$$\np(x)=\\frac{|\\mathbb{H}|\\cdot f(x)-|\\mathbb{H}|\\cdot Z_{\\mathbb{H}}(x)\\cdot h(x)-\\mu}{|\\mathbb{H}|\\cdot x}\n$$  \n\nmust be a polynomial of degree less than $|\\mathbb{H}|-1$ . To test this, the univariate sumcheck uses a low degree test (LDT) protocol on Reed-Solomon (RS) code. We will define Reed-Solomon Code in appendix and LDT below.  \n\nLow Degree Test and Rational Constraints. Low degree test allows a verifier to test whether a polynomial/vector belongs to an RS code, i.e., the vector is the evaluations of some polynomial of degree less than $m$ on $\\mathbb{L}$ .  \n\nIn our constructions, we use the LDT protocol in [14, Protocol 8.2], which was used to transform an RS-encoded IOP to a regular IOP. It applies the LDT protocol proposed in [10] protocol to a sequence of polynomials $\\vec{\\rho}$ and their rational constraint $p$ , which is a polynomial that can be computed as the division of the polynomials in $\\vec{\\rho}$ . In the case of univariate sumcheck, the sequence of polynomials is ${\\vec{\\rho}}=(f,h)$ and the rational constraint is given by Equation 5.  \n\nThe high level idea is as follows. First, the verifier multiplies each polynomial in $\\vec{\\rho}$ and the rational constraint $p$ with an appropriate monomial such that they have the same degree max, and takes their random linear combination. Then the verifier tests that the resulting polynomial is in $R S[\\mathbb{L},\\mathsf{m a x}+1]$ . At the end of the protocol, the verifier needs oracle access to $\\kappa$ evaluations of each polynomial in $\\vec{\\rho}$ and the rational constraint $p$ at points in $\\mathbb{L}$ indexed by $\\mathcal{T}$ , and checks that each evaluation of $p$ is consistent with the evaluations of the polynomials in $\\vec{\\rho}$ . We denote the protocol as $\\langle\\mathsf{L D T}.\\mathcal{P}(\\vec{\\rho},p)$ $\\vec{\\mathbf{\\rho}},p),\\mathsf{L D T.}\\mathcal{V}(\\vec{m},\\deg(p))\\rangle(\\mathbb{L})$ , where $\\vec{\\rho}$ is a sequence of polynomials over $\\mathbb{F}$ , $p(x)$ is their rational constraint, $\\vec{m}$ , $\\deg(p)$ is the degrees of the polynomials and the rational constraint to test, and $\\mathbb{L}$ is a multiplicative coset of $\\mathbb{F}$ . We state the properties of the protocol in the following lemma:  \n\nLemma 4. There exist an LDT protocol $\\langle\\mathsf{L D T}.\\mathcal{P}(\\vec{\\rho},p)$ , LDT. $\\mathcal{V}(\\vec{m},\\deg(p))\\rangle(\\mathbb{L})$ that is complete and sound with soundness error $\\begin{array}{r}{O(\\frac{|\\mathbb{L}|}{|\\mathbb{F}|})+\\mathsf{n e g l}(\\kappa),}\\end{array}$ , given oracle access to evaluations of each polynomial in $\\vec{\\rho}$ at $\\kappa$ points indexed by $\\mathcal{T}$ in $\\mathbb{L}$ . The proof size and the verification time are $O(\\log|\\mathbb{L}|)$ other than the oracle access, and the prover time is $O(\\mathbb{L})$ .  \n\nThe LDT protocol can be made zero knowledge in a straight-forward way by adding a random polynomial of degree max in $\\vec{\\rho}$ . That is, there exists a simulator $\\boldsymbol{S}$ such that given the random challenges of $\\mathcal{T}$ of any PPT algorithm $\\nu^{*}$ , it can simulate the view of $\\nu^{*}$ such that $\\mathsf{V i e w}(\\langle\\mathsf{L D T}.\\mathcal{P}(\\vec{\\rho},p),\\mathcal{V}^{*}(\\vec{m},\\deg(p))\\rangle(\\mathbb{L}))\\approx S^{\\mathcal{V}^{*}}(\\deg(p))$ . In particular, $s$ generates $p^{*}\\in R S[\\mathbb{L},\\deg(p)]$ and can simulate the view of any sequence of random polynomials $\\vec{\\rho}^{*}$ subject to the constraint that their evaluations at points indexed by $\\mathcal{T}$ are consistent with the oracle access of $p^{*}$ .  \n\nMerkle Tree. Merkle hash tree proposed by Ralph Merkle in [57] is a common primitive to commit a vector and open it at an index with logarithmic proof size and verification time. It consists of three algorithms:  \n\n$\\Upsilon{0}{0}\\mathfrak{t}_{c}$ MT.Commit(c)   \n• $(c_{i d x},\\pi_{i d x})$ MT.Open(idx, c)   \n. $(1,0)\\qquad\\mathsf{M T.V e r i f y}(\\mathsf{r o o t}_{c},i d x,c_{i d x},\\pi_{i d x})$   \nThe security follows the collision-resistant property of the hash function used to construct the Merkle tree.  \n\nWith these tools, the univariate sumcheck protocol works as follows. To prove $\\textstyle{\\mu}=\\sum_{a\\in\\mathbb{H}}f(a)$ , the verifier and the prover picks $\\mathbb{L}$ , a multiplic ative coset of $\\mathbb{F}$ and a superset of $\\mathbb{H}$ , where $|\\mathbb{L}|>k$ . $\\mathcal{P}$ decompose $f(x)=g(x)+Z_{\\mathbb{H}}(x)\\cdot h(x)$ as defined above, and computes the vectors $f\\vert_{\\mathbb{L}}$ and $h|_{\\mathbb{L}}$ . $\\mathcal{P}$ then commits to these two vectors using Merkle trees. $\\mathcal{P}$ then defines a polynomial $\\begin{array}{r}{p(x)=\\frac{|\\mathbb{H}|\\cdot f(x)-|\\mathbb{H}|\\cdot Z_{\\mathbb{H}}(x)\\cdot h(x)-\\mu}{|\\mathbb{H}|\\cdot x}}\\end{array}$ , which is a rational constraint of $f$ and $h$ . As explained above, in order to ensure the correctness of $\\mu$ , it suffices to test that the degree of $(f,h),p$ is less than $(k,k-|\\mathbb{H}|),|\\mathbb{H}|-1$ , which is done through the low degree test. At the end of the LDT, $\\nu$ needs oracle access to $\\kappa$ points of $f\\vert_{\\mathbb{L}}$ and $h\\vert_{\\mathbb{L}}$ . $\\mathcal{P}$ sends these points with their Merkle tree proofs, and $\\nu$ validates their correctness. The formal protocol and the lemma is presented in Appendix B. As shown in [14], it suffices to set $|\\mathbb{L}|=O(|\\mathbb{H}|)$ .  \n\n# III. TRANSPARENT ZERO KNOWLEDGE POLYNOMIAL DELEGATION  \n\nIn this section, we present our main construction, a zero knowledge verifiable polynomial delegation scheme without trusted setup. We first construct a VPD scheme that is correct and sound, then extend it to be zero knowledge. Our construction is inspired by the univariate sumcheck [14] described in Section II-D.  \n\nOur main idea is as follows. To evaluate an $\\ell$ -variate polynomial $f$ with variable degree $d$ at point $t=(t_{1},\\dots,t_{\\ell})$ , we model the evaluation as the inner product between the vector of coefficients in $f$ and the vector of all monomials in $f$ evaluated at $t$ . Formally speaking, let $N=|\\mathcal{W}_{\\ell,d}|=(d+1)^{\\ell}$ be the  \n\nProtocol 2 (Verifiable Polynomial Delegation). Let $\\mathcal{F}$ be a family of   \n$\\ell$ -variate polynomial over $\\mathbb{F}$ with variable-degree $d$ and $N=(d+|$   \n$1)^{\\ell}$ .We use $\\mathcal{W}_{\\ell,d}=\\{W_{i}(x_{1},\\ldots,x_{\\ell})\\}_{i=1}^{N}$ to denote the collection   \nof all monomials in $\\mathcal{F}$ . $r_{f}=\\perp$ and we omit if in the algorithms.   \n• pp $\\mathsf{K e y G e n}(1^{\\lambda})$ : Pick a hash function from the collisionresistant hash function family for Merkle tree. Find a multiplicative coset $\\mathbb{H}$ of $\\mathbb{F}$ such that $|\\mathbb{H}|=(d+1)^{\\ell}$ . Find a multiplicative coset $\\mathbb{L}$ of $\\mathbb{F}$ such that $|\\mathbb{L}|=O(|\\mathbb{H}|)>2|\\mathbb{H}|$ and $\\mathbb{H}\\subset\\mathbb{L}\\subset\\mathbb{F}$ .   \n• $\\mathsf{c o m}\\gets\\mathsf{C o m m i t}(f,\\mathsf{p p}).$ $\\begin{array}{r}{f(x)=\\sum_{i=1}^{N}c_{i}\\overset{.}{W_{i}}(\\overset{.}{x})}\\end{array}$ u. $f\\in{\\mathcal{F}}$ atpeosl mainadl $l(x):\\mathbb{F}\\rightarrow\\mathbb{F}$ $l|_{\\mathbb{H}}=(c_{1},\\ldots,c_{N})$ $\\mathcal{P}$ $l\\vert_{\\mathbb{L}}$ runs $\\mathsf{r o o t}_{l}\\gets\\mathsf{M T}.\\mathsf{C o m m i t}(l|_{\\mathbb{L}})$ . Output $\\mathsf{c o m}=\\mathsf{r o o t}_{l}$ .   \n• $((\\mu,\\pi);\\{0,1\\})\\leftarrow\\langle\\mathsf{O p e n}(f),\\mathsf{V e r i f y}(\\mathsf{c o m})\\rangle(t,\\mathsf{p p}).$ : This is an interactive protocol between $\\mathcal{P}$ and $\\nu$ . $I$ ) $\\mathcal{P}$ computes $\\mu=f(t)$ and sends it to $\\nu$ . 2) $\\mathcal{P}$ evaluates $T=(W_{1}(t),\\ldots,W_{N}(t))$ . $\\mathcal{P}$ finds the unique univariate polynomial $q(x):\\mathbb{F}\\rightarrow\\mathbb{F}$ such that $q|_{\\mathbb{H}}=T$ . 3) $\\mathcal{P}$ computes $l(x)\\cdot q(x)$ . $\\mathcal{P}$ uniquely decomposes $l(x)\\cdot q(x)=$ $g(x)+Z_{\\mathbb{H}}(x)\\cdot h(x)$ , where $\\begin{array}{r}{Z_{\\mathbb{H}}(x)=\\prod_{a\\in\\mathbb{H}}(x-a)}\\end{array}$ and the degrees of $g$ and $h$ are strictly less tha n $|\\mathbb{H}|$ and $|\\mathbb{H}|-1.\\mathcal{P}$ evaluates $h|_{\\mathbb{L}}$ and runs $\\mathsf{r o o t}_{h}\\gets\\mathsf{M T.C o m m i t}(h|_{\\mathbb{L}})$ and sends   \n4) $\\mathsf{r o o t}_{h}$ $\\nu$ . and invoke a $\\begin{array}{r l r}{p(x)}&{{}=}&{\\frac{|\\mathbb{H}|\\cdot l(x)\\cdot q(x)-\\mu-|\\mathbb{H}|\\cdot\\mathbb{Z}_{\\mathbb{H}}(x)h(x)}{|\\mathbb{H}|\\cdot x}}\\end{array}$ $\\mathcal{P}$ $\\nu$ low degree test: $\\langle\\mathsf{L D T}.\\mathcal P((i\\cdot q,h),p),\\mathsf{L D T}.\\mathcal V((2|\\mathbb{H}|-1,|\\mathbb{H}|-$ $1),|\\mathbb{H}|\\textrm{--}1)\\rangle(\\mathbb{L})$ . If the test fails, $\\nu$ aborts and output $O_{\\l}$ . Otherwise, at then end of the test, $\\nu$ needs oracle access to κ points of $l(x)\\cdot q(x),h(x)$ and $p(x)$ at indices $\\mathcal{L}$ . 5) For each index $i\\in\\mathcal{T}$ , let $a_{i}$ be the corresponding point in L. $\\mathcal{P}$ opens $(l(a_{i}),\\pi_{i}^{l})\\gets\\mathsf{M T.O p e n}(i,l|_{\\mathbb{L}})$ and $(\\bar{h(a_{i})},\\pi_{i}^{h})$ MT.Open $(i,h|_{\\mathbb{L}})$ . 6) $\\nu$ executes MT.Verify $(\\mathsf{r o o t}_{l},i,l(a_{i}),\\pi_{i}^{l})$ and MT.Verify $(\\mathsf{r o o t}_{h},i,h(a_{i}),\\pi_{i}^{h})$ for all points opened by $\\mathcal{P}$ . If any verification fails, abort and output 0. 7) To complete the low degree test, $\\mathcal{P}$ and $\\nu$ runs $\\langle{\\sf G K R.P},{\\sf G K R.}{\\mathcal V}\\rangle(C,t).$ , where circuit $C$ computes the evaluations of $q|_{\\mathbb{L}}$ and outputs the elements $q(a_{i})$ for $i\\in\\mathcal{T}$ (see Figure 3 in appendix). If any of the checks in GKR fails, $\\nu$ aborts and outputs $O$ . 8) For each $i\\in\\mathcal{T}$ , $\\nu$ computes $l(a_{i})\\cdot q(a_{i})$ . Together with $h(a_{i})$ , $\\nu$ completes the low degree test. If all checks above pass, $\\nu$ outputs $I$ .  \n\nnumber of possible monomials in an $\\ell$ -variate polynomial with variable degree $d$ , and let $c=(c_{1},\\ldots,c_{N})$ be the coefficients of $f$ in the order defined by $\\mathcal{W}_{\\ell,d}$ such that $f(x_{1},\\dots,x_{\\ell})=$ $\\textstyle\\sum_{i=1}^{N}c_{i}W_{i}(x)$ , where $W_{i}(x)$ is the $i$ -th monomial in $\\mathcal{W}_{\\ell,d}$ . Define the vector $T=(W_{1}(t),\\dots,W_{N}(t))$ , then naturally the evaluation equals $\\begin{array}{r}{f(t)=\\sum_{i=1}^{N}c_{i}\\cdot T_{i}}\\end{array}$ , the inner product of the two vectors. We the n select a multiplicative coset $\\mathbb{H}$ such that $|\\mathbb{H}|~=~N$ , 5 and interpolate vectors $c$ and $T$ to find the unique univariate polynomials that evaluate to $c$ and $T$ on $\\mathbb{H}$ . We denote the polynomials as $l(x)$ and $q(x)$ such that $l|_{\\mathbb{H}}={\\mathrm{~\\it~c~}}$ and $q|_{\\mathbb{H}}=T$ . With these definitions, $\\begin{array}{r}{f(t)=\\sum_{i=1}^{N}c_{i}\\cdot T_{i}=\\sum_{a\\in\\mathbb{H}}l(a)\\cdot q(a)}\\end{array}$ , which is the sum of the  polynomial $l(x)\\cdot q(x)$ on $\\mathbb{H}$ . The verifier can check the evaluation through a univariate sumcheck protocol with the prover. The detailed protocol is presented in step 1-4 of Protocol 2.  \n\nUp to this point, the construction for validating the inner product between a vector committed by $\\mathcal{P}$ and a public vector is similar to and simpler than the protocols to check linear constraints proposed in [5], [14]. However, naively applying the univariate sumcheck protocol incurs a linear overhead for the verifier. This is because as described in Section II-D, at the end of the univariate sumcheck, due to the low degree test, the verifier needs oracle access to the evaluations of $l(x)\\cdot q(x)$ at $\\kappa$ points on $\\mathbb{L}$ , a superset of $\\mathbb{H}$ . As $l(x)$ is defined by $c$ , i.e. the coefficients of $f$ , the prover can commit to $l\\vert_{\\mathbb{L}}$ at the beginning of the protocol, and opens to points the verifier queries with their Merkle tree proofs. $q(x)$ , however, is defined by the public vector $T$ , and the verifier has to evaluate it locally, which takes linear time. This is the major reason why the verification time in the zero knowledge proof schemes for generic arithmetic circuits in [5], [14] is linear in the size of the circuits.  \n\nReducing the verification time. In this paper, we propose an approach to reduce the cost of the verifier to poly-logarithmic for VPD. We observe that in our construction, though the size of $T$ and $q(x)$ is linear in $N$ , it is defined by only $\\ell=O(\\log N)$ values of the evaluation point $t$ . This means that the oracle access of $\\kappa$ points of $q(x)$ can be modeled as a function that: (1) Takes $t$ as input, evaluates all monomials $W_{i}(t)$ for all $W_{i}\\in\\mathcal{W}_{\\ell,d}$ as a vector $T$ ; (2) Extrapolates the vector $T$ to find polynomial $q(x)$ , and evaluates $q(x)$ on $\\mathbb{L}$ ; (3) Outputs $\\kappa$ points of $q|_{\\mathbb{L}}$ chosen by the verifier. Although the size of the function modeled as an arithmetic circuit is $\\Omega(N)$ with ${\\cal O}(\\log N)$ depth, and the size of its input and output is only $O(\\log N+\\kappa)$ . Therefore, instead of evaluating the function locally, the verifier can delegate this computation to the prover, and validate the result using the GKR protocol, as presented in Section II-C. In this way, we eliminate the linear overhead to evaluate these points locally, making the verification time of the overall VPD protocol poly-logarithmic. The formal protocol is presented in Protocol 2.  \n\nTo avoid any asymptotic overhead for the prover, we also design an efficient layered arithmetic circuit for the function mentioned above. The details of the circuit are presented in Figure 3. In particular, in the first part, each value $t_{i}$ in the input $t$ is raised to powers of $0,1,\\ldots,d$ . Then they are expanded to $T$ , the evaluations of all monomials in $\\mathcal{W}_{\\ell,d}$ , by multiplying one $t_{i}$ at a time through a $(d+1)$ -ary tree. The size of this part is ${\\cal O}(N)={\\cal O}((d+1)^{\\ell})$ and the depth is $O(\\log d+\\ell)$ . In the second part, the polynomial $q(x)$ and the vector $q|_{\\mathbb{L}}$ is computed from $T$ directly using FFTs. We first construct a circuit for an inverse FFT to compute the coefficients of polynomial $q(x)$ from its evaluations $T$ . Then we run an FFT to evaluate $q|_{\\mathbb{L}}$ from the coefficients of $q(x)$ . We implement FFT and IFFT using the Butterfly circuit [33]. The size of the circuit is $O(N\\log N)$ and the depth is ${\\cal O}(\\log N)$ . Finally, $\\kappa$ points are selected from $q|_{\\mathbb{L}}$ . As the whole delegation of the GKR protocol is executed at the end in Protocol 2 after these points being fixed by the verifier, the points to output are directly hard-coded into the circuit with size $O(\\kappa)$ and depth 1. No heavy techniques for random accesses in the circuit is needed. Therefore, the whole circuit is of size $O(N\\log N)$ and depth ${\\cal O}(\\log N)$ , with $\\ell$ inputs and $\\kappa$ outputs.  \n\nTheorem 1. Protocol 2 is a verifiable polynomial delegation protocol that is complete and sound under Definition 3.  \n\nWe give the proof in Appendix C.  \n\nEfficiency. The running time of Commit is $O(N\\log N)$ . $C$ in step 7 is a regular circuit with size $O(N\\log N)$ , depth $O(\\ell+$ $\\log d)$ and size of input and output $O(\\ell+\\kappa)$ . By Lemma 1 and 5, the prover time is $O(N\\log N)$ , the proof size and the verification time are $(\\log^{2}N)$ .  \n\nExtending to other ZKP schemes. We notice that our technique can be potentially applied to generic zero knowledge proof schemes in [5], [14] to improve the verification time for circuits/constraint systems with succinct representation. As mentioned previously, the key step that introduces linear verification time in these schemes is to check a linear constraint system, i.e., $y=\\mathbf{A}w$ , where $w$ is a vector of all values on the wires of the circuit committed by the prover, and A is a public matrix derived from the circuit such that $\\mathbf{A}w$ gives a vector of left inputs to all multiplication gates in the circuit. (This check is executed 2 more times to also give right inputs and outputs.) To check the relationship, it is turned into a vector inner product $\\mu=r y=r\\mathbf{A}\\cdot w$ by multiplying both sides by a random vector $r$ . Similar to our naive protocol to check inner product, the verification time is linear in order to evaluate the polynomial defined by $r\\mathbf{A}$ at $\\kappa$ points. With our new protocol, if the circuit can be represented succinctly in sublinear or logarithmic space, A can be computed by a function with sublinear or logarithmic number of inputs. We can use the GKR protocol to delegate the computation of $r\\mathbf{A}$ and the subsequent evaluations to the prover in a similar way as in our construction, and the verification time will only depend on the space to represent the circuit, but not on the total size of the circuit. This is left as a future work.  \n\n# A. Achieving Zero Knowledge  \n\nOur VPD protocol in Protocol 2 is not zero knowledge. Intuitively, there are two places that leak information about the polynomial $f$ : (1) In step 6 of Protocol 2, $\\mathcal{P}$ opens evaluations of $l(x)$ , which is defined by the coefficients of $f$ ; (2) In step 4, $\\mathcal{P}$ and $\\nu$ execute low degree tests on $(l(x)\\cdot q(x),h(x)),p(x)$ and the proofs of LDT reveal information about the polynomials, which are related to $f$ .  \n\nTo make the protocol zero knowledge, we take the standard approaches proposed in [5], [14]. To eliminate the former leakage of queries on $l(x)$ , the prover picks a random degree $\\kappa$ polynomial $r(x)$ and masks it as $l^{\\prime}(x)=l(x){+}Z_{\\mathbb{H}}(x){\\cdot}r(x)$ , where as before, $\\begin{array}{r}{Z_{\\mathbb{H}}(x)~=~\\prod_{a\\in\\mathbb{H}}(x-a)}\\end{array}$ . Note here that $l^{\\prime}(a)=l(a)$ for $a\\in\\mathbb{H}$ , yet a ny $\\kappa$ evaluations of $l^{\\prime}(x)$ outside $\\mathbb{H}$ do not reveal any information about $l(x)$ because of the masking polynomial $r(x)$ . The degree of $l^{\\prime}(x)$ is $|\\mathbb{H}|+\\kappa$ , and we denote domain $\\mathbb{U}=\\mathbb{L}-\\mathbb{H}$ .  \n\nProtocol 3 (Zero Knowledge Verifiable Polynomial Delegation). Let $\\mathcal{F}$ be a family of $\\ell$ -variate polynomial over $\\mathbb{F}$ with variable-degree $d$ and $N=(d+1)^{\\ell}$ .We use $\\mathcal{W}_{\\ell,d}=\\{W_{i}(x_{1},\\ldots,x_{\\ell})\\}_{i=1}^{N}$ to denote the collection of all monomials in $\\mathcal{F}$ .  \n\n• $\\mathsf{p p}\\gets\\mathsf{z k V P D.K e y G e n(1}^{\\lambda})$ : Same as KeyGen in Procotol 2. Define $\\mathbb{U}=\\mathbb{L}-\\mathbb{H}$ .   \n• com $\\gets\\mathsf{C o m m i t}(f,r_{f},\\mathsf{p p})$ : For a polynomial $\\textit{f}\\in\\mathcal{F}$ of the form $\\begin{array}{r c l}{f(x)}&{=}&{\\sum_{i=1}^{N}c_{i}W_{i}(x)}\\end{array}$ , find the unique univariate polynomial $l(x):\\mathbb{F}\\rightarrow\\mathbb{F}:$ such that $l\\vert_{\\mathbb{H}}~=~\\left(c_{1},\\ldots,c_{N}\\right)$ . $\\mathcal{P}$ samples a polynomial $r(x)$ with degree $\\kappa$ randomly and sets $l^{\\prime}(x)=l(x)+Z_{\\mathbb{H}}(x)\\cdot r(x)$ , where $\\begin{array}{r}{Z_{\\mathbb{H}}(x)=\\prod_{a\\in\\mathbb{H}}(x-a)}\\end{array}$ . $\\mathcal{P}$ evaluates $l{'}|_{\\mathbb{U}}$ and runs root $l^{\\prime}$ MT.Comm it\u0003 $(l^{\\prime}|_{\\mathbb{U}})$ . Output $\\mathsf{c o m}=\\mathsf{r o o t}_{l^{\\prime}}$ .   \n• $((\\mu,\\pi);\\{0,1\\})\\gets\\langle{\\sf O p e n}(f,r_{f})$ , Verif $\\prime(\\mathsf{c o m})\\rangle(t,\\mathsf{p p})$ : This is an interactive protocol between $\\mathcal{P}$ and $\\nu$ . It replaces the univariate sumscheck on $l(x)\\cdot q(x)$ by $l^{\\prime}(x)\\cdot q(x)+\\alpha s(x)$ and L by $\\mathbb{U}$ in Protocol 2. $I$ ) $\\mathcal{P}$ computes $\\mu=f(t)$ and sends it to $\\nu$ .   \n2) $\\mathcal{P}$ evaluates $T=(W_{1}(t),\\dots,W_{N}(t))$ . $\\mathcal{P}$ finds the unique univariate polynomial $q(x):\\mathbb{F}\\rightarrow\\mathbb{F}$ such that $q|_{\\mathbb{H}}=T$ . 3) $\\mathcal{P}$ samples randomly a degree $2|\\mathbb{H}|+\\kappa-1$ polynomial $s(x)$ . $\\mathcal{P}$ sends $\\begin{array}{r}{\\mathcal V S=\\sum_{a\\in\\mathbb{H}}s(a)}\\end{array}$ and $\\mathrm{root}_{s}\\gets$ MT.Commit ${\\bf\\Xi}(s|{\\bf g})$ . 4) $\\nu$ picks $\\alpha\\in\\mathbb{F}$ r a\u0002ndo∈mly and sends it to $\\mathcal{P}$ . 5) $\\mathcal{P}$ computes $\\alpha l^{\\prime}(x)\\cdot q(x)+s(x)$ . $\\mathcal{P}$ uniquely decomposes $i t$ as $g(x)+Z_{\\mathbb{H}}(x)\\cdot h(x)$ , where the degrees of $g$ and $h$ are strictly less than $|\\mathbb{H}|$ and $\\left\\vert\\mathbb{H}\\right\\vert+\\kappa$ . $\\mathcal{P}$ evaluates $h|_{\\mathbb{U}}$ and sends root $^h$ MT.Commit $(h|_{\\mathbb{U}})$ to $\\nu$ . 6) Let $\\begin{array}{r l r}{p(x)}&{{}=}&{\\frac{\\left|\\mathbb{H}\\right|\\cdot\\left(\\dot{\\alpha}l^{\\prime}(\\dot{x})\\cdot q(x)+s(x)\\right)-(\\alpha\\mu+S)-\\left|\\mathbb{H}\\right|\\cdot\\mathbb{Z}_{\\mathbb{H}}\\left(x\\right)h(x)}{\\left|\\mathbb{H}\\right|\\cdot x}}\\end{array}$ . $\\mathcal{P}$ and $\\nu$ invoke the low degree test: $\\langle\\mathsf{L D T}.\\mathcal{P}((l^{\\prime}\\mathrm{~\\boldmath~\\cdot~}\\$ $q,h,s),p)$ , LDT $\\mathcal{V}((2|\\mathbb{H}|+\\kappa,|\\mathbb{H}|+\\kappa,2|\\mathbb{H}|+\\kappa),|\\mathbb{H}|-1)\\rangle(\\mathbb{U})$ . If the test fails, $\\nu$ aborts and output $O$ . Otherwise, at the end of the test, $\\nu$ needs oracle access to $\\kappa$ points of $l^{\\prime}(x)\\cdot q(x),h(x),s(x)$ and $p(x)$ at indices $\\mathcal{T}$ . 7) For each index $i\\in\\mathcal{T},$ , let $a_{i}$ be the corresponding point in U. $\\mathcal{P}$ opens $(l^{\\prime}(a_{i}),\\pi_{i}^{l^{\\prime}})$ $\\mathsf{M T.O p e n}(i,l^{\\prime}|_{\\mathbb{U}})$ , $(h(a_{i}),\\pi_{i}^{h})$ $\\mathsf{M T}.\\mathsf{O p e n}(i,h|\\mathbb{U})$ and $(s(a_{i}),\\pi_{i}^{s})\\gets\\mathsf{M T.O p e n}(i,s|_{\\mathbb{U}})$ . 8) $\\nu$ executes MT.Verify $(\\mathsf{r o o t}_{l^{\\prime}},i,l^{\\prime}(a_{i}),\\pi_{i}^{l^{\\prime}}).$ , MT.Verify $(\\mathsf{r o o t}_{h},i,h(a_{i}),\\pi_{i}^{h})$ and MT.Ve $\\mathsf{r i f y}(\\mathsf{r o o t}_{s},i,s(a_{i}),\\pi_{i}^{s})$ for all points opened by $\\mathcal{P}$ . If any verification fails, abort and output 0. 9) $T o$ complete the low degree test, $\\mathcal{P}$ and $\\nu$ runs $\\langle{\\sf G K R.P},{\\sf G K R.}{\\mathcal{V}}\\rangle(C,t)$ , where circuit $C$ computes the evaluations of $q|_{\\mathbb{U}}$ and outputs the elements $q(a_{i})$ for $i\\in\\mathcal{T}.$ . If any of the checks in GKR fails, $\\nu$ aborts and outputs $O$ .   \n$I O,$ ) For each $i\\in\\mathcal{T}$ , $\\nu$ computes $l^{\\prime}(a_{i})\\cdot q(a_{i})$ . Together with $h(a_{i})$ and $s(a_{i})$ , $\\nu$ completes the low degree test. If all checks above pass, $\\nu$ outputs $I$ .  \n\nTo eliminate the latter leakage, $\\mathcal{P}$ samples a random polynomial $s(x)$ of the same degree as $l^{\\prime}(x)\\cdot q(x)$ , sends $\\begin{array}{r}{S=\\sum_{a\\in\\mathbb{H}}s(a)}\\end{array}$ to $\\nu$ and runs the univariate sumcheck protoc ol on their random linear combination: $\\alpha\\mu+S=$ $\\begin{array}{r}{\\sum_{a\\in\\mathbb{H}}(\\alpha l^{\\prime}(x)\\cdot q(x)+s(x))}\\end{array}$ for a random $\\alpha\\in\\mathbb{F}$ chosen by $\\nu$ . This ensures that both $\\mu$ and $S$ are correctly computed because of the random linear combination and the linearity of the univariate sumcheck, while leaking no information about $l^{\\prime}(x)\\cdot q(x)$ during the protocol, as it is masked by $s(x)$ .  \n\nOne advantage of our construction is that the GKR protocol used to compute evaluations of $q(x)$ in step 7 of Protocol 2 remains unchanged in the zero knowledge version of the VPD. This is because $q(x)$ and its evaluations are independent of the polynomial $f$ or any prover’s secret input. Therefore, it suffices to apply the plain version of GKR without zero knowledge, avoiding any expensive cryptographic primitives.  \n\nThe full protocol for our zkVPD is presented in Protocol 3. Note that all the evaluations are on $\\mathbb{U}=\\mathbb{L}-\\mathbb{H}$ instead of $\\mathbb{L}$ , as evaluations on $\\mathbb{H}$ leaks information about the original $l(x).s(x)$ is also committed and opened using Merkle tree for the purpose of correctness and soundness. The efficiency of our zkVPD protocol is asymptotically the same as our VPD protocol in Protocol 2, and the concrete overhead in practice is also small. We have the following theorem:  \n\nTheorem 2. Protocol 3 is a zero knowledge verifiable polynomial delegation scheme by Definition 3.  \n\nWe give the proof in Appendix D.  \n\n# IV. ZERO KNOWLEDGE ARGUMENT  \n\nFollowing the framework of [70], we can instantiate the zkVPD in Protocol 1 with our new construction of transparent zkVPD in Protocol 3 to obtain a zero knowledge argument of knowledge scheme for layered arithmetic circuits without trusted setup. In this section, we present two optimizations to improve the asymptotic performance, followed by the formal description of the scheme.  \n\n# A. zkVPD for Input Layer  \n\nAs presented in Section II-C, to extend the GKR protocol to a zero knowledge argument, we need a zkVPD protocol for the low degree extension $\\dot{V}_{D}$ of polynomial $V_{D}$ defined by Equation 3. The variable degree of $\\dot{V}_{D}$ for $x_{2},\\ldots,x_{s_{D}}$ is 2, and the variable degree for $x_{1}$ is 3. Naively applying our zkVPD protocol in Section III-A would incur a prover time of $O(s_{D}3^{s_{D}})$ , superlinear in the size of the input $n=O(2^{s_{D}})$ .  \n\nInstead, we observe that the low degree extension in Equation 3 is of a special form: it is the sum of the multilinear extension $\\tilde{V}_{D}$ defined by Equation 1 and $\\begin{array}{r}{Z_{D}(x)\\sum_{z\\in\\{0,1\\}}R_{D}(x_{1},z)}\\end{array}$ , where $Z_{D}$ is publicly known and $\\textstyle\\sum_{z\\in\\{0,1\\}}R_{D}(x_{1},z)$ is a degree-1 univariate polynomial, i.e. z 0,1 RD(x1, z) = a0 + a1x1. Therefore, the evaluation of $V_{D}$ at point $t~\\in~\\mathbb{F}^{s_{D}}$ can be modeled as the inner product between two vectors $T$ and $c$ of length $n+2$ . The first $n$ elements in $T$ are $\\begin{array}{r}{\\prod_{i=1}^{s_{D}}((1-t_{i})(1-b_{i})+t_{i}b_{i})}\\end{array}$ for all $b\\in\\{0,1\\}^{s_{D}}$ , conc atenated by two more elements $Z_{D}(t),Z_{D}(t)\\cdot t_{1}$ . Similarly, the first $n$ elements of $c$ are $V_{D}(b)$ for all $b\\in\\{0,1\\}^{s_{D}}$ , concatenated by $a_{0},a_{1}$ .  \n\nTherefore, $\\mathcal{P}$ and $\\nu$ replace vectors $T$ and $c$ in Protocol 3 by ones described above. In addition, the first part of the GKR circuit shown in Figure 3 to compute $T$ from $t_{1},\\ldots t_{s_{D}}$ is also changed according to the definition of $T$ above. The rest of the protocol remains the same and it is straight forward to prove that the modified protocol is still correct, sound and zero knowledge. In this way, the prover time is $O(n\\log n)$ , the proof size is ${\\bar{O}}(\\log^{2}n)$ and the verification time is $O(\\log^{2}n)$ .  \n\nB. zkVPD for Interior Layers  \n\nThe second place that uses zkVPD in Protocol 1 is on the masking polynomials $R_{i}$ and $\\delta_{i}$ in each layer. By Theorem 2, $\\delta_{i}:\\mathbb{F}^{2s_{i+1}+1}\\to\\mathbb{F}$ is a sparse polynomial that can be expressed as the sum of $2s_{i+1}+1$ univariate polynomials of degree $\\deg(\\delta_{i})=O(1)$ on each variable. Therefore, instead of using the generic zkVPD in Protocol 3 with $d=\\deg(\\delta_{i})$ , we model the evaluation of $\\delta_{i}$ as a vector inner product between two dense vectors of size $(\\deg(\\delta_{i})+1)\\cdot(2s_{i+1}+1)$ . The vector committed by $\\mathcal{P}$ consists of all coefficients in $\\delta_{i}$ , and the one known to $\\nu$ consists of the value of each variable raised to degree $0,1,\\ldots,\\deg(\\delta_{i})$ . In addition, as the size of the vector is asymptotically the same as the number of variables, in step 9-10 of Protocol 3, $\\nu$ can compute the evaluations of $q(x)$ directly in time $O(s_{i+1})$ and it is not necessary to delegate the computation to $\\mathcal{P}$ using GKR anymore. With this approach, the prover time for evaluating the masking polynomials $R_{i}$ and $\\delta_{i}$ of all layers is $O(D\\log C\\log\\log C)$ , the proof size is $O(D\\log\\log^{2}C)$ and the verification time is $O(D\\log C)$ . As shown in Lemma 2, this does not introduce any asymptotic overhead for the zero knowledge argument scheme.  \n\nTo further improve the efficiency in practice, we can also combine all the evaluations of $R_{i}$ and $\\delta_{i}$ into one big vector inner product using random linear combinations.  \n\n# C. Putting Everything Together  \n\nWith the optimizations above, the full protocol of our transparent zero knowledge argument scheme is presented in Protocol 5 in the appendix. Consider the following theorem:  \n\nTheorem 3. For a finite field $\\mathbb{F}$ and a family of layered arithmetic circuit ${\\mathcal{C}}_{\\mathbb{F}}$ over $\\mathbb{F}$ , Protocol $5$ is a zero knowledge argument of knowledge for the relation  \n\n$$\n{\\mathcal{R}}=\\{(C,x;w):C\\in{\\mathcal{C}}_{\\mathbb{F}}\\land C(x;w)=1\\},\n$$  \n\nas defined in Definition 2.  \n\nMoreover, for every $(C,x;w)\\in\\mathcal{R}$ , the running time of $\\mathcal{P}$ is $O(|C|+n\\log n)$ field operations, where $\\smash{\\underline{{n}}=|x|+|w|}$ . The running time of $\\nu$ is $O(|x|+D\\cdot\\log|C|+\\log^{2}n)$ if $C$ is regular with $D$ layers. $\\mathcal{P}$ and $\\nu$ interact $O(D\\log|C|)$ rounds and the total communication (proof size) is $O(D\\log|C|+\\log^{2}n)$ . In case $D$ is polylog $(|C|)$ , the protocol is a succinct argument.  \n\nSoundness follows the knowledge soundness of our zkVPD protocol (Appendix D) and Lemma 1. To prove zero knowledge, we present the simulator in Figure 5 in the Appendix. The efficiency follows Lemma 2 and the efficiency of our instantiations of the zkVPD protocol with optimizations described above.  \n\nRemoving interactions. Similar to [70], our construction can be made non-interactive in the random oracle model using Fiat-Shamir heuristic [37]. As shown in recent work [15], [30], applying Fiat-Shamir on the GKR protocol only incurs a polynomial soundness loss in the number of rounds.  \n\nRegular circuits and log-space uniform. In our scheme, the verification time is succinct only when the circuit is regular.  \n\nThis is the best that can be achieved for transparent ZKP, as in the worst case, the verifier must read the entire circuit, which takes linear time. In fact, as shown in [42], the verification time is succinct for all log-space uniform circuits. However, it introduces an extra overhead on the prover time, thus we state all of our results on regular circuits.  \n\nIn practice, with the help of auxiliary input and circuit squashing, most computations can be expressed as regular circuits with low depth, such as matrix multiplication, image scaling and Merkle hash tree in Section V. Asymptotically, as shown in [9], [16], [75], all random memory access (RAM) programs can be validated by circuits that are regular with log-depth in the running time of the programs (but linear in the size of the programs) by RAM-to-circuit reduction, which justifies the expressiveness of such circuits. f  \n\n# V. IMPLEMENTATION AND EVALUATION  \n\nWe implement Virgo, a zero knowledge proof system based on our construction in Section IV. The system is implemented in $\\mathrm{C}{+}{+}$ . There are around 700 lines of code for our transparent zkVPD protocol and 2000 lines for the GKR part.  \n\nHardware. We run all of the experiments on AMD RyzenTM 3800X Processor with 64GB RAM. Our current implementation is not parallelized and we only use a single CPU core in the experiments. We report the average running time of 10 executions, unless specified otherwise.  \n\n# A. Choice of Field with Efficient Arithmetic  \n\nOne important optimization we developed during the implementation is on the choice of the underlying field. Our scheme is transparent and does not use any discrete log or bilinear pairing as in [69], [70], [72], [73]. However, there is one requirement on the finite field: in order to run the low degree test protocol in [10], either the field is an extension of $\\mathbb{F}_{2}$ , or there exists a multiplicative subgroup of order $2^{k}$ in the field for large enough $k$ (one can think of $2^{k}\\geq|\\mathbb{L}|=O(|\\mathbb{H}|)=O(n))$ . Existing zero knowledge proof systems that use the LDT protocol as a building block such as Stark [9] and Aurora [14] run on the extension fields $\\mathbb{F}_{2^{64}}$ and $\\mathbb{F}_{2^{192}}$ . Modern CPUs (e.g., AMD RyzenTM 3800X Processor) have built-in instructions for field arithmetics on these extension fields, which improves the performance of these systems significantly. However, the drawback is that the arithmetic circuits representing the statement of ZKP must also operate on the same field, and the additions (multiplications) are different from integer or modular additions (multiplications) that are commonly used in the literature. Because of this, Stark [9] has to design a special SHA-256 circuit on ${\\mathbb F}_{2^{64}}$ , and Aurora [14] only reports the performance versus circuit size (number of constraints), but not on any commonly used functions.  \n\nOne could also use a prime field $p$ with an order- $2^{k}$ multiplicative subgroup. Equivalently, this requires that $2^{k}$ is a factor of $p-1$ . In fact, there exist many such primes and Aurora [14] also supports prime fields. However, the speed of field arithmetic is much slower than extension fields of $\\mathbb{F}_{2}$ (see Table I).  \n\nIn this paper, we provide an alternative to achieve the best of both cases. A first attempt is to use Mersenne primes, primes that can be expressed as $p=2^{m}-1$ for integers $m$ . As shown in [34], [64], multiplications modulo Mersenne primes is known to be very efficient. However, Mersenne primes do not satisfy the requirement of the LDT, as $p-1=$ $2^{m}-2=2\\cdot(2^{m-1}-1)$ only has a factor $2^{1}$ . Instead, we propose to use the extension field of a Mersenne prime $\\mathbb{F}_{p^{2}}$ .The multiplicative group of $\\mathbb{F}_{p^{2}}$ is a cyclic group of order $p^{\\overset{.}{2}}-1=(2^{m}-1)^{2}-1=2^{2m}-2^{\\overset{.}{m^{+}}1}=2^{m+1}(2^{m-1}-1)$ , thus it has a multiplicative subgroup of order $2^{m+1}$ , satisfying the requirement of LDT when $m$ is reasonably large. Meanwhile, to construct an arithmetic circuit representing the statement of the ZKP, we still encode all the values in the first slot of the polynomial ring defined by $\\mathbb{F}_{p^{2}}$ . In this way, the additions and multiplications in the circuit are on $\\mathbb{F}_{p}$ and our system can take the same arithmetic circuits over prime fields in prior work. Meanwhile, the LDT, zkVPD and GKR protocol are executed on $\\mathbb{F}_{p^{2}}$ , preserving the soundness over the whole field.  \n\nWith this alternative approach, we can implement modular multiplications on $\\mathbb{F}_{p^{2}}$ using 3 modular multiplications on $\\mathbb{F}_{p}$ . (The modular multiplication is analog to multiplications of complex numbers.) In our implementation, we choose Mersenne prime $p=2^{61}-1$ , thus our system provides $100+$ bits of security. We implement modular multiplications on $\\mathbb{F}_{p}$ for $p=2^{61}-1$ with only one integer multiplication in $\\mathrm{C}{+}{+}$ (two 64-bit integers to one 128-bit integer) and some bit operations. As shown in Table I, the field arithmetic on $\\mathbb{F}_{p^{2}}$ is comparable to ${\\mathbb{F}}_{2^{64}}$ , $2\\times$ faster than $\\mathbb{F}_{2^{192}}$ and $4\\times$ faster than a 128-bit prime field. Encoding numbers in $\\mathbb{F}_{p}$ for $p=2^{61}-1$ is enough to avoid overflow for all computations used in our experiments in Section V-B. For other computations requiring larger field, one can set $p$ as $2^{89}-1,2^{107}-1$ or $2^{127}-1$ , which incurs a moderate slow down. For example, the multiplication over $\\mathbb{F}_{p^{2}}$ for $p=2^{89}-1$ is $2.7\\times$ slower than $p=2^{61}-1$ .  \n\nThis optimization can also be applied to Stark [9] and Aurora [14], which use the same LDT in [10]. Currently they run on ${\\mathbb F}_{2^{64}}$ and $\\mathbb{F}_{2^{192}}$ and their performances are reported in Section V-C. With our optimization, they can run on $\\mathbb{F}_{p^{2}}$ with similar efficiency while taking arithmetic circuits in $\\mathbb{F}_{p}$ .  \n\n# B. Performance of zkVPD  \n\nIn this section, we present the performance of our new transparent zkVPD protocol, and compare it with the existing approach based on bilinear maps. We use the open-source code of [70], which implements the zkVPD scheme presented in [72]. For our new zkVPD protocol, we implement the univariate sumcheck and the low degree test described in Section II-D. We set the repetition parameter $\\kappa$ in Lemma 4 as  \n\n<html><body><table><tr><td></td><td>128-bit prime</td><td>F264</td><td>F2192</td><td>Our field</td></tr><tr><td>+</td><td>6.29ns</td><td>2.16ns</td><td>4.75ns</td><td>1.23ns</td></tr><tr><td>×</td><td>30.2ns</td><td>7.29ns</td><td>15.8ns</td><td>8.27ns</td></tr></table></body></html>  \n\nTABLE I: Speed of basic arithmetic on different fields. The time is averaged over 100 million runs and is in nanosecond.  \n\n![](/tmp/output/100_20250326004608/images/bf3a7eb26a5a3c7d87fef1bcd247547b2915223419dca29fa255ff7946f4d79e.jpg)  \nFig. 1: Comparison of our zkVPD and the pairing-based zkVPD in [72].  \n\n33, and the rate of the RS code as 32 (i.e., $|\\mathbb{L}|=32|\\mathbb{H}|;$ . These parameters provide $100+$ bits of security, based on Theorem 1.2 and Conjecture 1.5 in [10], and are consistent with the implementation of Aurora [14]. In addition, we use the field $\\mathbb{F}_{p^{2}}$ with $p=2^{61}-1$ , which has a multiplicative subgroup of order $2^{m+1}$ . Thus $|\\mathbb{L}|$ can be as big as $2^{60}$ and the size of the witness $\\left\\vert\\mathbb{H}\\right\\vert$ is up to $2^{55}$ . We pad the size of the witness to a power of 2, which introduces an overhead of at most $2\\times$ .  \n\nFigure 1 shows the prover time, verification time and proof size of the two schemes. We fix the variable degree of the polynomial as 1 and vary the number of variables from 12 to 20. The size of the multilinear polynomial is $2^{12}$ to $2^{20}$ . As shown in the figure, the prover time of our new zkVPD scheme is $8{-}10\\times$ faster than the pairing-based one. It only takes 11.7s to generate the proof for a polynomial of size $2^{20}$ . This is because our new scheme does not use any heavy cryptographic operations, while the scheme in [72] uses modular exponentiations on the base group of a bilinear map. In terms of the asymptotic complexity, though the prover time is claimed to be linear in [72], there is a hidden factor of $\\log|\\mathbb{F}|$ because of the exponentiations. The prover complexity of our scheme is $O(n\\log n)$ , which is strictly better than $O(n\\log|\\mathbb{F}|)$ field operations. Additionally, as explained in Section V-A, our scheme is on the extension field of a Mersenne prime, while the scheme in [72] is on a 254-bit prime field with bilinear maps, the basic arithmetic of which is slower.  \n\nThe verification time of our zkVPD scheme is also comparable to that of [72]. For $n=2^{20}$ , it takes $12.4\\mathrm{ms}$ to validate the proof in our scheme, and $20.9\\mathrm{ms}$ in [72].  \n\nThe drawback of our scheme is the proof size. As shown in Figure 1(c), the proof size of our scheme is $30–40\\times$ larger than that of [72]. This is due to the opening of the commitments using Merkle tree, which is a common disadvantage of all IOPbased schemes [5], [9], [14]. The proof size of our scheme can be improved by a factor of $\\log n$ using the vector commitment scheme with constant-size proofs in [20], with a compromise on the prover time. This is left as a future work.  \n\nFinally, the scheme in [72] requires a trusted setup phase, which takes 12.6s for $n=2^{20}$ . We remove the trusted setup completely in our new scheme.  \n\n# C. Performance of Virgo  \n\nIn this section, we present the performance of our ZKP scheme, Virgo, and compare it with existing ZKP systems.  \n\nMethodology. We first compare with Libra [70], as our scheme follows the same framework and replaces the zkVPD with our new transparent one. We use the open-source implementation and the layered arithmetic circuits at [4] for all the benchmarks. The circuits are generated using [63].  \n\nWe then compare the performance of Virgo to state-of-theart transparent ZKP systems: Ligero [5], Bulletproofs [28], Hyrax [69], Stark [9] and Aurora [14]. We use the opensource implementations of Hyrax, Bulletproofs and Aurora at [2] and [3]. As the implementation of Aurora runs on $\\mathbb{F}_{2^{192}}$ , we execute the system on a random circuit with the same number of constraints. For Ligero, as the system is not opensource, we use the same number reported in [5] on computing hashes. For Stark, after communicating with the authors, we obtain numbers for proving the same number of hashes in the 3rd benchmark. The experiments were executed on a server with 512GB of DDR3 RAM (1.6GHz) and 16 cores (2 threads per core) at speed of $3.2\\mathrm{GHz}$ .  \n\nBenchmarks. We evaluate the systems on three benchmarks: matrix multiplication, image scaling and Merkle tree, which are used in [69], [70].  \n\nMatrix multiplication: $\\mathcal{P}$ proves to $\\nu$ that it knows two matrices whose product equals a public matrix. We evaluate on different dimensions from $4\\times4$ to $256\\times256$ , and the size of the circuit is $n^{3}$ .   \n• Image scaling: It computes a low-resolution image by scaling from a high-resolution image. We use the classic Lanczos re-sampling [65] method. It computes each pixel of the output as the convolution of the input with a sliding window and a kernel function defined as: $k(x)=$ $\\operatorname{sinc}(x)/\\operatorname{sinc}(a x),\\operatorname{if}-a<x<a;k(x)=0,$ otherwise, where $a$ is the scaling parameter and $\\mathrm{sinc}(x)=\\sin(x)/x$ . We evaluate by fixing the window size as $16\\times16$ and increase the image size from $112\\mathrm{x}112$ to $1072\\mathrm{x}1072$ .   \nMerkle tree: $\\mathcal{P}$ proves to $\\nu$ that it knows the value of the leaves of a Merkle tree that computes to a public root value [19]. We use SHA-256 for the hash function. We implement it with a flat circuit where each sub-computation is one instance of the hash function. The consistency of the input and output of corresponding hashes are then checked by the circuit. There are $2M-1$ SHA256 invocations for a Merkle tree with $M$ leaves. We increase the number of leaves from 16 to 256. The circuit size of each SHA256 is roughly $2^{19}$ gates and the size of the largest Merkle tree instance is around $2^{26}$ gates.  \n\nComparing to Libra. Figure 2 shows the prover time, verification time and proof size of our ZKP system, Virgo, and compares it with Libra. The prover time of Virgo is $7-10\\times$ faster than Libra on the first two benchmarks, and $3{-}5\\times$ faster on the third benchmark. The speedup comes from our new efficient zkVPD. As shown in Section V-B, the prover time of our zkVPD is already an order of magnitude faster. Moreover, the GKR protocol for the whole arithmetic circuit must operate on the same field of the zkVPD. In Libra, it runs on a 254-bit prime field matching the base group of the bilinear map, though the GKR protocol itself is information-theoretic secure and can execute on smaller fields. This overhead is eliminated in Virgo, and both zkVPD and GKR run on our efficient extension field of Mersenne prime, resulting in an order of magnitude speedup for the whole scheme. It only takes 53.40s to generate the proof for a circuit of $2^{26}$ gates. Our improvement on the third benchmark is slightly less, as most input and values in the circuit are binary for SHA-256, which is more friendly to exponentiation used in Libra.  \n\n![](/tmp/output/100_20250326004608/images/a3a84dd951b093754c2a1c57cd0934e0821a3f97c51488e289fe9684eb670e77.jpg)  \nFig. 2: Comparisons of prover time, proof size and verification time between Virgo and existing ZKP systems.  \n\nThe verification time of Virgo is also significantly improved upon Libra, leading to a speedup of $10–30\\times$ in the benchmarks. This is because in Libra, the verification time of the zkVPD for the input layer is similar to that for the masking polynomials in each layer, both taking ${\\cal O}(\\log C)$ bilinear pairings. Thus the overall verification time is roughly $D$ times one instance of zkVPD verification. This is not the case in Virgo. As explained in the optimization in Section IV-B, we combine all the evaluations into one inner product through random linear combinations. Therefore, the verification time in Virgo is only around twice of the zkVPD verification time, ranging from 7ms to $50\\mathrm{ms}$ in all the benchmarks.  \n\nBecause of the zkVPD, the proof size of Virgo is larger than Libra. For example, Virgo generates a proof of 253KB for Merkle tree with 256 leaves, while the proof size of Libra is only 90KB. However, the gap is not as big as the zkVPD schemes themselves in Section V-B, as the proof size of Libra is dominated by the GKR protocol of the circuit, which is actually improved by $2\\times$ in Virgo because of the smaller field. Finally, Libra requires a one-time trusted setup for the pairingbased zkVPD, while Virgo is transparent.  \n\nComparing to other transparent ZKP Systems. Table II and Figure 2 show the comparison between Virgo and state-of-theart transparent ZKP systems. As shown in Figure 2, Virgo is the best among all systems in terms of practical prover time, which is faster than others by at least an order of magnitude. The verification time of Virgo is also one of the best thanks to the succinctness of our scheme. It only takes 50ms to verify the proof of constructing a Merkle tree with 256 leaves, a circuit of size $2^{26}$ gates. The verification time is competitive to Stark, and faster than all other systems by 2 orders of magnitude. The proof size of Virgo is also competitive to other systems. It is larger than Bulletproofs [28] and is similar to Hyrax, Stark and Aurora.  \n\nIn particular, our scheme builds on the univariate sumcheck proposed in [14]. Compared to the system Aurora, Virgo significantly improves the prover time due to our efficient field and the fact that the univariate sumcheck is only on the witness, but not on the whole circuit. For the computation in Figure 2, the witness size is $16\\times$ smaller than the circuit size. E.g., the witness size for one hash is around $2^{14}$ while the circuit size is $2^{18}$ . In the largest instance in the figure, the witness size is $2^{22}$ while the circuit size is $2^{26}$ . The verification time is also much faster as we reduce the complexity from linear to logarithmic. The proof size is similar to Aurora. Essentially the proof size is the same as that in Aurora on the same number of constraint as the witness size, plus the size of the GKR proofs in the zkVPD and for the whole circuit.  \n\nTABLE II: Performance of transparent ZKP systems. $C$ is the size of the regular circuit with depth $D$ , and $n$ is witness size.   \n\n\n<html><body><table><tr><td></td><td>Ligero [5]</td><td>Bulletproofs [28]</td><td>Hyrax° [69]</td><td>Stark [9]</td><td>Aurora [14]</td><td>Virgo</td></tr><tr><td>P time</td><td>O(Clog C)</td><td>O(C)</td><td>O(C log C)</td><td>O(C log² C)</td><td>O(Clog C)</td><td>O(C+nlog n)</td></tr><tr><td>V time</td><td>O(C)</td><td>O(C)</td><td>O(DlogC+√n）</td><td>O(log² C)</td><td>O(C)</td><td>O(DlogC+log² n)</td></tr><tr><td>Proof size</td><td>0(VC)</td><td>O(log C)</td><td>O(D log C + √n)</td><td>O(log² C)</td><td>O(log² C)</td><td>O(D log C+ log² n)</td></tr></table></body></html>  \n\n# VI. APPLICATIONS  \n\nIn this section, we discuss several applications of our new zkVPD and ZKP schemes.  \n\n# A. Verifiable Secret Sharing  \n\nVerifiable polynomial delegations (or polynomial commitments) are widely used in secret sharing to achieve malicious security. In Shamir’s secret sharing [62], the secret is embedded as the constant term of a univariate polynomial $f(x)$ , and the shares hold by party $i$ is the evaluation of the polynomial $f(i)$ . To update the shares, in proactive secret sharing [47], each party generates a random polynomial $\\delta(x)$ with constant term 0, and sends the evaluation of the polynomial $\\delta(i)$ to party $i$ . To prevent adversaries from changing the secret or sending inconsistent shares, the random polynomial is committed using a polynomial commitment scheme together with a proof that $\\delta(0)=0$ , and each evaluation to party $i$ comes with a proof of the polynomial evaluation. Similar mechanism is used in mobile secret sharing [61] to change the parties.  \n\nExisting schemes mainly apply the VPD scheme in [50], which requires a trusted setup phase to generate the structured reference string. In addition, the computation time to generate the proofs are high because of the use of modular exponentiation. For example, in a recent paper, Maram et al. [56] proposed a mobile and proactive secret sharing scheme on blockchain. As it is using the pairing-based VPD, the SRS has to be generated by a trusted party, posted on the blockchain while the trapdoor must be destroyed after the setup. Moreover, as shown in [56, Figure 5], it takes 185s for each party to generate the proofs of the polynomial evaluations in each phase of the scheme for a committee of 1000 parties, which is the bottleneck of the system.  \n\nUsing our new VPD scheme in Protocol 2, we can completely remove the trusted setup phase of these secret sharing schemes for the first time, while maintaining the succinct proof size and verification time. Additionally, the proof generation time is significantly improved. Based on Figure 1, it will take around 11s to generate the proofs for 1000 parties. The proof size will definitely increase. However, as the proofs are sent offline among the parties in [56], the overall throughput will be improved by at least an order of magnitude with reasonable bandwidth between parties.  \n\nB. Privacy on Blockchain  \n\nZero knowledge proof is widely used in blockchain systems to provide privacy for cryptocurrencies (e.g., Zcash [11]), smart contracts (e.g., Hawk [52]) and zero knowledge contingent payment [29]. As mentioned in the introduction, the most commonly deployed ZKP scheme, SNARK [16], requires a trusted setup phase. A trusted party is usually absent in the setting of blockchains and an expensive “ceremony” [13] among multiple parties is usually deployed to generate the SRS. To address this issue, there are recent attempts to use transparent ZKP schemes. For example, in [25], Buinz et at. proposed Zether, which uses a variant of Bulletproofs [28] to hide account balances and provide confidentiality for applications such as auction. However, due to the high prover time and verification time of Bulletproofs for general computations, providing full anonimity still remain impractical.  \n\nAs shown in Section V-C, among all transparent ZKP schemes, Virgo achieves the best prover time and one of the best verification time, which are critical for applications of ZKP on blockchains. Compared to existing GKR-based ZKP scheme, Virgo removes the trusted setup of Libra [70], and improves the verification time of both Libra and Hyrax [69] by 1-2 orders of magnitude. These make Virgo a good candidate to build privacy-preserving cryptocurrencies and smart contract without trusted setup. The overhead on the proof size is comparable to schemes based on IOPs, which is acceptable in scenarios such as permissioned blockchain and can be potentially reduced through proof composition [17].  \n\n# C. Large Scale Zero Knowledge Proof  \n\nOther than blockchain, there are many other applications of ZKP that require proving large statements. For example, defense advanced research project agency (DARPA) recently intended to use ZKP to prove the behavior of complicated programs without leaking sensitive information [1]. Such applications require scaling ZKP schemes to circuits with billions of gates. The obstacles in all existing ZKP schemes are the high overhead of running time and memory consumption on the prover. In our new scheme, we completely removes the operations of modular exponentiation in Hyrax [69] and Libra [70], which is the bottleneck of both the prover time and memory usage. Our implementation, Virgo, is purely based on symmetric-key operations, which are fast and memory friendly. As shown in the experiments, Virgo is promising to scale to large circuits and enable applications such as proving program behavior on secret data or states.  \n\n# ACKNOWLEDGMENTS  \n\nThis material is in part based upon work supported by the National Science Foundation(NSF) under Grant No. TWC1518899, DARPA under Grant No. N66001-15-C-4066 and Center for Long-Term Cybersecurity (CLTC). Any opinions, findings, and conclusions or recommendations expressed in this material are those of the author(s) and do not necessarily reflect the views of NSF, DARPA or CLTC.  \n\n[1] Darpa sieve program. https://www.darpa.mil/news-events/2019-07-18 [2] Hyrax reference implementation. https://github.com/hyraxZK/hyraxZK [3] libiop. https://github.com/scipr-lab/libiop [4] Libra implementation. https://github.com/sunblaze-ucb/fastZKP/tree/ Libra [5] Ames, S., Hazay, C., Ishai, Y., Venkitasubramaniam, M.: Ligero: Lightweight sublinear arguments without a trusted setup. In: Proceedings of the ACM SIGSAC Conference on Computer and Communications Security (2017) [6] Backes, M., Kate, A., Patra, A.: Computational verifiable secret sharing revisited. In: International Conference on the Theory and Application of Cryptology and Information Security. pp. 590–609. Springer (2011) [7] Baum, C., Bootle, J., Cerulli, A., Del Pino, R., Groth, J., Lyubashevsky, V.: Sub-linear lattice-based zero-knowledge arguments for arithmetic circuits. In: Annual International Cryptology Conference. pp. 669–699. Springer (2018) [8] Bayer, S., Groth, J.: Efficient zero-knowledge argument for correctness of a shuffle. In: Annual International Conference on the Theory and Applications of Cryptographic Techniques. pp. 263–280. Springer (2012) [9] Ben-Sasson, E., Bentov, I., Horesh, Y., Riabzev, M.: Scalable, transparent, and post-quantum secure computational integrity. Cryptology ePrint,   \n2018 [10] Ben-Sasson, E., Bentov, I., Horesh, Y., Riabzev, M.: Fast reed-solomon interactive oracle proofs of proximity. In: 45th International Colloquium on Automata, Languages, and Programming (ICALP 2018). Schloss Dagstuhl-Leibniz-Zentrum fuer Informatik (2018) [11] Ben-Sasson, E., Chiesa, A., Garman, C., Green, M., Miers, I., Tromer, E., Virza, M.: Zerocash: Decentralized anonymous payments from bitcoin. In: Proceedings of the Symposium on Security and Privacy SP,   \n2014 (2014) [12] Ben-Sasson, E., Chiesa, A., Genkin, D., Tromer, E., Virza, M.: SNARKs for C: Verifying program executions succinctly and in zero knowledge. In: CRYPTO 2013 [13] Ben-Sasson, E., Chiesa, A., Green, M., Tromer, E., Virza, M.: Secure sampling of public parameters for succinct zero knowledge proofs. In:   \n2015 IEEE Symposium on Security and Privacy. pp. 287–304. IEEE (2015) [14] Ben-Sasson, E., Chiesa, A., Riabzev, M., Spooner, N., Virza, M., Ward, N.P.: Aurora: Transparent Succinct Arguments for R1CS. Cryptology ePrint, 2018 [15] Ben-Sasson, E., Chiesa, A., Spooner, N.: Interactive oracle proofs. In: Theory of Cryptography Conference. pp. 31–60. Springer (2016) [16] Ben-Sasson, E., Chiesa, A., Tromer, E., Virza, M.: Succinct NonInteractive Zero Knowledge for a von Neumann Architecture. In: Proceedings of the USENIX Security Symposium, 2014 [17] Ben-Sasson, E., Chiesa, A., Tromer, E., Virza, M.: Scalable zero knowledge via cycles of elliptic curves. In: CRYPTO 2014, pp. 276–294 (2014) [18] Benabbas, S., Gennaro, R., Vahlis, Y.: Verifiable delegation of computation over large datasets. In: CRYPTO 2011. pp. 111–131 [19] Blum, M., Evans, W., Gemmell, P., Kannan, S., Naor, M.: Checking the correctness of memories. Algorithmica 12(2-3), 225–244 (1994) [20] Boneh, D., Bunz, B., Fisch, B.: Batching techniques for accumulators with applications to iops and stateless blockchains. Tech. rep., Cryptology ePrint Archive, Report 2018/1188, Tech. Rep (2018) [21] Bootle, J., Cerulli, A., Chaidos, P., Groth, J., Petit, C.: Efficient zeroknowledge arguments for arithmetic circuits in the discrete log setting. In: International Conference on the Theory and Applications of Cryptographic Techniques (2016) [22] Bootle, J., Cerulli, A., Ghadafi, E., Groth, J., Hajiabadi, M., Jakobsen, S.K.: Linear-time zero-knowledge proofs for arithmetic circuit satisfiability. In: International Conference on the Theory and Application of Cryptology and Information Security. pp. 336–365. Springer (2017) [23] Bowe, S., Chiesa, A., Green, M., Miers, I., Mishra, P., Wu, H.: Zexe: Enabling decentralized private computation. Cryptology ePrint Archive, Report 2018/962 (2018), https://eprint.iacr.org/2018/962 [24] Braun, B., Feldman, A.J., Ren, Z., Setty, S.T.V., Blumberg, A.J., Walfish, M.: Verifying computations with state. In: ACM SIGOPS 24th Symposium on Operating Systems Principles, SOSP, 2013 [25]Bin,.Agrawal SZamaniMBoneh,D:Zeher:Towards rivay in a smart contract world. IACR Cryptology ePrint Archive 2019, 191 (2019)   \n[26] Bunz, B., Fisch, B., Szepieniec, A: Transparent snarks from dark compilers. Cryptology ePrint Archive, Report 2019/1229 (2019), https: //eprint.iacr.org/2019/1229   \n[27] Byott, N.P., Chapman, R.J.: Power sums over finite subspaces of a field. Finite Fields and Their Applications 5(3), 254–265 (1999)   \n[28] Bunz, B., Bootle, J., Boneh, D., Poelstra, A., Wuille, P., Maxwell, G.: Bulletproofs: Short proofs for confidential transactions and more. In: Proceedings of the Symposium on Security and Privacy (SP), 2018. vol. 00, pp. 319–338   \n[29] Campanelli, M., Gennaro, R., Goldfeder, S., Nizzardo, L.: Zeroknowledge contingent payments revisited: Attacks and payments for services. In: Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security. pp. 229–243. ACM (2017)   \n[30] Canetti, R., Chen, Y., Holmgren, J., Lombardi, A., Rothblum, G.N., Rothblum, R.D.: Fiat-shamir from simpler assumptions. Cryptology ePrint Archive, Report 2018/1004 (2018)   \n[31] Chase, M., Derler, D., Goldfeder, S., Orlandi, C., Ramacher, S., Rechberger, C., Slamanig, D., Zaverucha, G.: Post-quantum zero-knowledge and signatures from symmetric-key primitives. In: Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security. pp. 1825–1842. ACM (2017)   \n[32] Chiesa, A., Forbes, M.A., Spooner, N.: A Zero Knowledge Sumcheck and its Applications. CoRR abs/1704.02086 (2017), http://arxiv.org/abs/ 1704.02086   \n[33] Cormen, T.H., Leiserson, C.E., Rivest, R.L., Stein, C.: Introduction to Algorithms, Third Edition. The MIT Press, 3rd edn. (2009)   \n[34] Cormode, G., Mitzenmacher, M., Thaler, J.: Practical Verified Computation with Streaming Interactive Proofs. In: Proceedings of the 3rd Innovations in Theoretical Computer Science Conference. ITCS ’12   \n[35] Costello, C., Fournet, C., Howell, J., Kohlweiss, M., Kreuter, B., Naehrig, M., Parno, B., Zahur, S.: Geppetto: Versatile verifiable computation. In: S&P 2015   \n[36]  Cramer, R, Damgard, I.: Zero-knowledge proofs for fnite field arithmetic, or: Can zero-knowledge be for free? In: Annual International Cryptology Conference, 1998   \n[37] Fiat, A., Shamir, A.: How to prove yourself: Practical solutions to identification and signature problems. In: Crypto 1986   \n[38] Fiore, D., Fournet, C., Ghosh, E., Kohlweiss, M., Ohrimenko, O., Parno, B.: Hash first, argue later: Adaptive verifiable computations on outsourced data. In: Proceedings of the ACM SIGSAC Conference on Computer and Communications Security (2016)   \n[39] Fiore, D., Gennaro, R.: Publicly verifiable delegation of large polynomials and matrix computations, with applications. In: CCS 2012. pp. 501–512   \n[40] Gennaro, R., Gentry, C., Parno, B., Raykova, M.: Quadratic span programs and succinct NIZKs without PCPs. In: EUROCRYPT 2013. pp. 626–645 (2013)   \n[41] Giacomelli, I., Madsen, J., Orlandi, C.: Zkboo: Faster zero-knowledge for boolean circuits. In: USENIX Security Symposium. pp. 1069–1083 (2016)   \n[42] Goldwasser, S., Kalai, Y.T., Rothblum, G.N.: Delegating Computation: Interactive Proofs for Muggles. J. ACM 62(4), 27:1–27:64 (Sep 2015)   \n[43] Goldwasser, S., Micali, S., Rackoff, C.: The knowledge complexity of interactive proof systems. SIAM Journal on computing 18(1), 186–208 (1989)   \n[44] Groth, J.: Linear algebra with sub-linear zero-knowledge arguments. In: Advances in Cryptology-CRYPTO 2009, pp. 192–208. Springer (2009)   \n[45] Groth, J.: Short pairing-based non-interactive zero-knowledge arguments. In: International Conference on the Theory and Application of Cryptology and Information Security. pp. 321–340. Springer (2010)   \n[46] Groth, J., Kohlweiss, M., Maller, M., Meiklejohn, S., Miers, I.: Updatable and universal common reference strings with applications to zksnarks. In: Annual International Cryptology Conference. pp. 698–728. Springer (2018)   \n[47] Herzberg, A., Jarecki, S., Krawczyk, H., Yung, M.: Proactive secret sharing or: How to cope with perpetual leakage. In: Annual International Cryptology Conference. pp. 339–352. Springer (1995)   \n[48] Ishai, Y., Kushilevitz, E., Ostrovsky, R.: Efficient arguments without short pcps. In: 22nd Annual IEEE Conference on Computational Complexity (CCC 2007)   \n[49] Ishai, Y., Kushilevitz, E., Ostrovsky, R., Sahai, A.: Zero-knowledge from secure multiparty computation. In: Proceedings of the annual ACM symposium on Theory of computing. pp. 21–30. ACM (2007) [50] Kate, A., Zaverucha, G.M., Goldberg, I.: Constant-size commitments to polynomials and their applications. In: ASIACRYPT 2010. pp. 177–194 [51] Kilian, J.: A note on efficient zero-knowledge proofs and arguments (extended abstract). In: Proceedings of the ACM Symposium on Theory of Computing (1992) [52] Kosba, A., Miller, A., Shi, E., Wen, Z., Papamanthou, C.: Hawk: The blockchain model of cryptography and privacy-preserving smart contracts. In: Proceedings of Symposium on security and privacy (SP),   \n2016 [53] Lipmaa, H.: Progression-free sets and sublinear pairing-based noninteractive zero-knowledge arguments. In: Theory of Cryptography Conference (2012) [54] Lund, C., Fortnow, L., Karloff, H., Nisan, N.: Algebraic Methods for Interactive Proof Systems. J. ACM 39(4), 859–868 (Oct 1992) [55] Maller, M., Bowe, S., Kohlweiss, M., Meiklejohn, S.: Sonic: Zeroknowledge snarks from linear-size universal and updateable structured reference strings. Cryptology ePrint Archive, Report 2019/099 (2019), https://eprint.iacr.org/2019/099 [56] Maram, S.K.D., Zhang, F., Wang, L., Low, A., Zhang, Y., Juels, A., Song, D.: Churp: Dynamic-committee proactive secret sharing. Cryptology ePrint Archive, Report 2019/017 (2019), https://eprint.iacr.org/2019/   \n017 [57] Merkle, R.C.: A certified digital signature. In: CRYPTO 1989. pp. 218–   \n238 [58] Micali, S.: Computationally sound proofs. SIAM J. Comput. (2000) [59] Papamanthou, C., Shi, E., Tamassia, R.: Signatures of correct computation. In: TCC 2013. pp. 222–242 (2013) [60] Parno, B., Howell, J., Gentry, C., Raykova, M.: Pinocchio: Nearly practical verifiable computation. In: S&P 2013. pp. 238–252 (2013) [61] Schultz, D.A., Liskov, B., Liskov, M.: Mobile proactive secret sharing. In: Proceedings of the twenty-seventh ACM symposium on Principles of distributed computing. pp. 458–458. ACM (2008) [62] Shamir, A.: How to share a secret. Communications of the ACM 22(11),   \n612–613 (1979) [63] Tange, O.: Gnu parallel - the command-line power tool. The USENIX Magazine 36(1), 42–47 (Feb 2011), http://www.gnu.org/s/parallel [64] Thaler, J.: Time-Optimal Interactive Proofs for Circuit Evaluation. In: Canetti, R., Garay, J.A. (eds.) Advances in Cryptology – CRYPTO 2013 (2013) [65] Turkowski, K.: Filters for common resampling tasks. In: Graphics gems. pp. 147–165. Academic Press Professional, Inc. (1990) [66] Valiant, P.: Incrementally verifiable computation or proofs of knowledge imply time/space efficiency. In: Theory of Cryptography Conference. pp.   \n1–18. Springer (2008) [67] Wahby, R.S., Ji, Y., Blumberg, A.J., Shelat, A., Thaler, J., Walfish, M., Wies, T.: Full accounting for verifiable outsourcing. In: Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security. ACM (2017) [68] Wahby, R.S., Setty, S.T., Ren, Z., Blumberg, A.J., Walfish, M.: Efficient ram and control flow in verifiable outsourced computation. In: NDSS (2015) [69] Wahby, R.S., Tzialla, I., Shelat, A., Thaler, J., Walfish, M.: Doublyefficient zkSNARKs without trusted setup. In: 2018 IEEE Symposium on Security and Privacy (SP). pp. 926–943. IEEE (2018) [70] Xie, T., Zhang, J., Zhang, Y., Papamanthou, C., Song, D.: Libra: Succinct zero-knowledge proofs with optimal prover computation. In: Advances in Cryptology (CRYPTO) (2019) [71] Yuan, J., Yu, S.: Proofs of retrievability with public verifiability and constant communication cost in cloud. In: Proceedings of the 2013 international workshop on Security in cloud computing. pp. 19–26. ACM (2013) [72] Zhang, Y., Genkin, D., Katz, J., Papadopoulos, D., Papamanthou, C.: A Zero-Knowledge version of vSQL. Cryptology ePrint, 2017 [73] Zhang, Y., Genkin, D., Katz, J., Papadopoulos, D., Papamanthou, C.: vSQL: Verifying arbitrary SQL queries over dynamic outsourced databases. In: IEEE Symposium on Security and Privacy (S&P) 2017 (2017) [74] Zhang, Y., Genkin, D., Katz, J., Papadopoulos, D., Papamanthou, C.: vSQL: Verifying arbitrary SQL queries over dynamic outsourced databases. In: Security and Privacy (SP), 2017 IEEE Symposium on. pp. 863–880. IEEE (2017) [75] Zhang, Y., Genkin, D., Katz, J., Papadopoulos, D., Papamanthou, C.: vRAM: Faster verifiable RAM with program-independent preprocessing.  \n\nIn: Proceeding of IEEE Symposium on Security and Privacy (S&P) (2018)  \n\n# APPENDIX A  \n\n# REED-SOLOMON CODE  \n\nLet $\\mathbb{L}$ be a subset of $\\mathbb{F}$ , an RS code is the evaluations of a polynomial $\\rho(x)$ of degree less than $m$ $m<\\mathbb{L})$ on $\\mathbb{L}$ . We use the notation $\\rho|_{\\mathbb{L}}$ to denote the vector of the evaluations $(\\rho(a))_{a\\in\\mathbb{L}}$ , and use $R S[\\mathbb{L},m]$ to denote the set of all such vectors generated by polynomials of degree less than $m$ . Note that any vector of size $|\\mathbb{L}|$ can be viewed as some univariate polynomial of degree less than $|\\mathbb{L}|$ evaluated on $\\mathbb{L}$ , thus we use vector and polynomial interchangeably.  \n\n# APPENDIX B  \n\n# UNIVARIATE SUMCHECK PROTOCOL  \n\nThe protocol of the univariate sumcheck in [14] is in Protocol 4. We have the following lemma:  \n\nLemma 5. Let $f:\\mathbb{F}\\rightarrow\\mathbb{F}$ be a univariate poynomial with degree less than $k$ and $\\mathbb{H}\\subseteq\\mathbb{L}\\subseteq\\mathbb{F}$ and $|\\mathbb{L}|>k$ . Protocol 4 is an interactive proof to prove $\\begin{array}{r}{\\mu=\\sum_{a\\in\\mathbb{H}}f(a)}\\end{array}$ with soundness $O(\\frac{\\mathbb{L}}{\\mathbb{F}}+\\mathsf{n e g l}(\\kappa))$ . The proof size a nd the verification time are $O(\\mathrm{{\\bar{log}}^{2}\\left|\\mathbb{L}\\right|})$ and the prover time is $O(|\\mathbb{L}|\\log|\\mathbb{L}|)$ .  \n\n$$\n\\begin{array}{c}{\\mathsf{A P P E N D I X C}}\\ {\\mathsf{P R O O F O F T H E O R E M1}}\\end{array}\n$$  \n\nProof. Completeness. By the definition of $l(x)$ and $q(x)$ , if   \n$\\mu=f(t)$ , then $\\begin{array}{r}{\\mu=\\sum_{a\\in H}l(a)\\cdot q(a)=\\sum_{a\\in H}g(a)=g(0).}\\end{array}$   \n$\\left\\vert\\mathbb{H}\\right\\vert$ by Lemma 3. T hu\bs, $\\begin{array}{r}{\\bar{p}(x)=\\frac{|\\mathbb{H}|\\cdot l(x)\\cdot q(x)-|\\mathbb{H}|\\cdot\\mathbb{Z}_{\\mathbb{H}}(x)h(x)-\\mu}{|\\mathbb{H}|\\cdot x}=}\\end{array}$   \ng(x)x−g(0) , which is in RS[L, |H| − 1]. The rest follows the   \ncompleteness of the LDT protocol and the GKR protocol.   \nSoundness. Let $\\varepsilon_{\\mathsf{L D T}},\\varepsilon_{\\mathsf{M T}},\\varepsilon_{\\mathsf{G K R}}$ be the soundness error of the   \nLDT, Merkle tree and GKR protocols. There are two cases for   \na malicious prover $\\mathcal{P}$ .   \nCase 1: $\\begin{array}{r l r}{\\nexists l^{*}}&{{}\\in}&{R S[\\mathbb{L},|\\mathbb{H}|+1]}\\end{array}$ such that $\\begin{array}{r l}{\\mathsf{c o m}}&{{}=}\\end{array}$   \nMT.Commit $(l^{*}|_{\\mathbb{L}})$ , i.e. com is not a valid commitment.   \n• By the check in step 6, if com is not a valid Merkle tree root, the verification passes with probability less than $\\varepsilon_{\\mathsf{M T}}$ .   \n• If $\\exists l^{**}\\:\\:\\:\\:\\:\\not\\in\\:\\:\\:\\:R S[\\mathbb{L},|\\mathbb{H}|\\:\\:+\\:\\:1]$ such that com MT.Commit $\\left(l^{**}|_{\\mathbb{L}}\\right)$ , if the points $v_{i}^{*}$ opened by $\\mathcal{P}$ in step 5 $v_{i}^{*}~\\neq~l^{**}(a_{i})$ for some $i$ , the verification passes with probability no more than $\\varepsilon_{\\mathsf{M T}}$ .   \n• If the output $q_{i}^{*}$ returned by $\\mathcal{P}$ in step 7 is $q_{i}^{*}\\neq q(a_{i})$ for some $i$ , the verification passes with probability less than $\\varepsilon_{\\mathsf{G K R}}$ .   \n• Otherwise, as $l^{**}(x)\\cdot q(x)\\not\\in R S[\\mathbb{L},2|\\mathbb{H}|+1]$ , by the checks of LDT in step 4, the verification passes with probability no more than εLDT.   \nCase 2: ∃l∗ ∈ $R S[\\mathbb{L},|\\mathbb{H}|+1]$ such that com $=$ $\\mathcal{A}\\mathsf{T.C o m m i t}(l^{\\ast}|_{\\mathbb{L}})$ . Let $\\begin{array}{r l r}{c^{*}}&{{}=}&{l^{*}|_{\\mathbb{H}}}\\end{array}$ and $\\begin{array}{r l}{f^{*}(x)}&{{}=}\\end{array}$   \n$\\textstyle\\sum_{i=1}^{N}c_{i}^{*}W_{i}({\\dot{x}})$ ,| then $\\mathsf{c o m}=\\mathsf{C o m m i t}(f^{*},\\mathsf{p p})$ . Suppose $\\mu^{*}\\neq$   \n$f^{*}(t)$ , then $\\begin{array}{r}{\\mu^{*}\\neq\\sum_{a\\in\\mathbb{H}}l^{*}(a)q(a)}\\end{array}$ . Then by Lemma 3, for all   \n$h\\in R S[\\mathbb{L},|\\mathbb{H}|+1]$ , $p^{*}\\notin R S[\\mathbb{L},|\\mathbb{H}|-1]$ , as $\\begin{array}{r}{\\sum_{a\\in\\mathbb{H}}(p^{*}(a)\\cdot a)=}\\end{array}$   \na H |H|·l∗(a)H·q(a)−μ∗ = a H(l∗(a) ·  q(a)) − μ∗ ̸= 0.   \nTherefore,  \n\nSimilar to case 1, if the commitment in step 3 is not a valid Merkle tree root, or the points opened by $\\mathcal{P}$ in step 5 are inconsistent with $h$ or $l^{*}$ , the verification passes with probability no more than $\\varepsilon_{\\mathsf{M T}}$ .  \n\n• If the output $q_{i}^{*}$ returned by $\\mathcal{P}$ in step 7 $q_{i}^{*}\\neq q(a_{i})$ for some $i$ , the verification passes with probability no more than $\\varepsilon_{\\mathsf{G K R}}$ . Otherwise, as $l^{*}\\cdot q\\in R S[\\mathbb{L},2|\\mathbb{H}|+1]$ , either $h\\not\\in$ $R S[\\mathbb{L},|\\mathbb{H}|+1]$ or $p\\notin R S[\\mathbb{L},|\\mathbb{H}|-1]$ as explained above. By the check in step 4, the verification passes with probability no more than $\\varepsilon_{\\mathsf{L D T}}$ .  \n\nBy the union bound, the probability of the event of a malicious prover is no more than $O(\\varepsilon_{\\mathsf{L D T}}+\\varepsilon_{\\mathsf{M T}}+\\varepsilon_{\\mathsf{G K R}})$ . As stated in Section II, $\\begin{array}{r}{\\varepsilon_{\\mathsf{L D T}}=O\\big(\\frac{|\\mathbb{L}|}{|\\mathbb{F}|}\\big)+\\mathsf{n e g l}\\big(\\kappa\\big),\\varepsilon_{\\mathsf{G K R}}=O\\big(\\frac{\\log^{2}N}{|\\mathbb{F}|}\\big)}\\end{array}$ and $\\varepsilon_{\\mathsf{M T}}=\\mathsf{n e g l}(\\lambda)$ . Therefore, with proper choice of parameters, the probability is $\\leq{\\mathsf{n e g l}}(\\lambda)$ .  \n\nAPPENDIX D PROOF OF THEOREM 2  \n\nProof. Completeness. It follows the completeness of Protocol 2.  \n\nSoundness. It follows the soundness of Protocol 2 and the random linear combination. In particular, in Case 2 of the proof of Theorem 1, if $\\exists l^{\\prime*}\\in R S[\\mathbb{L},|\\mathbb{H}|+\\kappa+1]$ , it can always be uniquely decomposed as $l^{*}(x)=l^{\\prime*}(x)-Z_{\\mathbb{H}}(x)r^{*}(x)$ such that $\\begin{array}{r}{\\sum_{a\\in\\mathbb{H}}l^{\\prime*}(a)=\\sum_{a\\in\\mathbb{H}}l^{*}(a)}\\end{array}$ and the degree of $l^{*}(x)$ is $\\big|\\mathbb{H}\\big|$ and  the degree of $r(x)$ is $\\kappa$ . If $\\begin{array}{r}{\\mu^{*}\\neq\\mu=\\sum_{a\\in\\mathbb{H}}(l^{*}(a)\\cdot q(a))=}\\end{array}$ $\\textstyle\\sum_{a\\in\\mathbb{H}}(l^{\\prime*}(a)\\cdot q(a))$ , let $\\begin{array}{r}{S^{*}=\\sum_{a\\in\\mathbb{H}}s^{*}(a)^{-}}\\end{array}$ where $s^{*}(x)$ is committed by $\\mathcal{P}$ in step 5, then $\\begin{array}{r}{\\sum_{a\\in\\mathbb{H}}(\\alpha l^{\\prime*}(a)\\cdot q(a)+s^{*}(a))=}\\end{array}$ αμ∗ + S∗ = αμ + S if and onl y if α = Sμ∗−Sμ∗ , which happens with probability $1/|\\mathbb{F}|$ . The probability of other cases are the same as the proof of Theorem 1, and we omit the details here.  \n\nZero knowledge. The simulator is given in Figure 4.  \n\nTo prove zero knowledge, $l_{\\mathrm{sim}}^{\\prime}$ in $S_{1}$ and $l^{\\prime}$ in zkVPD.Commit are both uniformly distributed. In $S_{2}$ , steps 1, 2 and 9 are the same as the real world in Protocol 3. No message is sent in steps 4, 8 and 10.  \n\nIn step 3 and 7, $s_{\\mathsf{s i m}}$ and $s$ are both randomly selected and their commitments and evaluations are indistinguishable. As $r(x)$ is a degree- $\\kappa$ random polynomial in the real world in Protocol 3, $\\kappa$ evaluations of $l^{\\prime}(x)$ opened in step 7 are independent and randomly distributed, which is indistinguishable from step 7 of $S_{2}$ in the ideal world. Finally, in step 7 of the ideal world, $\\nu^{*}$ receives $\\kappa$ evaluations of $h_{\\mathsf{s i m}}$ at point indexed by $\\mathcal{T}$ . Together with $l_{\\mathrm{sim}}^{\\prime}\\cdot q$ and $s_{\\mathsf{s i m}}$ , by Lemma 4, the view of steps 5-7 simulated by LDT. $\\boldsymbol{S}$ is indistinguishable from the real world with $h,l^{\\prime}\\cdot q$ and $s$ , which completes the proof.  \n\nOur zkVPD protocol is also a proof of knowledge. Here we give the formal definition of knowledge soundness of a zkVPD protocol in addition to Definition 3 and prove that our protocol has knowledge soundness.  \n\nKnowledge Soundness. For any PPT adversary $\\mathcal{A}$ , there exists a PPT extractor $\\mathcal{E}$ such that given access to the random tape  \n\nProtocol 4 (Univariate Sumcheck). Let $f$ be a degree $k$ univariate   \npolynomial on $\\mathbb{F}$ with degree less than $k$ and $\\mathbb{H},\\mathbb{L}$ be a multiplicative   \ncoset of $\\mathbb{F}$ such that $\\mathbb{H}\\subset\\mathbb{L}\\subset\\mathbb{F}$ and $|\\mathbb{L}|>k$ . To prove   \n$\\textstyle\\mu=\\sum_{a\\in\\mathbb{H}}f(a)$ , a univariate sumcheck protocol has the following   \nalgo rithms.   \n• SC.com ${\\mathsf{S C.C o m m i t}}(f)$ : $I$ ) $\\mathcal{P}$ computes polynomial $h$ such that $f(x)=g(x)+Z_{\\mathbb{H}}(x)$ · $h(x)$ . $\\mathcal{P}$ evaluates of $f\\vert_{\\mathbb{L}}$ and $h|_{\\mathbb{L}}$ . 2) $\\mathcal{P}$ commits to the vectors using Merkle tree root $f$ MT.Commit $\\left(f|_{\\mathbb{L}}\\right)$ and $\\mathsf{r o o t}_{h}$ MT.Commit $\\left(h\\vert_{\\mathbb{L}}\\right)$ . $\\mathcal{P}$ sends $\\mathcal{V}\\mathsf{c o m}=\\left(\\mathsf{r o o t}_{f},\\mathsf{r o o t}_{h}\\right)$ .   \n• $\\langle{\\mathsf{S C}}.{\\mathsf{P r o v e}}(f),{\\mathsf{S C}}.{\\mathsf{V e r i f y}}(\\mathsf{c o m},\\mu)\\rangle$ : 1) Let p(x) = |H|·f(x)−μ−|H|·ZH(x)h(x) . 2) $\\mathcal{P}$ and invoke the low degree test: $\\langle\\mathsf{L D T}.\\mathcal{P}((f,h),p)$ , $\\mathsf{L D T.}\\mathcal{V}((k,k-\\mathsf{\\Pi}|\\mathbb{H}|),|\\mathbb{H}|-\\mathsf{\\Pi}1)\\rangle(\\mathbb{L})$ . If the test fails, $\\nu$ aborts and output 0. Otherwise, at then end of the test, $\\nu$ needs oracle access to $\\kappa$ points of $f,h$ and $p$ in L. We denote their indices as $\\mathcal{L}$ . 3) For each index $i\\in\\mathcal{I}$ , $\\mathcal{P}$ opens $\\mathsf{M T.O p e n}(i,f|_{\\mathbb{L}})$ and $\\mathsf{M T.O p e n}(i,h|\\mathbb{L})$ . 4) $\\nu$ executes MT.Verify for all points opened by $\\mathcal{P}$ . If any verification fails, abort and output 0. 5) $\\nu$ completes the low degree test with these points. If all checks above pass, $\\nu$ outputs 1.  \n\n# Input: $t=(t_{1},\\dots,t_{\\ell})$  \n\n# Output: q()  \n\n1) Computing vector $T=(W_{1}(t),\\ldots,W_{N}(t))$ : • Compute $(t_{i}^{0},t_{i}^{1},\\ldots,t_{i}^{d})$ for $i=1,\\dots,\\ell$ . • Initialize vector $T_{0}=(1)$ . • For $i=1,\\dots,\\ell$ : $T_{i}=(t_{i}^{0}\\cdot T_{i-1},...,t_{i}^{d}\\cdot T_{i-1})$ , where “ ” here is scalar multiplication between a number and a vector and “,” means concatenation. Set $T=T_{\\ell}$ .   \n2) Computing $q|_{\\mathrm{L}}$ : • $q|_{\\mathbb{L}}=\\mathsf{F F T}(\\mathsf{I F F T}(T,\\mathbb{H}),\\mathbb{L})$   \n3) Outputting evaluations indexed by $I_{q}$ :  \n\nFig. 3: Arithmetic circuit $C$ computing evaluations of $q(x)$ at $\\kappa$ points in $\\mathbb{L}$ indexed by $\\mathcal{T}$ .  \n\nof $\\mathcal{A}$ , for every $\\mathsf{p p}\\gets\\mathsf{z k V P D.K e y G e n}(1^{\\lambda})$ , the following probability is $\\mathsf{n e g}|(\\lambda)$ :  \n\n$$\n\\operatorname*{Pr}{\\left[\\begin{array}{l l}{(\\mathsf{c o m}^{*},t)}&{\\mathcal{A}(1^{\\lambda},\\mathsf{p p}),}\\ {((y^{*},\\pi^{*});1)\\leftarrow\\langle\\mathcal{A}(),\\mathsf{z k V P D.V e r i f y}(\\mathsf{c o m}^{*})\\rangle(t,\\mathsf{p p}),}\\ {(f,r_{f})\\leftarrow\\mathcal{E}(1^{\\lambda},\\mathsf{p p}):}\\ {\\mathsf{c o m}^{*}\\neq\\mathsf{z k V P D.C o m m i t}(f,r_{f},\\mathsf{p p})\\lor f(t)\\neq y^{*}}\\end{array}\\right]}\n$$  \n\nOur zkVPD protocol is a proof of knowledge in the random oracle model because of the extractability of Merkle tree, as proven in [15], [66]. Informally speaking, given the root and sufficiently many authentication paths, there exists a PPT extractor that reconstructs the leaves with high probability. Additionally, in our protocol the leaves are RS encoding of the witness, which can be efficiently decoded by the extractor. We give a proof similar to [15], [66] below.  \n\nProof. Suppose the Merkle tree in our protocol is based on a random oracle $\\mathcal{R}:\\{0,1\\}^{2\\lambda}\\rightarrow\\{0,1\\}^{\\bar{\\lambda}}$ . We could construct  \n\n• com $S_{1}(1^{\\lambda},{\\mathsf{p p}})$ : Pick a random polynomial $l_{\\mathsf{s i m}}^{\\prime}(x)\\in R S[\\mathbb{L},|\\mathbb{H}|+\\kappa+1]$ . Evaluate $l_{\\mathrm{sim}}^{\\prime}|_{\\mathbb{U}}$ and output $\\mathrm{root}_{l_{\\mathrm{sim}}^{\\prime}}$ MT.Commit(lsimlu).   \n• $S_{2}(t,{\\mathsf{p p}})$ :   \n1) Given oracle access to $\\mu=f(t)$ , send it to $\\nu^{*}$ .   \n2) Evaluate $T=(W_{1}(t),\\ldots,W_{N}(t))$ . Find the unique univariate polynomial $q(x):\\mathbb{F}\\rightarrow\\mathbb{F}$ such that $q|_{\\mathbb{H}}=T$ .   \n43))  PRieccke iav ed $2|\\mathbb{H}|+\\kappa-1$ polynomial $s_{\\mathsf{s i m}}(x)$ randomly. Send $\\begin{array}{r}{\\mathcal{V}S_{\\mathsf{s i m}}=\\sum_{a\\in\\mathbb{H}}s_{\\mathsf{s i m}}(a)}\\end{array}$ and $\\mathrm{root}_{\\mathrm{s_{im}}}$ MT.Commit $\\left(s_{\\mathsf{s i m}}|\\mathbb{u}\\right)$ . $\\alpha\\in\\mathbb{F}$ $\\nu$   \n5) Let LDT. $\\boldsymbol{\\mathscr{S}}$ be the simulator the LDT protocol described in Section II-D. Given the random challenges $\\mathcal{T}$ of $\\nu^{*}$ , call LDT. $s$ to generate p\\*(x)∈RSL,[| 1]. For each point a in I, comute h such that p\\*(a) (a）a(a)+m(@)(sm)a(a)h Interpolate $h_{i}$ to get polynomial $h_{\\mathrm{{sim}}}$ and sends $\\mathsf{r o o t}_{h_{\\mathsf{s i m}}}\\gets\\mathsf{M T.C o m m i t}((h_{\\mathsf{s i m}}|_{\\mathbb{U}})$ to $\\nu^{*}$ .   \n6) Call LDT. $s$ to simulate the view of the low degree test $\\ensuremath{\\mathrm{LDT}}.S^{\\nu^{*}}$ .   \n7) For each index $i\\in\\mathcal{T}$ , let $a_{i}$ be the corresponding point in U. $\\mathcal{P}$ opens $(l_{\\mathsf{s i m}}^{\\prime}(a_{i}),\\pi_{i}^{l_{\\mathsf{s i m}}^{\\prime}})\\qquad\\mathsf{M T.O p e n}(i,l_{\\mathsf{s i m}}^{\\prime}|_{\\mathbb{U}}),(h_{i},\\pi_{i}^{h_{\\mathsf{s i m}}})$ MT.Open $(i,h_{\\sin}|_{\\mathbb{U}})$ and $(s_{\\mathsf{s i m}}(a_{i}),\\pi_{i}^{s_{\\mathsf{s i m}}})$ $\\mathsf{M T.O p e n(\\it i,s_{s i m}|\\mathrm{u})}$ .   \n8) Wait $\\nu^{\\ast}$ to validate the points.   \n9) Run $\\langle\\mathsf{G K R.P,G K R.}\\mathcal{V}\\rangle(\\overline{{C}},t)$ with $\\nu^{*}$ , where circuit $C$ computes the evaluations of $q|_{\\mathbb{U}}$ and outputs the elements $q(a_{i})$ for $i\\in\\mathcal{T}$ .   \n10) Wait $\\nu^{\\ast}$ for validation.  \n\na polynomial extractor $\\mathcal{E}$ with the same random type of $\\mathcal{A}$ working as follows:  \n\nSimulate $\\mathcal{A}^{\\mathcal{R}}$ , and let $q_{1},q_{2},\\cdots,q_{t}$ be the queries made by $\\mathcal{A}$ to $\\mathcal{R}$ in the order they are made where duplicates omitted. Define $q_{i}\\in\\mathcal{R}(q_{j})$ if the first $\\lambda$ bits or the last $\\lambda$ bits of $q_{i}$ is $\\mathcal{R}(q_{j})$ . If there exist some $i\\neq j$ , $\\mathcal{R}(q_{i})=\\mathcal{R}(q_{j})$ , or some $i\\leq j q_{i}\\in R(q_{j}).$ , $\\mathcal{E}$ aborts and outputs a random string as $(f,r_{f})$ .  \n\n$\\mathcal{E}$ constructs an acyclic directed graph $G$ according to the query set $Q=\\{q_{1},q_{2},\\cdot\\cdot\\cdot,q_{t}\\}$ . There is an edge from $q_{i}$ to $q_{j}$ in $G$ if and only if $q_{i}\\in R(q_{j})$ . The outdegree of each node is at most 2. When $\\mathcal{A}$ generates $\\Upsilon\\mathsf{O O}\\mathsf{t}_{l^{\\prime}}$ in step 2 of Protocol 3, if rootl′ does not equal $\\mathcal{R}(q)$ for some $q\\in Q$ , $\\mathcal{E}$ aborts and outputs a random string as $(f,r_{f})$ , otherwise we suppose $\\mathcal{R}(q_{r})=\\mathsf{r o o t}_{l^{\\prime}}$ . If a verification path of $\\pi^{*}$ is not valid, $\\mathcal{E}$ aborts and outputs a random string as $(f,r_{f})$ .  \n\nSince $\\mathcal{E}$ knows the correct depth of the Merkle tree, it could read off all leaf strings with this depth from the binary tree rooted at $q_{r}$ . If there exists missing leaf, $\\mathcal{E}$ aborts and outputs a random string as $(f,r_{f})$ , otherwise, it concatenates these leaf strings as $w^{\\prime}=l^{\\prime}|_{\\mathbb{U}}$ , and decodes $w=l^{\\prime}|_{\\mathbb{H}}$ using an efficient Reed–Solomon decoding algorithm (such as Berlekamp–Welch). $\\mathcal{E}$ could easily output $(f,r_{f})$ according to $w$ .  \n\nLet $E_{1}$ denote the event $((y^{*},\\pi^{*});1)$ $\\langle A(),z\\mathsf{k V P D.V e r i f y}(\\mathsf{c o m}^{*})\\rangle(t,\\mathsf{p p})$ and $E_{2}$ denote the event $\\mathsf{c o m}^{*}\\neq\\mathsf{z k V P D.C o m m i t}(f,r_{f},\\mathsf{p p})\\lor f(t)\\neq y^{*}$ , next we show $\\mathrm{Pr}[E_{1}\\land E_{2}]\\le\\mathsf{n e g l}(\\lambda)$ .  \n\nThe probability that $\\mathcal{E}$ aborts before constructing the graph $G$ is $\\mathsf{n e g}|(\\lambda)$ because of the collision-resistant property of the random oracle. If some node on a verification path(possibly including the root) of the proof $\\pi^{*}$ does not lie in the graph $G$ , $\\mathcal{A}$ has to guess the value to construct a valid verification path, which propability is also $\\mathsf{n e g}|(\\lambda)$ since $\\mathcal{R}$ is noninvertible. Additionally, if one leaf of the tree is missing, then $\\nu$ will be convinced with probability $\\mathsf{n e g}|(\\lambda)$ once it queries this leaf. And the probability this leaf is not be queried by $\\nu$ is at most $\\begin{array}{r}{(1-\\frac{1}{|\\mathbb{U}|})^{\\tilde{\\kappa}}={\\mathsf{n e g l}}(\\lambda)}\\end{array}$ as $\\kappa=O(\\lambda)$ .  \n\nIf $\\mathcal{E}$ does not abort, it could always extract some $(f,r_{f})$ satisfying $\\mathsf{c o m m}^{*}=\\mathsf{z k V P D.C o m m i t}(f,r_{f},\\mathsf{p p}).$ . In this case, $\\nu$ accepts the statement with probability $\\mathsf{n e g}|(\\lambda)$ if $f(t)\\neq y^{*}$ according to the soundness of zkVPD.  \n\nTherefore, $\\begin{array}{l l l}{{\\operatorname*{Pr}[E_{1}\\wedge E_{2}]}}&{{=}}&{{\\operatorname*{Pr}[E_{1}\\wedge E_{2}|\\mathcal{E}\\mathrm{~aborts}]+}}\\end{array}$ $\\operatorname*{Pr}[E_{1}\\land E_{2}|\\mathcal{E}$ does not abo ${\\mathrm{rt}}]\\leq{\\mathrm{Pr}}[E_{1}|\\mathcal{E}~\\mathrm{aborts}]+{\\mathrm{Pr}}[E_{1}\\wedge$ $E_{2}|\\mathcal{E}$ does not abort] $\\leq{\\mathsf{n e g l}}(\\lambda)+{\\mathsf{n e g l}}(\\lambda)={\\mathsf{n e g l}}(\\lambda)$ 口  \n\n# APPENDIX E PROOF OF THEOREM 3  \n\nProof. Completeness. It follows the completeness of Protocol 3 and the completeness of the GKR protocol in [70].  \n\nSoundness. It follows the soundness of Protocol 3 and the soundness of the GKR protocol with masking polynomials as proven in [32], [70]. The proof of knowledge property follows the knowledge soundness of our zkVPD protocol. In particular, the witness can be extracted using the extractor presented in Appendix D. More formally speaking, our construction is an interactive oracle proof (IOP) as defined in [15]. Applying the transformation from IOP to an argument system using Merkle tree preserves the proof of knowledge property. Our underlying IOP is proof of knowledge as the proofs are RS codes and the witness can be efficiently extracted through decoding.  \n\nZero knowledge. The simulator is given in Figure 5. $\\nu^{*}$ can behave arbitrarily in Step 3, 4(b), 4(e), 4(f) and 6. We include these steps as place holders to compare to Protocol 5.  \n\nTo prove zero-knowledge, Step 1, 2, 4(d) and 5 of both worlds are indistinguishable because of the zero knowledge property of the zkVPD protocol in Protocol 3. As the commitments and proofs are simulated in step 2 and 4(d) by $\\boldsymbol{S_{v p d}}$ without knowing the polynomials, Step 4(c) of both worlds are indistinguishable as shown in [70, Theorem 3]. Step 4(a) in both worlds are indistinguishable as $\\delta$ are randomly selected in both worlds.  \n\nProtocol 5 (Our Zero Knowledge Argument). Let $\\lambda$ be the security parameter, $\\mathbb{F}$ be a prime field. Let $C:\\mathbb{F}^{n}\\to\\mathbb{F}$ be a layered arithmetic circuit over $\\mathbb{F}$ with $D$ layers, input $x$ and witness $w$ such that $|x|+|w|\\le n$ and $1=C(x;w)$ .  \n\n• $\\mathcal{G}(1^{\\lambda})$ : set pp as $\\mathsf{p p}\\gets\\mathsf{z k V P D.K e y G e n(1}^{\\lambda})$ .   \n• $\\langle\\mathcal{P}(\\mathsf{p p},w),\\mathcal{V}(\\mathsf{p p})\\rangle($ (in):   \n1) $\\mathcal{P}$ selects a random bivariate polynomial $R_{D}$ . $\\mathcal{P}$ commits to the witness of $C$ by sending com $D$ zkVPD.Commit $\\big(\\dot{V}_{D}$ , pp) to $\\nu$ , where $\\dot{V}_{D}$ is defined by Equation 3.   \n2) $\\mathcal{P}$ randomly selects polynomials $R_{i}:\\mathbb{F}^{2}\\to\\mathbb{F}$ and $\\delta_{i}:\\mathbb{F}^{2s_{i+1}+1}\\to\\mathbb{F}$ for $i=0,\\ldots,D-1.$ $\\mathcal{P}$ commits to these polynomials by sending $\\mathsf{c o m}_{i,1}\\gets\\mathsf{z k V P D.C o m m i t}(R_{i},\\mathsf{p p})$ and $\\mathsf{c o m}_{i,2}\\gets\\mathsf{z k V P D.C o m m i t}(\\delta_{i},\\mathsf{p p})$ to $\\nu$ . $\\mathcal{P}$ also reveals $R_{0}$ to $\\nu_{_{i}}$ , as $V_{0}$ is defined by out and is known to $\\nu$ .   \n3) $\\nu$ evaluates $\\dot{V}_{0}(u^{(0)})$ and $\\dot{V}_{0}(v^{(0)})$ for randomly chosen ${\\boldsymbol u}^{(0)},{\\boldsymbol v}^{(0)}\\in\\mathbb{F}^{s_{0}}$ .   \n4) For $i=0,\\dots,D-1$ : a) $\\mathcal{P}$ sends $\\begin{array}{r}{H_{i}=\\sum_{x,y\\in\\{0,1\\}^{s_{i}+1},\\underline{{z}}\\in\\{0,1\\}}\\delta_{i}(x,y,z)}\\end{array}$ to $\\nu$ . ) $\\nu$ picks $\\alpha_{i},\\beta_{i},\\gamma_{i}$ randomly in $\\mathbb{F}$ . $c$ ) $\\nu$ and $\\mathcal{P}$ execute a sumcheck protocol on Equation 4. At the end of the sumcheck, $\\nu$ receives a claim of $f_{i}^{\\prime}$ at point $u^{(i+1)},v^{(i+1)}\\in$ $\\mathbb{F}^{s_{i+1}},g_{i}\\in\\mathbb{F}$ selected randomly by $\\nu$ . d) $\\mathcal{P}$ opens $R_{i}(u^{(i)},g_{i})$ , $R_{i}(v^{(i)},g_{i})$ a nVd $\\delta_{i}\\left(u^{\\left(i+1\\right)},v^{\\left(i+1\\right)},g_{i}\\right)$ using zkVPD.Open. $\\mathcal{P}$ sends $\\dot{V}_{0}(u^{(i+1)})$ and $\\dot{V}_{0}(v^{(i+1)})$ to $\\nu$ . $e$ ) $\\nu$ validates $R_{i}(u^{(i)},g_{i})$ , $R_{i}(v^{(i)},g_{i})$ and $\\delta_{i}\\left(u^{\\left(i+1\\right)},v^{\\left(i+1\\right)},g_{i}\\right)$ using zkVPD.Verify. If any of them outputs $O$ , abort and output 0. $f)\\nu$ checks the claim of $f_{i}^{\\prime}$ using $R_{i}(u^{(i)},g_{i})$ , $R_{i}(v^{(i)},g_{i})$ , $\\delta_{i}\\left(u^{\\left(i+1\\right)},v^{\\left(i+1\\right)},g_{i}\\right)$ , $\\dot{V}_{0}(u^{(i+1)})$ and $\\dot{V}_{0}(v^{(i+1)})$ . If it fails, output 0.   \n5) $\\mathcal{P}$ runs $(y_{1},\\pi_{1})$ $z k V P D.O p e n(\\dot{V}_{D},u^{(D)},{\\mathsf{p p}})$ , $(y_{2},\\pi_{2})$ $\\mathsf{z k V P D.O p e n}(\\dot{V}_{D},v^{(D)},\\mathsf{p p})$ and sends $y_{1},\\pi_{1},y_{2},\\pi_{2}$ to $\\nu$ .   \n6) $\\nu$ runs Verify $\\cdot\\left(\\pi_{1},y_{1},\\mathsf{c o m}_{D},u^{(D)},\\mathsf{p p}\\right)$ and Verify $\\cdot\\left(\\pi_{2},y_{2},\\mathsf{c o m}_{D},v^{(D)},\\mathsf{p p}\\right)$ and output 0 if either check fails. Otherwise, $\\nu$ checks $\\dot{V}_{D}(u^{(D)})=y_{1}$ and $\\dot{V}_{D}(v^{(D)})=y_{2}$ , and rejects if either fails. If all checks above pass, $\\nu$ output 1.   \nLet $\\lambda$ be the security parameter, $\\mathbb{F}$ be a prime field. Let $C:\\mathbb{F}^{n}\\to\\mathbb{F}$ be a layered arithmetic circuit over $\\mathbb{F}$ with $D$ layers, input $x$ and   \nwitness $w$ such that $|x|+|w|\\le n$ and $\\mathsf{o u t}=C(x;w)$ . We construct the simulator $s$ given the circuit $C$ , the output out and input size $n$ .   \nLet $\\ensuremath{\\boldsymbol{S}}_{v p d}$ , $S_{v p d,R_{i}}$ and $S_{v p d,\\delta_{i}}$ be simulators of zkVPD for the witness and masking polynomials. Let $\\boldsymbol{S}_{s c}$ be the simulator of the sumcheck   \nprotocol on Equation 4, given by [70, Theorem 3].   \n• $\\mathcal{G}(1^{\\lambda})$ : set pp as pp $z\\mathsf{k V P D.K e y G e n(1}^{\\lambda})$ .   \n• $(S({\\mathsf{p p}},C,{\\mathsf{o u t}},1^{n}),\\mathcal{V}^{*}(C,{\\mathsf{p p}}))$ : 1) $s$ invokes $\\ensuremath{\\boldsymbol{S}}_{v p d}$ to generate com $S_{v p d}(1^{\\lambda},{\\mathsf{p p}})$ and sends com to $\\nu^{\\ast}$ . 2) $s$ randomly selects polynomials $R_{s i m,i}:\\mathbb{F}^{2}\\to\\mathbb{F}$ and $\\delta_{s i m,i}:\\mathbb{F}^{2s_{i+1}+1}\\rightarrow\\mathbb{F}$ for $i=0,\\dots,D-1$ that have the same monomials as $R_{i}$ and $\\delta_{i}$ in step 2 of Protocol 5. $s$ invokes $S_{v p d,R_{i}}$ and $S_{v p d,\\delta_{i}}$ to generate $\\mathsf{c o m}_{i,1}\\gets S_{v p d,R_{i}}(1^{\\lambda},\\mathsf{p p}_{R_{i}})$ and $\\mathsf{c o m}_{i,2}\\gets S_{v p d,\\delta_{i}}\\big(1^{\\lambda},\\mathsf{p p}_{\\delta_{i}}\\big)$ and send them to $\\nu^{*}$ , where ${\\mathsf{p p}}_{R_{i}}$ and ${\\mathsf{p p}}_{\\delta_{i}}$ are corresponding public parameters. $s$ also reveals $R_{s i m,0}^{\\prime}$ to $\\nu$ , as $V_{0}$ is defined by out and is known to $\\nu^{*}$ . 3) Wait $\\nu^{*}$ to evaluate $\\dot{V}_{0}(u^{(0)})$ and $\\dot{V}_{0}(v^{(0)})$ for randomly chosen ${\\boldsymbol u}^{(0)},{\\boldsymbol v}^{(0)}\\in\\mathbb{F}^{s_{0}}$ . 4) For $i=0,\\dots,D-1$ : ba)) $s$ esceenivdes $\\begin{array}{r}{H_{i}=\\sum_{x,y\\in\\{0,1\\}_{.}^{s_{i}+1},z\\in\\{0,1\\}}\\delta_{s i m,i}(x,y,z)\\mathrm{to}}\\end{array}$ $\\nu^{*}$ . $\\alpha_{i},\\beta_{i},\\gamma_{i}$ $\\nu^{*}$ c) $s$ simulates the sumcheck protocol on Equation 4 using $\\boldsymbol{S}_{s c}$ . At the end of the sumcheck, $s$ receives queries of $\\delta_{s i m,i}$ and $R_{s i m,i}$ aSt point at point $\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}$ $u^{(i+1)},v^{(i+1)}\\in\\mathring{\\mathbb{F}}^{s_{i+1}},g_{i}\\in\\mathbb{F}$ and senid  ∈them to selected by $\\nu^{*}$ . $\\nu^{*}$ . $s$ randomly computes $\\dot{V}_{i+1}\\big(u^{(i+1)}\\big),\\dot{V}_{i+1}\\big(v^{(i+1)}\\big)$ satisfying Equation 4 d) $s$ computes $R_{s i m,i}(u^{(i)},g_{i})$ , $R_{s i m,i}(v^{(i)},g_{i})$ V and $\\delta_{s i m,i}\\big(\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}\\big)$ and invokes $S_{v p d,R_{i}}$ and $S_{v p d,\\delta_{i}}$ to generate the proofs of these evaluations. e) Wait for $\\nu^{*}$ to validate $R_{s i m,i}(\\boldsymbol{u}^{(i)},g_{i})$ , $R_{s i m,i}(v^{(i)},g_{i})$ and $\\delta_{s i m,i}\\big(\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}\\big)$ . f) Wait for $\\nu^{*}$ to check the last claim of the sumcheck about $f_{i}^{\\prime}$ using $\\stackrel{\\wedge}{R}{}_{s i m,i}\\big(\\boldsymbol{u}^{(i)},g_{i}\\big),R_{s i m,i}\\big(\\boldsymbol{v}^{(i)},g_{i}\\big),\\delta_{s i m,i}\\big(\\boldsymbol{u}^{(i+1)},\\boldsymbol{v}^{(i+1)},g_{i}\\big),$ $\\dot{V}_{i+1}(u^{(i+1)})$ and $\\dot{V}_{i+1}(v^{(i+1)})$ . 5) In last part of the protocol, $s$ needs to prove to $\\nu^{*}$ the values of $\\dot{V}_{D}(u^{(D)})$ and $\\dot{V}_{D}(v^{(D)})$ , where $\\boldsymbol{u}^{(D)}\\in\\mathbb{F}^{n}$ and $\\boldsymbol{v}^{(D)}\\in\\mathbb{F}^{n}$ are chosen by $\\nu^{*}$ . $s$ gives $u^{(D)}$ , $\\dot{V}_{D}(u^{(D)})$ to $\\boldsymbol{S_{v p d}}$ and invokes $S_{2}$ of $\\boldsymbol{S_{v p d}}$ in Figure 4 to simulate this process. Do the same process again for $v^{(D)}$ , $\\dot{V}_{D}(v^{(D)})$ . 6) Wait for $\\nu$ to run $z\\mathsf{k V P D}.\\mathsf{V}.$ erify to validate the value of $\\dot{V}_{D}(u^{(D)})$ and $\\dot{V}_{D}(v^{(D)})$ .  "}