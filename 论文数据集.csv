序号,关键字,问题,相关论文,论文摘要,论文顶会及年份,编号
1,"软件质量度量
安全度量
业务环境
指导原则
可靠性
度量标准
概率与统计
软件工程
安全性
实用性
现实性",如何在软件工程中建立有效的安全度量体系？,Measuring Up: How to Keep Security Metrics Useful and Realistic,本次演讲回顾了软件质量度量的历史，指出了以往度量中存在的问题，并提出了在当前系统与网络安全度量中应吸取的经验教训。演讲者强调，安全度量不仅要关注技术本身，还要结合业务环境，提出了有效安全度量的指导原则。,CCS-2007,86
2,"软件质量度量
安全度量
业务环境
指导原则
可靠性
度量标准
概率与统计
软件工程
安全性
实用性
现实性",安全度量应如何兼顾技术与业务环境的需求？,Measuring Up: How to Keep Security Metrics Useful and Realistic,本次演讲回顾了软件质量度量的历史，指出了以往度量中存在的问题，并提出了在当前系统与网络安全度量中应吸取的经验教训。演讲者强调，安全度量不仅要关注技术本身，还要结合业务环境，提出了有效安全度量的指导原则。,CCS-2007,86
3,"软件质量度量
安全度量
业务环境
指导原则
可靠性
度量标准
概率与统计
软件工程
安全性
实用性
现实性",软件质量度量的历史经验对当前安全性评估有何启示？,Measuring Up: How to Keep Security Metrics Useful and Realistic,本次演讲回顾了软件质量度量的历史，指出了以往度量中存在的问题，并提出了在当前系统与网络安全度量中应吸取的经验教训。演讲者强调，安全度量不仅要关注技术本身，还要结合业务环境，提出了有效安全度量的指导原则。,CCS-2007,86
4,"软件质量度量
安全度量
业务环境
指导原则
可靠性
度量标准
概率与统计
软件工程
安全性
实用性
现实性",在安全性度量中，哪些可靠性和统计方法最为关键？,Measuring Up: How to Keep Security Metrics Useful and Realistic,本次演讲回顾了软件质量度量的历史，指出了以往度量中存在的问题，并提出了在当前系统与网络安全度量中应吸取的经验教训。演讲者强调，安全度量不仅要关注技术本身，还要结合业务环境，提出了有效安全度量的指导原则。,CCS-2007,86
5,"安全协定
Web服务
访问控制
信任协商
特权管理
行为约束
多方协作
自动化策略
安全性
谈判协议",Web服务环境下如何实现多方协作的自动化安全策略？,Security-by-Contract for Web Services,本文提出了一种面向Web服务的安全协定框架，将服务、所需凭证和行为约束进行捆绑，允许客户端和服务端根据各自偏好进行协商。该框架支持多种协商策略，兼顾合作与防御恶意攻击，提升了分布式服务环境下的安全性和灵活性。,CCS-2007,146
6,"安全协定
Web服务
访问控制
信任协商
特权管理
行为约束
多方协作
自动化策略
安全性
谈判协议",如何在分布式系统中平衡安全性与服务可用性的需求？,Security-by-Contract for Web Services,本文提出了一种面向Web服务的安全协定框架，将服务、所需凭证和行为约束进行捆绑，允许客户端和服务端根据各自偏好进行协商。该框架支持多种协商策略，兼顾合作与防御恶意攻击，提升了分布式服务环境下的安全性和灵活性。,CCS-2007,146
7,"安全协定
Web服务
访问控制
信任协商
特权管理
行为约束
多方协作
自动化策略
安全性
谈判协议",特权管理与行为约束在Web服务安全中的作用有哪些？,Security-by-Contract for Web Services,本文提出了一种面向Web服务的安全协定框架，将服务、所需凭证和行为约束进行捆绑，允许客户端和服务端根据各自偏好进行协商。该框架支持多种协商策略，兼顾合作与防御恶意攻击，提升了分布式服务环境下的安全性和灵活性。,CCS-2007,146
8,"在线游戏
反作弊
异常检测
客户端仿真
远程测量
审计机制
安全性
系统架构
多平台兼容
自动化检测",如何利用异常检测提升在线游戏反作弊系统的通用性与效率？,Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,本文提出了Fides系统，一种基于异常检测的远程反作弊方法，通过服务器端控制器与客户端审计器协作，利用部分客户端仿真和远程测量，有效检测在线游戏中的作弊行为。该方法无需依赖作弊特征库，能够跨游戏、平台和硬件架构通用，提升了检测效率和适应性。,CCS-2009,25
9,"在线游戏
反作弊
异常检测
客户端仿真
远程测量
审计机制
安全性
系统架构
多平台兼容
自动化检测",客户端仿真与远程测量在游戏安全中的作用有哪些？,Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,本文提出了Fides系统，一种基于异常检测的远程反作弊方法，通过服务器端控制器与客户端审计器协作，利用部分客户端仿真和远程测量，有效检测在线游戏中的作弊行为。该方法无需依赖作弊特征库，能够跨游戏、平台和硬件架构通用，提升了检测效率和适应性。,CCS-2009,25
10,"在线游戏
反作弊
异常检测
客户端仿真
远程测量
审计机制
安全性
系统架构
多平台兼容
自动化检测",反作弊系统如何在不影响游戏体验的前提下实现高效检测？,Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,本文提出了Fides系统，一种基于异常检测的远程反作弊方法，通过服务器端控制器与客户端审计器协作，利用部分客户端仿真和远程测量，有效检测在线游戏中的作弊行为。该方法无需依赖作弊特征库，能够跨游戏、平台和硬件架构通用，提升了检测效率和适应性。,CCS-2009,25
11,"非干扰性
信息流安全
反应式编程
类型系统
Web浏览器
客户端安全
多方交互
安全建模
形式化方法
隐私保护",如何通过类型系统实现Web客户端程序的信息流安全？,Reactive Noninterference,本文针对反应式程序的信息流安全问题，提出了适用于Web浏览器和客户端Web应用的非干扰性定义，并设计了一种基于类型系统的安全证明方法。通过形式化建模和理论分析，证明了该方法能够有效防止敏感信息在多方交互中的泄露，为Web安全提供了理论基础。,CCS-2009,5
12,"非干扰性
信息流安全
反应式编程
类型系统
Web浏览器
客户端安全
多方交互
安全建模
形式化方法
隐私保护",反应式程序在多方交互场景下存在哪些安全挑战？,Reactive Noninterference,本文针对反应式程序的信息流安全问题，提出了适用于Web浏览器和客户端Web应用的非干扰性定义，并设计了一种基于类型系统的安全证明方法。通过形式化建模和理论分析，证明了该方法能够有效防止敏感信息在多方交互中的泄露，为Web安全提供了理论基础。,CCS-2009,5
13,"非干扰性
信息流安全
反应式编程
类型系统
Web浏览器
客户端安全
多方交互
安全建模
形式化方法
隐私保护",形式化建模在Web安全理论研究中的作用是什么？,Reactive Noninterference,本文针对反应式程序的信息流安全问题，提出了适用于Web浏览器和客户端Web应用的非干扰性定义，并设计了一种基于类型系统的安全证明方法。通过形式化建模和理论分析，证明了该方法能够有效防止敏感信息在多方交互中的泄露，为Web安全提供了理论基础。,CCS-2009,5
14,"JavaScript
eval
分阶段元编程
静态分析
程序转换
信息流安全
代码模板
安全验证
语言安全
元编程",如何通过分阶段元编程提升JavaScript动态代码的安全性分析能力？,Position Paper: The Science of Boxing  Analysing Eval using Staged Metaprogramming,本文针对JavaScript中动态代码执行（eval）带来的安全分析难题，提出将eval转换为分阶段元编程（staged metaprogramming）的方法。通过算法将字符串形式的代码转化为结构化的代码模板，便于静态分析和信息流安全验证。该方法为Web应用安全分析提供了新的理论和工具基础。,CCS-2013,225
15,"JavaScript
eval
分阶段元编程
静态分析
程序转换
信息流安全
代码模板
安全验证
语言安全
元编程",静态分析在Web应用安全中的作用有哪些？,Position Paper: The Science of Boxing  Analysing Eval using Staged Metaprogramming,本文针对JavaScript中动态代码执行（eval）带来的安全分析难题，提出将eval转换为分阶段元编程（staged metaprogramming）的方法。通过算法将字符串形式的代码转化为结构化的代码模板，便于静态分析和信息流安全验证。该方法为Web应用安全分析提供了新的理论和工具基础。,CCS-2013,225
16,"JavaScript
eval
分阶段元编程
静态分析
程序转换
信息流安全
代码模板
安全验证
语言安全
元编程",程序转换技术如何助力动态语言的安全验证？,Position Paper: The Science of Boxing  Analysing Eval using Staged Metaprogramming,本文针对JavaScript中动态代码执行（eval）带来的安全分析难题，提出将eval转换为分阶段元编程（staged metaprogramming）的方法。通过算法将字符串形式的代码转化为结构化的代码模板，便于静态分析和信息流安全验证。该方法为Web应用安全分析提供了新的理论和工具基础。,CCS-2013,225
17,"跨平台
恶意软件
漏洞利用
攻击向量
移动安全
代码复用
社会工程
驱动下载
安全威胁
多操作系统",跨平台恶意软件如何实现多操作系统间的传播与感染？,POSTER: Cross-Platform Malware: Write Once Infect Everywhere,本文系统性地研究了跨平台（X-platform）恶意软件及其利用的跨平台漏洞。通过对现有恶意软件家族和漏洞的分析，揭示了跨平台恶意软件的多样化实现方式及其分发渠道，并指出商业化漏洞利用工具包已广泛支持跨平台攻击，未来跨平台威胁值得持续关注。,CCS-2013,93
18,"跨平台
恶意软件
漏洞利用
攻击向量
移动安全
代码复用
社会工程
驱动下载
安全威胁
多操作系统",商业化漏洞利用工具包在跨平台攻击中扮演什么角色？,POSTER: Cross-Platform Malware: Write Once Infect Everywhere,本文系统性地研究了跨平台（X-platform）恶意软件及其利用的跨平台漏洞。通过对现有恶意软件家族和漏洞的分析，揭示了跨平台恶意软件的多样化实现方式及其分发渠道，并指出商业化漏洞利用工具包已广泛支持跨平台攻击，未来跨平台威胁值得持续关注。,CCS-2013,93
19,"跨平台
恶意软件
漏洞利用
攻击向量
移动安全
代码复用
社会工程
驱动下载
安全威胁
多操作系统",跨平台安全威胁对未来防护提出了哪些新挑战？,POSTER: Cross-Platform Malware: Write Once Infect Everywhere,本文系统性地研究了跨平台（X-platform）恶意软件及其利用的跨平台漏洞。通过对现有恶意软件家族和漏洞的分析，揭示了跨平台恶意软件的多样化实现方式及其分发渠道，并指出商业化漏洞利用工具包已广泛支持跨平台攻击，未来跨平台威胁值得持续关注。,CCS-2013,93
20,"控制流完整性
动态CFG
输入相关安全
软件防护
运行时监控
攻击防护
性能优化
安全加固
静态分析
软件漏洞防御",如何通过动态生成和强制执行每个输入对应的控制流图提升软件安全性？,Per-Input Control-Flow Integrity,本文提出了每输入控制流完整性（PICFI）技术，通过为每个具体输入动态生成和强制执行控制流图，显著减少攻击面并提升安全性，同时保持较低的性能开销。,CCS-2015,112
21,"控制流完整性
动态CFG
输入相关安全
软件防护
运行时监控
攻击防护
性能优化
安全加固
静态分析
软件漏洞防御",控制流完整性技术在防御代码重用攻击中的优势体现在哪些方面？,Per-Input Control-Flow Integrity,本文提出了每输入控制流完整性（PICFI）技术，通过为每个具体输入动态生成和强制执行控制流图，显著减少攻击面并提升安全性，同时保持较低的性能开销。,CCS-2015,112
22,"控制流完整性
动态CFG
输入相关安全
软件防护
运行时监控
攻击防护
性能优化
安全加固
静态分析
软件漏洞防御",如何在保证安全性的同时降低控制流完整性机制的运行时性能开销？,Per-Input Control-Flow Integrity,本文提出了每输入控制流完整性（PICFI）技术，通过为每个具体输入动态生成和强制执行控制流图，显著减少攻击面并提升安全性，同时保持较低的性能开销。,CCS-2015,112
23,"云计算安全
MapReduce
流量分析
数据隐私
安全硬件
加密通信
信息泄露防护
负载均衡
安全模型
隐私保护机制",如何防止在云端MapReduce任务中通过流量分析泄露敏感信息？,Observing and Preventing Leakage in MapReduce,本文分析了云端MapReduce任务中即使采用加密和安全硬件，仍可能通过中间流量模式泄露敏感信息，并提出了两种可行的防泄漏解决方案。,CCS-2015,113
24,"云计算安全
MapReduce
流量分析
数据隐私
安全硬件
加密通信
信息泄露防护
负载均衡
安全模型
隐私保护机制",在大数据处理框架中，如何权衡数据隐私保护与系统性能？,Observing and Preventing Leakage in MapReduce,本文分析了云端MapReduce任务中即使采用加密和安全硬件，仍可能通过中间流量模式泄露敏感信息，并提出了两种可行的防泄漏解决方案。,CCS-2015,113
25,"云计算安全
MapReduce
流量分析
数据隐私
安全硬件
加密通信
信息泄露防护
负载均衡
安全模型
隐私保护机制",大规模分布式计算中，哪些机制可有效防止通过中间通信模式推断原始数据内容？,Observing and Preventing Leakage in MapReduce,本文分析了云端MapReduce任务中即使采用加密和安全硬件，仍可能通过中间流量模式泄露敏感信息，并提出了两种可行的防泄漏解决方案。,CCS-2015,113
26,"工业控制系统
CPS
网络仿真
物理层交互
安全测试平台
SDN
协议仿真
攻击防御
可扩展性
实时性",如何通过可扩展的仿真平台提升工业控制系统网络的安全研究能力？,MiniCPS: A Toolkit for Security Research on CPS Networks,本文提出了MiniCPS工具包，结合网络仿真与物理层交互，为工业控制系统安全研究提供了可扩展、可复现的实验环境。,CCS-2015,170
27,"工业控制系统
CPS
网络仿真
物理层交互
安全测试平台
SDN
协议仿真
攻击防御
可扩展性
实时性",工业控制系统安全研究中，物理层与网络层的协同仿真有何重要意义？,MiniCPS: A Toolkit for Security Research on CPS Networks,本文提出了MiniCPS工具包，结合网络仿真与物理层交互，为工业控制系统安全研究提供了可扩展、可复现的实验环境。,CCS-2015,170
28,"工业控制系统
CPS
网络仿真
物理层交互
安全测试平台
SDN
协议仿真
攻击防御
可扩展性
实时性",软件定义网络（SDN）在工业控制系统安全防护中能发挥哪些作用？,MiniCPS: A Toolkit for Security Research on CPS Networks,本文提出了MiniCPS工具包，结合网络仿真与物理层交互，为工业控制系统安全研究提供了可扩展、可复现的实验环境。,CCS-2015,170
29,"云存储安全
恶意云仓库
BarFinder
攻击基础设施
云服务滥用
自动化检测
配置漏洞
内容污染
威胁建模
防御机制",如何识别和检测云存储服务中被滥用的恶意仓库？,Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,本文系统性研究了云存储服务被滥用为恶意基础设施的现象，提出了BarFinder检测方法，揭示了恶意仓库在攻击链中的关键作用，并分析了配置漏洞和内容污染等安全隐患。,CCS-2016,110
30,"云存储安全
恶意云仓库
BarFinder
攻击基础设施
云服务滥用
自动化检测
配置漏洞
内容污染
威胁建模
防御机制",云服务平台如何防范和应对仓库被恶意利用带来的安全威胁？,Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,本文系统性研究了云存储服务被滥用为恶意基础设施的现象，提出了BarFinder检测方法，揭示了恶意仓库在攻击链中的关键作用，并分析了配置漏洞和内容污染等安全隐患。,CCS-2016,110
31,"云存储安全
恶意云仓库
BarFinder
攻击基础设施
云服务滥用
自动化检测
配置漏洞
内容污染
威胁建模
防御机制",配置漏洞和内容污染在云存储安全中有哪些典型表现及防护措施？,Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,本文系统性研究了云存储服务被滥用为恶意基础设施的现象，提出了BarFinder检测方法，揭示了恶意仓库在攻击链中的关键作用，并分析了配置漏洞和内容污染等安全隐患。,CCS-2016,110
32,"密码生成策略
助记句
密码安全性
用户可用性
大规模实证
攻击模型
个性化策略
记忆负担
安全评估
用户行为",助记句密码生成策略的安全性受哪些因素影响？,An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,本文通过大规模用户实验，系统评估了多种助记句密码生成策略的安全性和可用性，发现具体指令和示例对密码分布和安全性有显著影响，提出了提升安全性的个性化建议。,CCS-2016,182
33,"密码生成策略
助记句
密码安全性
用户可用性
大规模实证
攻击模型
个性化策略
记忆负担
安全评估
用户行为",如何平衡密码的安全性与用户记忆负担？,An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,本文通过大规模用户实验，系统评估了多种助记句密码生成策略的安全性和可用性，发现具体指令和示例对密码分布和安全性有显著影响，提出了提升安全性的个性化建议。,CCS-2016,182
34,"密码生成策略
助记句
密码安全性
用户可用性
大规模实证
攻击模型
个性化策略
记忆负担
安全评估
用户行为",大规模实证研究对密码生成策略设计有何启示？,An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,本文通过大规模用户实验，系统评估了多种助记句密码生成策略的安全性和可用性，发现具体指令和示例对密码分布和安全性有显著影响，提出了提升安全性的个性化建议。,CCS-2016,182
35,"工业控制系统
蜜罐技术
虚拟仿真
高交互性
网络安全
攻击检测
物理过程模拟
SDN管理
成本效益
安全防护",高交互虚拟ICS蜜罐在工业控制系统安全中的作用有哪些？,Towards High-Interaction Virtual ICS Honeypots-in-a-Box,本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾成本、可维护性和仿真真实度，验证了其在攻击检测和安全防护中的有效性。,CCS-2016,196
36,"工业控制系统
蜜罐技术
虚拟仿真
高交互性
网络安全
攻击检测
物理过程模拟
SDN管理
成本效益
安全防护",如何通过虚拟仿真提升ICS蜜罐的可扩展性与维护性？,Towards High-Interaction Virtual ICS Honeypots-in-a-Box,本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾成本、可维护性和仿真真实度，验证了其在攻击检测和安全防护中的有效性。,CCS-2016,196
37,"工业控制系统
蜜罐技术
虚拟仿真
高交互性
网络安全
攻击检测
物理过程模拟
SDN管理
成本效益
安全防护",蜜罐系统在工业网络安全防御体系中应具备哪些关键特性？,Towards High-Interaction Virtual ICS Honeypots-in-a-Box,本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾成本、可维护性和仿真真实度，验证了其在攻击检测和安全防护中的有效性。,CCS-2016,196
38,"硬件增强安全
SGX
可信执行环境
隔离保护
隐私计算
加密技术
系统安全
安全挑战
创新应用
安全架构",硬件增强安全技术（如SGX）在保护应用程序安全中的优势有哪些？,Intel Software Guard Extensions - Introduction and Open Research Challenges,本报告介绍了Intel SGX技术及其在构建安全系统中的创新应用，强调了硬件隔离、隐私保护和可信执行环境在提升系统安全性方面的作用。,CCS-2016,243
39,"硬件增强安全
SGX
可信执行环境
隔离保护
隐私计算
加密技术
系统安全
安全挑战
创新应用
安全架构",可信执行环境如何应对现代计算环境中的安全挑战？,Intel Software Guard Extensions - Introduction and Open Research Challenges,本报告介绍了Intel SGX技术及其在构建安全系统中的创新应用，强调了硬件隔离、隐私保护和可信执行环境在提升系统安全性方面的作用。,CCS-2016,243
40,"硬件增强安全
SGX
可信执行环境
隔离保护
隐私计算
加密技术
系统安全
安全挑战
创新应用
安全架构",基于SGX的系统架构在隐私计算和数据保护方面有哪些创新？,Intel Software Guard Extensions - Introduction and Open Research Challenges,本报告介绍了Intel SGX技术及其在构建安全系统中的创新应用，强调了硬件隔离、隐私保护和可信执行环境在提升系统安全性方面的作用。,CCS-2016,243
41,"可验证外包
交互式证明
硬件加速
成本分析
电路外包
数据并行
系统优化
安全协议
自动化设计
应用扩展",如何实现高效且可验证的计算外包？,Full Accounting for Verifiable Outsourcing,本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了协议和硬件设计，扩展了可验证外包的应用范围。,CCS-2017,180
42,"可验证外包
交互式证明
硬件加速
成本分析
电路外包
数据并行
系统优化
安全协议
自动化设计
应用扩展",可验证外包系统在实际应用中面临哪些主要挑战？,Full Accounting for Verifiable Outsourcing,本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了协议和硬件设计，扩展了可验证外包的应用范围。,CCS-2017,180
43,"可验证外包
交互式证明
硬件加速
成本分析
电路外包
数据并行
系统优化
安全协议
自动化设计
应用扩展",如何通过自动化设计优化可验证外包系统的性能与适用性？,Full Accounting for Verifiable Outsourcing,本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了协议和硬件设计，扩展了可验证外包的应用范围。,CCS-2017,180
44,"代码签名
Windows Authenticode
恶意软件
公钥基础设施（PKI）
证书滥用
数字签名
信任机制
证书吊销
安全威胁
身份验证",如何系统性评估代码签名PKI在恶意软件传播中的信任风险？,Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,本文系统性分析了Windows代码签名PKI被滥用签发恶意软件的现象，提出了三类信任弱点的威胁模型，并通过大规模数据集揭示了证书滥用的方式、影响窗口及其对终端安全的影响，提出了改进代码签名生态的建议。,CCS-2017,98
45,"代码签名
Windows Authenticode
恶意软件
公钥基础设施（PKI）
证书滥用
数字签名
信任机制
证书吊销
安全威胁
身份验证",数字签名机制在操作系统安全防护中存在哪些潜在弱点？,Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,本文系统性分析了Windows代码签名PKI被滥用签发恶意软件的现象，提出了三类信任弱点的威胁模型，并通过大规模数据集揭示了证书滥用的方式、影响窗口及其对终端安全的影响，提出了改进代码签名生态的建议。,CCS-2017,98
46,"代码签名
Windows Authenticode
恶意软件
公钥基础设施（PKI）
证书滥用
数字签名
信任机制
证书吊销
安全威胁
身份验证",如何提升代码签名证书管理与吊销机制的安全性和响应速度？,Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,本文系统性分析了Windows代码签名PKI被滥用签发恶意软件的现象，提出了三类信任弱点的威胁模型，并通过大规模数据集揭示了证书滥用的方式、影响窗口及其对终端安全的影响，提出了改进代码签名生态的建议。,CCS-2017,98
47,"随机数生成器
密钥管理
加密通信
状态恢复攻击
标准化漏洞
VPN安全
认证流程
信息安全
密码学
互联网测量",如何评估随机数生成器在加密系统中的安全性风险？,Practical state recovery attacks against legacy RNG implementations,本文系统性分析了ANSI X9.17/X9.31伪随机数生成器（RNG）在实际产品中的安全隐患，发现多个厂商在实现中使用了硬编码密钥，导致攻击者可通过已知密钥恢复RNG状态，进而破解VPN等加密通信。作者通过逆向分析和互联网测量，验证了该漏洞在野外设备中的广泛存在，强调了标准化和认证流程的失误。,CCS-2018,54
48,"随机数生成器
密钥管理
加密通信
状态恢复攻击
标准化漏洞
VPN安全
认证流程
信息安全
密码学
互联网测量",标准化和认证流程在密码学安全中存在哪些潜在隐患？,Practical state recovery attacks against legacy RNG implementations,本文系统性分析了ANSI X9.17/X9.31伪随机数生成器（RNG）在实际产品中的安全隐患，发现多个厂商在实现中使用了硬编码密钥，导致攻击者可通过已知密钥恢复RNG状态，进而破解VPN等加密通信。作者通过逆向分析和互联网测量，验证了该漏洞在野外设备中的广泛存在，强调了标准化和认证流程的失误。,CCS-2018,54
49,"随机数生成器
密钥管理
加密通信
状态恢复攻击
标准化漏洞
VPN安全
认证流程
信息安全
密码学
互联网测量",加密通信设备如何防范状态恢复类攻击？,Practical state recovery attacks against legacy RNG implementations,本文系统性分析了ANSI X9.17/X9.31伪随机数生成器（RNG）在实际产品中的安全隐患，发现多个厂商在实现中使用了硬编码密钥，导致攻击者可通过已知密钥恢复RNG状态，进而破解VPN等加密通信。作者通过逆向分析和互联网测量，验证了该漏洞在野外设备中的广泛存在，强调了标准化和认证流程的失误。,CCS-2018,54
50,"同态加密
矩阵计算
神经网络
隐私保护
机器学习
加密推理
云计算安全
算法优化
数据安全
模型加密",同态加密如何提升云端机器学习的隐私保护能力？,Secure Outsourced Matrix Computation and Application to Neural Networks,本文提出了一种高效的同态加密矩阵计算方法，支持在加密数据和加密模型上进行神经网络推理。通过创新的矩阵编码和运算策略，实现了在不泄露数据和模型的前提下，安全高效地完成深度学习推理，显著提升了隐私保护下的机器学习应用能力。,CCS-2018,96
51,"同态加密
矩阵计算
神经网络
隐私保护
机器学习
加密推理
云计算安全
算法优化
数据安全
模型加密",高效的加密矩阵计算对深度学习推理有何意义？,Secure Outsourced Matrix Computation and Application to Neural Networks,本文提出了一种高效的同态加密矩阵计算方法，支持在加密数据和加密模型上进行神经网络推理。通过创新的矩阵编码和运算策略，实现了在不泄露数据和模型的前提下，安全高效地完成深度学习推理，显著提升了隐私保护下的机器学习应用能力。,CCS-2018,96
52,"同态加密
矩阵计算
神经网络
隐私保护
机器学习
加密推理
云计算安全
算法优化
数据安全
模型加密",在安全外包计算场景下，如何兼顾效率与安全性？,Secure Outsourced Matrix Computation and Application to Neural Networks,本文提出了一种高效的同态加密矩阵计算方法，支持在加密数据和加密模型上进行神经网络推理。通过创新的矩阵编码和运算策略，实现了在不泄露数据和模型的前提下，安全高效地完成深度学习推理，显著提升了隐私保护下的机器学习应用能力。,CCS-2018,96
53,"编程语言
程序分析
软件安全
信息流安全
安全验证
加密API
Web安全
安全策略
访问控制
研讨会综述",编程语言技术在提升软件系统安全性方面有哪些应用？,PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,本文件为PLAS 2019研讨会综述，聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等议题，推动了编程语言与安全交叉领域的前沿研究。,CCS-2019,133
54,"编程语言
程序分析
软件安全
信息流安全
安全验证
加密API
Web安全
安全策略
访问控制
研讨会综述",程序分析方法如何助力发现和修复安全漏洞？,PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,本文件为PLAS 2019研讨会综述，聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等议题，推动了编程语言与安全交叉领域的前沿研究。,CCS-2019,133
55,"编程语言
程序分析
软件安全
信息流安全
安全验证
加密API
Web安全
安全策略
访问控制
研讨会综述",安全策略与访问控制在现代软件系统中有何重要性？,PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,本文件为PLAS 2019研讨会综述，聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等议题，推动了编程语言与安全交叉领域的前沿研究。,CCS-2019,133
56,"安全多方计算
公平性
保证输出
主动攻击防护
协议设计
通信优化
实时计算
小规模多方
对称密钥
可信执行",如何在多方计算中实现公平性和保证输出？,Fast Actively Secure Five-Party Computation with Security Beyond Abort,本文提出了适用于五方参与、可容忍两方主动攻击的高效安全多方计算协议，支持公平性、保证输出等多种安全目标，并在通信和运行时性能上接近最优，适用于高延迟网络环境下的实时安全计算。,CCS-2019,51
57,"安全多方计算
公平性
保证输出
主动攻击防护
协议设计
通信优化
实时计算
小规模多方
对称密钥
可信执行",小规模多方计算协议在实际应用中有哪些优势？,Fast Actively Secure Five-Party Computation with Security Beyond Abort,本文提出了适用于五方参与、可容忍两方主动攻击的高效安全多方计算协议，支持公平性、保证输出等多种安全目标，并在通信和运行时性能上接近最优，适用于高延迟网络环境下的实时安全计算。,CCS-2019,51
58,"安全多方计算
公平性
保证输出
主动攻击防护
协议设计
通信优化
实时计算
小规模多方
对称密钥
可信执行",主动攻击防护在多方安全计算中为何重要？,Fast Actively Secure Five-Party Computation with Security Beyond Abort,本文提出了适用于五方参与、可容忍两方主动攻击的高效安全多方计算协议，支持公平性、保证输出等多种安全目标，并在通信和运行时性能上接近最优，适用于高延迟网络环境下的实时安全计算。,CCS-2019,51
59,"设备指纹识别
磁感应信号
CPU硬件差异
电磁辐射
智能设备认证
硬件安全
机器学习分类
抗伪造
移动终端安全
多方协作",如何利用CPU模块磁感应信号实现智能设备的高精度指纹识别？,DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,本文提出了一种基于CPU模块磁感应信号的设备指纹识别方法DeMiCPU，通过外部磁传感器采集CPU硬件差异信号，结合机器学习实现高精度设备认证，具备抗伪造和跨平台通用性。,CCS-2019,66
60,"设备指纹识别
磁感应信号
CPU硬件差异
电磁辐射
智能设备认证
硬件安全
机器学习分类
抗伪造
移动终端安全
多方协作",硬件层面差异如何提升设备指纹的唯一性与安全性？,DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,本文提出了一种基于CPU模块磁感应信号的设备指纹识别方法DeMiCPU，通过外部磁传感器采集CPU硬件差异信号，结合机器学习实现高精度设备认证，具备抗伪造和跨平台通用性。,CCS-2019,66
61,"设备指纹识别
磁感应信号
CPU硬件差异
电磁辐射
智能设备认证
硬件安全
机器学习分类
抗伪造
移动终端安全
多方协作",设备指纹识别系统在多平台和复杂环境下的鲁棒性如何保障？,DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,本文提出了一种基于CPU模块磁感应信号的设备指纹识别方法DeMiCPU，通过外部磁传感器采集CPU硬件差异信号，结合机器学习实现高精度设备认证，具备抗伪造和跨平台通用性。,CCS-2019,66
62,"配对运算
自动化验证
密码学工具
结构保持签名
盲身份加密
可验证随机函数
属性加密
安全自动化
密钥生成
算法正确性",如何实现配对元素的自动化验证以提升密码系统的安全性与设计效率？,Are These Pairing Elements Correct? Automated Verification and Applications,本文提出了配对积方程（PPE）自动化验证方法及AutoPPE工具，系统性分析了配对元素可验证性问题，提出了自动化搜索与验证算法，提升了结构保持签名、盲身份加密等密码系统的安全性与设计效率。,CCS-2019,99
63,"配对运算
自动化验证
密码学工具
结构保持签名
盲身份加密
可验证随机函数
属性加密
安全自动化
密钥生成
算法正确性",配对积方程（PPE）在现代密码学应用中的作用有哪些？,Are These Pairing Elements Correct? Automated Verification and Applications,本文提出了配对积方程（PPE）自动化验证方法及AutoPPE工具，系统性分析了配对元素可验证性问题，提出了自动化搜索与验证算法，提升了结构保持签名、盲身份加密等密码系统的安全性与设计效率。,CCS-2019,99
64,"配对运算
自动化验证
密码学工具
结构保持签名
盲身份加密
可验证随机函数
属性加密
安全自动化
密钥生成
算法正确性",自动化工具如何助力复杂密码协议的正确性验证与安全分析？,Are These Pairing Elements Correct? Automated Verification and Applications,本文提出了配对积方程（PPE）自动化验证方法及AutoPPE工具，系统性分析了配对元素可验证性问题，提出了自动化搜索与验证算法，提升了结构保持签名、盲身份加密等密码系统的安全性与设计效率。,CCS-2019,99
65,"隐私保护计算
多方安全协作
差分隐私
可信执行环境
人工智能安全
数据孤岛
企业级解决方案
联合风控
跨组织数据分析
合规性技术",如何在保障数据隐私和合规性的前提下实现多方协作智能分析？,Introduction to Secure Collaborative Intelligence (SCI) Lab,本文介绍了蚂蚁集团SCI实验室在多方协作智能分析领域的隐私保护技术实践，结合多方安全计算、差分隐私和可信执行环境，构建了支持联合风控、联合营销等多场景的企业级隐私保护平台。,CCS-2020,163
66,"隐私保护计算
多方安全协作
差分隐私
可信执行环境
人工智能安全
数据孤岛
企业级解决方案
联合风控
跨组织数据分析
合规性技术",多种隐私保护技术如何协同提升AI与BI系统的数据安全？,Introduction to Secure Collaborative Intelligence (SCI) Lab,本文介绍了蚂蚁集团SCI实验室在多方协作智能分析领域的隐私保护技术实践，结合多方安全计算、差分隐私和可信执行环境，构建了支持联合风控、联合营销等多场景的企业级隐私保护平台。,CCS-2020,163
67,"隐私保护计算
多方安全协作
差分隐私
可信执行环境
人工智能安全
数据孤岛
企业级解决方案
联合风控
跨组织数据分析
合规性技术",企业级隐私保护平台在金融等行业的应用价值体现在哪些方面？,Introduction to Secure Collaborative Intelligence (SCI) Lab,本文介绍了蚂蚁集团SCI实验室在多方协作智能分析领域的隐私保护技术实践，结合多方安全计算、差分隐私和可信执行环境，构建了支持联合风控、联合营销等多场景的企业级隐私保护平台。,CCS-2020,163
68,"属性加密
隐私保护数据共享
多组织协作
访问控制
元数据转换
多词汇支持
代理重加密
动态属性
密钥撤销
数据中心化",如何实现多组织间的隐私保护数据共享与访问控制？,PRShare: A Framework for Privacy-Preserving Interorganizational Data Sharing,本文提出了PRShare框架，基于属性加密与代理属性转换，实现了多组织间的隐私保护数据共享，支持多词汇、动态属性、密钥撤销等功能，提升了数据中心化与合规性。,CCS-2020,182
69,"属性加密
隐私保护数据共享
多组织协作
访问控制
元数据转换
多词汇支持
代理重加密
动态属性
密钥撤销
数据中心化",属性加密与代理属性转换技术在数据共享中的优势有哪些？,PRShare: A Framework for Privacy-Preserving Interorganizational Data Sharing,本文提出了PRShare框架，基于属性加密与代理属性转换，实现了多组织间的隐私保护数据共享，支持多词汇、动态属性、密钥撤销等功能，提升了数据中心化与合规性。,CCS-2020,182
70,"属性加密
隐私保护数据共享
多组织协作
访问控制
元数据转换
多词汇支持
代理重加密
动态属性
密钥撤销
数据中心化",如何在不暴露组织内部元数据的前提下实现灵活的数据访问策略？,PRShare: A Framework for Privacy-Preserving Interorganizational Data Sharing,本文提出了PRShare框架，基于属性加密与代理属性转换，实现了多组织间的隐私保护数据共享，支持多词汇、动态属性、密钥撤销等功能，提升了数据中心化与合规性。,CCS-2020,182
71,"调度器侧信道
实时系统
不可区分性
差分隐私
任务调度
噪声注入
安全防护
系统安全
Linux内核
性能与安全权衡",如何通过引入不可区分性机制提升实时系统对调度器侧信道攻击的防护能力？,Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"本文提出了""调度不可区分性""概念，借鉴差分隐私思想，通过在任务调度中引入受控噪声，打破实时系统的确定性执行模式，从而有效防御调度器侧信道攻击，并在Linux内核和实际应用中验证了安全性与性能的平衡。",CCS-2021,45
72,"调度器侧信道
实时系统
不可区分性
差分隐私
任务调度
噪声注入
安全防护
系统安全
Linux内核
性能与安全权衡",实时系统在保障安全性与服务质量（QoS）之间如何实现有效权衡？,Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"本文提出了""调度不可区分性""概念，借鉴差分隐私思想，通过在任务调度中引入受控噪声，打破实时系统的确定性执行模式，从而有效防御调度器侧信道攻击，并在Linux内核和实际应用中验证了安全性与性能的平衡。",CCS-2021,45
73,"调度器侧信道
实时系统
不可区分性
差分隐私
任务调度
噪声注入
安全防护
系统安全
Linux内核
性能与安全权衡",差分隐私思想在操作系统安全领域有哪些创新应用？,Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"本文提出了""调度不可区分性""概念，借鉴差分隐私思想，通过在任务调度中引入受控噪声，打破实时系统的确定性执行模式，从而有效防御调度器侧信道攻击，并在Linux内核和实际应用中验证了安全性与性能的平衡。",CCS-2021,45
74,"补丁分析
漏洞挖掘
.NET框架
MSIL指令
大规模软件
安全补丁
方法调用路径
自动化分析
差异检测
安全性评估",如何基于中间语言实现大规模.NET软件的高效安全补丁分析？,Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,本文提出了MSILDiffer框架，基于MSIL指令对.NET程序集进行补丁差异分析，结合粗粒度与细粒度特征提取及方法调用路径分析，实现了大规模软件补丁的高效定位与漏洞挖掘。,CCS-2022,124
75,"补丁分析
漏洞挖掘
.NET框架
MSIL指令
大规模软件
安全补丁
方法调用路径
自动化分析
差异检测
安全性评估",安全补丁分析在1-day漏洞挖掘与防护中有哪些关键作用？,Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,本文提出了MSILDiffer框架，基于MSIL指令对.NET程序集进行补丁差异分析，结合粗粒度与细粒度特征提取及方法调用路径分析，实现了大规模软件补丁的高效定位与漏洞挖掘。,CCS-2022,124
76,"补丁分析
漏洞挖掘
.NET框架
MSIL指令
大规模软件
安全补丁
方法调用路径
自动化分析
差异检测
安全性评估",如何提升补丁分析工具在大规模软件中的覆盖率与准确性？,Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,本文提出了MSILDiffer框架，基于MSIL指令对.NET程序集进行补丁差异分析，结合粗粒度与细粒度特征提取及方法调用路径分析，实现了大规模软件补丁的高效定位与漏洞挖掘。,CCS-2022,124
77,"一次性密码本
密钥保管方案
门限方案
信息安全
影子分发
多方安全
Galois域
多项式插值
安全证明
分布式密钥管理",门限方案如何提升密钥保管与分发的安全性？,ONE-TIME PADS ARE KEY SAFEGUARDING SCHEMES NOT CRYPTOSYSTEMS. FAST KEY SAFEGUARDING SCHEMES (THRESHOLD SCHEMES) EXIST.,本文系统阐述了一次性密码本本质上属于密钥保管方案而非传统密码系统，提出并分析了门限方案（如几何门限、Lagrange插值门限）在密钥分发与保管中的安全性与高效实现，强调了Galois域在计算效率上的优势。,SP-1980,2
78,"一次性密码本
密钥保管方案
门限方案
信息安全
影子分发
多方安全
Galois域
多项式插值
安全证明
分布式密钥管理",如何利用Galois域优化门限密钥保管方案的计算效率？,ONE-TIME PADS ARE KEY SAFEGUARDING SCHEMES NOT CRYPTOSYSTEMS. FAST KEY SAFEGUARDING SCHEMES (THRESHOLD SCHEMES) EXIST.,本文系统阐述了一次性密码本本质上属于密钥保管方案而非传统密码系统，提出并分析了门限方案（如几何门限、Lagrange插值门限）在密钥分发与保管中的安全性与高效实现，强调了Galois域在计算效率上的优势。,SP-1980,2
79,"一次性密码本
密钥保管方案
门限方案
信息安全
影子分发
多方安全
Galois域
多项式插值
安全证明
分布式密钥管理",分布式密钥管理中如何防范影子泄露与不诚实参与者的风险？,ONE-TIME PADS ARE KEY SAFEGUARDING SCHEMES NOT CRYPTOSYSTEMS. FAST KEY SAFEGUARDING SCHEMES (THRESHOLD SCHEMES) EXIST.,本文系统阐述了一次性密码本本质上属于密钥保管方案而非传统密码系统，提出并分析了门限方案（如几何门限、Lagrange插值门限）在密钥分发与保管中的安全性与高效实现，强调了Galois域在计算效率上的优势。,SP-1980,2
80,"入侵容忍
文件碎片化
分布式存储
安全性
容错机制
密钥管理
阈值方案
数据可用性
分布式系统
加密算法",如何通过文件碎片化与分布式存储提升系统对入侵和故障的容忍能力？,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,本文提出了一种基于细粒度文件碎片化与分布式存储的入侵容忍方法，将敏感文件分割为多个碎片并分散存储于不同归档节点，即使部分节点被攻破也难以重组原文件，同时结合阈值密钥管理提升安全性与可用性。,SP-1986,6
81,"入侵容忍
文件碎片化
分布式存储
安全性
容错机制
密钥管理
阈值方案
数据可用性
分布式系统
加密算法",分布式文件系统中碎片化与加密结合有哪些安全优势与挑战？,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,本文提出了一种基于细粒度文件碎片化与分布式存储的入侵容忍方法，将敏感文件分割为多个碎片并分散存储于不同归档节点，即使部分节点被攻破也难以重组原文件，同时结合阈值密钥管理提升安全性与可用性。,SP-1986,6
82,"入侵容忍
文件碎片化
分布式存储
安全性
容错机制
密钥管理
阈值方案
数据可用性
分布式系统
加密算法",在多用户环境下，如何实现高效且安全的分布式文件访问与管理？,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,本文提出了一种基于细粒度文件碎片化与分布式存储的入侵容忍方法，将敏感文件分割为多个碎片并分散存储于不同归档节点，即使部分节点被攻破也难以重组原文件，同时结合阈值密钥管理提升安全性与可用性。,SP-1986,6
83,"容错技术
计算机病毒防护
程序流监控
N版本编程
安全性
错误检测
设计多样性
回滚恢复
系统完整性
恶意逻辑防御",如何利用容错技术提升计算机系统对病毒和恶意逻辑的防护能力？,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,本文提出将容错技术（如程序流监控和N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本投票机制，实现对病毒传播和特洛伊木马的检测与遏制，兼顾物理故障与恶意攻击的容忍。,SP-1988,9
84,"容错技术
计算机病毒防护
程序流监控
N版本编程
安全性
错误检测
设计多样性
回滚恢复
系统完整性
恶意逻辑防御",程序流监控与N版本编程在防御软件层面攻击中的优势体现在哪些方面？,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,本文提出将容错技术（如程序流监控和N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本投票机制，实现对病毒传播和特洛伊木马的检测与遏制，兼顾物理故障与恶意攻击的容忍。,SP-1988,9
85,"容错技术
计算机病毒防护
程序流监控
N版本编程
安全性
错误检测
设计多样性
回滚恢复
系统完整性
恶意逻辑防御",在高安全需求场景下，如何实现对恶意逻辑和随机故障的统一防护？,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,本文提出将容错技术（如程序流监控和N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本投票机制，实现对病毒传播和特洛伊木马的检测与遏制，兼顾物理故障与恶意攻击的容忍。,SP-1988,9
86,"访问控制模型
安全性分析
ESPM模型
权限传播
联合创建
可判定性
复杂性分析
安全策略
多用户系统
表达能力",如何在保证表达能力的同时提升访问控制模型的安全性分析可行性？,Safety Analysis For The Extended Schematic Protection Model,本文提出并分析了扩展示意保护模型（ESPM），通过引入联合创建操作，在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，兼顾了表达力与可判定性。,SP-1991,1
87,"访问控制模型
安全性分析
ESPM模型
权限传播
联合创建
可判定性
复杂性分析
安全策略
多用户系统
表达能力",联合创建操作如何影响访问控制模型的安全性与分析复杂性？,Safety Analysis For The Extended Schematic Protection Model,本文提出并分析了扩展示意保护模型（ESPM），通过引入联合创建操作，在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，兼顾了表达力与可判定性。,SP-1991,1
88,"访问控制模型
安全性分析
ESPM模型
权限传播
联合创建
可判定性
复杂性分析
安全策略
多用户系统
表达能力",在多用户系统中，如何实现灵活且可验证的权限传播与撤销机制？,Safety Analysis For The Extended Schematic Protection Model,本文提出并分析了扩展示意保护模型（ESPM），通过引入联合创建操作，在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，兼顾了表达力与可判定性。,SP-1991,1
89,"硬件环境
安全协议
资源约束
加密技术
单用户设备
分布式系统
访问管理
协议简化
身份验证
未来趋势",硬件环境的变化如何影响安全协议的设计与实现？,The Hardware Environment,本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的提升，协议可以更注重简洁性与可验证性，未来安全关注点将转向访问管理、身份验证和审计等高层问题。,SP-1999,18
90,"硬件环境
安全协议
资源约束
加密技术
单用户设备
分布式系统
访问管理
协议简化
身份验证
未来趋势",单用户与单用途设备的普及对系统安全策略有何影响？,The Hardware Environment,本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的提升，协议可以更注重简洁性与可验证性，未来安全关注点将转向访问管理、身份验证和审计等高层问题。,SP-1999,18
91,"硬件环境
安全协议
资源约束
加密技术
单用户设备
分布式系统
访问管理
协议简化
身份验证
未来趋势","在分布式系统中，网络通信的绝缘""特性如何影响安全机制的重点？""",The Hardware Environment,本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的提升，协议可以更注重简洁性与可验证性，未来安全关注点将转向访问管理、身份验证和审计等高层问题。,SP-1999,18
92,"广播加密
智能卡
用户撤销
密钥管理
反盗版
信息安全
分组密钥
系统仿真
通信复杂度
可扩展性",如何在大规模广播加密系统中实现高效的用户撤销与密钥管理？,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,本文提出了一种适用于智能卡环境的高效广播加密用户撤销方案，通过分阶段密钥管理和系统仿真，显著提升了系统的可扩展性和抗盗版能力，降低了智能卡端的计算与存储压力。,SP-2003,7
93,"广播加密
智能卡
用户撤销
密钥管理
反盗版
信息安全
分组密钥
系统仿真
通信复杂度
可扩展性",智能卡在广播加密系统中的性能瓶颈如何被有效规避？,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,本文提出了一种适用于智能卡环境的高效广播加密用户撤销方案，通过分阶段密钥管理和系统仿真，显著提升了系统的可扩展性和抗盗版能力，降低了智能卡端的计算与存储压力。,SP-2003,7
94,"广播加密
智能卡
用户撤销
密钥管理
反盗版
信息安全
分组密钥
系统仿真
通信复杂度
可扩展性",在抗盗版场景下，如何平衡广播加密系统的通信开销与安全性？,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,本文提出了一种适用于智能卡环境的高效广播加密用户撤销方案，通过分阶段密钥管理和系统仿真，显著提升了系统的可扩展性和抗盗版能力，降低了智能卡端的计算与存储压力。,SP-2003,7
95,"Web安全
浏览器隔离
虚拟机沙箱
安全架构
用户控制
网络策略
恶意代码防护
性能评估
安全漏洞
操作系统安全",如何通过虚拟机沙箱机制提升Web应用与浏览器的安全隔离？,A Safety-Oriented Platform for Web Applications,本文提出了Tahoma架构，通过在虚拟机中隔离每个Web应用及其浏览器实例，实现了强隔离和用户可控的Web安全平台，显著提升了对恶意代码和安全漏洞的防护能力。,SP-2006,11
96,"Web安全
浏览器隔离
虚拟机沙箱
安全架构
用户控制
网络策略
恶意代码防护
性能评估
安全漏洞
操作系统安全",Web应用的网络访问策略如何影响整体系统的安全性与可控性？,A Safety-Oriented Platform for Web Applications,本文提出了Tahoma架构，通过在虚拟机中隔离每个Web应用及其浏览器实例，实现了强隔离和用户可控的Web安全平台，显著提升了对恶意代码和安全漏洞的防护能力。,SP-2006,11
97,"Web安全
浏览器隔离
虚拟机沙箱
安全架构
用户控制
网络策略
恶意代码防护
性能评估
安全漏洞
操作系统安全",在不牺牲用户体验的前提下，如何实现Web浏览环境的高安全性与高性能？,A Safety-Oriented Platform for Web Applications,本文提出了Tahoma架构，通过在虚拟机中隔离每个Web应用及其浏览器实例，实现了强隔离和用户可控的Web安全平台，显著提升了对恶意代码和安全漏洞的防护能力。,SP-2006,11
98,"可重构硬件
FPGA安全
隔离机制
物理隔离
接口追踪
配置擦除
内存保护
嵌入式系统
安全设计
性能权衡",如何在多信任级别的可重构硬件系统中实现高效的物理隔离与接口安全？,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"本文提出了""护城河与吊桥""隔离机制，通过物理隔离、接口追踪和配置擦除等手段，实现了FPGA等可重构硬件中多核心的安全隔离与受控通信，提升了嵌入式系统的安全性与可验证性。",SP-2007,13
99,"可重构硬件
FPGA安全
隔离机制
物理隔离
接口追踪
配置擦除
内存保护
嵌入式系统
安全设计
性能权衡",接口追踪与配置擦除技术在防范硬件后门和信息泄露中的作用有哪些？,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"本文提出了""护城河与吊桥""隔离机制，通过物理隔离、接口追踪和配置擦除等手段，实现了FPGA等可重构硬件中多核心的安全隔离与受控通信，提升了嵌入式系统的安全性与可验证性。",SP-2007,13
100,"可重构硬件
FPGA安全
隔离机制
物理隔离
接口追踪
配置擦除
内存保护
嵌入式系统
安全设计
性能权衡",在嵌入式系统安全设计中，如何平衡隔离机制的安全性与硬件资源利用率？,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"本文提出了""护城河与吊桥""隔离机制，通过物理隔离、接口追踪和配置擦除等手段，实现了FPGA等可重构硬件中多核心的安全隔离与受控通信，提升了嵌入式系统的安全性与可验证性。",SP-2007,13
101,"植入式医疗设备
无线安全
隐私保护
软件无线电攻击
零功耗防御
能量收集
认证机制
患者感知
医疗信息安全
安全设计权衡",如何评估和提升植入式医疗设备在无线通信下的安全性与隐私保护能力？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本文系统分析了心脏起搏器和植入式除颤器的无线通信安全与隐私风险，提出了基于软件无线电的攻击方法，并创新性地提出了零功耗防御机制，有效提升了设备的安全性与患者感知能力。,SP-2008,14
102,"植入式医疗设备
无线安全
隐私保护
软件无线电攻击
零功耗防御
能量收集
认证机制
患者感知
医疗信息安全
安全设计权衡",零功耗认证与患者感知机制在医疗设备安全防护中的作用有哪些？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本文系统分析了心脏起搏器和植入式除颤器的无线通信安全与隐私风险，提出了基于软件无线电的攻击方法，并创新性地提出了零功耗防御机制，有效提升了设备的安全性与患者感知能力。,SP-2008,14
103,"植入式医疗设备
无线安全
隐私保护
软件无线电攻击
零功耗防御
能量收集
认证机制
患者感知
医疗信息安全
安全设计权衡",在医疗设备安全设计中，如何平衡安全性、隐私保护与设备能耗之间的关系？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本文系统分析了心脏起搏器和植入式除颤器的无线通信安全与隐私风险，提出了基于软件无线电的攻击方法，并创新性地提出了零功耗防御机制，有效提升了设备的安全性与患者感知能力。,SP-2008,14
104,"MPEG视频加密
选择性加密
多媒体安全
压缩与安全权衡
I帧加密
I块加密
视频隐私保护
实时传输
性能评估
安全机制改进",如何在保证实时性能的前提下提升MPEG视频传输的安全性？,An Empirical Study of Secure MPEG Video Transmissions,本文通过实证研究分析了MPEG视频选择性加密方案的安全性，发现仅加密I帧难以满足高安全需求，提出了加密I块和调整I帧频率等改进措施，并探讨了安全性与压缩效率的权衡。,NDSS-1996,1
105,"MPEG视频加密
选择性加密
多媒体安全
压缩与安全权衡
I帧加密
I块加密
视频隐私保护
实时传输
性能评估
安全机制改进",选择性加密在多媒体数据保护中的优势与局限性体现在哪些方面？,An Empirical Study of Secure MPEG Video Transmissions,本文通过实证研究分析了MPEG视频选择性加密方案的安全性，发现仅加密I帧难以满足高安全需求，提出了加密I块和调整I帧频率等改进措施，并探讨了安全性与压缩效率的权衡。,NDSS-1996,1
106,"MPEG视频加密
选择性加密
多媒体安全
压缩与安全权衡
I帧加密
I块加密
视频隐私保护
实时传输
性能评估
安全机制改进",在多媒体安全设计中，如何平衡加密强度与带宽/计算资源消耗？,An Empirical Study of Secure MPEG Video Transmissions,本文通过实证研究分析了MPEG视频选择性加密方案的安全性，发现仅加密I帧难以满足高安全需求，提出了加密I块和调整I帧频率等改进措施，并探讨了安全性与压缩效率的权衡。,NDSS-1996,1
107,"Java安全模型
分布式策略管理
SPKI证书
授权机制
访问控制
动态权限
可扩展性
临时密钥
能力系统
匿名授权",如何利用SPKI授权证书提升大规模分布式Java环境的安全策略管理能力？,Distributed Policy Management for Java 2,本文提出将SPKI授权证书引入Java 2安全模型，实现了分布式、动态、可扩展的安全策略管理，支持临时密钥与权限委托，提升了分布式系统的安全性与灵活性。,NDSS-1999,14
108,"Java安全模型
分布式策略管理
SPKI证书
授权机制
访问控制
动态权限
可扩展性
临时密钥
能力系统
匿名授权",分布式环境下，如何实现对Java类的动态授权与权限委托？,Distributed Policy Management for Java 2,本文提出将SPKI授权证书引入Java 2安全模型，实现了分布式、动态、可扩展的安全策略管理，支持临时密钥与权限委托，提升了分布式系统的安全性与灵活性。,NDSS-1999,14
109,"Java安全模型
分布式策略管理
SPKI证书
授权机制
访问控制
动态权限
可扩展性
临时密钥
能力系统
匿名授权",在大规模分布式系统中，如何平衡安全性、可扩展性与管理复杂度？,Distributed Policy Management for Java 2,本文提出将SPKI授权证书引入Java 2安全模型，实现了分布式、动态、可扩展的安全策略管理，支持临时密钥与权限委托，提升了分布式系统的安全性与灵活性。,NDSS-1999,14
110,"植入式医疗设备
心脏除颤器
无线通信安全
软件无线电攻击
零功耗防御
隐私保护
逆向工程
医疗设备安全",植入式医疗设备在无线通信过程中存在哪些主要的安全与隐私风险？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本论文分析了植入式心脏除颤器（ICD）的安全性与隐私风险。通过对ICD无线通信协议的逆向工程，作者展示了利用软件无线电对ICD进行攻击的多种方式，包括隐私泄露、设备重编程、拒绝服务等，并提出了三种基于射频能量收集的零功耗防御机制。论文强调了医疗设备安全与隐私保护的挑战，并呼吁在未来设备设计中平衡安全、隐私与可用性。,SP-2008,14
111,"植入式医疗设备
心脏除颤器
无线通信安全
软件无线电攻击
零功耗防御
隐私保护
逆向工程
医疗设备安全",针对资源受限的医疗设备，如何在不增加能耗的前提下提升其安全性？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本论文分析了植入式心脏除颤器（ICD）的安全性与隐私风险。通过对ICD无线通信协议的逆向工程，作者展示了利用软件无线电对ICD进行攻击的多种方式，包括隐私泄露、设备重编程、拒绝服务等，并提出了三种基于射频能量收集的零功耗防御机制。论文强调了医疗设备安全与隐私保护的挑战，并呼吁在未来设备设计中平衡安全、隐私与可用性。,SP-2008,14
112,"植入式医疗设备
心脏除颤器
无线通信安全
软件无线电攻击
零功耗防御
隐私保护
逆向工程
医疗设备安全",逆向工程在医疗设备安全研究中有哪些应用和意义？,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,本论文分析了植入式心脏除颤器（ICD）的安全性与隐私风险。通过对ICD无线通信协议的逆向工程，作者展示了利用软件无线电对ICD进行攻击的多种方式，包括隐私泄露、设备重编程、拒绝服务等，并提出了三种基于射频能量收集的零功耗防御机制。论文强调了医疗设备安全与隐私保护的挑战，并呼吁在未来设备设计中平衡安全、隐私与可用性。,SP-2008,14
113,"JavaScript安全
符号执行
字符串约束
客户端代码注入
自动化漏洞检测
Web安全
动态分析
GUI事件探索",如何利用符号执行技术提升JavaScript客户端代码的自动化安全分析能力？,A Symbolic Execution Framework for JavaScript,本论文提出了首个针对JavaScript客户端代码的符号执行分析系统Kudzu。该系统通过动态符号执行和自动GUI事件探索，能够系统性地覆盖JavaScript程序的执行空间，并自动发现客户端代码注入等安全漏洞。为应对JavaScript复杂的字符串操作，作者设计了新的字符串约束语言及求解器Kaluza。实验表明，Kudzu可自动发现真实Web应用中的未知漏洞，提升了客户端Web安全分析的自动化和有效性。,SP-2010,26
114,"JavaScript安全
符号执行
字符串约束
客户端代码注入
自动化漏洞检测
Web安全
动态分析
GUI事件探索",针对JavaScript复杂字符串操作，现有安全分析工具面临哪些挑战，如何解决？,A Symbolic Execution Framework for JavaScript,本论文提出了首个针对JavaScript客户端代码的符号执行分析系统Kudzu。该系统通过动态符号执行和自动GUI事件探索，能够系统性地覆盖JavaScript程序的执行空间，并自动发现客户端代码注入等安全漏洞。为应对JavaScript复杂的字符串操作，作者设计了新的字符串约束语言及求解器Kaluza。实验表明，Kudzu可自动发现真实Web应用中的未知漏洞，提升了客户端Web安全分析的自动化和有效性。,SP-2010,26
115,"JavaScript安全
符号执行
字符串约束
客户端代码注入
自动化漏洞检测
Web安全
动态分析
GUI事件探索",自动化工具在Web应用客户端安全检测中有哪些实际应用成效？,A Symbolic Execution Framework for JavaScript,本论文提出了首个针对JavaScript客户端代码的符号执行分析系统Kudzu。该系统通过动态符号执行和自动GUI事件探索，能够系统性地覆盖JavaScript程序的执行空间，并自动发现客户端代码注入等安全漏洞。为应对JavaScript复杂的字符串操作，作者设计了新的字符串约束语言及求解器Kaluza。实验表明，Kudzu可自动发现真实Web应用中的未知漏洞，提升了客户端Web安全分析的自动化和有效性。,SP-2010,26
116,"安全两方计算
双重执行协议
半诚实模型
恶意安全
隐私保护
加密电路
输出一致性验证
高效协议",如何在保证高效性的同时提升安全两方计算协议对恶意对手的防护能力？,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,本论文提出了一种基于双重执行（Dual Execution）的安全两方计算协议，旨在在接近半诚实模型效率的前提下，显著提升对恶意对手的安全性。该协议通过两次角色互换的加密电路执行及高效的输出一致性验证，理论上仅允许恶意方多获知一比特信息。实验表明，该方法在实际应用中几乎不增加额外开销，同时大幅提升了协议的安全保障，为大规模隐私保护计算提供了实用方案。,SP-2012,18
117,"安全两方计算
双重执行协议
半诚实模型
恶意安全
隐私保护
加密电路
输出一致性验证
高效协议",双重执行（Dual Execution）协议在安全两方计算中具有什么优势和适用场景？,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,本论文提出了一种基于双重执行（Dual Execution）的安全两方计算协议，旨在在接近半诚实模型效率的前提下，显著提升对恶意对手的安全性。该协议通过两次角色互换的加密电路执行及高效的输出一致性验证，理论上仅允许恶意方多获知一比特信息。实验表明，该方法在实际应用中几乎不增加额外开销，同时大幅提升了协议的安全保障，为大规模隐私保护计算提供了实用方案。,SP-2012,18
118,"安全两方计算
双重执行协议
半诚实模型
恶意安全
隐私保护
加密电路
输出一致性验证
高效协议",在安全两方计算协议中，如何权衡信息泄露与协议效率？,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,本论文提出了一种基于双重执行（Dual Execution）的安全两方计算协议，旨在在接近半诚实模型效率的前提下，显著提升对恶意对手的安全性。该协议通过两次角色互换的加密电路执行及高效的输出一致性验证，理论上仅允许恶意方多获知一比特信息。实验表明，该方法在实际应用中几乎不增加额外开销，同时大幅提升了协议的安全保障，为大规模隐私保护计算提供了实用方案。,SP-2012,18
119,"智能卡安全
随机数生成器
非接触式支付
门禁系统
密钥管理
卡片克隆
加密协议
实证分析",现实中非接触式智能卡系统在随机数生成和密钥管理方面存在哪些主要安全隐患？,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,本论文系统分析了现实中广泛应用的非接触式支付与门禁智能卡（如MiFare Classic、HID Prox等）在随机数生成与密钥管理方面的安全隐患。通过对多国50余张真实智能卡的实测，揭示了随机数生成器（RNG）质量低下、密钥复用等问题极易导致卡片克隆和攻击。论文还展示了不同国家和应用场景下攻击的实际难度和影响，强调了行业安全改进缓慢、用户安全意识薄弱等现象，并呼吁采用更安全的智能卡和管理措施。,SP-2013,45
120,"智能卡安全
随机数生成器
非接触式支付
门禁系统
密钥管理
卡片克隆
加密协议
实证分析",智能卡克隆攻击的实际可行性受哪些因素影响？,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,本论文系统分析了现实中广泛应用的非接触式支付与门禁智能卡（如MiFare Classic、HID Prox等）在随机数生成与密钥管理方面的安全隐患。通过对多国50余张真实智能卡的实测，揭示了随机数生成器（RNG）质量低下、密钥复用等问题极易导致卡片克隆和攻击。论文还展示了不同国家和应用场景下攻击的实际难度和影响，强调了行业安全改进缓慢、用户安全意识薄弱等现象，并呼吁采用更安全的智能卡和管理措施。,SP-2013,45
121,"智能卡安全
随机数生成器
非接触式支付
门禁系统
密钥管理
卡片克隆
加密协议
实证分析",行业和用户在智能卡安全防护方面存在哪些共性问题？,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,本论文系统分析了现实中广泛应用的非接触式支付与门禁智能卡（如MiFare Classic、HID Prox等）在随机数生成与密钥管理方面的安全隐患。通过对多国50余张真实智能卡的实测，揭示了随机数生成器（RNG）质量低下、密钥复用等问题极易导致卡片克隆和攻击。论文还展示了不同国家和应用场景下攻击的实际难度和影响，强调了行业安全改进缓慢、用户安全意识薄弱等现象，并呼吁采用更安全的智能卡和管理措施。,SP-2013,45
122,"垃圾邮件
僵尸网络
网络安全
邮件过滤
黑产经济
自动化攻击
参数调优
经验分析",垃圾邮件僵尸网络中，哪些因素对垃圾邮件活动的成功率影响最大？,The Tricks of the Trade: What Makes Spam Campaigns Successful?,本论文通过分析大型垃圾邮件僵尸网络（Cutwail）控制服务器数据，系统研究了影响垃圾邮件活动成功的关键因素。研究发现，僵尸网络开发者为垃圾邮件发送者提供了详细但部分无效的操作手册，实际成功的垃圾邮件活动更多依赖经验和参数微调。论文揭示，影响成功的关键在于邮件地址库质量、合理的重试机制等，而僵尸主机地理位置和数量等传统认为重要的参数实际影响有限。研究为垃圾邮件防御和僵尸网络治理提供了新思路。,SP-2014,57
123,"垃圾邮件
僵尸网络
网络安全
邮件过滤
黑产经济
自动化攻击
参数调优
经验分析",垃圾邮件发送者在实际操作中更依赖哪些手段提升活动效果？,The Tricks of the Trade: What Makes Spam Campaigns Successful?,本论文通过分析大型垃圾邮件僵尸网络（Cutwail）控制服务器数据，系统研究了影响垃圾邮件活动成功的关键因素。研究发现，僵尸网络开发者为垃圾邮件发送者提供了详细但部分无效的操作手册，实际成功的垃圾邮件活动更多依赖经验和参数微调。论文揭示，影响成功的关键在于邮件地址库质量、合理的重试机制等，而僵尸主机地理位置和数量等传统认为重要的参数实际影响有限。研究为垃圾邮件防御和僵尸网络治理提供了新思路。,SP-2014,57
124,"垃圾邮件
僵尸网络
网络安全
邮件过滤
黑产经济
自动化攻击
参数调优
经验分析",本研究对垃圾邮件防御和僵尸网络治理有何启示？,The Tricks of the Trade: What Makes Spam Campaigns Successful?,本论文通过分析大型垃圾邮件僵尸网络（Cutwail）控制服务器数据，系统研究了影响垃圾邮件活动成功的关键因素。研究发现，僵尸网络开发者为垃圾邮件发送者提供了详细但部分无效的操作手册，实际成功的垃圾邮件活动更多依赖经验和参数微调。论文揭示，影响成功的关键在于邮件地址库质量、合理的重试机制等，而僵尸主机地理位置和数量等传统认为重要的参数实际影响有限。研究为垃圾邮件防御和僵尸网络治理提供了新思路。,SP-2014,57
125,"能力系统
软件隔离
内存安全
C语言安全
硬件安全架构
对象能力模型
可信计算基
安全编译器",如何通过硬件-软件协同设计提升C语言系统的内存安全与隔离能力？,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,本论文提出了CHERI，一种混合能力系统架构，通过在传统RISC指令集、编译器和操作系统中引入细粒度的能力（capability）机制，实现可扩展的软件隔离与内存安全。CHERI支持对象能力模型，显著提升了C语言可信计算基（TCB）的安全性和可编程性。通过硬件-软件协同，CHERI在性能、可扩展性和安全性上优于传统基于MMU的隔离方案，并在多种UNIX应用中验证了其实用性。,SP-2015,46
126,"能力系统
软件隔离
内存安全
C语言安全
硬件安全架构
对象能力模型
可信计算基
安全编译器",能力系统在现代操作系统安全架构中有哪些优势？,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,本论文提出了CHERI，一种混合能力系统架构，通过在传统RISC指令集、编译器和操作系统中引入细粒度的能力（capability）机制，实现可扩展的软件隔离与内存安全。CHERI支持对象能力模型，显著提升了C语言可信计算基（TCB）的安全性和可编程性。通过硬件-软件协同，CHERI在性能、可扩展性和安全性上优于传统基于MMU的隔离方案，并在多种UNIX应用中验证了其实用性。,SP-2015,46
127,"能力系统
软件隔离
内存安全
C语言安全
硬件安全架构
对象能力模型
可信计算基
安全编译器",CHERI架构在实际应用中如何兼顾安全性与性能？,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,本论文提出了CHERI，一种混合能力系统架构，通过在传统RISC指令集、编译器和操作系统中引入细粒度的能力（capability）机制，实现可扩展的软件隔离与内存安全。CHERI支持对象能力模型，显著提升了C语言可信计算基（TCB）的安全性和可编程性。通过硬件-软件协同，CHERI在性能、可扩展性和安全性上优于传统基于MMU的隔离方案，并在多种UNIX应用中验证了其实用性。,SP-2015,46
128,"隐私影响评估
社会影响评估
物联网
隐私设计
数据最小化
透明性
责任创新
用户信任",物联网系统设计中，如何实现从隐私影响评估到社会影响评估的转变？,From Privacy Impact Assessment to Social Impact Assessment,"本论文针对物联网（IoT）用户信任危机，提出将""隐私影响评估（PIA）""扩展为""社会影响评估（SIA）""，并主张在产品概念阶段及系统工程全流程中嵌入社会影响考量。作者分析了现有""隐私设计""理念的不足，强调需结合法律、社会和技术手段，推动IoT系统在数据最小化、安全、透明、可持续性等方面的责任创新，以重建公众对数字化环境的信任。",SP-2016,76
129,"隐私影响评估
社会影响评估
物联网
隐私设计
数据最小化
透明性
责任创新
用户信任",隐私设计理念在IoT环境下存在哪些局限，如何改进？,From Privacy Impact Assessment to Social Impact Assessment,"本论文针对物联网（IoT）用户信任危机，提出将""隐私影响评估（PIA）""扩展为""社会影响评估（SIA）""，并主张在产品概念阶段及系统工程全流程中嵌入社会影响考量。作者分析了现有""隐私设计""理念的不足，强调需结合法律、社会和技术手段，推动IoT系统在数据最小化、安全、透明、可持续性等方面的责任创新，以重建公众对数字化环境的信任。",SP-2016,76
130,"隐私影响评估
社会影响评估
物联网
隐私设计
数据最小化
透明性
责任创新
用户信任",IoT系统中，如何平衡创新、合规与用户信任？,From Privacy Impact Assessment to Social Impact Assessment,"本论文针对物联网（IoT）用户信任危机，提出将""隐私影响评估（PIA）""扩展为""社会影响评估（SIA）""，并主张在产品概念阶段及系统工程全流程中嵌入社会影响考量。作者分析了现有""隐私设计""理念的不足，强调需结合法律、社会和技术手段，推动IoT系统在数据最小化、安全、透明、可持续性等方面的责任创新，以重建公众对数字化环境的信任。",SP-2016,76
131,"风险感知
移动安全
用户行为
信息安全
网络窃听
应用设计
安全可用性
用户调研",移动设备用户在敏感事务中主要关注哪些安全风险？,Perceptions of Risk in Mobile Transactions,本论文通过用户调研，分析了移动设备敏感事务中用户对信息安全风险的感知。结果显示，用户对网络窃听、肩窥等风险有一定认知，但对设备丢失、恶意软件等风险关注度较低。不同用户群体（IT员工、银行用户、医生）在风险认知和应对措施上存在差异。作者建议，移动应用设计应兼顾安全性与可用性，提升用户对实际风险的认知，并通过系统级防护弥补用户感知不足。,SP-2016,93
132,"风险感知
移动安全
用户行为
信息安全
网络窃听
应用设计
安全可用性
用户调研",如何提升移动应用在安全性与可用性之间的平衡？,Perceptions of Risk in Mobile Transactions,本论文通过用户调研，分析了移动设备敏感事务中用户对信息安全风险的感知。结果显示，用户对网络窃听、肩窥等风险有一定认知，但对设备丢失、恶意软件等风险关注度较低。不同用户群体（IT员工、银行用户、医生）在风险认知和应对措施上存在差异。作者建议，移动应用设计应兼顾安全性与可用性，提升用户对实际风险的认知，并通过系统级防护弥补用户感知不足。,SP-2016,93
133,"风险感知
移动安全
用户行为
信息安全
网络窃听
应用设计
安全可用性
用户调研",用户风险感知不足时，移动应用应采取哪些补救措施？,Perceptions of Risk in Mobile Transactions,本论文通过用户调研，分析了移动设备敏感事务中用户对信息安全风险的感知。结果显示，用户对网络窃听、肩窥等风险有一定认知，但对设备丢失、恶意软件等风险关注度较低。不同用户群体（IT员工、银行用户、医生）在风险认知和应对措施上存在差异。作者建议，移动应用设计应兼顾安全性与可用性，提升用户对实际风险的认知，并通过系统级防护弥补用户感知不足。,SP-2016,93
134,"说话人识别
对抗攻击
黑盒攻击
语音生物识别
机器学习安全
鲁棒性评估
防御机制
迁移性攻击",说话人识别系统在黑盒对抗攻击下存在哪些主要安全隐患？,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,本论文系统性研究了说话人识别系统（SRS）在实际黑盒环境下的对抗攻击，提出了FAKEBOB攻击方法，能够在开源和商业系统中实现高达99%的攻击成功率，并揭示了现有防御手段的局限性，强调了SRS安全性亟需提升。,SP-2021,18
135,"说话人识别
对抗攻击
黑盒攻击
语音生物识别
机器学习安全
鲁棒性评估
防御机制
迁移性攻击",如何提升语音生物识别系统对迁移性和物理世界攻击的鲁棒性？,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,本论文系统性研究了说话人识别系统（SRS）在实际黑盒环境下的对抗攻击，提出了FAKEBOB攻击方法，能够在开源和商业系统中实现高达99%的攻击成功率，并揭示了现有防御手段的局限性，强调了SRS安全性亟需提升。,SP-2021,18
136,"说话人识别
对抗攻击
黑盒攻击
语音生物识别
机器学习安全
鲁棒性评估
防御机制
迁移性攻击",现有对抗攻击防御方法在说话人识别系统中面临哪些挑战？,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,本论文系统性研究了说话人识别系统（SRS）在实际黑盒环境下的对抗攻击，提出了FAKEBOB攻击方法，能够在开源和商业系统中实现高达99%的攻击成功率，并揭示了现有防御手段的局限性，强调了SRS安全性亟需提升。,SP-2021,18
137,"电磁辐射
隐蔽信道
LoRa扩频
数据渗漏
物理安全
抗干扰
远距离攻击
防御检测",基于LoRa扩频的电磁隐蔽信道如何突破传统物理安全防护？,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,本论文提出了EMLoRa，一种利用内存电磁辐射实现LoRa扩频的超强隐蔽信道，首次实现了数百米远距离、穿透屏蔽的物理数据泄漏，揭示了传统电磁防护手段的不足。,SP-2021,87
138,"电磁辐射
隐蔽信道
LoRa扩频
数据渗漏
物理安全
抗干扰
远距离攻击
防御检测",EMLoRa在实际环境下的数据渗漏和设备定位能力表现如何？,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,本论文提出了EMLoRa，一种利用内存电磁辐射实现LoRa扩频的超强隐蔽信道，首次实现了数百米远距离、穿透屏蔽的物理数据泄漏，揭示了传统电磁防护手段的不足。,SP-2021,87
139,"电磁辐射
隐蔽信道
LoRa扩频
数据渗漏
物理安全
抗干扰
远距离攻击
防御检测",现有能量检测与CNN检测方法在EMLoRa信号识别上存在哪些局限？,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,本论文提出了EMLoRa，一种利用内存电磁辐射实现LoRa扩频的超强隐蔽信道，首次实现了数百米远距离、穿透屏蔽的物理数据泄漏，揭示了传统电磁防护手段的不足。,SP-2021,87
140,"AI代码生成
安全漏洞
自动化编程
弱点检测
软件工程
静态分析
安全实践
代码质量",AI代码生成工具在高风险安全弱点场景下存在哪些主要安全隐患？,Asleep at the Keyboard? Assessing the Security of GitHub Copilot's Code Contributions,本论文系统性评估了GitHub Copilot等AI代码生成工具在高风险安全弱点场景下的表现，发现约40%的生成代码存在安全漏洞，强调了AI辅助编程需配合安全检测与人工审查。,SP-2022,95
141,"AI代码生成
安全漏洞
自动化编程
弱点检测
软件工程
静态分析
安全实践
代码质量",影响AI代码生成工具安全性的主要上下文因素有哪些？,Asleep at the Keyboard? Assessing the Security of GitHub Copilot's Code Contributions,本论文系统性评估了GitHub Copilot等AI代码生成工具在高风险安全弱点场景下的表现，发现约40%的生成代码存在安全漏洞，强调了AI辅助编程需配合安全检测与人工审查。,SP-2022,95
142,"AI代码生成
安全漏洞
自动化编程
弱点检测
软件工程
静态分析
安全实践
代码质量",如何提升AI代码生成工具在多领域、多语言下的安全性与实用性？,Asleep at the Keyboard? Assessing the Security of GitHub Copilot's Code Contributions,本论文系统性评估了GitHub Copilot等AI代码生成工具在高风险安全弱点场景下的表现，发现约40%的生成代码存在安全漏洞，强调了AI辅助编程需配合安全检测与人工审查。,SP-2022,95
143,"内部威胁
冒名攻击
异常检测
图划分
马尔可夫聚类
文件系统行为
隐私保护
一类学习",如何利用图划分方法提升对内部冒名攻击的检测能力？,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,本论文提出了一种基于图划分的文件系统访问事件异常检测方法，能够有效识别冒名攻击者。通过将用户行为序列建模为有向加权图，并采用马尔可夫聚类算法提取行为模式，实现了对异常行为的高效检测。实验在WUIL和TWOs数据集上取得了高AUC和低误报率，验证了方法的有效性。,SP-2018,99
144,"内部威胁
冒名攻击
异常检测
图划分
马尔可夫聚类
文件系统行为
隐私保护
一类学习",文件系统访问日志在用户行为建模与安全检测中的作用有哪些？,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,本论文提出了一种基于图划分的文件系统访问事件异常检测方法，能够有效识别冒名攻击者。通过将用户行为序列建模为有向加权图，并采用马尔可夫聚类算法提取行为模式，实现了对异常行为的高效检测。实验在WUIL和TWOs数据集上取得了高AUC和低误报率，验证了方法的有效性。,SP-2018,99
145,"内部威胁
冒名攻击
异常检测
图划分
马尔可夫聚类
文件系统行为
隐私保护
一类学习",一类学习方法在安全异常检测领域有哪些优势？,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,本论文提出了一种基于图划分的文件系统访问事件异常检测方法，能够有效识别冒名攻击者。通过将用户行为序列建模为有向加权图，并采用马尔可夫聚类算法提取行为模式，实现了对异常行为的高效检测。实验在WUIL和TWOs数据集上取得了高AUC和低误报率，验证了方法的有效性。,SP-2018,99
146,"隐私风险评估
数据主体
威胁建模
GDPR
数据保护
风险分解
蒙特卡洛模拟
eHealth应用",如何在隐私威胁建模中实现以数据主体为中心的风险评估？,Privacy Risk Assessment for Data Subject-aware Threat Modeling,本论文提出了一种面向数据主体的隐私风险评估模型，结合GDPR等法规要求，将数据类型、数据主体类型、攻击者画像等多维因素纳入风险分解，并通过蒙特卡洛模拟实现量化评估。该模型已在eHealth应用中验证，提升了隐私威胁建模的合规性与精细化水平。,SP-2019,109
147,"隐私风险评估
数据主体
威胁建模
GDPR
数据保护
风险分解
蒙特卡洛模拟
eHealth应用",GDPR等法规对隐私风险评估方法提出了哪些新要求？,Privacy Risk Assessment for Data Subject-aware Threat Modeling,本论文提出了一种面向数据主体的隐私风险评估模型，结合GDPR等法规要求，将数据类型、数据主体类型、攻击者画像等多维因素纳入风险分解，并通过蒙特卡洛模拟实现量化评估。该模型已在eHealth应用中验证，提升了隐私威胁建模的合规性与精细化水平。,SP-2019,109
148,"隐私风险评估
数据主体
威胁建模
GDPR
数据保护
风险分解
蒙特卡洛模拟
eHealth应用",多维参数分解在隐私风险量化中的作用是什么？,Privacy Risk Assessment for Data Subject-aware Threat Modeling,本论文提出了一种面向数据主体的隐私风险评估模型，结合GDPR等法规要求，将数据类型、数据主体类型、攻击者画像等多维因素纳入风险分解，并通过蒙特卡洛模拟实现量化评估。该模型已在eHealth应用中验证，提升了隐私威胁建模的合规性与精细化水平。,SP-2019,109
149,"神经网络安全
后门攻击
模型可解释性
触发器逆向
防御机制
神经元剪枝
对抗样本检测
深度学习安全",深度神经网络在后门攻击下存在哪些主要安全隐患？,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,本论文提出了Neural Cleanse系统，首次实现了对深度神经网络后门攻击的通用检测与缓解。通过逆向工程重建触发器、神经元剪枝和对抗样本检测等方法，有效识别并消除模型中的后门威胁，提升了深度学习系统的安全性。,SP-2019,70
150,"神经网络安全
后门攻击
模型可解释性
触发器逆向
防御机制
神经元剪枝
对抗样本检测
深度学习安全",如何实现对深度神经网络后门攻击的自动化检测与缓解？,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,本论文提出了Neural Cleanse系统，首次实现了对深度神经网络后门攻击的通用检测与缓解。通过逆向工程重建触发器、神经元剪枝和对抗样本检测等方法，有效识别并消除模型中的后门威胁，提升了深度学习系统的安全性。,SP-2019,70
151,"神经网络安全
后门攻击
模型可解释性
触发器逆向
防御机制
神经元剪枝
对抗样本检测
深度学习安全",模型可解释性在神经网络安全防护中的作用有哪些？,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,本论文提出了Neural Cleanse系统，首次实现了对深度神经网络后门攻击的通用检测与缓解。通过逆向工程重建触发器、神经元剪枝和对抗样本检测等方法，有效识别并消除模型中的后门威胁，提升了深度学习系统的安全性。,SP-2019,70
152,"零知识证明
多项式委托
透明协议
后量子安全
高效验证
区块链隐私
可扩展性
密码学协议",如何实现无需可信设置的高效零知识证明协议？,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,本论文提出了一种无需可信设置的高效零知识证明协议（Virgo），基于透明多项式委托，支持分层算术电路的高效证明与验证。该方案仅依赖轻量级哈希等原语，具备后量子安全性，且在区块链、隐私计算等场景下具有良好扩展性。,SP-2020,100
153,"零知识证明
多项式委托
透明协议
后量子安全
高效验证
区块链隐私
可扩展性
密码学协议",透明多项式委托在区块链和隐私计算中的应用价值是什么？,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,本论文提出了一种无需可信设置的高效零知识证明协议（Virgo），基于透明多项式委托，支持分层算术电路的高效证明与验证。该方案仅依赖轻量级哈希等原语，具备后量子安全性，且在区块链、隐私计算等场景下具有良好扩展性。,SP-2020,100
154,"零知识证明
多项式委托
透明协议
后量子安全
高效验证
区块链隐私
可扩展性
密码学协议",后量子安全性在现代密码学协议设计中的意义？,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,本论文提出了一种无需可信设置的高效零知识证明协议（Virgo），基于透明多项式委托，支持分层算术电路的高效证明与验证。该方案仅依赖轻量级哈希等原语，具备后量子安全性，且在区块链、隐私计算等场景下具有良好扩展性。,SP-2020,100
155,"自动信任协商
访问控制策略
数字凭证
敏感信息保护
渐进信任建立
策略图
策略披露
安全通信",在开放系统中，如何通过自动信任协商实现对敏感访问控制策略的有效保护？,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,本文提出了两种自动信任协商策略，分别针对如何在不泄露敏感访问控制策略的前提下，实现陌生方之间的信任建立。通过引入策略图和渐进披露机制，兼顾了安全性与灵活性，提升了开放环境下的安全通信能力。,NDSS-2001,16
156,"自动信任协商
访问控制策略
数字凭证
敏感信息保护
渐进信任建立
策略图
策略披露
安全通信",数字凭证和策略图在多方信任协商中的作用体现在哪些方面？,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,本文提出了两种自动信任协商策略，分别针对如何在不泄露敏感访问控制策略的前提下，实现陌生方之间的信任建立。通过引入策略图和渐进披露机制，兼顾了安全性与灵活性，提升了开放环境下的安全通信能力。,NDSS-2001,16
157,"自动信任协商
访问控制策略
数字凭证
敏感信息保护
渐进信任建立
策略图
策略披露
安全通信",如何在保证安全性的同时，减少信任协商过程中凭证和策略的冗余披露？,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,本文提出了两种自动信任协商策略，分别针对如何在不泄露敏感访问控制策略的前提下，实现陌生方之间的信任建立。通过引入策略图和渐进披露机制，兼顾了安全性与灵活性，提升了开放环境下的安全通信能力。,NDSS-2001,16
158,"多安全域网络
联盟网络架构
访问控制
加密技术
虚拟专用网
多级安全
动态成员管理
军事通信",多安全域网络架构如何支持动态成员管理与多级安全需求？,An Architecture for Flexible Multi-Security Domain Networks,本文分析了美军及多国联盟网络的现有安全架构局限，提出了一种支持动态成员管理和多级安全的联盟网络架构。该架构结合对称与非对称加密、虚拟专用网和仲裁服务器，实现了灵活的访问控制和高效的安全通信。,NDSS-2001,8
159,"多安全域网络
联盟网络架构
访问控制
加密技术
虚拟专用网
多级安全
动态成员管理
军事通信",联盟网络中，虚拟专用网和仲裁服务器在安全隔离与权限分配中有何作用？,An Architecture for Flexible Multi-Security Domain Networks,本文分析了美军及多国联盟网络的现有安全架构局限，提出了一种支持动态成员管理和多级安全的联盟网络架构。该架构结合对称与非对称加密、虚拟专用网和仲裁服务器，实现了灵活的访问控制和高效的安全通信。,NDSS-2001,8
160,"多安全域网络
联盟网络架构
访问控制
加密技术
虚拟专用网
多级安全
动态成员管理
军事通信",在多国军事通信环境下，如何实现不同安全级别和社区的高效互操作？,An Architecture for Flexible Multi-Security Domain Networks,本文分析了美军及多国联盟网络的现有安全架构局限，提出了一种支持动态成员管理和多级安全的联盟网络架构。该架构结合对称与非对称加密、虚拟专用网和仲裁服务器，实现了灵活的访问控制和高效的安全通信。,NDSS-2001,8
161,"跨站脚本攻击
动态污点分析
静态分析
Web安全
浏览器安全
信息流追踪
客户端防护
敏感数据保护",如何通过动态污点分析和静态分析提升Web浏览器对跨站脚本攻击的防护能力？,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,本文提出了一种结合动态污点分析与静态分析的浏览器端XSS防护机制，能够实时追踪敏感信息流动，阻止敏感数据被恶意脚本泄露到第三方，有效提升了Web用户的安全性。,NDSS-2007,15
162,"跨站脚本攻击
动态污点分析
静态分析
Web安全
浏览器安全
信息流追踪
客户端防护
敏感数据保护",信息流追踪技术在防范Web应用敏感数据泄露中的作用有哪些？,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,本文提出了一种结合动态污点分析与静态分析的浏览器端XSS防护机制，能够实时追踪敏感信息流动，阻止敏感数据被恶意脚本泄露到第三方，有效提升了Web用户的安全性。,NDSS-2007,15
163,"跨站脚本攻击
动态污点分析
静态分析
Web安全
浏览器安全
信息流追踪
客户端防护
敏感数据保护",在实际Web环境中，如何平衡XSS防护的安全性与用户体验？,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,本文提出了一种结合动态污点分析与静态分析的浏览器端XSS防护机制，能够实时追踪敏感信息流动，阻止敏感数据被恶意脚本泄露到第三方，有效提升了Web用户的安全性。,NDSS-2007,15
164,"Sybil攻击
社交网络安全
伪造身份检测
潜在社区模型
生成式模型
贝叶斯推断
Gibbs采样
网络结构分析
自动化检测
算法复杂度",如何利用潜在社区模型提升社交网络中Sybil攻击的检测能力？,The Latent Community Model for Detecting Sybil Attacks,本文提出了一种基于潜在社区（LC）生成式模型的Sybil攻击检测方法，通过对社交网络结构的统计建模，能够自动识别伪造身份群体。该方法结合贝叶斯推断和Gibbs采样，有效提升了对复杂网络中Sybil攻击的检测准确性。,NDSS-2012,8
165,"Sybil攻击
社交网络安全
伪造身份检测
潜在社区模型
生成式模型
贝叶斯推断
Gibbs采样
网络结构分析
自动化检测
算法复杂度",贝叶斯推断和Gibbs采样在网络安全建模中的作用有哪些？,The Latent Community Model for Detecting Sybil Attacks,该论文将贝叶斯推断和Gibbs采样方法应用于社交网络的潜在社区建模，实现了对节点属性和社区结构的联合推断。此方法不仅提升了Sybil检测的自动化程度，还为大规模网络安全分析提供了理论基础。,NDSS-2012,8
166,"Sybil攻击
社交网络安全
伪造身份检测
潜在社区模型
生成式模型
贝叶斯推断
Gibbs采样
网络结构分析
自动化检测
算法复杂度",在大规模网络环境下，如何平衡Sybil检测算法的准确性与计算复杂度？,The Latent Community Model for Detecting Sybil Attacks,该方法通过潜在社区模型实现了对大规模网络中Sybil攻击的高效检测，但在处理树状或稀疏攻击结构时存在一定局限。论文讨论了算法复杂度与检测效果的权衡，并指出未来可在分布式环境和非社交网络场景下进一步扩展。,NDSS-2012,8
167,"社交网络安全
大规模数据防护
创新安全机制
实时威胁检测
自动化防御系统
用户教育
内容筛查
安全策略
机器学习
安全文化",如何在大规模社交网络中实现实时威胁检测与自动化防御？,Innovating to Protect the Graph (Facebook Security),本文介绍了Facebook在大规模社交网络环境下，通过创新安全机制和自动化防御系统，实现了对不断演化的安全威胁的实时检测与响应。系统集成了机器学习、用户教育、内容筛查等多种手段，显著提升了平台的整体安全性。,NDSS-2013,46
168,"社交网络安全
大规模数据防护
创新安全机制
实时威胁检测
自动化防御系统
用户教育
内容筛查
安全策略
机器学习
安全文化",机器学习和用户教育在社交平台安全防护中的作用有哪些？,Innovating to Protect the Graph (Facebook Security),Facebook安全体系结合了机器学习算法（如随机森林、SVM、逻辑回归等）与用户教育、社会化举报机制，实现了垃圾信息、恶意登录、恶意内容的高效识别与拦截。用户教育和社会化举报提升了整体防护能力和用户安全意识。,NDSS-2013,46
169,"社交网络安全
大规模数据防护
创新安全机制
实时威胁检测
自动化防御系统
用户教育
内容筛查
安全策略
机器学习
安全文化",在快速变化的互联网环境下，如何构建可持续创新的安全文化？,Innovating to Protect the Graph (Facebook Security),"Facebook强调安全团队与产品团队协同创新，倡导""快速迭代、主动防御""的安全文化。通过漏洞赏金、攻防演练、跨行业合作等方式，持续提升安全能力，确保平台在面对新型威胁时具备快速响应和自我进化能力。",NDSS-2013,46
170,"虚拟机内省
语义鸿沟
二进制代码复用
动态污点分析
内核数据重定向
性能优化
云安全监控
动态补丁
元数据缓存
混合虚拟化",如何通过混合虚拟化和元数据缓存提升虚拟机内省的实时性与性能？,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,本文提出了HYBRID-BRIDGE系统，结合离线训练与在线内核数据重定向，通过元数据缓存和动态补丁机制，实现了虚拟机内省的高效语义还原。该方法显著降低了性能开销，适用于云环境下大规模实时安全监控。,NDSS-2014,40
171,"虚拟机内省
语义鸿沟
二进制代码复用
动态污点分析
内核数据重定向
性能优化
云安全监控
动态补丁
元数据缓存
混合虚拟化",动态污点分析与内核数据重定向在提升VMI安全性和兼容性方面有何作用？,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,HYBRID-BRIDGE通过将动态污点分析与内核数据重定向解耦，既保证了语义还原的完整性，又大幅提升了系统兼容性和运行效率。该机制支持多种内核版本和复杂场景下的安全监控。,NDSS-2014,40
172,"虚拟机内省
语义鸿沟
二进制代码复用
动态污点分析
内核数据重定向
性能优化
云安全监控
动态补丁
元数据缓存
混合虚拟化",在云服务环境下，如何实现对大规模虚拟机的高效安全监控与异常检测？,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,HYBRID-BRIDGE系统通过混合虚拟化和动态元数据补全机制，实现了对大规模云虚拟机的高效安全监控与异常检测，兼顾了实时性、准确性与系统资源消耗的平衡。,NDSS-2014,40
173,"Android安全
SSL/TLS中间人攻击
证书验证
静态分析
动态分析
自动化检测
UI自动化
大规模应用分析
输入生成
移动应用安全",如何通过静态与动态分析结合实现大规模Android应用SSL/TLS中间人漏洞的自动化检测？,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,SMV-HUNTER系统结合静态分析与动态UI自动化，能够在无需人工干预的情况下，自动检测大规模Android应用中的SSL/TLS中间人攻击漏洞，极大提升了检测效率与准确性。,NDSS-2014,43
174,"Android安全
SSL/TLS中间人攻击
证书验证
静态分析
动态分析
自动化检测
UI自动化
大规模应用分析
输入生成
移动应用安全",UI自动化与智能输入生成在移动应用安全检测中的作用有哪些？,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,SMV-HUNTER通过UI自动化和智能输入生成技术，能够自动遍历应用界面并触发潜在漏洞路径，实现对复杂交互场景下安全问题的高效发现。,NDSS-2014,43
175,"Android安全
SSL/TLS中间人攻击
证书验证
静态分析
动态分析
自动化检测
UI自动化
大规模应用分析
输入生成
移动应用安全",在移动互联网环境下，如何提升第三方应用对SSL/TLS证书验证的安全性？,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,SMV-HUNTER揭示了大量Android应用在自定义证书验证实现中的安全隐患，强调了标准化验证流程和自动化检测工具在提升移动应用SSL/TLS安全性方面的重要性。,NDSS-2014,43
176,"P2P混合
匿名通信协议
比特币隐私
去中心化混币
DC-net
抗干扰机制
区块链安全
协议终止性
消息去关联
高效实现",如何通过P2P混合协议提升区块链系统中交易的匿名性与去关联性？,P2P Mixing and Unlinkable Bitcoin Transactions,本文提出了DiceMix协议，一种高效的P2P匿名混合协议，显著提升了比特币等区块链系统中交易的匿名性和去关联性。DiceMix通过创新的抗干扰机制和最优通信轮次设计，实现了无需第三方的高效混币，兼顾协议终止性与安全性，适用于大规模实际部署。,NDSS-2017,52
177,"P2P混合
匿名通信协议
比特币隐私
去中心化混币
DC-net
抗干扰机制
区块链安全
协议终止性
消息去关联
高效实现",DiceMix协议在应对恶意节点干扰和保障协议终止性方面有哪些创新？,P2P Mixing and Unlinkable Bitcoin Transactions,DiceMix协议通过引入并发运行、密钥揭示与恶意节点逐步剔除机制，实现了在存在恶意节点时的协议终止性保障。其抗干扰设计确保每轮失败后都能识别并排除恶意节点，最终实现所有诚实节点的匿名混合，提升了协议的健壮性与实用性。,NDSS-2017,52
178,"P2P混合
匿名通信协议
比特币隐私
去中心化混币
DC-net
抗干扰机制
区块链安全
协议终止性
消息去关联
高效实现",在实际区块链应用中，如何平衡匿名性、效率与系统兼容性？,P2P Mixing and Unlinkable Bitcoin Transactions,该论文提出的CoinShuffle++协议基于DiceMix，兼容现有比特币系统，无需更改底层协议即可实现高效混币。实验表明，50人参与的混币仅需8秒，显著优于以往方案，兼顾了匿名性、效率与系统兼容性，适合大规模实际应用。,NDSS-2017,52
179,"ARM架构
特权分离
虚拟地址调整
内存隔离
安全硬件
AArch64
内核安全
系统软件
性能评估
安全机制创新",如何利用ARM 64位架构的新硬件特性实现系统软件的高效特权分离？,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,本文提出了Hilps技术，基于ARM AArch64架构的TxSZ硬件特性，实现了系统软件各级别的高效特权分离。通过动态调整虚拟地址范围，Hilps可在同一特权级别下实现内存隔离，保护内核、虚拟机监控器及安全OS等关键组件，且性能开销极低，适用于实际部署。,NDSS-2017,6
180,"ARM架构
特权分离
虚拟地址调整
内存隔离
安全硬件
AArch64
内核安全
系统软件
性能评估
安全机制创新",Hilps技术在ARM系统中实现特权分离时，如何兼顾安全性与系统性能？,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,Hilps通过动态调整TxSZ寄存器，实现内外域的虚拟地址空间隔离，内域拥有对全部内存的访问权限，外域受限。实验表明，Hilps在多种基准测试下仅带来不到1%的系统开销，兼顾了高安全性与优异性能，适合移动与嵌入式设备。,NDSS-2017,6
181,"ARM架构
特权分离
虚拟地址调整
内存隔离
安全硬件
AArch64
内核安全
系统软件
性能评估
安全机制创新",在多级系统软件共存环境下，如何实现各级别的独立安全隔离？,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,该论文提出的Hilps机制可应用于ARM 64位架构下的普通OS、虚拟机监控器和安全OS，实现各级别系统软件的独立隔离与安全监控。通过内外域切换与内存映射策略，Hilps为多级系统环境提供了统一、高效的安全隔离解决方案。,NDSS-2017,6
182,"云安全
域名验证证书
DNS陈旧记录
IP地址复用
SSL证书滥用
自动化攻击
证书透明性
云服务弹性
安全最佳实践
防御机制",云服务环境下，如何防范因DNS陈旧记录和IP地址复用导致的域名接管与证书滥用风险？,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,本文系统分析了云计算环境中因DNS陈旧记录和IP地址复用导致的域名接管攻击，揭示了攻击者可通过分配云中已释放IP并利用自动化域名验证机制获取受信任SSL证书，进而实施钓鱼、流量劫持等攻击。论文提出了基于证书透明性和信任链的防御机制，并为云用户和运营商提供了安全最佳实践建议。,NDSS-2018,13
183,"云安全
域名验证证书
DNS陈旧记录
IP地址复用
SSL证书滥用
自动化攻击
证书透明性
云服务弹性
安全最佳实践
防御机制",自动化证书颁发流程中，哪些环节最易被攻击者利用实现域名接管？,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,论文指出，自动化证书颁发流程（如ACME协议）在未充分校验DNS记录新鲜度和IP归属时，易被攻击者通过复用云中释放的IP地址实现域名接管。攻击者可在极短时间内获取受信任证书，利用浏览器信任链实施中间人攻击和钓鱼等威胁。,NDSS-2018,13
184,"云安全
域名验证证书
DNS陈旧记录
IP地址复用
SSL证书滥用
自动化攻击
证书透明性
云服务弹性
安全最佳实践
防御机制",针对云环境下的域名接管威胁，如何设计高效且易部署的防御机制？,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,论文提出了一种基于历史证书信任链的验证机制，要求新证书申请需证明对既有证书的连续控制权，结合证书透明性日志和多重验证挑战，有效防止因DNS陈旧和IP复用导致的域名接管。该机制兼顾安全性与自动化部署需求，适合大规模云服务环境推广。,NDSS-2018,13
185,"自动化补丁生成
开源软件安全
移动应用安全
二进制补丁
变体分析
函数级修复
配置推断
内存热补丁
Android安全
漏洞检测",如何实现对移动应用中开源组件的自动化二进制补丁？,Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,本论文提出了OSSPATCHER系统，实现了对移动应用中集成的有漏洞开源组件的自动化二进制补丁。系统通过源代码补丁自动生成二进制补丁并注入应用，结合变体分析、配置推断和函数级修复，解决了配置多样性、符号缺失、静态链接等难题。实验表明，OSSPATCHER可在无需开发者参与的情况下，自动修复大规模Android应用中的安全漏洞，且对性能影响极小，具有良好的实际应用价值。,NDSS-2019,26
186,"数据完整性证明
协作式自主系统
嵌入式安全
控制流证明
模块化度量
无人机安全
物联网安全
远程认证
软硬件隔离
高可扩展性",在协作式自主系统中，如何实现高效且可扩展的数据完整性证明？,DIAT: Data Integrity Attestation for Resilient Collaboration of Autonomous Systems,本论文提出了DIAT方案，实现了协作式自主系统中数据完整性的高效证明。DIAT通过模块化度量、数据流监控和控制流证明，结合轻量级硬件安全架构，实现了对嵌入式设备间数据生成、处理和传输全过程的可信认证。系统在无人机飞控平台上实现，实验和仿真结果表明DIAT具备高可扩展性、低性能开销和强安全性，适用于物联网、无人机等大规模协作场景。,NDSS-2019,3
187,"缩略图保留加密
图像隐私保护
云存储安全
格式保持加密
可用性与隐私平衡
人机识别
加密算法
用户体验
图像浏览
安全评估",如何通过缩略图保留加密（TPE）在云端实现图像隐私与可用性的平衡？,Balancing Image Privacy and Usability with Thumbnail-Preserving Encryption,本论文提出了缩略图保留加密（TPE）方案，实现了在云端存储和管理图片时，既保护用户隐私又不影响图片浏览体验。TPE通过加密算法仅保留图片缩略图信息，云服务商无法访问原始内容。论文系统分析了TPE的安全性、性能和用户体验，实验表明TPE在缩略图分辨率可调的前提下，能有效防止AI识别和隐私泄露，同时保证用户高效管理和识别图片。该方案为云端图像隐私保护提供了新的思路和实用工具。,NDSS-2019,72
188,"算法复杂性漏洞
拒绝服务攻击
Java安全
模糊测试
遗传算法
自动化漏洞检测
输入生成
资源消耗
大数据分析
开源库安全",如何通过自动化模糊测试发现Java库中的算法复杂性漏洞？,HotFuzz: Discovering Algorithmic Denial-of-Service Vulnerabilities Through Guided Micro-Fuzzing,本论文提出了HotFuzz系统，通过微模糊测试和遗传算法，自动化发现Java库中的算法复杂性（AC）漏洞。系统可自动为每个方法生成测试用例，监控资源消耗，识别潜在拒绝服务风险。实验证明，HotFuzz在JRE和主流Maven库中发现了大量未知AC漏洞，包括BigDecimal、正则表达式等关键组件，推动了自动化安全检测技术的发展。,NDSS-2020,17
189,"反骚扰电话
用户界面设计
安全警示
行为实验
来电认证
人机交互
风险感知
移动应用安全
黑名单机制
用户决策",反骚扰电话应用的界面设计如何影响用户对风险的感知与应对？,Are You Going to Answer That? Measuring User Responses to Anti-Robocall Application Indicators,本论文系统分析了反骚扰电话应用的界面设计对用户风险感知和行为的影响。通过对主流应用的界面元素梳理、用户访谈和行为实验，发现警示颜色、图标、来电认证等设计能有效引导用户决策，降低骚扰电话接听率。论文还揭示了攻击者可通过伪造号码绕过部分防护，强调多重机制和持续优化界面设计对提升移动通信安全的重要意义。,NDSS-2020,62
190,"Iago攻击
遗留代码安全
系统调用模糊测试
受信任执行环境（TEE）
操作系统安全
漏洞检测工具
内存破坏
接口防护
SGX安全
自动化分析",如何通过系统调用返回值模糊测试发现遗留代码中的Iago攻击漏洞？,Emilia: Catching Iago in Legacy Code,本论文提出了Emilia工具，通过系统调用返回值模糊测试，自动化发现遗留代码在受信任执行环境（如SGX）下的Iago攻击漏洞。研究发现，Iago漏洞在主流应用和库中普遍存在，82.4%可通过简单接口检查缓解，但现有接口转发层防护不完善。论文建议结合自动化检测与接口防护，提升TEE环境下遗留代码的安全性。,NDSS-2021,8
191,"5G安全测距
无线定位
距离操控攻击
OFDM符号
物理层安全
信号完整性
毫米波通信
车联网安全
时间同步
高精度定位",5G网络中如何实现对距离操控攻击的高效防护？,V-Range: Enabling Secure Ranging in 5G Wireless Networks,本论文提出了V-Range系统，实现了5G无线网络下的安全测距。V-Range通过缩短OFDM符号长度、能量聚合和多层信号与数据完整性校验，有效防御距离缩短、放大及载波频偏等多种攻击。系统兼容5G标准，支持亚6GHz和毫米波频段，实验表明其测距误差极低，攻击检测率高，适用于车联网、智能制造等安全与精度要求极高的场景。论文还分析了V-Range在实际部署中的参数选择、信道影响及与现有标准的兼容性，为5G定位安全提供了创新方案。,NDSS-2022,57
192,"P2P网络
加密货币
匿名性
Dandelion协议
Dandelion++
Lightning Network
贝叶斯推断
协作节点
网络安全
隐私保护",加密货币P2P网络中常见的匿名性协议面临哪些主要安全挑战？,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,本论文系统分析了加密货币P2P网络中主流匿名性协议（如Dandelion、Dandelion++、Lightning Network）的安全性，提出了基于贝叶斯推断的匿名性评估框架。研究发现，现有协议在协作攻击下匿名性有限，提出了改进建议，为未来加密货币网络的隐私保护提供了理论和实践参考。,NDSS-2023,78
193,"P2P网络
加密货币
匿名性
Dandelion协议
Dandelion++
Lightning Network
贝叶斯推断
协作节点
网络安全
隐私保护",贝叶斯推断方法在评估P2P匿名通信协议安全性方面有哪些应用？,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,本论文系统分析了加密货币P2P网络中主流匿名性协议（如Dandelion、Dandelion++、Lightning Network）的安全性，提出了基于贝叶斯推断的匿名性评估框架。研究发现，现有协议在协作攻击下匿名性有限，提出了改进建议，为未来加密货币网络的隐私保护提供了理论和实践参考。,NDSS-2023,78
194,"P2P网络
加密货币
匿名性
Dandelion协议
Dandelion++
Lightning Network
贝叶斯推断
协作节点
网络安全
隐私保护",提升加密货币P2P网络匿名性时应重点关注哪些设计要素？,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,本论文系统分析了加密货币P2P网络中主流匿名性协议（如Dandelion、Dandelion++、Lightning Network）的安全性，提出了基于贝叶斯推断的匿名性评估框架。研究发现，现有协议在协作攻击下匿名性有限，提出了改进建议，为未来加密货币网络的隐私保护提供了理论和实践参考。,NDSS-2023,78
195,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",分布式系统中如何实现跨物理主机的统一安全策略强制？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
196,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",如何利用可信硬件和虚拟机技术提升大规模分布式环境的安全性与可扩展性？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
197,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",动态联盟环境下，安全策略验证与信任管理面临哪些关键挑战？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
198,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",电子投票系统中，如何实现无需外部设备的人机可验证软件完整性？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
199,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",现有软件自证明技术在防范电子投票机软件篡改方面存在哪些局限？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
200,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",处理器架构发展对软件完整性验证技术的安全性有何影响？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
201,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",基于流量特征的僵尸网络C&C检测中，时序特征校准对分类器性能有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
202,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",在网络流量分类中，特征工程与数据集混合策略如何影响恶意流量检测的泛化能力？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
203,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",跨网络环境迁移时，流量分类器对时序特征分布变化的鲁棒性如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
204,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",如何通过时序特征校准提升流量分类器对僵尸网络C&C流量的检测准确性？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
205,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",流量分类器在不同特征处理和数据集混合策略下的泛化能力如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
206,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",时序特征伪迹对网络流量安全检测的误报与漏报有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
207,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",能量比例计算趋势下，功耗侧信道对计算活动信息泄漏的风险有哪些新变化？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
208,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",如何利用全系统功耗分析提升嵌入式和医疗设备的恶意软件检测能力？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
209,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",功耗特征工程与机器学习方法在功耗侧信道攻击与防护中有哪些应用前景？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
210,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",IPMI远程管理接口在服务器安全体系中存在哪些主要攻击面及其风险？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
211,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",服务器BMC固件中常见的安全漏洞类型及其利用方式有哪些？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
212,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",如何通过网络隔离和固件更新等手段提升IPMI设备的安全防护能力？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
213,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",k-匿名性通信系统中对称披露机制如何缓解社交关系稀疏带来的带宽与隐私权衡？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
214,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",社交网络中k-匿名性分组策略对通信隐私与系统效率有何影响？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
215,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",如何通过调整匿名集规模与分组方式提升k-匿名性通信系统的抗流量分析能力？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
216,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络如何通过同态加密与私有集合交集协议实现隐私友好的好友发现？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
217,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络在消息同步与NAT穿透方面面临哪些关键技术挑战？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
218,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",如何通过分布式密钥管理与消息副本机制提升社交网络的隐私与可用性？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
219,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何通过网络攻防竞赛设计提升学生在渗透测试中的枢纽攻击与防御能力？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
220,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",攻防竞赛中植入程序开发与多阶段攻击策略对网络安全教育有何促进作用？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
221,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何平衡攻防竞赛中的进攻性实践与安全防御技能培养，促进网络安全人才成长？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
222,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",大规模取证分析中如何通过依赖关系保持的数据压缩技术提升日志处理效率与准确性？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
223,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",依赖图优化与版本化建模在提升安全事件溯源能力方面有哪些关键作用？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
224,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",如何在不损失取证分析准确性的前提下，实现跨平台大规模系统日志的高效压缩与快速检索？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
225,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",协作程序模型下，如何通过委托图机制实现对传感器访问的精细化授权与追踪？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
226,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",移动操作系统中，委托事件追踪与用户授权结合能否有效防止系统服务权限被恶意程序滥用？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
227,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",EnTrust系统在提升用户隐私保护与操作系统兼容性方面有哪些创新机制与实验结果？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
228,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",分布式系统中如何实现跨物理主机的统一安全策略强制？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
229,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",如何利用可信硬件和虚拟机技术提升大规模分布式环境的安全性与可扩展性？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
230,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",动态联盟环境下，安全策略验证与信任管理面临哪些关键挑战？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
231,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",电子投票系统中，如何实现无需外部设备的人机可验证软件完整性？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
232,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",现有软件自证明技术在防范电子投票机软件篡改方面存在哪些局限？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
233,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",处理器架构发展对软件完整性验证技术的安全性有何影响？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
234,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",基于流量特征的僵尸网络C&C检测中，时序特征校准对分类器性能有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
235,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",在网络流量分类中，特征工程与数据集混合策略如何影响恶意流量检测的泛化能力？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
236,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",跨网络环境迁移时，流量分类器对时序特征分布变化的鲁棒性如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
237,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",如何通过时序特征校准提升流量分类器对僵尸网络C&C流量的检测准确性？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
238,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",流量分类器在不同特征处理和数据集混合策略下的泛化能力如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
239,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",时序特征伪迹对网络流量安全检测的误报与漏报有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
240,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",能量比例计算趋势下，功耗侧信道对计算活动信息泄漏的风险有哪些新变化？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
241,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",如何利用全系统功耗分析提升嵌入式和医疗设备的恶意软件检测能力？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
242,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",功耗特征工程与机器学习方法在功耗侧信道攻击与防护中有哪些应用前景？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
243,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",IPMI远程管理接口在服务器安全体系中存在哪些主要攻击面及其风险？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
244,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",服务器BMC固件中常见的安全漏洞类型及其利用方式有哪些？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
245,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",如何通过网络隔离和固件更新等手段提升IPMI设备的安全防护能力？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
246,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",k-匿名性通信系统中对称披露机制如何缓解社交关系稀疏带来的带宽与隐私权衡？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
247,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",社交网络中k-匿名性分组策略对通信隐私与系统效率有何影响？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
248,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",如何通过调整匿名集规模与分组方式提升k-匿名性通信系统的抗流量分析能力？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
249,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络如何通过同态加密与私有集合交集协议实现隐私友好的好友发现？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
250,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络在消息同步与NAT穿透方面面临哪些关键技术挑战？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
251,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",如何通过分布式密钥管理与消息副本机制提升社交网络的隐私与可用性？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
252,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何通过网络攻防竞赛设计提升学生在渗透测试中的枢纽攻击与防御能力？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
253,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",攻防竞赛中植入程序开发与多阶段攻击策略对网络安全教育有何促进作用？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
254,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何平衡攻防竞赛中的进攻性实践与安全防御技能培养，促进网络安全人才成长？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
255,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",大规模取证分析中如何通过依赖关系保持的数据压缩技术提升日志处理效率与准确性？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
256,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",依赖图优化与版本化建模在提升安全事件溯源能力方面有哪些关键作用？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
257,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",如何在不损失取证分析准确性的前提下，实现跨平台大规模系统日志的高效压缩与快速检索？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
258,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",协作程序模型下，如何通过委托图机制实现对传感器访问的精细化授权与追踪？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
259,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",移动操作系统中，委托事件追踪与用户授权结合能否有效防止系统服务权限被恶意程序滥用？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
260,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",EnTrust系统在提升用户隐私保护与操作系统兼容性方面有哪些创新机制与实验结果？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
261,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",分布式系统中如何实现跨物理主机的统一安全策略强制？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
262,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",如何利用可信硬件和虚拟机技术提升大规模分布式环境的安全性与可扩展性？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
263,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",动态联盟环境下，安全策略验证与信任管理面临哪些关键挑战？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
264,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",电子投票系统中，如何实现无需外部设备的人机可验证软件完整性？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
265,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",现有软件自证明技术在防范电子投票机软件篡改方面存在哪些局限？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
266,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",处理器架构发展对软件完整性验证技术的安全性有何影响？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
267,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",基于流量特征的僵尸网络C&C检测中，时序特征校准对分类器性能有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
268,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",在网络流量分类中，特征工程与数据集混合策略如何影响恶意流量检测的泛化能力？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
269,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",跨网络环境迁移时，流量分类器对时序特征分布变化的鲁棒性如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
270,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",如何通过时序特征校准提升流量分类器对僵尸网络C&C流量的检测准确性？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
271,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",流量分类器在不同特征处理和数据集混合策略下的泛化能力如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
272,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",时序特征伪迹对网络流量安全检测的误报与漏报有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
273,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",能量比例计算趋势下，功耗侧信道对计算活动信息泄漏的风险有哪些新变化？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
274,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",如何利用全系统功耗分析提升嵌入式和医疗设备的恶意软件检测能力？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
275,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",功耗特征工程与机器学习方法在功耗侧信道攻击与防护中有哪些应用前景？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
276,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",IPMI远程管理接口在服务器安全体系中存在哪些主要攻击面及其风险？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
277,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",服务器BMC固件中常见的安全漏洞类型及其利用方式有哪些？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
278,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",如何通过网络隔离和固件更新等手段提升IPMI设备的安全防护能力？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
279,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",k-匿名性通信系统中对称披露机制如何缓解社交关系稀疏带来的带宽与隐私权衡？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
280,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",社交网络中k-匿名性分组策略对通信隐私与系统效率有何影响？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
281,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",如何通过调整匿名集规模与分组方式提升k-匿名性通信系统的抗流量分析能力？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
282,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络如何通过同态加密与私有集合交集协议实现隐私友好的好友发现？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
283,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络在消息同步与NAT穿透方面面临哪些关键技术挑战？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
284,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",如何通过分布式密钥管理与消息副本机制提升社交网络的隐私与可用性？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
285,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何通过网络攻防竞赛设计提升学生在渗透测试中的枢纽攻击与防御能力？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
286,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",攻防竞赛中植入程序开发与多阶段攻击策略对网络安全教育有何促进作用？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
287,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何平衡攻防竞赛中的进攻性实践与安全防御技能培养，促进网络安全人才成长？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
288,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",大规模取证分析中如何通过依赖关系保持的数据压缩技术提升日志处理效率与准确性？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
289,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",依赖图优化与版本化建模在提升安全事件溯源能力方面有哪些关键作用？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
290,"取证分析
系统日志压缩
依赖关系保持
事件归约
图优化
大规模数据处理
信息流追踪
版本化建模
安全溯源
实验评估",如何在不损失取证分析准确性的前提下，实现跨平台大规模系统日志的高效压缩与快速检索？,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,本文提出了依赖关系保持的数据压缩方法，实现了对大规模系统审计日志的高效归约。通过全局依赖图建模与多种优化算法，显著减少了日志存储与分析开销，同时保证了取证分析的准确性。实验表明，该方法可将日志体积缩减35倍以上，且不影响溯源结果。,USENIX_Security-2018,41
291,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",协作程序模型下，如何通过委托图机制实现对传感器访问的精细化授权与追踪？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
292,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",移动操作系统中，委托事件追踪与用户授权结合能否有效防止系统服务权限被恶意程序滥用？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
293,"传感器访问控制
委托图授权
协作程序安全
Android权限管理
输入事件追踪
系统服务滥用
用户授权机制
隐私保护
跨程序攻击
实验评估",EnTrust系统在提升用户隐私保护与操作系统兼容性方面有哪些创新机制与实验结果？,EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,本文提出EnTrust授权系统，通过构建委托图，将输入事件、程序间委托与传感器操作关联，实现对协作程序环境下传感器访问的精细化授权。EnTrust可有效防御混淆代理、特洛伊木马及中间人攻击，实验显示其在提升攻击检测率、降低用户授权负担和系统开销方面均优于传统首次授权机制。,USENIX_Security-2019,70
294,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",分布式系统中如何实现跨物理主机的统一安全策略强制？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
295,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",如何利用可信硬件和虚拟机技术提升大规模分布式环境的安全性与可扩展性？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
296,"分布式系统安全
可信计算基础
虚拟机监控
强制访问控制
安全策略验证
远程认证
动态联盟
安全硬件
策略简化
信任逻辑",动态联盟环境下，安全策略验证与信任管理面临哪些关键挑战？,Shame on Trust in Distributed Systems,本文提出了Shamon（共享参考监控器）概念，旨在为大规模分布式系统提供可验证、可扩展的安全策略强制基础。通过结合可信计算硬件、虚拟机监控和简化的强制访问控制策略，Shamon实现了跨物理主机的安全目标一致性与动态联盟管理。论文分析了现有分布式系统信任基础的局限，提出了基于硬件凭证和远程认证的底层信任机制，并探讨了策略验证、动态成员管理和信任逻辑等关键技术挑战，为互联网级分布式系统的安全授权提供了新思路。,USENIX_Security-2006,28
297,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",电子投票系统中，如何实现无需外部设备的人机可验证软件完整性？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
298,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",现有软件自证明技术在防范电子投票机软件篡改方面存在哪些局限？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
299,"电子投票安全
软件自证明
完整性验证
人机可验证
引导加载器
软件篡改检测
硬件信任根
时间测量
攻击分析
系统可用性",处理器架构发展对软件完整性验证技术的安全性有何影响？,On the Difficulty of Validating Voting Machine Software with Software,本文研究了基于Pioneer框架的人机可验证软件自证明机制，发现现有软件自证明技术难以为电子投票机提供实际可用的完整性保障。作者设计并实现了基于Pioneer的自证明投票机，并对其代码进行了修正和安全增强，集成到GRUB引导加载器，实现了对投票软件及操作系统的认证加载。通过实测攻击，证明该方案在当前技术条件下不具备实际可行性，并指出随着处理器并行化发展，攻击将更易实现。论文为电子投票系统的完整性验证和未来改进方向提供了重要参考。,USENIX_Security-2007,46
300,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",基于流量特征的僵尸网络C&C检测中，时序特征校准对分类器性能有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
301,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",在网络流量分类中，特征工程与数据集混合策略如何影响恶意流量检测的泛化能力？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
302,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域适应性",跨网络环境迁移时，流量分类器对时序特征分布变化的鲁棒性如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种通过对模拟僵尸网络C&C流量进行时序特征校准后混入真实网络流量的方法，系统评估了不同校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪特征会显著影响分类器的精度与误报率，部分模型对时序特征敏感，校准后检测性能下降。研究揭示了流量特征工程与数据集构建对安全检测评估的关键影响，为实际网络环境下的恶意流量检测方法提供了参考。,USENIX_Security-2011,85
303,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",如何通过时序特征校准提升流量分类器对僵尸网络C&C流量的检测准确性？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
304,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",流量分类器在不同特征处理和数据集混合策略下的泛化能力如何评估？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
305,"流量分类
僵尸网络检测
命令与控制流量
时序特征校准
机器学习
数据集混合
特征工程
网络安全评估
误报分析
跨域泛化",时序特征伪迹对网络流量安全检测的误报与漏报有何影响？,Salting Public Traces with Attack Traffic to Test Flow Classifiers,本文提出了一种将时序校准后的僵尸网络C&C流量注入公开背景流量的方法，系统评估了不同特征处理和校准策略下多种机器学习流量分类器对C&C流量的检测效果。实验发现，C&C流量的时序伪迹会显著影响分类器的检测结果，部分模型对时序特征敏感，导致误报和漏报率变化。论文为流量分类器的特征工程、跨域泛化和安全评估提供了实证依据。,USENIX_Security-2011,85
306,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",能量比例计算趋势下，功耗侧信道对计算活动信息泄漏的风险有哪些新变化？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
307,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",如何利用全系统功耗分析提升嵌入式和医疗设备的恶意软件检测能力？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
308,"能量比例计算
功耗侧信道
信息泄漏
隐私保护
恶意软件检测
电力分析
嵌入式设备安全
机器学习
特征工程
医疗设备安全",功耗特征工程与机器学习方法在功耗侧信道攻击与防护中有哪些应用前景？,# Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,本文分析了能量比例计算趋势下，计算机功耗与工作负载紧密相关，导致通过全系统功耗分析泄漏敏感信息的风险提升。论文提出，节能带来的噪声底线降低，使得攻击者更易通过功耗轨迹识别计算活动。研究还展示了功耗分析既可能威胁隐私，也可用于恶意软件检测，尤其对嵌入式和医疗设备具有现实意义。,USENIX_Security-2012,55
309,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",IPMI远程管理接口在服务器安全体系中存在哪些主要攻击面及其风险？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
310,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",服务器BMC固件中常见的安全漏洞类型及其利用方式有哪些？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
311,"服务器远程管理
IPMI安全
嵌入式控制器
固件漏洞
默认凭证风险
缓冲区溢出
命令注入
攻击面分析
网络隔离
防御策略",如何通过网络隔离和固件更新等手段提升IPMI设备的安全防护能力？,Illuminating the Security Issues Surrounding Lights-Out Server Management,本文系统分析了服务器远程管理接口（IPMI）及其嵌入式控制器（BMC）带来的安全风险。研究发现，主流IPMI固件存在缓冲区溢出、命令注入等典型漏洞，且大量设备因默认凭证、网络暴露等问题面临远程攻击威胁。论文通过实证揭示了全球数万台服务器管理接口暴露在公网的现状，并提出了隔离部署、及时更新固件等防御建议。,USENIX_Security-2013,47
312,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",k-匿名性通信系统中对称披露机制如何缓解社交关系稀疏带来的带宽与隐私权衡？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
313,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",社交网络中k-匿名性分组策略对通信隐私与系统效率有何影响？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
314,"k-匿名性
对称披露
社交网络隐私
匿名通信
群组划分
带宽开销
差分隐私
流量分析防护
匿名集规模
资源权衡",如何通过调整匿名集规模与分组方式提升k-匿名性通信系统的抗流量分析能力？,Symmetric Disclosure: a Fresh Look at k-Anonymity,本文提出了"对称披露"通信机制，系统分析了社交网络中k-匿名性系统因关系稀疏导致的带宽开销问题。通过让通信双方同时指定源与目标匿名集，有效降低了覆盖流量需求。论文结合理论建模与实际社交图数据，探讨了群组规模、资源消耗与隐私保护的平衡，为匿名通信系统设计提供了新思路。,USENIX_Security-2014,87
315,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络如何通过同态加密与私有集合交集协议实现隐私友好的好友发现？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
316,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",去中心化社交网络在消息同步与NAT穿透方面面临哪些关键技术挑战？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
317,"去中心化社交网络
隐私保护
同态加密
好友发现
消息同步
Bloom过滤器
NAT穿透
密钥管理
去信任架构
实验评估",如何通过分布式密钥管理与消息副本机制提升社交网络的隐私与可用性？,ReClaim: a Privacy-Preserving Decentralized Social Network,本文提出了ReClaim系统，实现了无需中心化服务器的隐私保护型社交网络。系统采用同态加密与私有集合交集协议（PSI）实现好友发现，结合Bloom过滤器进行高效消息同步，并通过好友及其好友存储消息，提升了抗NAT与高动态环境下的可用性。实验表明，ReClaim可在10分钟内实现95%间接好友连接。,USENIX_Security-2014,93
318,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何通过网络攻防竞赛设计提升学生在渗透测试中的枢纽攻击与防御能力？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
319,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",攻防竞赛中植入程序开发与多阶段攻击策略对网络安全教育有何促进作用？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141
320,"网络攻防竞赛
渗透测试教学
网络拓扑
枢纽攻击
植入程序
攻防对抗
安全服务维护
主动学习
教育创新
实验评估",如何平衡攻防竞赛中的进攻性实践与安全防御技能培养，促进网络安全人才成长？,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,本文提出了King of the Hill（KotH）网络攻防竞赛，创新性地将枢纽攻击、植入程序开发和赛前准备等渗透测试关键环节融入教学。竞赛要求学生在复杂网络拓扑中攻防对抗，既需突破多层防线，也需维护关键服务安全。实验结果表明，KotH有效提升了学生的实战渗透测试与防御能力。,USENIX_Security-2018,141

