{"text": "# Anonymous Multi-Hop Locks for Blockchain Scalability and Interoperability  \n\n<PERSON><PERSON><PERSON>§, <PERSON>¶†, <PERSON>†, <PERSON><PERSON><PERSON>‡, <PERSON>† <PERSON><PERSON><PERSON>-Alexander-University Erlangen-<PERSON>, TU Wien,  Purdue University  \n\nAbstract—Tremendous growth in cryptocurrency usage is exposing the inherent scalability issues with permissionless blockchain technology. Payment-channel networks (PCNs) have emerged as the most widely deployed solution to mitigate the scalability issues, allowing the bulk of payments between two users to be carried out off-chain. Unfortunately, as reported in the literature and further demonstrated in this paper, current PCNs do not provide meaningful security and privacy guarantees [30], [40].  \n\nIn this work, we study and design secure and privacypreserving PCNs. We start with a security analysis of existing PCNs, reporting a new attack that applies to all major PCNs, including the Lightning Network, and allows an attacker to steal the fees from honest intermediaries in the same payment path. We then formally define anonymous multi-hop locks (AMHLs), a novel cryptographic primitive that serves as a cornerstone for the design of secure and privacy-preserving PCNs. We present several provably secure cryptographic instantiations that make AMHLs compatible with the vast majority of cryptocurrencies. In particular, we show that (linear) homomorphic one-way functions suffice to construct AMHLs for PCNs supporting a script language (e.g., Ethereum). We also propose a construction based on ECDSA signatures that does not require scripts, thus solving a prominent open problem in the field.  \n\nAMHLs constitute a generic primitive whose usefulness goes beyond multi-hop payments in a single PCN and we show how to realize atomic swaps and interoperable PCNs from this primitive. Finally, our performance evaluation on a commodity machine finds that AMHL operations can be performed in less than 100 milliseconds and require less than 500 bytes of communication overhead, even in the worst case. In fact, after acknowledging our attack, the Lightning Network developers have implemented our ECDSA-based AMHLs into their PCN. This demonstrates the practicality of our approach and its impact on the security, privacy, interoperability, and scalability of today’s cryptocurrencies.  \n\n# I. INTRODUCTION  \n\nCryptocurrencies are growing in popularity and are playing an increasing role in the worldwide financial ecosystem. In fact, the number of Bitcoin transactions grew by approximately $30\\%$ in 2017, reaching a peak of more than 420, 000 transactions per day in December 2017 [2]. This striking increase in demand has given rise to scalability issues [20], which go well beyond the rapidly increasing size of the blockchain. For instance, the permissionless nature of the consensus algorithm used in Bitcoin today limits the transaction rate to tens of transactions per second, whereas other payment networks such as Visa support peaks of up to 47,000 transactions per second [9].  \n\nAmong the various proposals to solve the scalability issue [22], [23], [38], [48], payment-channels have emerged as the most widely deployed solution in practice. In a nutshell, two users open a payment channel by committing a single transaction to the blockchain, which locks their bitcoins in a deposit secured by a Bitcoin (smart) contract. These users can then perform several payments between each other without the need for additional blockchain transactions, by simply locally agreeing on the new deposit balance. A transaction is required only at the end in order to close the payment channel and unlock the final balances of the two parties, thereby drastically reducing the transaction load on the blockchain. Further research has proposed the concept of payment-channel network [48] (PCN), where two users not sharing a payment channel can still pay each other using a path of open channels between them. Unfortunately, current PCNs fall short of providing adequate security, privacy, and interoperability guarantees.  \n\n# A. State-of-the-art in PCNs  \n\nSeveral practical deployments of PCNs exist today [5], [8], [10] based on a common reference description for the Lightning Network (LN) [6]. Unfortunately, this proposal is neither privacy-preserving, as shown in recent works [30], [40], nor secure, which stays in contrast to what until now was commonly believed, as we show in this work. In fact, we present a new attack, the wormhole attack, which applies not only to the LN, the most widely deployed PCN, but also other PCNs based on the same cryptographic lock mechanism, such as the Raiden Network [7].  \n\nPCNs have attracted plenty of attention also from academia. Malavolta et al. [40] proposed a secure and privacy-preserving protocol for multi-hop payments. However, this solution is expensive as it requires to exchange a non-trivial amount of data (i.e., around 5 MB) between the users in the payment path and it also hinders interoperability as it requires the Hash TimeLock Contract (HTLC) supported in the cryptocurrency.  \n\nGreen and Miers presented BOLT, a hub-based privacy-preserving payment for PCNs [30]. BOLT requires cryptographic primitives only available in Zcash and it cannot be seamlessly deployed in Bitcoin. Moreover, this approach is limited to paths with a single intermediary and the extension to support paths of arbitrary length remains an open problem.  \n\nThe rest of the existing PCN proposals suffer from similar drawbacks. Apart from not formalizing provable privacy guarantees, they are restricted to a setting with a trusted execution environment [36] or with a Turing complete scripting language [25], [26], [33], [43] so that they cannot seamlessly work with prominent cryptocurrencies today (except for Ethereum).  \n\nPoelstra introduced the notion of scriptless scripts, a modified version of a digital signature scheme so that a signature can only be created when faithfully fulfilling a cryptographic condition [47]. The resulting signature is verifiable following the unmodified digital signature scheme. When applied to script-based systems like Bitcoin or Ethereum, they are accompanied by core scripts (e.g., script to verify the signature itself). This approach reduces the space required for cryptographic operations in the script, saving thus invaluable bytes on the blockchain. Moreover, it improves upon the fungibility of the cryptocurrency as transactions from payment channels no longer require a script different from other payments.  \n\nAlthough interesting, current proposals [47] lack formal security and privacy treatment and are based only on the Schnorr signature scheme, thus being incompatible with major cryptocurrencies like Bitcoin. Although there exist early proposals for Schnorr adoption in Bitcoin [51], it is unclear whether they will be realized.  \n\nIn summary, existing proposals are neither generically applicable nor interoperable, since they rely on specific features (e.g., contracts) of individual cryptocurrencies or trusted hardware. Furthermore, there seems to be a gap between secure realization of PCNs and what is developed in practice, as we demonstrate with our attack, which affects virtually all deployed PCNs.  \n\n# B. Our Contributions  \n\nIn this work, we contribute to the rigorous understanding of PCNs and present the first interoperable, secure, and privacy-preserving cryptographic construction for multi-hop locks (AMHLs). Specifically,  \n\n• We analyze the security of existing PCNs, reporting a new attack (the wormhole attack) which allows dishonest users to steal the payment fees from honest users along the path. This attack applies to the LN, as well as any decentralized PCN (following the definition in [40]) where the sender does not know in advance the intermediate users along the path to the receiver. We communicated the attack to the LN developers, who acknowledged the issue. • In order to construct secure and privacy-preserving PCNs, we introduce a novel cryptographic primitive called anonymous multi-hop lock (AMHL). We model the security of such a primitive in the UC framework [18] to inherit the underlying composability guarantees. Then we show that AMHLs can be generically combined with any blockchain to construct a fully-fledged PCN. • As a theoretical insight emerging from the wormhole attack, we establish a lower bound on the communication complexity of secure PCNs (Section III) that follow the definition from [40]: Specifically, we show that an extra round of communication to determine the path is necessary to have a secure transaction. • We show how to realize AMHLs in different settings. In particular, we demonstrate that (linearly) homomorphic operations suffice to build any scriptbased AMHL. Furthermore, we show how to realize AMHLs in a scriptless setting. This approach is of special interest because it reduces the transaction size, and, consequently, the blockchain load. We give a concrete construction based on the ECDSA signature scheme, solving a prominent problem in the literature [47]. This makes AMHLs compatible with the vast majority of cryptocurrencies (including Bitcoin and Ethereum). In fact, AMHLs have been implemented and tested in the LN [28], [29]. • We implemented our cryptographic constructions and show that they require at most 60 milliseconds to be computed and a communication overhead of less than 500 bytes in the worst case. These results demonstrate that AMHLs are practical and ready to be deployed. In fact, AMHLs can be leveraged to design atomic swaps and interoperable (cross-currency) PCNs.  \n\nOrganization. Section II shows the background on PCNs. Section III describes the wormhole attack. Section IV formally defines AMHLs. Section V contains our protocols for AMHLs and Section VI analyzes their performance. Section VII describes applications for AMHLs. Section VIII discusses the related work and Section IX concludes this paper.  \n\n# II. CONTEXT: PAYMENT CHANNEL NETWORKS  \n\n# A. Payment Channels  \n\nA payment channel allows two users to exchange bitcoin without committing every single payment to the Bitcoin blockchain. For that, users first publish an on-chain transaction to deposit bitcoin into a multisignature address controlled by both users. Such deposit also guarantees that all bitcoin are refunded at a possibly different but mutually agreed time if the channel expires. Users can then perform off-chain payments by adjusting the distribution of the deposit (that we will refer to as balance) in favor of the payee. When no more off-chain payments are needed (or the capacity of the payment channel is exhausted), the payment channel is closed with a closing transaction included in the blockchain. This transaction sends the deposited bitcoin to each user according the most recent balance in the payment channel. We refer the reader to [22], [23], [42], [48] for further details.  \n\n# B. A Payment Channel Network (PCN)  \n\nA PCN can be represented as a directed graph $\\mathbb{G}=$ $(\\mathbb{V},\\mathbb{E})$ , where the set $\\mathbb{V}$ of vertices represents the Bitcoin accounts and the set $\\mathbb{E}$ of weighted edges represents the payment channels. Every vertex $U\\in\\mathbb{V}$ has associated a non-negative number that denotes the fee it charges for forwarding payments. The weight on a directed edge $(U_{1},U_{2})\\in\\mathbb{E}$ denotes the amount of remaining bitcoin that $U_{1}$ can pay to $U_{2}$ .  \n\nA PCN is used to perform off-chain payments between two users with no direct payment channel between them but rather connected by a path of open payment channels. For that, assume that $S$ wants to pay $\\alpha$ bitcoin to $R$ , which is reachable through a path of the form $S\\to U_{1}\\to...\\to U_{n}\\to R$ . For their payment to be successful, every link must have a capacity $\\gamma_{i}\\geq\\alpha_{i}^{\\prime}$ , where $\\begin{array}{r}{\\alpha_{i}^{\\prime}=\\alpha-\\sum_{j=1}^{\\bar{i}-1}f e e(U_{j})}\\end{array}$ (i.e., the initial payment value minus the fees charged by intermediate users in the path). If the payment is successful, edges from $S$ to $R$ are decreased by $\\alpha_{i}^{\\prime}$ . Importantly, to ensure that $R$ receives exactly $\\alpha$ bitcoin, $S$ must start the payment with a value $\\begin{array}{r}{\\alpha^{*}\\stackrel{\\cdot}{=}\\alpha+\\sum_{j=1}^{n}f e e(U_{j})}\\end{array}$ . We refer the reader to [30], [40], [42],  [48] for further details.  \n\nThe concepts of payment channels and PCNs have already attracted considerable attention from academia [23], [30], [31], [35], [40], [42], [43]. In practice, the Lightning Network (LN) [6], [48] has emerged as the most prominent example. Currently, there exist several independent implementations of the LN for Bitcoin [5], [8], [10]. Moreover, the LN is also considered as a scalability solution in other blockchainbased payment systems such as Ethereum [7].  \n\nA fundamental property for multi-hop payments is atomicity: Either the capacity of all channels in the path is updated or none of the channels is changed. Partial updates can lead to coin losses for the users on the path. For instance, a user could pay a certain amount of bitcoin to the next user in the path but never receive the corresponding bitcoin from the previous neighbour. The LN tackles this challenge by relying on a smart contract called Hash Time-Lock Contract (HTLC) [48]. This contract locks $x$ bitcoin that can be released only if the contract’s condition is fulfilled. The contract relies on a collision-resistant hash function $H$ and it is defined in terms of a hash value $y:=H(R)$ , where $R$ is chosen uniformly at random, the amount of bitcoin $x$ , and a timeout $t$ , as follows: (i) If Bob produces the condition $R^{*}$ such that $H(R^{*})=y$ before $t$ days, Alice pays Bob $x$ bitcoin; (ii) If $t$ days elapse, Alice gets back $x$ bitcoin.  \n\nFig. 1 shows an example of the use of HTLC in a payment. For simplicity, we assume that every user charges a fee of one bitcoin and the payment amount is 10 bitcoin. In this payment, Edward first sets up the payment by creating a random value $R$ and sending ${\\bar{H(R)}}$ to Alice. Then, the commitment phase starts by Alice. She first sets on hold 13 bitcoin and then successively every intermediate user sets on hold the received amount minus his/her own fee. After Dave sets 10 coins on hold with Edward, the latter knows that the corresponding payment amount is on hold at each channel and he can start the releasing phase (depicted in green). For that, he reveals the value $R$ to Dave allowing him to fulfill the HTLC contract and settle the new capacity at the payment channel. The value $R$ is then passed backwards in the path allowing the settlement of the payment channels.  \n\nPrivacy Issues in PCNs. Recent works [30], [40] show that the current use of HTLC leaks a common identifier along the payment path (i.e., the condition $H(R))$ that can be used by an adversary to tell who pays to whom. Current solutions to this privacy issue are expensive in terms of computation and communication [40] or incompatible with major cryptocurrencies [30]. This calls for an in-depth study of this cryptographic tool.  \n\n![](/tmp/output/50_20250327015325/images/728afb65a88971fb458e036f5382c6c26c28c361115fb12fe49ac8180456b12a.jpg)  \nFig. 1: Payment (with and without wormhole attack) from Alice to Edward for value 10 using HTLC contract. The honest (attacked) releasing phase is depicted in green (red). Non-bold (bold) numbers show the capacity of payment channels before (after) the payment. We assume a common fee of 1 coin.  \n\n# III. WORMHOLE ATTACK IN EXISTING PCNS  \n\nIn a nutshell, the wormhole attack allows two colliding users on a payment path to exclude intermediate users from participating in the successful completion of a payment, thereby stealing the payment fees which were intended for honest path nodes.  \n\nIn more detail, assume a payment path $(U_{0},\\ldots,U_{i}$ , $\\ldots,U_{j},\\ldots,U_{n})$ used by $U_{0}$ to pay an amount $\\alpha+$ $\\textstyle\\sum_{k}\\gamma_{k}$ to $U_{n}$ , where $\\gamma_{k}=f e e(U_{k})$ denotes the fee charged by the intermediate user $U_{k}$ as a reward for enabling the payment. Further assume that $U_{i}$ and $U_{j}$ are two adversarial users that may deviate from the protocol if some economic benefit is at stake. The adversarial strategy is as follows.  \n\nIn the commitment phase, every user behaves honestly. This, in particular, implies that every honest user has locked a certain amount of coins in the hope of getting rewarded for this. In the releasing phase, honest users $U_{j+1},\\ldots,U_{n}$ correctly fulfill their HTLC contracts and settle the balances and rewards in their corresponding payment channels.  \n\nThe user $U_{j}$ behaves honestly with $U_{j+1}$ effectively settling the balance in their payment channel. On the other hand, $U_{j}$ waits until the timeout set in the HTLC with $U_{j-1}$ is about to expire and then agrees with $U_{j-1}$ to cancel the HTLC and set the balance in their payment channel back to the last agreed one. Note that from $U_{j-1}$ ’s point of view, this is a legitimate situation (e.g., there might not be enough coins in a payment channel at some user after $U_{j}$ and the payment had to be canceled). Moreover, the channel between $U_{j-1}$ and $U_{j}$ does not need to be closed, it is just rolled back to a previous balance, a feature present in the Lightning Network.  \n\nAs $U_{j-1}$ believes that the payment did not go through, she also cancels the HTLC with $U_{j-2}$ , who in turns cancels the HTLC with $U_{j-3}$ and so on. This process continues until $U_{i}$ is approached by $U_{i+1}$ . Here, $U_{i}$ cancels the HTLC with $U_{i+1}$ . However, $U_{i}$ gets the releasing condition $R$ from $U_{j}$ and can use it to fulfill the HTLC with $U_{i-1}$ and therefore settle the new balance in that payment channel. Therefore, from the point of view of users $U_{1},\\ldots,U_{i-1}$ , the payment has been successfully carried out. An illustrative example of this attack is shown in Fig. 1 with the attacked releasing phase depicted in red.  \n\nDiscussion. An adversary controlling users $U_{i}$ and $U_{j}$ ibnit ctohiinss  siencsttieoand  goeft so nalny bbietnceoifints  oifn $\\textstyle\\sum_{k=i+1}^{j}\\gamma_{k}$ $\\gamma_{i}+\\gamma_{j}$ behaves honestly. We make several observations here. First, the impact of this attack grows with the number of intermediate users between $U_{i}$ and $U_{j}$ as well as the number of payments that take both $U_{i}$ and $U_{j}$ in their path. While the Lightning Network is at its infancy, other well-established networks such as Ripple use paths with multiple intermediaries. For instance, in the Ripple network, more than $27\\%$ of the payments use more than two intermediaries [44]. Actually, paths with three intermediaries (e.g., sender $\\rightarrow$ bank $\\rightarrow$ currencyexchange $\\rightarrow\\mathrm{bank\\rightarrow}$ receiver) are essential for currency exchanges, a key use case in LN itself [1]. When the LN grows to the scale of the Internet, routes may consist of several intermediaries as in the Internet today. Given these evidences, we expect long paths in the LN.  \n\nSecond, honest intermediate users cannot trivially distinguish the situation in which they are under attack from the situation where the payment is simply unsuccessful (e.g., there are not enough coins in one of the channels or one of the users is offline). In both cases, the view for the honest users is that the timeout established in the HTLC is reached, the payment failed and they get their initially committed coins reimbursed. In short, the wormhole attack allows an adversary to steal the fees from intermediate honest users without leaving a inculpatory trace to them.  \n\nThird, fees are the main incentive for intermediary users. The wormhole attack takes away this crucial benefit. In fact, this attack not only makes honest users lose their fees, but also incur collateral costs: Coins locked for the payment under attack cannot be used for another (possibly successful) payment simultaneously.  \n\nResponsible Disclosure. We notified this attack to the LN developers and they have acknowledged this issue. Additionally, they have implemented our ECDSA-based construction (see Section V-D) and tested it for its integration in the LN, having thereby a fix for the wormhole attack and leveraging its privacy and practical benefits [28], [29].  \n\n$(\\mathbf{In})$ evitability of the Wormhole Attack. The wormhole attack is not restricted to the LN, but generally applies to PCNs with multi-hop payments that involve only two rounds of communication. We assume a communication round to consist of traversing the payment path once, either forth (e.g., for setting up the payment) or back (e.g., for releasing the coins). Additionally, we assume that in PCNs the communication between nodes is restricted to their direct neighbors, so in particular, there is no broadcast.1 Consequently, using two rounds of communication for a payment implies that the payment is not preceded by a routing phase in which pathspecific information is sent to nodes in the path. Under these assumptions, we state the lower bound informally in Theorem 1 and defer the formal theorem and the proof to the extended version of the paper [41].  \n\nTheorem 1 (Informal). For all two-round (without broadcast channels) multi-hop payment protocols there exists a path prone to the wormhole attack.  \n\nIn this work we show that adding an additional round of communication suffices to overcome this impossibility result.2 In particular, with one additional round of communication, the sender of a payment can communicate path-specific secret information to the intermediate nodes. This information can then be used to make the release keys unforgeable for an attacker. The cryptographic protocols we introduce in the remainder of this paper adopt this approach.  \n\n# IV. DEFINITION  \n\nHere we introduce a new cryptographic primitive called anonymous multi-hop lock (AMHL). This primitive generalizes the locking mechanism used for payments in state-of-the-art PCNs such as the LN. In Section VII we show that AMHL is the main cryptographic component required to construct fully-fledged PCNs. As motivated in the previous section, we model the primitive such that it allows for an initial setup phase where the first node of the path provides the other nodes with some secret (path-specific) state. Formally, an AMHL is defined with respect to a universe of users $\\mathbb{U}$ and it is a five-tuple of PPT algorithms and protocols $\\mathbb{L}=({\\mathsf{K G e n}}$ , Setup, Lock, Rel, Vf) defined as follows:  \n\nDefinition 1. An AMHL $\\mathbb{L}=$ (KGen, Setup, Lock, $\\mathsf{R e l,V f}$ consists of the following efficient algorithms:  \n\n$\\{(\\mathsf{s k}_{i},\\mathsf{p k}),(\\mathsf{s k}_{j},\\mathsf{p k})\\}\\gets\\langle\\mathsf{K G e n}_{U_{i}}(1^{\\lambda}),\\mathsf{K G e n}_{U_{j}}(1^{\\lambda})\\rangle:$ On input the security parameter $1^{\\lambda}$ the key generation protocol returns a shared public key pk and a secret key $\\mathsf{s k}_{i}$ $(\\mathsf{s k}_{j}$ , respectively) to $U_{i}$ and $U_{j}$ .  \n\n$\\begin{array}{r l r}{\\{s_{0}^{I},\\ldots,(s_{n}^{I},k_{n})\\}}&{{}\\leftarrow}&{\\langle\\mathsf{S e t u p}_{U_{0}}(1^{\\lambda},U_{1},\\ldots,U_{n})_{1}}\\end{array}$ ${\\mathsf{S e t u p}}_{U_{1}}(1^{\\lambda}),\\ldots$ , ${\\mathsf{S e t u p}}_{U_{n}}(1^{\\lambda})\\rangle:O n$ input a vector of identities $(U_{1},\\dots,U_{n})$ and the security parameter $1^{\\lambda}$ , the setup protocol returns, for $i\\in[0,n]$ , a state $s_{i}^{I}$ to user $U_{i}$ . The user $U_{n}$ additionally receives a key $k_{n}$ . $\\{(\\ell,s_{i}^{R}),(\\ell,s_{i+1}^{L})\\}\\qquad\\leftarrow\\qquad\\langle\\mathrm{Lock}_{U_{i}}(s_{i}^{I},\\mathsf{s k}_{i},\\mathsf{p k}),$ $\\mathsf{L o c k}_{U_{i+1}}(s_{i+1}^{I},\\mathsf{s k}_{i+1},\\mathsf{p k})\\rangle$ : On input two initial states $s_{i}^{I}$ and $s_{i+1}^{I}$ , two secret keys $\\mathsf{s k}_{i}$ and $\\mathsf{s k}_{i+1}$ , and a public key $\\mathsf{p k},$ the locking protocol is executed between two users $(U_{i},U_{i+1})$ and returns a lock $\\ell$ and a right state $s_{i}^{R}$ to $U_{i}$ and the same lock $\\ell$ and a left state $s_{i+1}^{L}$ to $U_{i+1}$ . $k$ $k^{\\prime}\\leftarrow\\mathsf{R e l}(k,(s^{I},s^{L},s^{R})):\\hdots O$ n input an opening key and a triple of states $(s^{\\tilde{I}},s^{L},s^{R})$ , the release algorithm returns a new opening key $k^{\\prime}$ .  \n\n$\\{0,1\\}\\leftarrow\\lor\\mathsf{f}(\\ell,k):O n$ input a lock $\\ell$ and a key $k$ the verification algorithm returns a bit $b\\in\\{0,1\\}$ .  \n\nCorrectness. An AMHL is correct if the verification algorithm Vf always accepts an honestly generated lockkey pair. For a more detailed and formal correctness definition, we refer the reader to the extended version [41].  \n\nKey Ideas. Fig. 2 illustrates the usage of the different protocols underlying the AMHL primitive. First, we assume an (interactive) KGen phase that emulates the opening of payment channels that compose the PCN.  \n\nIn the setup phase (green arrows), the introduction of the initial state at each intermediate user is crucial for security and privacy. Intuitively, we can use this initial state as “rerandomization factor” to ensure that locks in the same path are unlinkable for the adversary.  \n\nNext, in the locking phase, each pair of users jointly executes the Lock protocol to generate a lock $\\ell_{i}$ . The creation of this lock represents the commitment from $U_{i}$ to perform an application-dependent action if a cryptographic problem is solved by $U_{i+1}$ . In the case of LN, this operation represents the commitment of $U_{i}$ to pay a certain amount of coins to $U_{i+1}$ if $U_{i+1}$ solves the cryptographic condition. Each user also learns some extra state $\\bar{s}_{i}^{R}$ (resp. $s_{i+1}^{L}\\rangle$ ) that will be needed for releasing the lock later on. While these extra states are not present in the LN (i.e., every lock is based on the same cryptographic puzzle $H(R))$ , they are crucial for security. They make the releasing of different locks in the path independent and thus ensure that a lock $\\ell_{i}$ can only be released if $\\ell_{i+1}$ has been released before.  \n\nFinally, after the entire path is locked, the receiver $U_{n}$ can generate a key for releasing its left lock. Then, each intermediate node can derive a valid key for its left lock from a valid key for its right lock using the Rel algorithm. This last phase resembles the opening phase of the LN where each pair of users settles the new balances for their deposit at each payment channel in the payment path.  \n\n# A. Security and Privacy Definition  \n\nTo model security and privacy in the presence of concurrent executions we resort to the universal composability framework from Canetti [18]. We allow thereby the composition of AMHLs with other application-dependent protocols while maintaining security and privacy guarantees.  \n\nAttacker Model. We model the players in our protocol as interactive Turing machines that communicate with a trusted functionality $\\mathcal{F}$ via secure and authenticated channels. We model the attacker $\\mathcal{A}$ as a PPT machine that has access to an interface corrupt $(\\cdot)$ that takes as input a user identifier $U$ and provides the attacker with the internal state of $U$ . All the subsequent incoming and outgoing communication of $U$ are then routed through $\\mathcal{A}$ . We consider the static corruption model, that is, the attacker is required to commit to the identifiers of the users he wishes to corrupt ahead of time.3  \n\n![](/tmp/output/50_20250327015325/images/870c710d804e81ed44a249edb87d6cf328af1a781db576ca862d4f6a621f7baa.jpg)  \nFig. 2: Usage of the AMHL primitive. It is assumed that links between the users on the path have been created upfront (using KGen) and that the resulting public and secret keys are implicitly given as argument to the corresponding executions of Lock. Otherwise, the inputs (outputs) to (from) the Lock protocol and the Rel algorithm are indicated by blue (orange) arrows.  \n\nCommunication Model. Communication happens through the secure message transmission functionality $\\mathcal{F}_{\\mathrm{smt}}$ that informs the attacker whenever some communication happens between two users and the attacker can delay the delivery of the message arbitrarily (for a concrete functionality see [18]). We also assume the existence of a functionality $\\mathcal{F}_{\\sf a n o n}$ (see [17] for an example), which provides user with an anonymous communication channel. In its simplest form, $\\mathcal{F}_{\\sf a n o n}$ is identical to $\\mathcal{F}_{\\mathrm{smt}}$ , except that it omits the identifier of the sender from the message sent to the receiver. We assume a synchronous communication network, where the execution of the protocol happens in discrete rounds. The parties are always aware of the current round and if a message is created at round $i$ , then it is delivered at the beginning of the $(i+1)$ -th round. Our model assumes that computation is instantaneous. In the real world, this is justified by setting a maximum time bound for message transmission, which is known by all users. If no message is delivered by the expiration time, then the message is set to be $\\perp$ . We remark that such an assumption is standard in the literature [25] and for an example of the corresponding ideal functionality $\\mathcal{F}_{\\mathsf{s y n}}$ we refer the reader to [18], [32].  \n\nUniversal Composability. Let $\\mathsf{E X E C}_{\\tau,\\mathcal{A},\\mathcal{E}}$ be the ensemble of the outputs of the environment $\\mathcal{E}$ when interacting with the attacker $\\mathcal{A}$ and users running protocol $\\tau$ (over the random coins of all the involved machines).  \n\nDefinition 2 (Universal Composability). A protocol $\\tau$ UC-realizes an ideal functionality $\\mathcal{F}$ if for any PPT adversary $\\mathcal{A}$ there exists a simulator $s$ such that for any environment $\\mathcal{E}$ the ensembles $\\mathsf{E X E C}_{\\tau,\\mathcal{A},\\mathcal{E}}$ and $\\mathsf{E X E C}_{\\mathcal{F},S,\\mathcal{E}}$ are computationally indistinguishable.  \n\nIdeal Functionality. We formally define the ideal world functionality $\\mathcal{F}$ for AMHLs in the following. For a more modular treatment, our UC definition models only the cryptographic lock functionality, rather than aiming at a comprehensive characterization of PCNs. In Section VII we show how one can construct a full PCN (e.g., as defined in [40]) by composing this functionality with time locks, balance updates, and on-chain channel management. For ease of exposition we assume that each pair of users establishes only a single link per direction. The model can be easily extended to handle the more generic case. $\\mathcal{F}$ works in interaction with a universe of users $\\mathbb{U}$ and initializes two empty lists $(\\mathcal{U},\\mathcal{L}):=\\emptyset$ , which are used to track the users and the locks, respectively. The list $\\mathcal{L}$ represents a set of lock chains. The entries are of the form $(l i d_{i},U_{i},U_{i+1},f,l i d_{i+1})$ where $l i d_{i}$ is a lock identifier that is unique even among other lock chains in $\\mathcal{L},U_{i}$ and $U_{i+1}$ are the users connected by the lock, $f\\in\\{\\mathsf{l n i t},\\mathsf{L o c k},\\mathsf{R e l}\\}$ is a flag that represents the status of the lock, and $l i d_{i+1}$ is the identifier of the next lock in the path. For sake of better readability, we define functions operating on $\\mathcal{L}$ extracting lockspecific information given the lock’s identifier, such as the lock’s status $(g e t S t a t u s(\\cdot))$ , the nodes it is connecting $(g e t L e f t(\\cdot)$ , getRight( )), and the next lock’s identifier $(g e t N e x t L o c k(\\cdot))$ . In addition we define an update function updateStatus $(\\cdot,\\cdot)$ that changes the status of a lock to a new flag.  \n\nThe interfaces of the functionality $\\mathcal{F}$ are specified in Fig. 3. The KeyGen interface allows a user to establish a link with another user (specifying whether it wants to be the left or the right part of the link). The Setup interface allows a user $U_{0}$ to setup a path (starting from $U_{0}$ ) along previously established links. The Lock interface allows a user to create a lock with its right neighbor on a previously created path and the Release algorithm allows a user to release the lock with its left neighbor, in case that the user is either the receiver or its right lock has been released before. Finally, the GetStatus interface allows one to check the current status of a lock, i.e., whether it is initialized, locked or released. Internally, the locks are assigned identifiers that are unique across all paths. We define the interfaces sends and receives to exchange messages through the $\\mathcal{F}_{\\mathrm{smt}}$ functionality and the interface sendan to send messages via $\\mathcal{F}_{\\sf a n o n}$ .  \n\n![](/tmp/output/50_20250327015325/images/c0f0ae3c86aed78ff82466efd3a591965a7f7adec21577c647f48fd3ac089c43.jpg)  \nFig. 3: Ideal functionality for cryptographic locks (AMHLs)  \n\nB. Discussion  \n\nWe discuss how the security and privacy notions of interest for AMHLs are captured by functionality $\\mathcal{F}$ .  \n\nAtomicity. Loosely speaking, atomicity means that every user in a path is able to release its left lock in case that his right lock was already released. This is enforced by $\\mathcal{F}$ as i) it is keeping track of the chain of locks and their current status in the list $\\mathcal{L}$ and ii) the Release interface of $\\mathcal{F}$ allows one to release a lock lid (changing the flag to Rel) if lid is locked and the follow-up lock $(g e t N e x t L o c k(l i d))$ was already released.  \n\nConsistency. An AMHL is consistent if no attacker can release his left lock without its right lock being released before. This prevents scenarios where some AMHL is released before the receiver is reached and, more generically, the wormhole attack described in Section III. To see why our ideal functionality models this property, observe that the Release interface allows a user to release the left lock only if the right lock has already been released or the user itself is the receiver. In this context, no wormhole attack is possible as intermediate nodes cannot be bypassed.  \n\nRelationship Anonymity. Relationship anonymity [12] requires that each intermediate node does not learn any information about the set of users in an AMHL beyond its direct neighbors. This property is satisfied by $\\mathcal{F}$ as the lock identifiers are sampled at random and during the locking phase a user only learns the identifiers of its left and right lock as well as its left and right neighbor. We discuss this further in the extended version [41].  \n\n# V. CONSTRUCTIONS  \n\n# A. Cryptographic Building Blocks  \n\nThroughout this work we denote by $1^{\\lambda}\\in\\mathbb{N}^{+}$ the security parameter. Given a set $S$ , we denote by $x\\gets\\mathbb{S}S$ the sampling of an element uniformly at random from $S$ , and we denote by $x\\leftarrow A(i n)$ the output of the algorithm $A$ on input in. We denote by $\\mathsf{m i n}(a,b)$ the function that takes as input two integers and returns the smaller of the two. To favor readability, we omit session identifiers from the description of the protocols. In the following we briefly recall the cryptographic building blocks of our schemes.  \n\nHomomorphic One-Way Functions. A function $_{\\textit{g}:}$ $\\mathcal{D}\\rightarrow\\mathcal{R}$ is one-way if, given a random element $x\\in\\mathcal{R}$ , it is hard to compute a $y\\in\\mathcal D$ such that $g(y)=x$ . We say that a function $g$ is homomorphic if $\\mathcal{D}$ and $\\mathcal{R}$ define two abelian groups and for each pair $(a,b)\\in\\mathcal{D}^{2}$ it holds that $g(a\\circ b)=g(a)\\circ g(b)$ , where $\\circ$ denotes the group operation. Throughout this work we denote the corresponding arithmetic group additively.  \n\nCommitment Scheme. A commitment scheme COM consists of a commitment algorithm $({\\mathsf{d e c o m}},{\\mathsf{c o m}})\\gets$ $\\mathsf{C o m m i t}(1^{\\lambda},m)$ and a verification algorithm $\\{0,1\\}\\leftarrow$ $\\mathsf{V}_{\\mathsf{c o m}}(\\mathsf{c o m},\\mathsf{d e c o m},m)$ . The commitment algorithm allows a prover to commit to a message $m$ without revealing it. In a second phase, the prover can convince a verifier that the message $m$ was indeed committed by showing the unveil information decom. The security of a commitment scheme is captured by the standard ideal functionality ${\\mathcal{F}}_{\\mathsf{c o m}}$ [18].  \n\nNon-Interactive Zero-Knowledge. Let $R$ be an NP relation and let $L$ be the set of positive instances, i.e., $L:=\\{x\\mid\\exists w$ s.t. $R(x,w)=1\\}$ . A non-interactive zero-knowledge proof [15] scheme NIZK consists of an efficient prover algorithm $\\pi\\gets\\mathsf{P}_{\\mathsf{N I Z K}}(w,x)$ and an efficient verifier $\\{0,1\\}\\leftarrow\\lor\\mathsf{N e}(x,\\pi)$ . A NIZK scheme allows the prover to convince the verifier about the existence of a witness $w$ for a certain statement $x$ without revealing any additional information. The security of a NIZK scheme is modeled by the following ideal functionality $\\mathcal{F}_{\\sf N I Z K}$ : On input (prove, $s i d,x,w)$ by the prover, check if $R(x,w)=1$ and send $(\\mathsf{p r o o f},s i d,x)$ to the verifier if this is the case.  \n\nHomomorphic Encryption. One of the building blocks of our work is the additive homomorphic encryption scheme $\\mathsf{H E}:=(\\mathsf{K G e n}_{\\mathsf{H E}},\\mathsf{E n c}_{\\mathsf{H E}},\\mathsf{D e c}_{\\mathsf{H E}})$ from Paillier [45]. The scheme supports homomorphic operation over the ciphertexts of the form $\\mathsf{E n c}_{\\mathsf{H E}}(\\mathsf{p k},m)$ · $\\begin{array}{l l l}{{\\mathsf{E n c}_{\\mathsf{H E}}(\\mathsf{p k},m^{\\prime})}}&{{=}}&{{\\mathsf{E n c}_{\\mathsf{H E}}(\\mathsf{p k},m+m^{\\prime})}}\\end{array}$ . We assume that Paillier’s encryption scheme satisfies the notion of ecCPA security, as defined in the work of Lindell [37].  \n\nECDSA Signatures. Let $\\mathbb{G}$ be an elliptic curve group of order $q$ with base point $G$ and let $H:\\{0,1\\}^{*}\\stackrel{\\textstyle-}{\\rightarrow}$ $\\{0,1\\}^{|q|}$ be a collision resistant hash function. The key generation algorithm $\\mathsf{K G e n}_{\\mathsf{E C D S A}}(1^{\\lambda})$ samples a private key as a random value $x\\leftarrow\\mathfrak{s}\\mathbb{Z}_{q}$ and sets the corresponding public key as ${\\cal Q}:=\\dot{x^{\\prime}}\\cdot{\\cal G}$ . To sign a message $m$ , the signing algorithm $\\mathsf{S i g}_{\\mathsf{E C D S A}}(\\mathsf{s k},m)$ samples some $k\\leftarrow\\mathbb{Z}_{q}$ and computes $e:=H(m)$ . Let $({\\overline{{r_{x},}}}{\\overline{{r_{y}}}}):=R=k:G$ , then the signing algorithm computes $r\\quad:=r_{x}$ mod $q$ and $\\begin{array}{r}{s}{:=}\\ {s}\\end{array}$ mod $q$ . The signature consists of $(r,s)$ . The verification algorithm $\\mathsf{V f}_{\\mathsf{E C D S A}}(\\mathsf{p k},\\sigma,m)$ recomputes $e=H(m)$ and returns 1 if and only if $(x,y)={\\frac{e}{s}}\\cdot G+{\\frac{r}{s}}\\cdot Q$ and $r=x$ mod $q$ . It is a well known fact that for every valid signature $(r,s)$ , also the pair $(r,-s)$ is a valid signature. To make the signature strongly unforgeable we augment the verification equation with a check that $s\\leq{\\frac{\\breve{q}-1}{2}}$ . We assume the existence of an interactive protoco2l $\\Pi_{\\mathsf{K G e n}}^{\\mathsf{E C D S A}}$ executed between two users where the one receives $(x_{0},Q,\\mathsf{s k})$ , where $\\mathsf{s k}$ is a Paillier secret key and $Q=x_{0}\\cdot x_{1}\\cdot G$ , whereas the other obtains $(x_{1},Q,\\mathsf{E n c}_{\\mathsf{H E}}(\\mathsf{p k},x_{0}))$ , where $\\mathsf{p k}$ is the corresponding Paillier public-key. For correctness, we require that the Paillier modulus is $N=O(q^{4})$ . We assume that the parties have access to an ideal functionality $\\mathcal{F}_{{\\sf k g e n}}^{{\\sf E C D S A}}$ (refer to [41] for a precise definition) that securely computes the tuples for both parties. An efficient protocol has been recently proposed by Lindell [37].  \n\nAnonymous Communication. We assume an anonymous communication channel $\\Pi_{\\mathsf{a n o n}}$ available among users in the network, which is modelled by the ideal functionality $\\mathcal{F}_{\\sf a n o n}$ . It anonymously delivers messages to users in the network (e.g., see [17]).  \n\n# B. Generic Construction  \n\nAn interesting question related to AMHLs is under which class of hard problems such a primitive exists. A generic construction using trapdoor permutations was given (implicitly) in [40]. Here we propose a scheme from any homomorphic one-way function. Examples of homomorphic one-way functions include discrete logarithm and the learning with errors problem [49]. Let $g:\\mathcal{D}\\rightarrow\\mathcal{R}$ be a homomorphic one-way function, and let $\\mathcal{F}_{\\sf a n o n}$ be the ideal functionality for an anonymous communication channel. The algorithms of our construction are given in Fig. 4. Note that KeyGen simply returns the users identities and thus it is omitted.  \n\nIn the setup algorithm, the user $U_{0}$ initializes the AMHL by sampling $n$ values $(y_{0},\\dotsc,y_{n-1})$ from the domain of $g$ . Then it sends (via $\\mathcal{F}_{\\mathsf{a n o n}})$ a tdriiaptlee $\\begin{array}{r}{(g(\\sum_{j=0}^{i-1}y_{j}),g(\\sum_{j=0}^{i}y_{j}),y_{i})\\underset{\\ast}{}}\\end{array}$ oc aena cthh einn tcerhemcek$U_{i}$ that the triple is well formed using the homomorphic properties of $g$ . Two contiguous users $U_{i}$ and $U_{i+1}$ can agree on the shared value of $\\ell_{i}:=Y_{i}=g(\\textstyle\\sum_{j=0}^{i}y_{j})_{\\textstyle.}$ by simply comparing the second and first element of their triple, respectively. Note that publishing a valid opening key $k$ such that $g(k)~=~\\ell$ corresponds to inverting the one-way function $g$ . The opening of the locks can be triggered by the last node in the chain Un: The initial key kn := Pin=−01 yi consists of a valid lock is released, each intermediate user $U_{i}$ has enough information to release its “left” lock. To see this, observe that $\\begin{array}{r}{g(k_{i+1}-y_{i})=g(\\sum_{j=0}^{i}y_{i}-y_{i})=g(\\sum_{j=0}^{i-1}y_{i})=}\\end{array}$ $Y_{i-1}$ . For the security of the construction, we state the following theorem. Due to space constraints, the proof is deferred to the extended version [41].  \n\n![](/tmp/output/50_20250327015325/images/e47e7ec3aa87096e71d08f30fe195f39e9ba068dddff2bd6925c6e5c94239792.jpg)  \nFig. 4: Algorithms and protocols for the generic construction  \n\nTheorem 2. Let g be a homomorphic one-way function, then the construction in Fig. 4 UC-realizes the ideal functionality $\\mathcal{F}$ in the $(\\mathcal{F}_{\\mathsf{s y n}},\\mathcal{F}_{\\mathsf{s m t}},\\mathcal{F}_{\\mathsf{a n o n}})$ -hybrid model.  \n\nThe generic construction presented here requires a cryptocurrency supporting scripts that define (linearly) homomorphic operations. This construction is therefore of special interest in blockchain technologies such as Ethereum [4] and Hyperledger Fabric [11], where any user can freely deploy a smart contract without restrictions in the cryptographic operations available. We stress that any function with homomorphic properties is suitable to implement our construction. For instance, lattice-based functions (e.g., from the learning with errors problem) can be used for applications where postquantum cryptography is required. However, many cryptocurrencies, led by Bitcoin, do not support unrestricted scripts and the deployment of generic AMHLs requires non-trivial changes (i.e., a hard fork). To overcome this challenge, we turn our attention to scriptless AMHLs, where a signature scheme can simultaneously be used for authorization and locking.  \n\n# C. Scriptless Schnorr-based Construction  \n\nThe crux of a scriptless locking mechanism is that the lock can consist only of a message $m$ and a public key pk of a given signature scheme and can be released only with a valid signature $\\sigma$ of $m$ under pk. Scriptless locks stem from an idea of Poelstra [46], who proposed a way to embed contracts into Schnorr signatures. In this work we cast Poelstra’s informal idea in our framework and we formally characterize its security and privacy guarantees. We further optimize this scheme in order to save one round of communication.  \n\nRecall that a public key in a Schnorr signature consists of an element $Q:=\\textit{x}\\cdot G$ and a signature $\\sigma:=(k\\cdot G,s)$ on a message $m$ is generated by sampling $k\\leftarrow\\mathbb{Z}_{q}$ , computing $\\begin{array}{r}{e:=H(Q\\|\\bar{k}\\cdot G\\|m)}\\end{array}$ , and setting $s:=k-x e$ . On a very high level, the locking mechanism consists of an “incomplete” distributed signing of some message $m$ : Two users $U_{i}$ and $U_{i+1}$ agree on a randomly chosen element $R_{0}+R_{1}$ using a coin tossing protocol, then they set the randomness of the signature to be $R:=R_{0}+R_{1}+Y_{i}$ . Next they jointly compute the value $s:=r_{0}+r_{1}+e\\cdot(x_{0}+x_{1})$ as if $Y_{i}$ was not part of the randomness, where $e$ is the hash of the transcript so far. The resulting $(R,s)$ is not a valid signature on $m$ , since the additive term $y^{*}$ (where $y^{*}\\cdot G=Y_{i},$ ) is missing from the computation of $s$ . However, once the discrete logarithm of $Y_{i}$ is revealed, a valid signature $m$ can be computed by $U_{i+1}$ . Leveraging this observation, we can enforce an atomic opening: The subsequent locking (between $U_{i+1}$ and $U_{i+2})$ is conditioned on some $Y_{i+1}=Y_{i}+y_{i+1}\\cdot G$ . This way, the opening of the right lock reveals the value $y^{\\ast}+y_{i+1}$ and $U_{i+1}$ can immediately extract $y^{*}$ and open its left lock with a valid signature on $m$ . We defer the formal description and the analysis of the scheme to the extended version [41].  \n\n# D. Scriptless ECDSA-based Construction  \n\nThe Schnorr-based scheme is limited to cryptocurrencies that use Schnorr signatures to authorize transactions and thus is not compatible with those systems, prominently Bitcoin, that implement ECDSA signatures. Therefore, an ECDSA-based scriptless AMHL is interesting both from a practical and a theoretical perspective as to whether it can be done at all. Prior to our work, the existence of such a construction was regarded an open question [47]. The core difficulty is that the Schnorr-based construction exploits the linear structure of the signature, whereas the ECDSA signing algorithm completely breaks this linearity feature (e.g., it requires to compute multiplicative shares of a key and the inverse of elements within a group). In the following, we show how to overcome these problems, introducing an ECDSA-based construction for AMHLs: Locks are of the form $(\\mathsf{p k},m)$ and can only be opened with an ECDSA signature $\\sigma$ on $m$ under $\\mathsf{p k}$ .  \n\n<html><body><table><tr><td colspan=\"2\">Setupu(1^) Setupu。(1^,Ui,...,Un) yo ←s Zq; Yo = yo · G Vi ∈ [1,n - 1] : yi ←s Zq Y := Yi-1 + yi· G stmt; := {y s.t. Y; = y · G} stmt; := {y s.t. Y; = y · G} b ←VNizK(stmt, πi) (Yi-1,Yi,πi) π←PNIzK (j=0 yj, stmti)(Yn-1,kn:=Z=0 yi) if b = 0 then abort Y := Yi-1 + yi·G return (Yi-1, Yi, yi) return yo Locku (s,sk, pk)</td></tr><tr><td colspan=\"2\">LockU+1 (s+1, ski+1,pk) parse s as (Y, Yo, yo) parse s+1 as (Yi, Yi,y1) parse ske as (co, skHE) parse ski+1 as (c1,c) ro <s Zq; Ro := ro · G; Rb := ro · Yo r1 ←s Zq; R1 := r1 · G; R := r1 · Y stmto := {ro s.t. Ro = ro · G and R = ro · Yo} stmt1 := {r1 s.t. R1 = r1 · G and R = r1 · Yi} To ←PNizk(ro, stmto) π1 ← PNIzK(r1, Stmt1) <com (decom,com) ← Commit(1^,(R1, R', π1)) (Ro,R6,πo) if VNizk(stmto, To) ≠ 1 then abort (rx,ry) :=R=r1 · Ro; p<sZq2 if Vcom(com, decom, (R1, R'π1)) ≠ 1 then abort (decom,R1,R,m1,c') c' := crz(r1)-1α1 . EncHE(pk, H(m)(r1)-1 + pq) if VNizk(stmt1, π1） ≠ 1 then abort S ←DeCHe(skHe, C’)</td></tr></table></body></html>  \n\nLet $\\mathbb{G}$ be an elliptic curve group of order $q$ with base point $G$ and let $H:\\{0,\\bar{1}\\}^{*}\\stackrel{\\cdot}{\\rightarrow}\\{0,1\\}^{|q|}$ be a hash function. The ECDSA-based construction is shown in Fig. 5. Each pair of users $(U_{i},U_{j})$ generates a shared ECDSA public key ${\\mathsf{p k}}=(x_{i}\\cdot x_{j})\\cdot G$ via the $\\mathcal{F}_{{\\sf k g e n}}^{{\\sf E C D S A}}$ functionality. Additionally, $U_{i}$ receives a Paillier secret key sk and his share $x_{i}$ , whereas and $U_{j}$ receives the share $x_{j}$ and the Paillier encryption $c$ of $x_{i}$ . The key generation functionality is fully described in [41].  \n\nThe setup here is very similar to the setup of the generic construction in Fig. 4 except that the one-way function $g$ is now instantiated with discrete logarithm over elliptic curves. Each intermediate user $U_{i}$ receives a triple $(Y_{i-1},Y_{i},y_{i})$ such that $Y_{i}:=Y_{i-1}+y_{i}\\cdot G$ , from $\\mathcal{F}_{\\sf a n o n}$ . For technical reasons, the initiator of the AMHL also includes a proof of wellformedness for each $Y_{i}$ .  \n\nThe locking algorithm is initiated by two users $U_{i}$ and $U_{i+1}$ who agree on a message $m$ (which encodes a unique id) and on a value $Y_{i}:=y^{*}\\cdot G$ of unknown discrete logarithm. The two parties then run a coin tossing protocol to agree on a randomness $R=(r_{0}\\cdot r_{1})\\cdot Y_{i}$ . When compared to the Schnorr instance, the crucial technical challenge here is that the randomnesses are composed multiplicatively due to the structure of the ECDSA signature and therefore, the trick applied in the Schnorr construction no longer works here. $R$ is computed through a Diffie-Hellman-like protocol, where the parties exchange $r_{0}\\cdot Y_{i}$ and $r_{1}\\cdot Y_{i}$ and locally recompute $R$ . As before, the shared ECDSA signature is computed by “ignoring” the term $Y_{i}$ , since the parties are unaware of its discrete logarithm. The corresponding tuple $\\begin{array}{r}{\\Big(r_{x},s^{\\prime}:=\\frac{r_{x}\\cdot(x_{0}\\cdot x_{i+1})+\\not H(m)}{r_{0}\\cdot r_{1}}\\Big)}\\end{array}$ is jointly computed using the encryption of $x_{0}$ and the homomorphic properties of Paillier encryption. This effectively means that $(r_{x},s^{\\prime})=(r_{x},s^{*}\\cdot y^{*})$ , where $(r_{x},s^{*})$ is a valid ECDSA signature on $m$ . In order to check the validity of $s^{\\prime}$ , the parties additionally need to exchange the value $R^{*}:=(r_{0}\\cdot r_{1})\\cdot G\\stackrel{.}{=}(y^{*})^{-1}\\cdot R$ . The computation of $R^{*}$ (together with the corresponding consistency proof) is piggybacked in the coin tossing. Given $R^{*}$ , the validity of $s^{\\prime}$ can be easily verified by both parties by recomputing it “in the exponent”.  \n\nFrom the perspective of $U_{i+1}$ , releasing his left lock without a key for his right lock implies solving the discrete logarithm of $Y_{i}$ . On the converse, once the right lock is released, the value $y^{\\ast}+y_{i+1}$ is revealed (where $y_{i+1}$ is part of the state of $U_{i+1})$ and a valid signature can be computed as $\\left(r_{x},{\\frac{s^{\\prime}}{y^{*}}}\\right)$ . The security of the construction is established by the following theorem (see [41] for a full proof).  \n\nTheorem 3. Let COM be a secure commitment scheme and let NIZK be a non-interactive zero knowledge proof. If ECDSA signatures are strongly existentially unforgeable and Paillier encryption is ecCPA secure, then the $\\mathcal{F}$ nisnt rtuhcet i $\\mathbf{\\xi}(\\mathcal{F}_{\\mathsf{k g e n}}^{\\mathsf{E C D S A}}$ ,. $\\mathcal{F}_{\\mathsf{s y n}}$ ,C -Frsematl,i ${\\mathcal{F}}_{\\mathsf{a n o n},}$ ) -ihdyebarl ifdu nmctoidoenl.ality  \n\n# E. Hybrid AMHLs  \n\nWe observe that, when instantiated over the same elliptic curve $\\mathbb{G}$ , the setup protocols of the Schnorr and ECDSA constructions are identical. This means that the initiator of the lock does not need to know whether each intermediate lock is computed using the ECDSA or Schnorr method. This opens the doors to hybrid AMHLs: Given a unified setup, the intermediate pair of users can generate locks using an arbitrary locking protocol. The resulting AMHL is a chaining of (potentially) different locks and the release algorithm needs to be adjusted accordingly. For the case of ECDSA-Schnorr the user needs to extract the value $y^{\\ast}$ from the right Schnorr signature $(R^{*},s^{*})$ and his state $s^{R}:=s^{\\prime}=$ $\\boldsymbol s^{*}-\\boldsymbol y^{*}+\\boldsymbol y_{i+1}$ and $s^{I}:=(Y_{i},Y_{i+1},y_{i+1})$ . Given $y^{*}$ , he can factor it out of its left state $s^{L}=((r,s\\cdot y^{*}),m,\\mathsf{p k})$ and recover a valid ECDSA signature.  \n\nThe complementary case (Schnorr-ECDSA) is handled mirroring this algorithm. Similar techniques also apply to the generic construction, when the one-way function is instantiated appropriately (i.e., with discrete logarithm over the same curve). This flexibility enables atomic swaps and cross-currency payments (see Section VII). The security for the hybrid AMHLs follows similar to the standard case.  \n\n# VI. PERFORMANCE ANALYSIS  \n\n# A. Implementation Details  \n\nWe have developed a prototypical Python implementation to demonstrate the feasibility of our construction and evaluate its performance. We have implemented the cryptographic operations required by AMHLs as described in this work. We have used the Charm library [3] for the cryptographic operations. We have instantiated ECDSA over the elliptic curve secp256k1 (the one used in Bitcoin) and we have implemented the homomorphic one-way function as $g(x)\\mathrel{\\mathop:}=x\\cdot G$ over the same curve. Zero-knowledge protocols for discrete logarithms have been implemented using $\\Sigma$ protocols [21] and made non-interactive using the Fiat-Shamir heuristic [27]. For a commitment scheme we have used SHA-256 modeled as a random oracle [13].  \n\n# B. Evaluation  \n\nTestbed. We conducted our experiments on a machine with an Intel Core i7, 3.1 GHz and 8 GB RAM. We consider the Setup, Lock, Rel and Vf algorithms. We do not consider KGen as we use off-the-shelf algorithms without modification. Moreover, the key generation is executed only once upon creating a link and thus does not affect the online performance of AMHLs. We refer to [37] for a detailed performance evaluation of the ECDSA key generation. The results of our performance evaluation are shown in Table I.  \n\nComputation Time. We measure the computation time required by the users to perform the different algorithms. For the case of two-party protocols (e.g., Setup and Lock) we consider the time for the two users together. We make two main observations: First, the script-based construction based on discrete logarithm is faster than scriptless AMHLs. Second, all the algorithms require computation time of at most 60 milliseconds on a commodity hardware.  \n\n<html><body><table><tr><td></td><td></td><td>Generic</td><td>Schnorr</td><td>ECDSA</td></tr><tr><td rowspan=\"2\">Setup</td><td>Time (ms)</td><td>0.3.n</td><td>1·n</td><td>1·n</td></tr><tr><td>Comm (bytes)</td><td>96·n</td><td>128.n</td><td>128.n</td></tr><tr><td rowspan=\"2\">Lock</td><td>Time (ms)</td><td>一</td><td>2</td><td>60</td></tr><tr><td>Comm (bytes)</td><td>32</td><td>256</td><td>416</td></tr><tr><td rowspan=\"2\">Rel</td><td>Time (ms)</td><td>一</td><td>0.002</td><td>0.02</td></tr><tr><td>Comm (bytes)</td><td>0</td><td>0</td><td>0</td></tr><tr><td rowspan=\"2\">Vf</td><td>Time (ms)</td><td>一</td><td>0.6</td><td>0.06</td></tr><tr><td>Comm (bytes)</td><td>0</td><td>0</td><td>0</td></tr><tr><td colspan=\"2\">Comp Cost (gas)</td><td>350849·n</td><td>0</td><td>0</td></tr><tr><td colspan=\"2\">Lock size (bytes) Open size (bytes)</td><td>32 32</td><td>32+m 64</td><td>32+|m 64</td></tr></table></body></html>\n\nTABLE I: Comparison of the resources required to execute the algorithms for the different AMHLs. We denote by $n$ the length of the path. We denote the negligible computation times by – (e.g., single memory read). We denote the size of an application-dependent message by $|m|$ (e.g., a transaction in a payment-channel network).  \n\nCommunication Overhead. We measure the communication overhead as the amount of information that users need to exchange during the execution of interactive protocols, in particular, Setup and Lock. As expected, the generic construction based on discrete logarithm requires less communication overhead than scriptless constructions. The scriptless construction based on ECDSA requires a higher communication overhead. This is mainly due to having the signing key distributed multiplicatively and a more complex structure of the final signature when compared to the Schnorr approach.  \n\nComputation Cost. We measure the computation cost in terms of the gas required by a smart contract implementing the corresponding algorithm in Ethereum. Naturally, we consider this cost only for the generic approach based on discrete logarithm. We observe that setting up the corresponding contract requires 350849 unit of gas per hop. At the time of writing, each AMHL therefore costs considerably less than 0.01 USD.  \n\nApplication Overhead. We measure the overhead incurred by the application in terms of the memory required to handle application-dependent data, i.e., information defining the lock and the opening. In tune with the rest of measurements, the generic construction based on discrete logarithms requires the smallest amount of memory, both for lock and opening information. The different scriptless approaches require the same amount of memory from the application.  \n\nScalability. We study the running time and communication overhead required by each of the roles in a multihop lock protocol (i.e., sender, receiver and intermediate user). We consider only the generic approach and the ECDSA construction as representative of the scriptless approach. In the absence of significant metrics from current PCNs, we consider a path length of ten hops as suggested for similar payment networks such as the Ripple credit network [39].  \n\nRegarding the computation time, the sender requires 3ms with the generic approach and $10\\mathrm{ms}$ with the ECDSA scriptless approach. The computation time at intermediate users remain below 1ms for ECDSA and negligible with the generic approach as they only have to check the consistency of the locks with the predecessor and the successor, independently of the length of the path. Similarly, the computation overhead of the receiver remains below 1ms as she only checks if a given key is valid to open the lock according to the Vf algorithm. In summary, a non-private payment over a path of 5 users takes over $600\\mathrm{{ms}}$ as reported in [40]. Extending it with the constructions presented in this work provides formal privacy guarantees at virtually no overhead.  \n\nRegarding the communication overhead, the sender must send a message of about 960 bytes for the generic approach while about 1280 bytes are required instead if ECDSA scriptless locks are used. Since Sphinx, the anonymous communication network used in the LN, requires padded messages at each node to ensure anonymity, we foresee that every intermediate user must forward a message of the same size.  \n\nComparing these results with other multi-hop and privacy-preserving PCNs available in the literature, we make the following observations. First, the overhead for the constructions presented in this work are in tune with TeeChain [36], where the overhead per hop is about 0.4 ms in a setting where cryptographic operations required for the multi-hop locks have been replaced by a trusted execution environment. Second, our constructions significantly reduce the communication and computation overhead required by multi-hop HTLC [40]: While a payment using multi-hop HTLC requires approximately 5 seconds and 17MB of communication, our approach requires only few milliseconds and less than 1MB.  \n\nIn summary, the evaluation results show that even with an unoptimized implementation, our constructions offer significant improvements on computation and communication overhead and are ready to be deployed in practice.  \n\n# VII. APPLICATIONS  \n\nA. Payment-Channel Networks  \n\nAMHLs can be generically combined with a blockchain B to construct a fully-fledged PCN. Loosely speaking, the transformation works as follows: In the first round the sender sets up the locks running the Setup algorithm, then each pair of intermediate users executes the Lock protocol and establishes the following AMHL contract.  \n\n<html><body><table><tr><td>AMHL (Alice,Bob,, , t): 1) If Bob produces the condition k such that Vf(e, k) =</td></tr><tr><td>1 before t days, Alice pays Bobrcoins.</td></tr><tr><td>2) If t days elapse, Alice g gets back r coins.</td></tr></table></body></html>  \n\nWhere $\\ell$ is the output lock and $x$ and $t$ are chosen as specified in Section II. Note that we have to assume that B supports the $\\mathsf{V f}$ algorithm and time management in its script language. The rest of the payment is unchanged except that the intermediate users execute the Rel algorithm to extract a valid key $k$ to claim the corresponding payment. In the extended version [41], we provide the exact description of the algorithms and we prove the following theorem.  \n\nTheorem 4 (Informal). Let B a secure blockchain and let L be a secure AMHL, then we can construct a secure PCN (as defined in [40]).  \n\nNote that even though we defined security of AMHLs in the UC framework, the composition of multiple AMHL instances in one protocol as needed for realizing PCNs does not come for free if those instances have shared state. Formally, such shared state can arise from the use of a shared KGen algorithm. Consequently, we need to show for the KGen algorithms of the presented constructions that they behave independently over multiple invocations and finally make use of the JUC theorem [19] to obtain the composability result.  \n\nThis shows that AMHLs are the only cryptographic primitive (except for the blockchain) needed to construct PCNs. The only limitation is that the blockchain needs to support the verification of the corresponding contract in their scripting language (see the discussion above). For this reason, the scriptless-construction are preferred for those blockchains where the scripting language does not support the evaluation of a homomorphic one-way function (such as Bitcoin).  \n\nApplication to the Lightning Network. When applied to the LN, the ECDSA AMHL construction conveys several advantages: First, it eliminates the security issues existing in the current LN due to the use of the HTLC contract. Second, it reduces the transaction size as a single signature is required per transaction. This has the benefit of lowering the communication overhead, the transaction fees, and the blockchain memory requirements for closing a payment channel. In fact, we have received feedback from the LN community indicating the suitability of our ECDSA-based construction. Moreover, results from the implementation and testing done by LN developers are available [28], [29].  \n\nThe applicability of our proposals are not restricted to the LN or Bitcoin: There exist other PCNs that could similarly take advantage of the scriptless AMHLs presented in this work. For instance, the Raiden Network has been presented as a payment channel network for solving the scalability issue in Ethereum. The adoption of our ECDSA scriptless AMHLs would bring the same benefits to the Raiden Network.  \n\n# B. Atomic Swaps  \n\nAssume two users $U_{0}$ and $U_{1}$ holding coins in two different cryptocurrencies and want to exchange them. An atomic swap protocol ensures that either the coins are swapped or the balances are untouched, i.e., the exchange must be performed atomically. The widely used protocol for atomic swaps described in [16] leverages the HTLC contract to perform the swap. In a nutshell, an atomic swap can be seen as a multi-hop payment over a path of the form $(U_{0},U_{1},U_{0})$ . This approach inherits the security concerns of HTLC contract. Scriptless AMHLs also enhance this application domain with formally proven security guarantees.  \n\nAdditionally, our constructions contribute to the fungibility of the coins, a crucial aspect for any available (crypto)currency. Current protocols rely on transactions that are clearly distinguishable from regular payments (i.e., one-to-one payments). In particular, atomic swap transactions contain the HTLC contract, in contrast to regular transactions. Scriptless AMHLs eliminate this issue since even atomic swap transactions only require a single signature from a public key, making them indistinguishable from regular payments. Similar arguments also apply for multi-hop payments in PCNs.  \n\n# C. Interoperable PCNs  \n\nAmong the cryptocurrencies existing today, an interesting problem consists in performing a multi-hop payment where each link represents a payment channel defined in a different cryptocurrency. In this manner, a user with a payment channel funded in a given cryptocurrency can use it to pay to another user with a payment channel in a different cryptocurrency. Currently, the InterLedger protocol [50] tackles this problem with a mechanism to perform cross-currency multihop payments that relies on the HTLC contract, aiming to ensure the payment atomicity across different hops.  \n\nHowever, apart from the already discussed issues associated with HTLC, the InterLedger protocol mandates that all cryptocurrencies implement HTLC contracts. This obviously hinders the deployment of this approach. Instead, it is possible to use the different AMHL constructions presented in this work on a single path, as described in Section V-E, therefore expanding the domain of cross-currency multi-hop payments.  \n\n# VIII. RELATED WORK  \n\nA recent work [24] shows a protocol to compute an ECDSA signature using multi-party computation. However, it is not as efficient as Lindell’s approach [37].  \n\nThere exists extensive literature proposing constructions for payment channels [22], [23], [35], [48]. These works focus on a single payment channel, and their extension to PCNs remain an open challenge. TumbleBit [31] and Bolt [30] support off-chain payments while achieving payment anonymity guarantees. However, the anonymity guarantees of these approaches are restricted to single-hop payments and their extension to support multi-hop payments remains an open challenge.  \n\nState channels [25], [33], [43] and state channel networks [26] cannot work with prominent cryptocurrencies except from Ethereum. TeeChain [36] requires the availability of a trusted execution environment at each user. Instead, our proposal can be seamlessly deployed today in virtually all cryptocurrencies, including Ethereum. In addition, AMHL enables operations between different blockchains, which is clearly not the case for Ethereum-only solutions. If we focus on the specific setting of payment channels in Ethereum, AMHL is more efficient (i.e., it requires less gas and bytes) as payment conditions are encoded in the signature and not in additional scripts. Finally, [25], [26] provide a different privacy notion: two endpoints can communicate privately but the intermediate nodes know that a virtual channel is opened between them. This information is instead concealed with AMHL. Formalizing this privacy leakage and comparing it with our privacy definition is an interesting future work.  \n\nThe LN has emerged as the most promising approach for PCN in practice. Its current description [6] is being followed by several implementations [5], [8], [10]. However, these implementations suffer from the security and privacy issues with PCNs as described in this work. Instead, we provide several constructions for AMHLs that can be leveraged to have secure and anonymous multi-hop payments.  \n\nMalavolta et al. [40] propose a protocol for secure and anonymous multi-hop payments compatible with the current LN. Their approach, however, imposes an overhead of around $\\mathrm{5~MB}$ for the nodes in the network, therefore hindering its deployability. Here, we propose several efficient constructions that require only a few bytes of communication.  \n\nIn the recent literature, we can find proposals for secure and privacy-preserving atomic swaps. Tesseract [14] leverages trusted hardware to perform real time cryptocurrency exchanges. The Merkleized Abstract Syntax Trees (MAST) protocol has been proposed as a privacy solution for atomic swaps [34]. However, MAST relies on scripts that are not available in the major cryptocurrencies today. Moreover, specific contracts for atomic swaps hinder the fungibility of the currency: An observer can easily differentiate between a regular payment and a payment resulting from an atomic swap.  \n\n# IX. CONCLUSION  \n\nWe rigorously study the cryptographic core functionality for security, privacy, and interoperability guarantees in PCNs, presenting a new attack on today’s PCNs (the wormhole attack) and proposing a novel cryptographic construction (AMHLs). We instantiate AMHLs in two settings: script-based and scriptless. In the script-based setting, we demonstrate that AMHLs can be realized from any (linear) homomorphic operation. In the scriptless setting, we propose a construction based on ECDSA, thereby catering the vast majority of cryptocurrencies deployed today. Our performance evaluation shows that AMHLs are practical: All operations take less than 100 milliseconds to run and introduce a communication overhead of less than 500 bytes.  \n\nWe show that AMHLs can be combined in a single path and are of interest in several applications apart from PCNs, such as atomic swaps and interoperable PCNs. In fact, LN developers have implemented and tested AMHL for LN. In the future, we plan to devise cryptographic instantiations of PCNs for the few cryptocurrencies not yet covered, most notably Monero.  \n\nAcknowledgements. The authors would like to thank Elizabeth Stark, Conner Fromknecht and Olaluwa Osuntokun (Lightning Network Labs) for insightful discussions on the writeup of this paper. They would also like to thank Foteini Baldimitsi for her helpful comments on Universal Composability.  \n\nThis work has been partially supported by the National Science Foundation under grant CNS-1719196, the European Research Council (ERC) under the European Unions Horizon 2020 research (grant agreement No 771527-BROWSEC); by Netidee through the project EtherTrust (grant agreement 2158) and PROFET (grant agreement P31621); by the Austrian Research Promotion Agency through the Bridge-1 project PR4DLT (grant agreement 13808694); by COMET K1 SBA, ABC; by Chaincode Labs and the Austrian Science Fund (FWF) through the Meitner program; by the German research foundation (DFG) through the collaborative research center 1223; by the German Federal Ministry of Education and Research (BMBF) through the project PROMISE (16KIS0763); and by the state of Bavaria at the Nuremberg Campus of Technology (NCT). NCT is a research cooperation between the Friedrich-Alexander-Universitat Erlangen-Nurnberg (FAU) and the Technische Hochschule Nuirnberg Georg Simon Ohm (THN).  \n\n# REFERENCES  \n\n[1] “5 potential use cases for bitcoin’s lightning network,” https: //tinyurl.com/y6u4tnda.   \n[2] “Blockchain explorer information,” https://blockchain.info/.   \n[3] “Charm: A framework for rapidly prototyping cryptosystems,” https://github.com/JHUISI/charm.   \n[4] “Ethereum website,” https://www.ethereum.org/.   \n[5] “Lightning network daemon,” https://github.com/ lightningnetwork/lnd.   \n[6] “Lightning network specifications,” https://github.com/ lightningnetwork/lightning-rfc.   \n[7] “Raiden network,” http://raiden.network/.   \n[8] “A scala implementation of the lightning network,” https:// github.com/ACINQ/eclair.   \n[9] “Stress test prepares visanet for the most wonderful time of the year,” Blog entry, http://www.visa.com/blogarchives/us/2013/10/10/ stress-test-prepares-visanet-for-the-most-wonderful-time-of-the-year/ index.html.   \n[10] “c-lightning – a lightning network implementation in c,” Acceses in May 2018, https://github.com/ElementsProject/lightning.   \n[11] E. Androulaki, A. Barger, V. Bortnikov, C. Cachin, K. Christidis, A. D. Caro, D. Enyeart, C. Ferris, G. Laventman, Y. Manevich, S. Muralidharan, C. Murthy, B. Nguyen, M. Sethi, G. Singh, K. Smith, A. Sorniotti, C. Stathakopoulou, M. Vukolic, S. W. Cocco, and J. Yellick, “Hyperledger fabric: A distributed operating system for permissioned blockchains,” in EuroSys, 2018, pp. 30:1–30:15.   \n[12] M. Backes, A. Kate, P. Manoharan, S. Meiser, and E. Mohammadi, “Anoa: A framework for analyzing anonymous communication protocols,” in CSF, 2013, pp. 163–178.   \n[13] M. Bellare and P. Rogaway, “Random oracles are practical: A paradigm for designing efficient protocols,” in CCS, 1993.   \n[14] I. Bentov, Y. Ji, F. Zhang, Y. Li, X. Zhao, L. Breidenbach, P. Daian, and A. Juels, “Tesseract: Real-time cryptocurrency exchange using trusted hardware,” in ePrint Archive, 2017, p. 1153. [Online]. Available: http://eprint.iacr.org/2017/1153   \n[15] M. Blum, P. Feldman, and S. Micali, “Non-interactive zeroknowledge and its applications,” in Symposium on Theory of Computing, 1988, pp. 103–112.   \n[16] S. Bowe and D. Hopwood, “Hashed time-locked contract transactions,” Bitcoin Improvement Proposal, https://github.com/ bitcoin/bips/blob/master/bip-0199.mediawiki.   \n[17] J. Camenisch and A. Lysyanskaya, “A formal treatment of onion routing,” in CRYPTO, 2005.   \n[18] R. Canetti, “Universally composable security: A new paradigm for cryptographic protocols,” in FOCS, 2001, pp. 136–.   \n[19] R. Canetti and T. Rabin, “Universal composition with joint state,” in Annual International Cryptology Conference, 2003, pp. 265–281.   \n[20] K. Croman, C. Decker, I. Eyal, A. E. Gencer, A. Juels, AKoaMilaahEnD and R. Wattenhofer, “On Scaling Decentralized Blockchains,” in FC, 2016, pp. 106–125.   \n[21] I. Damgard, “On $\\sigma$ -protocols,” Lecture Notes, University of Aarhus, Department for Computer Science, 2002.   \n[22] C. Decker, R. Russel, and O. Osuntokun, “eltoo: A simple layer2 protocol for bitcoin,” https://blockstream.com/eltoo.pdf.   \n[23] C. Decker and R. Wattenhofer, “A fast and scalable payment network with bitcoin duplex micropayment channels,” in Stabilization, Safety, and Security of Distributed Systems, 2015.   \n[24] J. Doerner, Y. Kondi, E. Lee, and a. shelat, “Secure two-party threshold ecdsa from ecdsa assumptions,” in S&P, 2018.   \n[25] S. Dziembowski, L. Eckey, S. Faust, and D. Malinowski, “Perun: Virtual payment hubs over cryptocurrencies,” in ePrint, 2017. [Online]. Available: https://eprint.iacr.org/2017/635   \n[26] S. Dziembowski, S. Faust, and K. Hostakova, “General state channel networks,” in CCS, 2018.   \n[27] A. Fiat and A. Shamir, “How to prove yourself: Practical solutions to identification and signature problems,” in Conference on the Theory and Application of Cryptographic Techniques, 1986.   \n[28] C. Fromknecht, “Instantiating scriptless 2p-ecdsa: fungible 2-of-2 multisigs for bitcoin today,” Talk at ScalingBitcoin 2018, https://tokyo2018.scalingbitcoin.org/transcript/ tokyo2018/scriptless-ecdsa.   \n[29] ——, “tpec: 2p-ecdsa signatures,” Github repository, https:// github.com/cfromknecht/tpec.   \n[30] M. Green and I. Miers, “Bolt: Anonymous payment channels for decentralized currencies,” in CCS, 2017.   \n[31] E. Heilman, L. Alshenibr, F. Baldimtsi, A. Scafuro, and S. Goldberg, “TumbleBit: An untrusted bitcoin-compatible anonymous payment hub,” in NDSS, 2017.   \n[32] J. Katz, U. Maurer, B. Tackmann, and V. Zikas, “Universally composable synchronous computation,” in Theory of cryptography, 2013, pp. 477–498.   \n[33] R. Khalil and A. Gervais, “Revive: Rebalancing off-blockchain payment networks,” in CCS, 2017, pp. 439–453.   \n[34] J. Lau, “Merkelized abstract syntax tree,” Bitcoin Improvement Proposal, https://tinyurl.com/yc9jh6lv.   \n[35] J. Lind, I. Eyal, P. R. Pietzuch, and E. G. Sirer, “Teechan: Payment channels using trusted execution environments,” 2016, http://arxiv.org/abs/1612.07766.   \n[36] J. Lind, O. Naor, I. Eyal, F. Kelbert, P. R. Pietzuch, and E. G. Sirer, “Teechain: Reducing storage costs on the blockchain with offline payment channels,” in Systems and Storage Conference, 2018, p. 125.   \n[37] Y. Lindell, “Fast Secure Two-Party ECDSA Signing,” in CRYPTO, 2017, pp. 613–644.   \n[38] L. Luu, V. Narayanan, C. Zheng, K. Baweja, S. Gilbert, and P. Saxena, “A secure sharding protocol for open blockchains,” in CCS, 2016, pp. 17–30.   \n[39] G. Malavolta, P. Moreno-Sanchez, A. Kate, and M. Maffei, “SilentWhispers: Enforcing security and privacy in credit networks,” in NDSS, 2017.   \n[40] G. Malavolta, P. Moreno-Sanchez, A. Kate, M. Maffei, and S. Ravi, “Concurrency and privacy with payment-channel networks,” in CCS, 2017.   \n[41] G. Malavolta, P. Moreno-Sanchez, C. Schneidewind, A. Kate, and M. Maffei, “Privacy-preserving multi-hop locks for blockchain scalability and interoperability,” Cryptology ePrint Archive, Report 2018/472, 2018, https://eprint.iacr.org/2018/ 472.   \n[42]   P. McCorry, M. Moser, S. F. Shahandashti, and F. Hao, “Towards bitcoin payment networks,” in ACISP, 2016.   \n[43] A. Miller, I. Bentov, R. Kumaresan, and P. McCorry, “Sprites: Payment channels that go faster than lightning,” in FC, 2019.   \n[44] P. Moreno-Sanchez, N. Modi, R. Songhela, A. Kate, and S. Fahmy, “Mind your credit: Assessing the health of the ripple credit network,” in WWW, 2018, pp. 329–338.   \n[45] P. Paillier, “Public-key cryptosystems based on composite degree residuosity classes,” in International Conference on the Theory and Applications of Cryptographic Techniques, 1999, pp. 223–238.   \n[46] A. Poelstra, “Lightning in scriptless scripts,” Mailing list post, https://lists.launchpad.net/mimblewimble/msg00086.html.   \n[47] “Scriptless scripts,” Presentation slides, https://download.wpsoftware.net/bitcoin/wizardry/mw-slides/ 2017-05-milan-meetup/slides.pdf.   \n[48] J. Poon and T. Dryja, “The bitcoin lightning network: Scalable off-chain instant payments,” Technical Report, https://lightning. network/lightning-network-paper.pdf.   \n[49] O. Regev, “On lattices, learning with errors, random linear codes, and cryptography,” Journal of the ACM, vol. 56, no. 6, p. 34, 2009.   \n[50] E. S. Stefan Thomas, “A Protocol for Interledger Payments,” Whitepaper, https://interledger.org/interledger.pdf.   \n[51] P. Wuille, “Schnorr Bitcoin Improvement Proposal,” https: //github.com/sipa/bips/blob/bip-schnorr/bip-schnorr.mediawiki.  "}