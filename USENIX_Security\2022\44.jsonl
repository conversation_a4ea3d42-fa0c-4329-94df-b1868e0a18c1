{"text": "# Dynamic Searchable Encryption with Optimal Search in the Presence of Deletions  \n\n<PERSON><PERSON> and <PERSON><PERSON>, Hong Kong University of Science and Technology; <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, UC Santa Cruz  \n\nhttps://www.usenix.org/conference/usenixsecurity22/presentation/chamani  \n\n# This paper is included in the Proceedings of the 31st USENIX Security Symposium.  \n\nAugust 10–12, 2022 • Boston, MA, USA 978-1-939133-31-1  \n\nOpen access to the Proceedings of the 31st USENIX Security Symposium is sponsored by USENIX.  \n\n# Dynamic Searchable Encryption with Optimal Search in the Presence of Deletions  \n\nJavad <PERSON><PERSON> HKUST HKUST Karbasforushan UC Santa Cruz UC Santa Cruz  \n\n# Abstract  \n\nWe focus on the problem of Dynamic Searchable Encryption (DSE) with efficient (optimal/quasi-optimal) search in the presence of deletions. Towards that end, we first propose OSSE, the first DSE scheme that can achieve asymptotically optimal search time, linear to the result size and independent of any prior deletions, improving the previous state of the art by a multiplicative logarithmic factor. We then propose our second scheme LLSE, that achieves a sublogarithmic search overhead $(\\log\\log i_{w}$ , where $i_{w}$ is the number or prior insertions for a keyword) compared to the optimal achieved by OSSE. While this is slightly worse than our first scheme, it still outperforms prior works, while also achieving faster deletions and asymptotically smaller server storage. Both schemes have standard leakage profiles and are forward-and-backward private. Our experimental evaluation is very encouraging as it shows our schemes consistently outperform the prior state-ofthe-art DSE by $1.2–6.6\\times$ in search computation time, while also requiring just a single roundtrip to receive the search result. Even compared with prior simpler and very efficient constructions in which all deleted records are returned as part of the result, our OSSE achieves better performance for deletion rates ranging from $45\\%$ , while the previous state-of-the-art quasi-optimal scheme achieves this for $65{-}75\\%$ deletion rates.  \n\n# 1 Introduction  \n\nSearchable encryption (SE) [18, 62] allows a user to upload her dataset to an untrusted server in encrypted form, while maintaining the ability to search over it without revealing raw data to the server. Since its introduction by Song et al. [62], it has become a heavily studied research topic with numerous works that try to improve its security [22, 39, 42, 57], efficiency [6, 21, 25, 26, 54], expressiveness of supported types of queries [11, 12, 16, 23, 24, 29, 38, 52], and support for multiple users [35, 59, 60, 67, 68]. One common denominator in SE research has been the focus on highly practical schemes that can be used in real-world applications and scale to large datasets, at the cost of a clearly defined leakage of information about the dataset to the server such as whether queries are repeated, the size of the returned result, and when the same results are returned. Partly due to this emphasis on efficient performance, SE has been used in a variety of settings starting from simple text keyword search [18, 62], and including encrypted relational [12, 40], NoSQL [58], or graph [31, 50, 52] databases. Consequently, SE has been proposed for applications such as annotated image search [3], encrypted email clients [53], and very recently for maintaining a secure gun registry [43].  \n\nOur focus in this work is dynamic searchable encryption (DSE) [8, 44, 45], that is, schemes that allow the user to efficiently modify the dataset, without having to re-run a costly setup process from scratch. More specifically, we are interested in DSE with efficient search performance in the presence of deletions. Ideally, the search time for a query should be optimal, i.e., proportional to the result size itself, independently of the number of prior deletions that affect this result. For instance, consider an extremely simple example for a DSE keyword search over a textual dataset, returning which files contain it. If the keyword appears in 100 files but subsequently 90 of them are deleted, the search overhead should only be proportional to its actual returned result (i.e., 10). Given the wide range of potential SE applications, this can make a big difference in the system’s performance.  \n\nIn particular, in a graph database deleting a node immediately triggers deletions of all its adjacent edges. For a relational database (likewise, for streaming systems [4, 36] and pure key-value storage [1]), which use a (LSM-like) keyvalue store (or an encrypted store in the case of DSE) as a storage layer [30], a single deletion/update of a tuple/value will produce a multiplicative number of deleted/cancellation tuples—at least one for each attribute. These deleted tuples can later interleave with future search queries affecting the search performance. Such issues have also been observed recently by [61]; that work considers settings in which “lowlevel” deletes are triggered not only by users’ direct actions, and studies scenarios of delete-intensive query workloads.  \n\nTable 1: Comparison with prior DSE with quasi-optimal search. $N$ is the upper bound on the $(w,i d)$ pairs and $D$ total number of deletions, $|W|$ is the number of distinct keywords, and for each keyword, $i_{w}$ and $d_{w}$ are the number of insertions and deletions, $a_{w}=i_{w}+d_{w}$ is the total number of updates, and $n_{w}=i_{w}-d_{w}$ is the number of non-deleted $(w,i d)$ pairs. RT is the number of roundtrips for completing a search query. We assume oblivious maps are instantiated with [66]. For QOS, we refer to the version from [20] with counters locally stored at the client.  \n\n<html><body><table><tr><td>Scheme</td><td>Search</td><td>Insert</td><td>Delete</td><td>SearchRT</td><td>ServerStorage</td><td>BP</td></tr><tr><td>SPS [63]</td><td>O(min{aw + logN,nw log² N})</td><td>O(log² N)</td><td>O(log² N)</td><td>1</td><td>O(N)</td><td></td></tr><tr><td>Orion [14]</td><td>O(nw log² N)</td><td>O(log² N)</td><td>O(log² N)</td><td>O(logN)</td><td>O(N)</td><td>I</td></tr><tr><td>Horus [14]</td><td>O(nw log dw log N + 1og2 (W|)</td><td>O(log² N)</td><td>O(log² N)</td><td>O(logN)</td><td>O(N)</td><td>IⅢI</td></tr><tr><td>QOS [20]</td><td>O(nw logiw)</td><td>O(log² N)</td><td>0(log? N)</td><td>2</td><td>O(N)</td><td>IⅢI</td></tr><tr><td>OSSE (Sec.4)</td><td>O(nw + logiw)</td><td>O(log² N)</td><td>O(log? N)</td><td>1</td><td>O(N + DlogN)</td><td>IⅢI</td></tr><tr><td>LLSE (Sec. 5)</td><td>O((nw + logiw) · loglogN)</td><td>O(log² N)</td><td>O(log² N)</td><td>1</td><td>O(N)</td><td>IⅢI</td></tr></table></body></html>  \n\nPrior DSE with (quasi-)optimal search. The majority of DSE schemes in the literature (e.g., [9, 14, 20, 28]) adopt a “lazy deletion” policy, treating deletions as actual entries on their own behalf (similar to the LSM-like policy mentioned above). In subsequent searches, the client retrieves all relevant inserted entries and “deletion entries” and filters out the actual result. While this leads to a conceptually simple search process, it can have significant adverse impact on the system’s search performance, as motivated in our discussion above. That said, a small number of works focus on the same goal as we do and we provide an overview of their asymptotic performance in Table 1, starting from Stefanov et al. [63] that proposed the first DSE where the search performance is always quasi-optimal, i.e., within a poly-logarithmic factor from the result size. Unfortunately, their construction has the drawback that it reveals to the server the id’s of records that contained the searched keyword but have since been deleted1, which may not be acceptable in some applications (e.g., as related to the right-to-erasure of EU GDPR article 17).  \n\nFormally this property has been defined as backward privacy in [9], distinguishing it from forward privacy [15, 63] that minimizes information leakage during updates. Focusing on works that achieve both forward and backward privacy, the best such scheme (QOS from [20]) requires $O(n_{w}\\log i_{w})$ for a search for keyword $w$ with result size $n_{w}$ after $i_{w}$ relevant entry insertions.2 Previous works (Orion and Horus from [14]) embed the entire dataset inside an oblivious index [19,32,64,66]. If this is instantiated with the state-of-the-art oblivious indexes [5], the result would be asymptotically the same as QOS, i.e., a logarithmic factor slower than the optimal. Interestingly, there exist very strong lower-bound results [32, 51, 56] that show that relying solely on oblivious indexes one cannot hope to further improve search performance asymptotically.  \n\nMotivated by this, we ask whether it is possible to design a forward-and-backward private DSE with search performance that is asymptotically within a sublogarithmic factor from the optimal $O(n_{w})$ , or indeed with optimal search performance.  \n\nThis work. We present the first DSE schemes that answer the above question in the affirmative. Our first construction OSSE (Section 4) is the only existing DSE that, for large enough result sizes, requires an optimal number of $O(n_{w})$ operations for search queries (and for very small hidden constants). Its main drawback is a somewhat increased storage, as the size of the encrypted storage grows logarithmically with the total number of deletions (that said, in our experimental evaluation we show that this is not a prohibitive factor in practice). Our second scheme LLSE requires $O(n_{w}\\log\\log i_{w})$ operations for searches with large enough results. While this is weaker than our first result, LLSE still outperforms the prior state of the art QOS by a factor or $O(\\log i_{w}/\\log\\log i_{w})$ , while also achieving better deletion performance than OSSE and QOS, and asymptotically optimal storage (see Table 1). Moreover, both our schemes retrieve the search result with a single roundtrip.  \n\nAt the core of our results lies a carefully chosen arrangement of entries in the encrypted dataset as conceptual keyword binary trees (see Figure 3 and discussion in Section 4.1). With insertions lying at the leaf level, subsequent deletions “prune” this tree to minimize the number of traversed such nodes during a search. The key challenge is how to manipulate these trees without revealing significant information to the server. Interestingly, our schemes use an oblivious map to store tree data, however, they only access it during updates (using retrieved information as “guidelines” to prepare future searches) thus making searches extremely fast and non-interactive.  \n\nOur contributions can be summarized as follows:   \n1. We propose OSSE, the first DSE that can achieve asymptotically optimal search performance (for $n_{w}\\ge\\log i_{w,}$ ), independently of prior deletions, and LLSE, a DSE with search performance within an asymptotic loglog factor from the optimal, that also achieves faster deletions and linear storage. For both schemes, we prove their security for standard leakages and show they are forward-andbackward private (BP-III).  \n\n2. We propose a series of performance optimizations (Section 6) and report on the efficiency of prototype implementations of our schemes (Section 7.1) with encouraging results (e.g., optimized OSSE takes $<1\\mathrm{ms}$ of computation time to retrieve a result of 100 id’s and $<10\\mathrm{ms}$ for a result of 20K id’s, from a database of 1M entries).  \n\n3. We compare the performance of our schemes vs. existing quasi-optimal ones (Section 7.2) for different settings (variable deletion rates, for both random and “structured” deletions). Both our constructions consistently outperform QOS by roughly $\\mathbf{1}.2\\mathbf{-4}\\times$ but for large result sizes and large deletion rates the improvement is up to ${\\bf6.6\\times}$ .  \n\n4. We also compare our schemes with conceptually simpler DSE with “lazy” deletion (Section 7.2). As expected, as deletion rates increase our schemes outperform $\\mathsf{S D}_{d}$ from [20]. Concretely, for OSSE this happens for rates $45{\\cdot}55\\%$ , and our fully optimized OSSE with result caching, in the best case, can outperform $\\mathsf{S D}_{d}$ even after a single deletion. Surprisingly, for large enough sizes this carries over even for a “succinct-client-storage” version of our schemes albeit for larger deletion rates.  \n\nRelated Work. In our security formulation, we use the standard SE definition of [18] extended to the dynamic setting in [63]. The QOS scheme from Demertzis et al. [20] is currently the best existing quasi-optimal DSE and we benchmark our constructions’ performance with it. In [20], the authors propose two versions of QOS with small $O(1)$ and large $O(|W|)$ client storage. We mainly compare our schemes with the second one, but in Section 7.2 we measure performance in both settings. From a technical viewpoint, QOS also encodes entries in keyword trees, however, the way they are maintained cannot achieve our target of sublogarithmic overhead. Moreover, the design of OSSE, allows us to benefit from “early” stop while parsing the tree if empty nodes are found, whereas QOS needs to keep traversing the layers.  \n\nRegarding “lazy” deletion schemes, there exist several candidates for performance comparison, e.g., from [9, 14, 20]. Among them, Mitra (with the recently proposed optimizations of [17]) and $\\mathsf{S D}_{d}$ stand out. Although they have very similar performance, we chose the latter since, as shown in [20], it can give slightly faster searches, and it is the only one with succinct client storage without performing oblivious map accesses during searches. One point to note is that the way all existing DSE schemes implement “lazy” deletion is by storing a separate record for each deletion. This is unlike “lazy” deletion in plaintext databases where the deletion is usually marked in-place (e.g., using a special flag). Storing this entry separately is necessary; touching the same location in the encrypted index during an update as a previously accessed one would violate forward privacy (see Section 2). Consequently, when using such a scheme the server observes the result size “growing” after deletions, whereas in our schemes it “shrinks.” In both cases, this is already captured by the standard leakage profile used in the literature (see Section 2 and Appendix B.1). We also note many of these DSE schemes use similar building blocks as ours (encrypted maps and oblivious indexes). However, the way we use them to embed our “shrinking” keyword trees (Section 4.1) is what allows us to match practical performance with theoretical improvements, achieving the first scheme with optimal search.  \n\nAnother DSE scheme is Aura [65] that uses revocable encryption to achieve single-roundtrip searches, same as ours. However, its approach significantly blows up the client storage as it requires storing a representation of all deletions locally. Moreover, during searches [65, Alg. 1] the server extracts all insertion and deletion entries for keyword $w$ before “filtering out” the latter so that scheme’s search is (at least) linear to $a_{w}$ in the worst case. Finally, recent work by Patel et al. [56] shows it is fundamentally impossible (in the leakage cell probe model) to achieve sublogarithmic search overhead if one requires searches and updates to be independently simulatable without state “carrying” across them during simulation. In relation to this, our schemes are positive evidence that it is possible to overcome this lower bound (even in the more stringent setting with deletions) if one can settle for independent update simulation (guaranteed by our forward privacy) whose state is then available for subsequent searches.  \n\n# 2 Preliminaries  \n\nWe denote by $\\uplambda$ a security parameter and by $\\nu(\\lambda)$ a negligible function in $\\uplambda$ . PPT stands for probabilistic polynomial-time. Our protocols are executed between two parties, a client and a server. The notion $P(x;y)$ denotes a (possibly multi-round) protocol execution where $x$ and $y$ are the client’s input and the server’s input, respectively.  \n\nWe consider a collection of $F$ files/documents (this is an abstraction that can possibly capture other data types, e.g., semi-structured data, database records, etc.) with identifiers $i d_{1},\\ldots$ , $i d_{F}$ , each of which contains textual keywords from a given alphabet Λ. The database $D B$ consists of keywords and file identifiers such that $(w,i d)\\in D B$ if and only if the file id contains keyword $w$ . Let $W$ denote the set of all keywords in $D B$ , $|W|$ its cardinality, and $N$ an upper bound on the total number of keyword/document pairs. Then, $D B(w)$ corresponds to the set of documents containing keyword $w$ .  \n\nWe rely on classic cryptographic tools, such as pseudorandom functions and standard symmetric-key encryption. For completeness, we provide their descriptions in Appendix A.  \n\nOblivious Maps. A map (dictionary) is a data structure that maps addresses (keys) to values and provides read (get) and write (put) access methods. An oblivious map (OMAP) is a privacy-preserving version of a regular map that hides the type and content of operations. An OMAP consists of three procedures: (i) OMAP.Setup for initializing the data structure, (ii) OMAP.put to add/overwrite a key/value pair, and (iii) OMAP.get to retrieve the value for a given key. Intuitively, all equal-length sequences of data accesses (get/put) are indistinguishable from each other, even for an adversary that stores the data structure itself (without knowing the secret key). More concretely, this interaction can be simulated given only the number of operations (see [66] for the detailed security formulation). In our schemes, we use the popular OMAP of Wang et al. [66] which stores an AVL-tree inside a PathORAM [64] oblivious array. For a map with capacity $N$ , each access takes ${\\cal O}(\\log^{2}N)$ operations and ${\\cal O}(\\log N)$ roundtrips. We refer interested readers to [66] for additional details.  \n\n# 3 Dynamic Searchable Encryption  \n\nA dynamic symmetric searchable encryption scheme $(D S E)\\Sigma$ consists of algorithm Setup, and protocols Search and Update that are executed between a client and a server.  \n\n• $\\mathrm{Setup}(1^{\\lambda},N)$ returns $(K,\\mathbb{\\sigma},E D B)$ where $K$ is the client’s secret key, $\\upsigma$ its local state, and $E D B$ is an (empty) encrypted database, initialized for capacity $N$ , sent to the server. • $\\operatorname{Search}(K,w,\\mathbb{\\sigma};E D B)$ is a (possibly interactive) protocol for retrieving $\\overline{{D B(w)}}$ , and may also modify $K,\\infty,E D B$ . Update $(K,o p,i d,w,\\mathbb{\\sigma};E D B)$ is a (possibly interactive) protocol for inserting/removing a document-keyword entry $(i d,w)$ to/from the database. Operation $o p$ is either add or del and the protocol may modify $K,\\upsigma$ and $E D B$ .  \n\nHere, we mostly follow the API description of [8,9,14]. Using these procedures, the client runs Setup for an empty database, followed by up to $N$ executions of insertions to “populate” $E D B$ . Other works [28, 46] propose different but functionally equivalent definitions for Update (e.g., inserting or deleting an entire document, which can be decomposed to multiple calls of Update). Finally, the above definition isolates the goal of retrieving only $D B(w)$ during Search, i.e., the id’s of all files/documents containing $w$ . In some applications, the client would like to retrieve the actual documents; this is omitted from our model as it can always be done with an extra round of interaction after the completion of Search.  \n\nAt a high level, a DSE is correct if the returned $D B(w)$ result is correct for every query and the given sequence of prior updates. The security of a DSE scheme is parametrized by a leakage function $\\mathcal{L}\\dot{=}(\\mathcal{L}^{S t p},\\mathcal{L}^{S r c h},\\mathcal{L}^{U p d t})$ that captures the information that is revealed to server during the execution of the different processes. $\\mathcal{L}^{S t p}$ corresponds to leakage during setup, $\\mathcal{L}^{S r c h}$ during a search operation, and $\\mathcal{L}^{U p d t}$ during updates. Commonly encountered types of leakage in the relevant literature, that also occur with our schemes, consist of: (i) search pattern that reveals which searches are related to the same $w$ , (ii) volume pattern, i.e., $|D B(w))|$ during a search for $w$ , and (iii) database size leakage during setup (in our case, the upper bound $N$ ). Another very important type of leakage is access pattern that reveals the actual contents of $D B(w)$ when $w$ is searched for. This is inherently unavoidable when the actual documents need to be retrieved as explained above (unless stored with “costly” oblivious storage). Here, we focus on retrieving $D B(w)$ . Thus our schemes do not directly leak this but, when used in an application that retrieves the actual documents, this has to be taken into account.  \n\nInformally, a secure DSE scheme with leakage $\\mathcal{L}$ should reveal nothing about the database $D B$ other than this leakage. This is formally captured by a standard real/ideal experiment with two games $\\mathsf{R e a l}^{\\mathsf{D S E}}$ , $\\mathsf{i d e a l}^{\\mathsf{D S E}}$ presented in Figure 8 in Appendix B, using the following formulation from [63].  \n\nDefinition 1. A DSE scheme $\\Sigma$ is adaptively-secure with leakage function $\\mathcal{L}$ , iff for any PPT adversary Adv issuing polynomially many queries $q$ , there exists a stateful PPT simulator $S i m=$ (SimInit, SimSearch, SimU pdate) such that $\\vert\\operatorname*{Pr}[\\mathsf{R e a l}_{A d v}^{\\mathsf{D S E}}(\\lambda,q)=1]-\\operatorname*{Pr}[\\mathsf{l d e a l}_{A d v,S i m,\\mathcal{L}}^{\\mathsf{D S E}}(\\bar{\\lambda},q)=1]\\vert<\\nu(\\lambda).$  \n\nAs explained above, many DSE schemes store deletions as actual entries in $E D B$ and filter them to retrieve the actual result during searches. Ghareh Chamani et al. [14], proposed the following definition for schemes that avoid this and achieve search time close to the optimal $O(n_{w})$ .  \n\nDefinition 2. A DSE scheme $\\Sigma$ has optimal (resp.   \nquasi-optimal) search, $i f$ Search takes $O(|D B(w)|)$ (resp.   \n$O(|D B(w)|$ · polylog $(N),$ ) operations.  \n\nForward & Backward Privacy. Forward privacy [15, 63] limits the information revealed due to updates in $E D B$ . Informally, a scheme is forward private if it is not possible to relate an update to previous operations, when it takes place. E.g., it should be impossible to tell whether an insertion is for a new keyword or a previously existing/searched one. Previous works (e.g., [69]) have shown the potential of forward privacy to thwart certain types of leakage-abuse attacks.  \n\nDefinition 3. An $\\mathcal{L}$ -adaptively-secure $D S E$ scheme that supports addition/deletion of a single keyword is forward private iff the update leakage function $\\mathcal{L}^{U p d t}$ can be written as: $\\mathcal{L}^{\\omega}{}^{p d t}(o p,\\boldsymbol{w},i d)=\\mathcal{L}^{\\prime}{}^{\\boldsymbol{U}}{}^{p\\check{d}t}(o p,i d)$ where $\\mathcal{L}^{\\prime}$ is a stateless function, op is insertion or deletion, and id is a file identifier.  \n\nBackward privacy [9] specifies the server should not be able to learn the id’s of documents that contained $w$ , if they have since been deleted. Clearly, this is only meaningful if a search for $w$ did not take place prior to this deletion. Bost et al. [9] proposed three different “flavors” of backward privacy with varying leakage patterns, all of which achieve the minimum requirement of hiding id’s of such deleted entries. Here, we only aim for BP-III, according to the following definition from [9] (the involved leakage functions are defined in Appendix B.1).  \n\nDefinition 4. An $\\mathcal{L}$ -adaptively-secure DSE scheme has backward privacy $(B P{-}I I I)$ , iff $\\mathcal{L}^{\\dot{U}p d t}(o p,w,i d)=\\mathcal{L}^{'}(o p,w)$ and $\\mathcal{L}^{S r c h}(w)=\\mathcal{L}^{\\prime\\prime}(T i m e D B(w),D e l H i s t(w))$ , where $\\mathcal{L}^{'}$ and $\\mathscr{L}^{\\prime\\prime}$ are stateless functions.  \n\n# 4 Optimal-Search DSE (OSSE)  \n\nWe are now ready to present our first construction OSSE. We first describe the main idea of maintaining a conceptual  \n\n![](/tmp/output/44_20250325222488/images/8c4c0aed83e5c317a77b4afbb08d8451de2aab9a5aa45c8fbfa1b3c0e679380b.jpg)  \n\nbinary tree to store the entries for each keyword $w$ and then we provide details about the algorithms of the scheme.  \n\n# 4.1 Labeled Binary Trees for Stored Entries  \n\nIn our schemes, we store entries of the form $(w,i d)$ such that keyword $w$ appears in document (more generally, record) $i d$ . Conceptually, each such entry is mapped to a leaf of a complete binary tree $T_{w}$ with $N$ leafs (where $N$ is an upper bound for the number of total entries in the dataset), in chronological order from left to right. The first entry for $w$ is stored at the leftmost leaf, etc. We consider a function $\\mathsf{I a b}(\\nu)$ that maps each tree node $\\nu$ to a unique numerical label, as follows. First, leafs are ordered $1,...,N$ left-to-right, then the remaining nodes are ordered bottom-to-top and left-to-right. E.g., nodes directly above the leafs are labelled starting from the left as $N+1,\\ldots,3N/2$ , as shown in Figure 3. In the following, we often refer to tree nodes directly by their labels. Note that we use one tree for each keyword but the schemes do not require storing and maintaining “complete” trees (that would make the storage $N\\cdot|W|)$ —these are just “conceptual” encodings of the $N$ actual $(w,i d)$ entries spread among keywords.  \n\nHaving explained $T_{w}$ , extracting the result $D B(w)$ after $i_{w}$ insertions can be achieved trivially by starting from the Minimum Covering Subset (MCS) of nodes for the leafs in $[1,i_{w}]$ . The MCS consists of the smallest set of nodes whose leaf descendants are exactly the leafs labeled $1,\\dots,i_{w}$ . E.g., in Figure 3(top), the MCS of [1, 15] is $\\{29,27,23,15\\}$ . Following the edges downwards from the MCS, this process will parse in total $<2i_{w}$ nodes. However, recall that our goal is to do better than this, in the presence of deletions. When a deletion takes place, we \"remove\" from $T_{w}$ the corresponding leaf node $\\nu$ and its parent $p p$ , effectively “pruning” the tree. In practice, we do this by setting the leaf’s sibling sib as the direct child (left or right, depending on the tree topology) of the grandparent $g p$ of $\\nu$ , skipping its parent $p p$ . We illustrate this in Figure 1. Handling deletions in this way, leads to the following result which we prove as part of our analysis: Given a sequence of $i_{w}$ insertions and $d_{w}\\leq i_{w}$ deletions, the number of nodes of $T_{w}$ below the MCS that need to be accessed to extract the result $D B(w)$ with size $n_{w}$ is $<2\\cdot|n_{w}|$ .  \n\nIntuition: From trees to a secure index. Next, we will use these keyword trees to build the secure index for our DSE. The “classic” approach followed by many prior works [8, 9, 11, 11, 14, 20, 28] is to take each entry (in our case, its corresponding tree node) and store it in an encrypted map.  \n\n![](/tmp/output/44_20250325222488/images/4c548bfcb25319d53b819d14b20d41c5d7a1f4a347e66be6010648e397d93990.jpg)  \nFigure 2: $O M_{T r e e}$ data structure of OSSE and LLSE, with node 29 in Figure 3(middle) as an example.  \n\nThis is a standard key-value hashmap but: (i) keys (positions) are computed with a pseudorandom hash function, and (ii) values are encrypted in a way that only reveals them to the server during searches (and for leaf nodes, not even then). Although this approach gives us the desired efficiency benefit, in our case, it introduces a subtle security issue. The same node may be accessed repeatedly during deletions. E.g., in Figure 3(bottom), deleting “cousin” leafs 10 and 11 requires “touching” their grandparent 27 twice. Even though the value in the hashmap is encrypted, the server can clearly see the same position is accessed both times, which unfortunately violates forward privacy.  \n\nTo avoid this issue, in our schemes we use the following trick. First, we give each $T_{w}$ node its own increasing access counter, initially set to 0 and incremented by 1 each time the node is modified. Next, when pseudorandomly computing the new encrypted map position for this node after it is accessed during an update, we also include the incremented access counter (and a freshly encrypted value). Thus, a new write to update the information of an existing tree node is indistinguishable from random and cannot be linked to any previous operation. The remaining challenge is how can the user know these access counters, which is necessary both for searches and for updates. We solve this by storing node information also in an oblivious index (see Figure 2) that, crucially, is accessed only during updates. Then during searches, our two schemes follow a different strategy: the first stores node access counters in their parent nodes, whereas in the second the server “finds” the correct access counter by performing a binary search. In the next parts, we present in detail all the data structures used in our DSE and the details of our algorithms.  \n\n# 4.2 OSSE Data Structures & Algorithms  \n\nWe are now ready to describe OSSE in detail. First, we present the different data structures that we use to maintain and manipulate the trees $T_{w}$ for the different keywords.  \n\n• ${\\underline{{O M_{T r e e}}}}$ is an oblivious map that maps pairs of keywords and node labels $(w,|\\mathsf{a}\\mathsf{b}(\\nu))$ to that node data structure $\\nu$ containing the following: $(\\nu.l e f t,\\nu.l e f t A c c,\\nu.r i g h t $ , $\\nu.r i g h t A c c)$ ). These are the label of the left child of $\\nu$ with its access counter, and the label of the right child of $\\nu$ with its access counter (see Figure 2). This oblivious map is accessed during updates (but not searches) to change $T_{w}$ . • ${\\underline{{O M_{D e l}}}}$ is an oblivious map that maps pairs of keywords  \n\n![](/tmp/output/44_20250325222488/images/5714543cf1d63afb3df499362133b241833b8020eea072fc0a79a73d3424a4cc.jpg)  \n\nFigure 3: Example of the tree $T_{w}$ state after executing insertion/deletion with OSSE and LLSE. (top) The tree after 15 insertions for $w$ . (middle) The tree when deleting node 8 (and consequently its parent 20) and sibling nodes 3,4 (which also deletes their parents 18 and grandparent 25). After these deletions, remaining siblings replace the removed parent nodes (e.g., 7 is now the right chilg of 26). (bottom) The tree when deleting nodes 9-15. After the deletion, the only remaining node in the right subtree (node 16) will be the right child of 31 (for future insertion). In all cases, nodes in blue boxes denote the MCS nodes that will have to be accessed in a subsequent search for $w$ .  \n\nand document identifiers $(w,i d)$ to $\\lvert a\\rvert\\mathrm{b}(\\nu)$ , where $\\nu$ is the leaf of $T_{w}$ that stores this entry. This is accessed only during deletions, to efficiently identify the node to be pruned. • $E M$ is an encrypted map mapping node label and access counter pairs $(\\left|\\mathsf{a}\\mathsf{b}(\\nu),a c c\\right.)$ to: (i) stored document $i d$ , if $\\nu$ is a leaf, and (ii) (v.le f t, v.le f tAcc, v.right, v.rightAcc), otherwise. It is traversed in searches and modified in updates. • $M_{C n t}$ is a plaintext map stored at the client that maps keyword $w$ to counters $(c n t_{w},i_{w},d_{w})$ , corresponding to number of searches, insertions, and deletions for $w$ . • $M_{T k s}$ is a plaintext map stored at the server that maps pairs of tokens and node labels $(t k_{0},l a b)$ to tokens $c t k$ . The lookup token $t k_{0}$ is unique for each keyword $w$ . This is used to facilitate searches for $w$ that involve nodes that have been written/removed prior to the previous search, and it returns the correct “old” token for this node entry. By default, in each search the server first searches at $E M$ with the latest token $t k$ received from the client, and only in case this returns nothing it falls back to $M_{T k s}$ .  \n\nSetup. The client first initializes these data structures (with capacity $N$ for the two OMAPs). It then samples a pseudorandom function key $k\\leftarrow P R F.G e n(1^{\\lambda})$ and a symmetric encryption key $s k\\leftarrow S K E.G e n(1^{\\lambda})$ . Its local state $\\upsigma$ consists of $M_{C n t}$ and the state of the two OMAPs, and its key $K$ is $(k,s k)$ . Finally, it sends $E D B=\\left\\{E M,O M_{D e l},O M_{t r e e}\\right\\}$ to the server. Throughout the scheme, all parties have access to hash functions $H_{0},H_{1}$ (modelled as random oracles).  \n\nInsertion. The process to insert a keyword and document identifier pair $(w,i d)$ is shown in Algorithm 1. The client first computes the label of the leaf where this will be stored based on its insertion counter from $M_{C n t}$ and stores a corresponding entry at $O M_{D e l}$ . It also uses the PRF $F$ to compute a token $t k$ that is unique for this keyword and the number of previous searches. Finally, it creates an entry to be stored in $E M$ by the server. It calculates the address by hashing $t k$ , the leaf’s label, and its access counter 1, and the value by encrypting $i d$ .  \n\nDeletion. Removing an entry with OSSE requires more effort, as shown in Algorithm 2. First the client increments the deletions counter $d_{w}$ and retrieves the label of the node of $T_{w}$ that stores the entry $(w,i d)$ to be removed from $O M_{D e l}$ (lines 1-3). The next step requires retrieving all the nodes of $T_{w}$ from the root of the tree down to node $p o s$ , with a logarithmic number of OMAP accesses. Using the information from the retrieved nodes, the client deduces which are the actual parent $p p$ , grandparent $g p$ , and sibling $s i b$ of pos (lines 4-11). Then  \n\nClient: 1: $(c n t_{w},i_{w},d_{w})\\gets M_{C n t}.\\mathrm{get}(w);\\quad t k\\gets F_{k}(w,c n t_{w})$ 2: MCnt.pu $\\mathsf{t}(w,(c n t_{w},++i_{w},d_{w}))$   \n3: $O M_{D e l}.\\mathrm{put}((w,i d),i_{w})$   \n4: addr ← H0(tk, iw, 1); $\\nu a l\\gets E n c(s k,(w,i d))$ 5: Send $(a d d r,\\nu a l)$ to server   \nServer: 6: $E M.\\operatorname{put}(a d d r,\\nu a l)$  \n\nit can “prune” the tree and remove pos and $p p$ , by setting sib as the direct (left or right) child of $g p$ (lines 12-14).  \n\nFinally, it needs to store all this updated information back to $E D B$ . As in insertion, this will take place in two parts. Once in $E M$ for future searches and once in $O M_{T r e e}$ for future updates. To guarantee forward privacy, the entries to $E M$ must be unrelated to prior entries for $w$ from the viewpoint of the server. This is done by incrementing the access counter acc of retrieved nodes (stored at all nodes and their parents) and then computing the new $E M$ address-value pairs for these incremented counters (lines 15-23). Addresses are then computed as in insertion. Values are computed by XOR’ing the node’s data with the output of $H_{1}$ on the same input as for the address. Due to the randomness of $H_{1}$ and the pseudorandom token $t k$ , these writes to $E M$ appear entirely random to the server. We note that some of these nodes are “redundant” as they may have been removed from prior deletions. Still, to hide this information the client puts back their entries, although they will not be used for searches. $O M_{T r e e}$ will also store the updated node information (line 19); these accesses do not reveal any information due to the OMAP security.  \n\nFor en example, see Figure 3(middle). When leaf 8 is removed, its parent 20 is also removed and its sibling 7 will become the new right child of its grandparent 26. Moreover the access counters of all nodes along the path to the root (26, 29, and 31) are increased to 1.  \n\nSearch. Having spent the necessary effort to maintain and prune $T_{w}$ during updates, we can now benefit during searches (Algorithm 3). The client computes $s$ , the next-next power of 2 from $i_{w}$ . This is the parent of the largest tree of the MCS and let start be its label. Another way to view it is that this is the highest node in $T_{w}$ that may be affected due to deletions in the leaf range $[1,i_{w}]$ —if all of them are deleted—which makes it the ideal starting point of the search. (For completeness OSSE maintains an extra node above the root of $T_{w}$ ; we omit it from the presentation for simplicity.) Then, the client needs to calculate the access counter for $s$ . This is very easy due to two facts. First, each deletion increments the access counter of the entire path up to the root of $T_{w}$ . Second, $s$ lies on this path for all prior deletions for $w$ . Thus, its access counter is $d_{w}$ which is why we store this deletion counter in $M_{C n t}$ . Finally, the client sends $(i_{w},s t a r t,d_{w})$ to the server, together with the  \n\nClient: 1: $(c n t_{w},i_{w},d_{w})\\gets M_{C n t}.\\mathrm{get}(w);\\quad t k\\gets F_{k}(w,c n t_{w})$ 2: $M_{C n t}.\\mathrm{put}(w,(c n t_{w},i_{w},++d_{w}))$ 3: $p o s\\leftarrow O M_{D e l}.\\operatorname*{get}(\\left(w,i d\\right))$   \n4: Let $V,K V\\gets\\emptyset$ 5: Let Labs be the labels on the path from root to leaf pos 6: for l Labs pos do 7: $\\nu_{l}\\leftarrow O M_{T r e e}.\\mathrm{get}((w,l))$ 8: if $\\nu_{l}$ is null then initialize to default 9: Add $\\nu_{l}$ to $V$   \n10: Let $p p,g p$ be the parent and grandparent of pos   \n11: Let sib be the label of the sibling of pos   \n12: if $p p$ is left child of $g p$ then   \n13: $\\begin{array}{r l}&{\\mathrm{~\\dot{~}g~}p.l e f t\\leftarrow s i b;\\quad\\mathrm{~}g p.l e f t A c c\\leftarrow s i b.a c c}\\ &{\\mathrm{~\\dot{~}g~}p.r i g h t\\leftarrow s i b;\\quad\\mathrm{~}g p.r i g h t A c c\\leftarrow s i b.a c c}\\end{array}$   \n14: els   \n15: for each $\\nu_{l}\\in V$ do $\\triangleright$ start from root   \n16: Let $\\nu_{l^{\\prime}}$ be the child of $\\nu_{l}$ on the path   \n17: if $\\nu_{l^{\\prime}}$ is left child of $\\nu_{l}$ then $\\nu_{l}.l e f t A c c++$   \n18: else $\\nu_{l}$ .rightAcc++   \n19: $\\begin{array}{r l}{\\nu_{l}.a c c{++};}&{{}O M_{T r e e}.\\mathrm{put}((w,l),\\nu_{l})}\\end{array}$   \n20: $a d d r\\leftarrow H_{0}(t k,l,\\nu_{l}.a c c);\\quad t\\leftarrow H_{1}(t k,l,\\nu_{l}.a c c)$   \n21: $\\nu a l\\leftarrow t\\oplus(\\nu_{l}.l e f t,\\nu_{l}.l e f t A c c,\\nu_{l}.r i g h t,\\nu_{l}.r i g h t A c c$   \n22: Add $(a d d r,\\nu a l)$ to KV   \n23: Send KV to server   \nServer:   \n24: for $(a d d r,\\nu a l)\\in K V$ do EM.put(addr, val)  \n\npseudorandom token $t k$ for this combination of keyword and search counter (and increments this counter), and $t k_{0}$ , the first search token for $w$ that serves as a unique identifier.  \n\nArmed with this information, the server traverses $T_{w}$ recursively from start to leafs containing the result (ignoring nodes that do not have any descendant leafs in $[1,i_{w}]$ . To “access” a node, it computes its candidate entry in $E M$ , in a way that mimics how this was computed by the client during previous updates. With access to the token $t k$ , the server retrieves the actual information stored for intermediate nodes (i.e., $E M$ is a response-revealing encrypted map): their children and their children’s access counters. In this way, it can keep traversing the tree until it either finds leafs, or it hits null entries. Note that, for each label the server first “tests” the latest token $t k$ (line 13); if this does not return anything, it looks up the entry for $t k_{0}$ in $M_{T k s}$ to see if there is an entry to be found for this label from a prior token for $w$ (lines 14-18).  \n\nFor instance, see Figure 3(bottom). It starts from $s$ (the conceptual node above root 31). Knowing $i_{w}=15$ , it ignores its right child (node 16), moving to its left child node 27 and from then to 17, 26, etc., each time reading the access counter from the parent. If a leaf is reached, the server adds the retrieved ciphertext from $E M$ to the result (note that leaf access counters are never incremented) (line 19). More interestingly, if a null entry is reached this implies no deletions have taken place among the leafs of the sub-tree rooted at this node. So the server can immediately add the $E M$ entries of all these leafs to the result (lines 27-30).  \n\n# 4.3 OSSE Efficiency & Security Analysis  \n\nWe now analyse OSSE in terms of its asymptotic efficiency and its achieved security properties. First, in Appendix C.1 we prove the following lemma regarding its search efficiency.  \n\nLemma 1. For a keyword $w$ for which $i_{w}$ insertions have taken place and $\\left|D B(w)\\right|=n_{w}$ , the search process accesses at most $2n_{w}+\\log i_{w}$ nodes from $T_{w}$ .  \n\nFrom the above, it follows that search takes $O(n_{w}+\\log i_{w})$ operations, i.e., linear in the result size and independent from the number of insertions. Moreover, searches require a single roundtrip to complete. Regarding updates, insertions take just one oblivious map and encrypted map access. Deletions require ${\\cal O}(\\log N)$ such operations to access the path above the node to be removed. The setup takes $O(N)$ time (for $N$ an upper bound on the number of entries in the dataset). Finally, client storage consists of three counters per keyword in $|W|$ , as well as the oblivious maps’ client state and the secret keys. The server storage is linear in the total number of entries $N$ but it grows logarithmically with the number of deletions $D$ , so it is overall $O(N+D\\log N).$ In the absolute worst case, if all entries are deleted, this becomes $O(N\\log N)$ .  \n\nThe correctness of OSSE (i.e., that a search returns the correct result $D B(w)$ , as formulated by all prior updates for $w$ ) follows from the construction and the correctness of the underlying primitives, with one exception. If two distinct inputs to $H_{0}$ or $H_{1}$ (or the same input for the two different hash functions) map to the same hash value, this affects the correctness of the scheme, e.g., as the latter entry will “overwrite” the prior one. This is a common issue with prior DSE works that use an encrypted map as we do (e.g., [8, 28]) and the solution is to pick the range of $H_{0},H_{1}$ so as to make this probability negligible in the security parameter $\\uplambda$ . Let $M$ be the total number of entries that will be stored in $E M$ throughout the execution (for OSSE, $M\\leq N\\log N)$ . If we then set the range of $H_{0},H_{1}$ to $2^{\\lambda+2\\log M}$ , a simple birthday problem analysis guarantees the negligible probability of this event.  \n\nRegarding the security of OSSE, we can analyze its leakage as follows. First, during setup the server only learns the upper bound $N$ . During updates, note that the server’s view consists of two types of data: (i) the messages involved in the oblivious map access (or accesses for deletions), (ii) the $(a d d r,\\nu a l)$ pairs it inserts to the encrypted map. For (i), assuming a secure oblivious map these leak no information. For (ii), each of these is computed with a new keyword/token/access-counter combination, even when it pertains to a previously accessed node, using a secure PRF. Without access to the token $t k$ used as input to this PRF (this will only be revealed during a  \n\nClient:   \n1: $(c n t_{w},i_{w},d_{w})\\gets M_{C n t}$ .get(w);   \n2: $t k\\leftarrow F_{k}(w,c n t_{w});\\quad t k_{0}\\leftarrow F_{k}(w,0)$   \n3: ${\\cal M}_{{\\cal C}n t}.\\mathrm{put}(w,\\left(++c n t_{w},i_{w},d_{w}\\right))$   \n4: Let $s$ be the smallest power-of-2 such that $i_{w}\\leq s$   \n5: Let $\\nu$ be the root of the subtree of leafs $1,\\ldots,2s$   \n6: $s t a r t\\gets\\mathsf{l a b}(\\nu)$   \n7: Send $(t k,s t a r t,i_{w},d_{w},t k_{0})$ to server   \nServer:   \n8: Initialize emptylists TLabs, Res   \n9: Append $\\left(s t a r t,d_{w}\\right)$ to TLabs   \n10: while TLabs $\\neq\\emptyset$ do   \n11: Remove the first entry $(l a b,a c c)$ from TLabs   \n12: if $l a b\\leq N$ then $a c c\\gets1$ $\\triangleright l a b$ is a leaf   \n13: $c t k\\leftarrow t k;\\quad\\nu a l\\leftarrow E M.\\operatorname*{get}(H_{0}(c t k,l a b,a c c))$   \n14: if val is null then   \n15: $\\begin{array}{r l}&{c t k\\gets M_{T k s}.\\mathrm{get}(t k_{0},l a b)}\\ &{\\mathbf{if}~c t k\\neq\\mathrm{null}~\\mathbf{then}}\\ &{~\\nu a l\\gets E M.\\mathrm{get}(H_{0}(c t k,l a b,a c c))}\\ &{\\mathbf{se}~M_{T k s}.\\mathrm{put}((t k_{0},l a b),(t k))}\\end{array}$   \n16:   \n17:   \n18: el   \n19: if $l a b\\leq N$ then Append $(l a b,\\nu a l)$ to Res $\\triangleright$ is a leaf   \n20: else if $\\nu a l\\neq\\mathrm{null}$ then   \n21: $d a t a\\gets\\nu a l\\oplus H_{1}(t k,l a b,a c c)$   \n22: Parse data as $\\left(l e f t,l e f t A c c,r i g h t,r i g h t A c c\\right)$   \n23: if le f t or right $=\\perp$ then set to default values   \n24: Append $(l e f t,l e f t A c c)$ to TLabs   \n25: if ∃ leaf $l$ below right with $l\\leq i_{w}$ then   \n26: Append $(r i g h t,r i g h t A c c)$ to TLabs   \n27: else $\\triangleright$ no deletions below lab   \n28: Let $l$ be the leftmost leaf below lab   \n29: Let $l^{\\prime}$ be the rightmost leaf below lab, with $l^{\\prime}\\leq i_{w}$   \n30: Append $(i,0)$ to TLabs, for $i=l,\\ldots,l^{\\prime}$   \n31: Send Res to client   \nClient:   \n32: Initialize set $D B(w)\\leftarrow\\emptyset$   \n33: for $(l a b,\\nu a l)$ in Res do Add $D e c(s k,\\nu a l)$ to $D B(w)$  \n\nsubsequent search), these addresses and values are indistinguishable from random when they refer to intermediate nodes. When pertaining to leafs, the values are encrypted using a secure encryption scheme, and exactly one such value is sent for insertions. Finally, the total number of accesses and entries is fixed depending only on the type of operation (and $N$ for deletions). From the above, OSSE satisfies forward privacy.  \n\nIn a search for keyword $w$ , the server learns tokens $t k_{0},t k$ The first reveals when previous searches for $w$ took place. With the second, the server can exhaustively access $E M$ for all labels and learn the entire topology of $T_{w}$ . Remembering when these entries were written reveals when prior insertions and deletions for $w$ took place. Moreover, by extracting all versions of $T_{w}$ nodes for different access counters and correlating this with the update timestamps reveals the specific insertion that each deletion cancelled. However, since document ids remain encrypted, the server cannot learn which documents contained $w$ but have since been removed (if no search for $w$ took place since). Based on this leakage profile, OSSE satisfies backward privacy. We formally state and prove its security in Appendix C.2.  \n\n# 5 Log-Log Search DSE with Improved Storage & Deletion (LLSE)  \n\nOur OSSE scheme achieves excellent search performance, however, during deletions it performs a logarithmic number of oblivious/encrypted map accesses. Moreover, this also increases storage. Here we present our second scheme, LLSE, that requires only $O(1)$ map accesses during updates and has $O(N)$ storage, at the trade-off of an extra $O(\\log\\log i_{w})$ factor for searches (which is still asymptotically better than the previous state-of-the-art quasi-optimal search scheme from [20]).  \n\nThe main idea behind LLSE is to still use the same $T_{w}$ tree structure but enhance it so that each node has a parent pointer. This allows us, during deletes, to access directly the node to be removed, its parent, and its grandparent, without starting each time from the root. This simple idea introduces a negative side-effect: during searches we can no longer rely on every node to store its children’s access counters. To overcome this, for each encountered node, the server performs a binary search to find the largest acc for which there exists an entry in $E M$ . It is easy to see that the upper bound for this binary search is roughly twice the height of the node in $T_{w}$ , which yields the $O(\\log\\log i_{w})$ extra overhead. The actual implementation of this is trickier as one needs to maintain access counters across searches (without re-encrypting every node/access-counter combination entry, as this increases the search overhead to $O(\\log i_{w}))$ , as we explain in detail next.  \n\n# 5.1 LLSE Data Structures & Algorithms  \n\nFirst we describe the involved data structures. These are the same as for OSSE, with the following exceptions.  \n\n• ${\\underline{{O M_{T r e e}}}}$ stores for node $\\nu$ its own access counter acc, and its left child, right child, and parent $(\\nu.l e f t,\\nu.r i g h t,\\nu.p r n t)$ , but not their access counters (see Figure 2). • $E M$ is the same as OSSE, except for non-leaf nodes $\\nu$ for which it stores $(\\nu.l e f t,\\nu.r i g h t)$ , but not access counters. • $\\underline{{M_{C n t}}}$ , instead of $d_{w}$ , stores locally the label of the correct parent for the next future insertion. Initially, this is $N+$ 1 (the natural parent of the leaf with label 1). After $i_{w}$ insertions, it is set to the natural parent of the leaf with label $i_{w}+1$ . However, if the most recent updates for $w$ are a series of one or more deletions, the parent for the next  \n\nClient:   \n1: (cntw, iw, nxtPrntw) ← MCnt.get(w); tk ← Fk(w, cntw)   \nIf update is insertion $(o p=a d d)$   \n2: $O M_{D e l}{\\cdot}\\mathrm{put}((w,i d),i_{w})$   \n3: $O M_{T r e e}.\\mathrm{put}((\\l{w},i_{w}),(\\bot,\\bot,n x t P r n t_{w}))$   \n4: $n x t P r n t_{w}\\gets$ label of the parent of $i_{w}+1$ (unused) leaf   \n5: $M_{C n t}.\\mathrm{put}(w,(c n t_{w},++i_{w},n x t P r n t_{w}))$ )   \n6: ad $i r\\gets H_{0}(t k,i_{w},0)$ ; $\\nu a l\\gets E n c(s k,(w,i d))$   \n7: Send $(a d d r,\\nu a l)$ to server   \nIf update is deletion $(o p=d e l)$   \n$\\mathrm{8:}\\mathrm{~}p o s\\gets O M_{D e l}.\\mathrm{get}((w,i d))$   \n9: $\\nu\\leftarrow O M_{T r e e\\cdot}\\mathrm{get}((w,p o s))$   \n10: $p p\\leftarrow O M_{T r e e\\cdot}\\mathrm{get}((w,\\nu.p r n t))$ $\\triangleright$ parent node   \n11: $g p\\gets O M_{T r e e}.\\mathsf{g e t}((w,p p.p r n t))$ $\\triangleright$ grandparent node   \n$\\triangleright$ if parent/grandparent not found set it to default   \n12: if $\\nu$ is left child of $p p$ then $s i b P o s\\gets p p.$ right   \n13: else $s i b P o s\\gets p p.l e f t$   \n14: $s i b\\gets O M_{T r e e\\cdot}\\mathrm{get}\\big(\\big(w,s i b P o s\\big)\\big)$ $\\triangleright$ sibling node   \n15: $s i b.p r n t\\gets p p$ .prnt   \n16: if $p p$ is left child of $g p$ then $g p.l e f t\\gets s i b P o s$   \n17: else $g p.r i g h t\\gets s i b P o s$   \n18: $g p.a c c{+}{+}$   \n19: Update $n x t P r n t_{w}$ if it is affected   \n20: $M_{C n t}.\\mathrm{put}(w,(c n t_{w},i_{w},n x t P r n t_{w}))$   \n21: a $\\scriptstyle{l d r\\leftarrow H_{0}(t k,p p.p r n t,g p.a c c)}$   \n22: $\\nu a l\\gets H_{1}(t k,p p.p r n t,g p.a c c)\\oplus(g p.l e f t,g p.r i g h t)$   \n23: $O M_{T r e e}.\\mathrm{put}((w,s i b P o s),s i b)$   \n24: $O M_{T r e e}.\\mathrm{put}((w,p p.p r n t),g p)$   \n25: Send $(a d d r,\\nu a l)$ to server   \nServer:   \n26: EM.put(addr, val)  \n\ninsertion lies higher in $T_{w}$ . The correct value for this pointer is calculated by the client during updates.  \n\n• $M_{T k s}$ now stores, not only the prior token version for a label, but also the access counter of the latest previous entry. The goal of this is subtle but crucial: It is necessary for launching the binary search for the correct range. This is best illustrated by an example. Consider that during the first search for $w$ , the binary search for node $\\nu$ with height $h$ is correctly executed in the range $[0,2h]$ and the result is acc. In the second search for $w$ the server will try to run the same binary search in vain as at least some of the entries are the ones that were calculated with the old token (from the first search)! To avoid this, $M_{T k s}$ stores for $\\nu$ the old token and the access counter acc, so that the server can retrieve it and then launch the next binary search in $[a c c,2h]$ .  \n\nSetup. As in OSSE, this process initializes all data structures and generates keys. The one difference is the capacity of $O M_{T r e e}$ which is $2N$ as it will store all nodes, including leafs. Insertion. This proceeds very similarly with OSSE, as shown in Algorithm 4. The main difference is that it takes one additional OMAP access in order to write the new leaf’s information to $O M_{T r e e}$ (line 3). This is necessary for finding its parent quickly, in case it is deleted in the future.  \n\nDeletion. This is basically a “lightweight” version of deletions with our first scheme. Since nodes store their parents labels, we can now retrieve the node pos to be deleted from in $O M_{T r e e}$ , and directly from this its parent and grandparent (Algorithm 4, lines 9-11), without having to fetch the entire path from the root. The remaining of the algorithm is essentially the same as before, except for the need to calculate what the next parent’s label nxtPrnt for $w$ will be. This is only affected if the current deletion removes the latest entry, or is part of a series of consecutive deletions that includes the latest entry. Finally, this process adds only one entry to $E M$ , for the updated grandparent node, which makes the storage grow only linearly with the total number of operations.  \n\nSearch. The search process for LLSE is shown in Algorithm 5. The client first sends the necessary data to bootstrap the search. The server traverses the tree from start in largely the same manner as in OSSE, with one crucial difference: it does not know the correct acc for the nodes it encounters. This is resolved as follows. First (lines 12-15), it checks whether there is a previous entry in $M_{T k s}$ for this keyword/label combination. If found, then it extracts sacc, the access counter that node lab had when (and if) it was last accessed during the previous search for $w$ , and otk which is the token during that search. Clearly, if the node has ever been written, the correct current acc can only be larger or equal than sacc. In both cases, it then begins a binary search in the range between this value (or 0, if not found) and twice the lab node’s height, looking for the largest value acc for which the entry for lab in EM is not empty, which is indeed the correct acc for node lab. When this counter is computed, the correct data for lab is retrieved from $E M$ with the appropriate token (lines 21-25). The search then progresses to the retrieved node’s children, or its “natural” children if no entry is found for lab (lines 26-31).  \n\n# 5.2 LLSE Efficiency & Security Analysis  \n\nThe search efficiency of LLSE is similar to OSSE. Each search entails the same tree traversal, so it accesses $O(n_{w}+\\log i_{w})$ nodes. But this time retrieving each node’s data requires a binary search with max range $[0,2h]$ , where $h\\leq\\log N$ is the node’s height. This follows from the fact that for a node at height $h$ each of its children pointers can change due to deletions at most $h-1$ times (each time moving “up” a layer in the tree). Therefore, with LLSE searches require $O((n_{w}+\\log i_{w})\\cdot\\log\\log N)$ operations. Updates require $O(1)$ oblivious and encrypted map accesses, both for insertions and for deletions—an ${\\cal O}(\\log N)$ factor faster than OSSE for the latter. Finally, the storage is linear in both insertions $N$ and deletions $D$ ; since $D\\leq N$ , its overall storage is optimal $O(N)$ . Other efficiency metrics are asymptotically the same  \n\nClient:   \n1: $\\left(c n t_{w},i_{w},n x t P r n t_{w}\\right)\\gets M_{C n t}.\\mathbf{g}\\mathbf{e}\\mathbf{t}(w);$   \n2: $t k\\leftarrow F_{k}(w,c n t_{w});\\quad t k_{0}\\leftarrow F_{k}(w,0)$   \n3: ${\\cal M}_{{\\cal C}n t}.\\mathrm{put}(w,\\left(++c n t_{w},i_{w},d_{w}\\right))$   \n4: Let $s$ be the smallest power-of-2 such that $i_{w}\\leq s$   \n5: Let $\\nu$ be the root of the subtree of leafs $1,\\ldots,2s$   \n6: $s t a r t\\gets\\mathsf{l a b}(\\nu)$   \n7: Send $(t k,s t a r t,i_{w},t k_{0})$ to server   \nServer:   \n8: Initialize emptylists TLabs, Res   \n9: Append start to TLabs   \n10: while TLabs $\\neq\\emptyset$ do   \n11: Remove the first entry lab from TLabs   \n12: $s a c c\\gets0;\\quad c t k\\gets t k$   \n13: $r\\leftarrow M_{T k s}.\\mathrm{get}(t k_{0},l a b)$   \n14: if $r\\neq\\mathrm{null}$ then parse $r$ as $(o t k,o s a c c)$   \n15: $s a c c\\gets o s a c c;\\quad c t k\\gets o t k$   \n16: if $l a b\\leq N$ then $\\triangleright l a b$ is a leaf   \n17: $\\nu a l\\leftarrow E M.\\mathrm{get}(H_{0}(c t k,l a b,1))$   \n18: Append $(l a b,\\nu a l)$ to Res; continue   \n19: Let $h$ be the height of node lab   \n20: Perform binary search in $[s a c c,2h]$ to find max acc   \nsuch that $E M.\\mathrm{get}(H_{0}(t k,l a b,a c c))\\neq\\mathrm{null}$   \n21: if $a c c>s a c c$ then   \n22: $\\nu a l\\gets E M.\\mathrm{get}\\big(H_{0}\\big(t k,l a b,a c c\\big)\\big)$   \n23: $M_{t k s}.\\mathrm{put}((t k_{0},l a b)),(t k,a c c))$   \n24: else if $a c c=s a c c$ and $a c c>0$ then   \n25: $\\nu a l\\gets E M.\\mathrm{get}(H_{0}(c t k,l a b,a c c))$   \n26: if $a c c>0$ then   \n27: $(l e f t,r i g h t)\\gets\\nu a l\\oplus H_{1}(t k,l a b,a c c)$   \n28: else $(l e f t,r i g h t)\\leftarrow$ natural children of lab   \n29: Append $l e f t$ to TLabs   \n30: if ∃ leaf below right with label $\\leq i_{w}$ then   \n31: Append right to TLabs   \n32: Send Res to client   \nClient:   \n33: Initialize set $D B(w)\\leftarrow\\emptyset$   \n34: for $(l a b,\\nu a l)$ in Res do Add $D e c(s k,\\nu a l)$ to $D B(w)$  \n\nas OSSE. It is easy to see the two schemes have the same leakage profile; a curious server can ignore the binary search and try all access counters to extract all $T_{w}$ node information from $E M$ . Thus, LLSE is also forward-and-backward private. We formally state its security in Appendix C.2; due to lack of space, we delegate the proof to the full version [13].  \n\n# 6 Optimizations  \n\nIn this section, we present some optimizations that can improve the performance of our schemes.  \n\n![](/tmp/output/44_20250325222488/images/a23f3af4c88a71af1d3fe52ca9c0a49fafadb891c62fe976ac9ba4be945ceffb.jpg)  \nFigure 4: Search computation time vs. (a) variable $n_{w}$ for $\\left|D B\\right|=1\\mathbf{M}$ , (b) variable $|D B|$ for $n_{w}=100$ , (c) variable $|D B|$ for $n_{w}=20K$ , (d) variable $n_{w}$ for $|D B|=1\\mathbf{M}$ and different OSSE cache settings.  \n\n(I) Improving Tree Traversals (during search). The search algorithms of OSSE and LLSE need to search the sub-trees that contain the MCS nodes for the range $[1,i_{w}]$ . We can optimize this by storing as additional metadata two bits for each MCS root node, which are used to: (i) avoid deleted sub-trees and (ii) start the search from the parents of the MCS nodes that contain a single leaf. To maintain these metadata bits, during deletions we simply check whether the leaf to be deleted is the last or second-last in its MCS sub-tree. Otherwise, the metadata do not change. During search, these bits are sent to the server who then chooses its best strategy. For example, in Figure 3(bottom), only MCS root node 29 is searched, because all the other MCS sub-trees are empty. In cases like this, where almost all MCS sub-trees are empty, this optimization can reduce the number of operations to linear in $n_{w}$ . It also works naturally with search result caching, which we describe next. Finally, this does not affect the leakage of the scheme (a “curious” server can already access all tree nodes anyway).  \n\n$(\\mathbf{II})$ Search Result Caching. Similar to [17], we propose a second optimization in which previous results can be cached at the server. This entirely avoids sub-tree traversals for unchanged nodes (since the last search) and directly returns the cached results. This is done by using a simple hashmap at the server and storing the returned results in this map using the search token as the key. The client sends the previous search token together with the next search query, and the server uses it to find cached results (if any). Identifying whether a node has been changed between two consecutive searches can be done using its access counter in OSSE (and, potentially, in LLSE at the cost of additional bookkeeping). For example, in Figure 3 assume a search query was performed before the deletion of nodes 9-15 and a second query is performed after the deletion of these nodes (in Figure 3(bottom)). In this case, the second query will get the results for node 29 directly from the cache (since that sub-tree remains unchanged). We note this optimization does not incur extra leakage as the information of when past updates and searches for $w$ took place is already part of our schemes’ search leakage profile.  \n\n(III) Storage Clean-Up in OSSE. When deleting a leaf in OSSE, log N nodes are re-written with incremented access counters. This is necessary to avoid leaking information about the tree topology, but it also causes an extra logarithmic write and space overhead.We can use a similar approach to Java’s background garbage collection, so that the server will be able to remove “stale ” records that are identified after a relevant search query (exposing stale records during deletes would violate forward privacy definition). This can run periodically or in the background between queries to keep storage low. We experimentally evaluate its impact in Section 7.3.  \n\n(IV) Constant Client Storage. Our schemes require nonconstant client storage to maintain the necessary metadata $M_{C n t},M_{T k s}$ . An approach to reduce the DSE client storage that has already been proposed in [20] and is applicable in our schemes is to store $M_{C n t},M_{T k s}$ at the server using OMAPs, at the cost of additional roundtrips of communication. Due to the oblivious property, all these accesses are simulatable, revealing no additional information to the server. In practice this mainly affects the search performance since it takes an OMAP access to retrieve the metadata for $w\\cdot$ —an additive ${\\cal O}(\\log^{2}N)$ factor. We experimentally evaluate this approach for our schemes in Section 7.2.  \n\n# 7 Experimental Evaluation  \n\nWe now report on the performance of our schemes and compare them with prior state-of-the-art DSE. We implemented OSSE and LLSE with approximately $6K$ lines of code in $\\mathrm{C}{+}{+}$ . Our code is available at https://github. com/jgharehchamani/OS-SSE. For symmetric encryption, PRF, and hashing we used the AES implementation of OpenSSL [2]. For comparison with Horus, QOS, and $\\mathsf{S D}_{d}$ we used the publicly available code of [14, 20], also in $\\mathrm{C}{+}{+}$ . We used a machine with eight-core Intel Xeon E-2174G 3.8GHz processor, 128GB RAM, 1TB SSD, running Ubuntu16.04 LTS. In all our experiments, the database is stored in RAM.  \n\nWe focus on measuring computation time and communication size for Search and Update queries. We measure these for variable size synthetic datasets with $|D B|=10^{3}–10^{7}$ records, each time setting the total number of distinct keywords $|W|$ to one-hundredth of $|D B|$ . Likewise, we report results for varying result size $n_{w}$ between $10–10^{5}$ documents. Moreover, to demonstrate the impact of deletions in the search performance we report results for variable deletion rates from $0.90\\%$ . Finally, since our OSSE scheme is the first to achieve optimal search but it has asymptotically increased server storage, we run a specific experiment to demonstrate that in practice this storage blowup is often not prohibitively large. Experiments were repeated $10\\times$ and the average is reported.  \n\n# 7.1 Performance of our Schemes  \n\nOur first set of experiments focuses on the performance of OSSE, LLSE, and on evaluating the effect of our resultcaching optimization from Section 6. Specifically, OSSE\\* denotes the version of OSSE with result caching. Regarding smart tree traversal, we integrated this optimization to both OSSE and $\\mathsf{O S S E^{\\star}}$ and we found it to have very small impact in most cases. For this part, we fixed the deletion rate to $10\\%$ .  \n\nSearch Performance. Figure 4 shows search computation time as the result size (a) and database size (b,c) change. First, as expected from our analysis search time grows linearly with $n_{w}$ whereas its change with respect to the database size $|D B|$ is not considerable (note the exponential growth in the $x$ -axis). More importantly, all our schemes have excellent performance in practice. E.g., even for retrieving large results of $100K$ id’s from a dataset of size 1M they take less than $200\\mathrm{ms}$ , whereas for smaller result sizes their performance is even better!  \n\nIn Figures 4(a-c), OSSE\\* specifically refers to “best-case” result caching, i.e., when two searches for $w$ take place without an intermediate update, so the second search can maximally benefit from caching. Not surprisingly, $\\mathsf{O S S E^{\\star}}$ outperforms the other two schemes by $2.1\\substack{-4.8\\times}$ . To better evaluate the effect of result caching on OSSE, we ran an experiment with variable caching benefit, shown in Figure 4(d). In this setting, after inserting a number of entries for $w$ and removing $10\\%$ , we execute a search for $w$ to fill the cache. We then delete additional entries so as to invalidate a certain percentage of the cache for $w$ and report the time for a second search and variable result size for: (i) $\\mathsf{O S S E^{\\star}}$ where the entire result is in cache, (ii) $\\mathsf{O S S E^{\\star}}$ with $10\\%$ and (iii) $\\mathsf{O S S E^{\\star}}$ with $30\\%$ of the cached result is modified, and (iv) OSSE-none of the cash can be re-used. As expected, the more modified the result is the second time, the smaller the improvement achieved. E.g., for $|D B|=1M$ and result size $100K$ OSSE\\* takes 61ms, OSSE\\* with $10\\%$ and $30\\%$ modification takes 97ms and 111ms, and OSSE takes $134\\mathrm{ms}$ . In that sense, OSSE and $\\mathsf{O S S E^{\\star}}$ represent an upper and lower bound for the search performance of our first scheme, and in the following experiments for search performance we report both of them.  \n\nFinally, search communication size is the same for both OSSE and LLSE and it is concretely optimal. The client sends at most two search tokens and the server responds with exactly $n_{w}$ ciphertexts. For example, for $|D B|=1M$ , result size $1K$ and 128-bit AES ciphertexts this is approximately 16KB.  \n\nUpdate Performance. Figure 5 shows the update computation time for OSSE and LLSE vs. variable $|D B|$ . The obvious conclusions from the figures are: (i) the update time slightly increases with $D B$ due to the increase in the size of the OMAPs (again, note the exponential $x\\cdot$ -axis), (ii) OSSE has faster insertion than LLSE as it needs one oblivious map access while LLSE needs two, (iii) as expected, deletion with LLSE is much faster than OSSE. Overall, OSSE is $2-2.2\\times$ faster than LLSE in insertion and LLSE is $3.2–7.2\\times$ faster than OSSE in deletion. That said, both schemes have good performance, e.g., OSSE and LLSE take 4ms and $9\\mathrm{ms}$ to insert an entry and $101\\mathrm{ms}$ and 16ms to delete one from a database of size 1M. Update communication sizes follow the same trend: larger in LLSE for insertion, but smaller for deletion.  \n\n![](/tmp/output/44_20250325222488/images/139d7fe0f413baf13f00193238fb1c69dac201ea2f2352efdc07561c29ca3cb5.jpg)  \nFigure 5: (a) Insertion, (b) Deletion computation time vs. $|D B|$  \n\n# 7.2 Comparison with Other DSE  \n\nNext, we compare the performance of our schemes with prior forward and backward private schemes, focusing on existing DSE with quasi-optimal search, QOS from [20] and Horus from [14]. We also compare their performance with $\\mathsf{S D}_{d}$ from [20], a state-of-the-art DSE that achieves very fast search but linear in the total number of insertions $a_{w}$ .  \n\nSearch Performance. Our main motivation for studying optimal DSE schemes is to avoid “paying” for deleted entries during a search. Thus, we ran a set of experiments with variable deletion percentages to measure the effect of deletions for the different schemes. We also included Horus in our experiments, however, it is not shown in the figures, as it was 2-5 orders of magnitude slower than all other schemes.  \n\nRandom deletions. First we vary the deletion percentage between $0.90\\%$ after a fixed number of insertions for the queried keyword, with deletions chosen at random among them. We try two cases: (a) small results $i_{w}=100$ , (b) big results $i_{w}=20K$ , in Figures 6 (a),(b), respectively. The search time of all optimal/quasi-optimal schemes generally decreases with deletion percentage increases (except for OSSE that for 0- $20\\%$ slightly increases as it is already optimal initially), while for $\\mathsf{S D}_{d}$ it grows as expected, since it stores deletions as entries. Second, our schemes OSSE,LLSE, $\\mathsf{O S S E^{\\star}}$ outperform the prior best quasi-optimal search DSE QOS across all delete percentages and are $\\phantom{-}.4–2.6\\times,1.2–1.8\\times,3.2–6.6\\times$ faster, respectively. The encouraging side-effect of this is that they become truly faster than $\\mathsf{S D}_{d}$ at $55\\%$ , $55\\%$ , and $5\\%$ deletion rates for small results and at $45\\%$ , $50\\%$ , and $5\\%$ deletion rates for large results, respectively. In contrast, QOS becomes faster than $\\mathsf{S D}_{d}$ only after $65\\%$ and $75\\%$ deletion for the two settings. This is the joint effect of our compact tree design, elimination of re-encryption in searches, and optimizations. In practice, we believe this really drives the case for the practicality of DSE with search time independent of past deletions.  \n\n![](/tmp/output/44_20250325222488/images/2f33556eb9ea73ecc3d0f93806b484b955d7ea91972d8418d6767037df876c9a.jpg)  \nFigure 6: Search computation time for $|D B|=1\\mathbf{M}$ and (a) variable deletion percentage for $i_{w}=100$ , (b) variable deletion percentage for $i_{w}=20\\mathrm{K}$ , (c) structured deletion for $i_{w}=100$ , (d) structured deletion for $i_{w}=20\\mathrm{K}$ .  \n\nStructured deletions. We repeated this comparison for the case of more “structured” deletions in which consecutive tree leafs are pruned in batch to see how this affects performance. In practice, this deletion pattern may occur in applications such as an encrypted relational database that performs a bulk update in a table, effectively removing all entries for a column and inserting new ones. Subsequent such modifications will again delete consecutively place entries. To evaluate this, we insert a fixed number of entries for $w$ and then delete a variable percentage of them by choosing consecutive ranges of size 1, 2, 4, 8 at random with $10\\%$ probability. In this manner, we achieve deletion rates of $10,20,40,80\\%$ but in a certain structure. The result is shown in Figure 6 (c),(d). The general observation is that the impact of this is not significant, with all schemes achieving slightly better performance (roughly 1.1- $1.7\\times)$ as these deletions eliminate entire sub-trees and reduce the number of accessed nodes in a “nice” manner, but the improvement is not that considerable. The one exception to this is $\\mathsf{S D}_{d}$ that is unaffected as it treats all deletions equally. Small-client storage versions. Demertzis et al. [20] consider a scenario with small-client users who cannot store keyword counters locally. For QOS, they do this by storing them in an oblivious map of size $|W|$ and retrieving them at the beginning of each search. As discussed in Section 6, we can do the same for our schemes. One expects that this OMAP access will dominate the search time for all schemes. Indeed this is shown for small results in Figure 7(a) where all schemes except $\\mathsf{S D}_{d}$ have essentially the same performance. $\\mathsf{S D}_{d}$ outperforms all other candidates as it achieves small client storage without the need for an oblivious map in search. However, for medium $(i_{w}=5K)$ and larger $(i_{w}=20K)$ ) result sizes (Figure 7(b),(c)), surprisingly our schemes eventually outperform all competitors. E.g., for large result size 20K, OSSE\\*, OSSE, LLSE, are always better than QOS and even outperform $\\mathsf{S D}_{d}$ at $25\\%$ , $65\\%$ , and $70\\%$ , deletion rates, respectively.  \n\nUpdate performance. Regarding insertion computation times (Figure 5), OSSE and QOS have almost the same insertion (and about $2\\times$ faster than LLSE). For deletions, LLSE outperforms QOS by approximately $3{-}6.4\\times$ .  \n\n# 7.3 Effect of Deletion Storage for OSSE  \n\nOne of the drawbacks of OSSE is that it needs to store $\\log N$ entries in the encrypted map for every deletion. In the absolute worse case (number of deletions $D=N_{\\cdot}$ ), this blows the storage for $E M$ by a logarithmic factor. That said, there are two important observations that point towards this not being such a prohibitive factor. First, the server’s storage also consist of two OMAPs that are setup to max capacity $N$ and their size is not affected by deletions, so the blowup is only partial. Second, as we explained in Section 6, the server can perform a “clean-up” in the background or periodically, using the tokens it gathers from searches to remove unnecessary “stale” entries. Here, we attempt to demonstrate the effect this cleanup can have in keeping the storage smaller. The crucial measure here is how often searches take place, i.e., the relative frequency of searches and deletions for a keyword.  \n\nIn lack of a real-world query workload that would allow us to accurately evaluate this, we perform a simplified experiment where we first insert 100K entries for a keyword and then we randomly delete $30\\%$ . What we vary is how searches are interleaved with these 30K deletions among $\\{1/10K$ , $1/1K,1/100,1/50,1/20,1/10,1/4,1/2\\}$ . E.g., $1/2$ means that each deletion is followed by a search for the keyword, whereas $1/10K$ means only three searches happen, each after $10K$ deletions. What we report in Figure 7(d) is the average number of entries in $E M$ throughout the execution of all these searches/deletions (assuming for simplicity that cleanup happens immediately after each search). For comparison we also include the same number for LLSE, as well as an “optimal” plaintext storage where deletions just mark a cell as available.  \n\nAs expected, lower search frequency leads to overall increase in the server’s storage in OSSE. However, even when search occurs after 10K deletions the overall average storage is at most $2\\times$ the optimal size 100K, as a result of cleanup. Besides the average, we also measured the 75-th and 90-th percentile as 280K and 116K entries, respectively. This can be interpreted as $90\\%$ of the time the entries’ storage for this keyword is less than $3\\times$ the optimal and $75\\%$ of the time it is less than $1.16\\times$ . While our simplified experiment runs for one $w$ , if we assume similar query distribution across keywords these results can be extrapolated for the dataset. On the other hand, with LLSE the storage is almost the same for different search intervals, e.g., for our experiments the average is $1.07{\\cdot}1.1\\times$ the optimal number of entries.  \n\n![](/tmp/output/44_20250325222488/images/74fed73f78c6c9fd2b67247fc5e2e6b8ee7a7e80b709d5ad93d256c55f5b3a9a.jpg)  \nFigure 7: Search computation time for $|D B|=1\\mathbf{M}$ and variable deletion percentage for: (a) $i_{w}=100$ using OMAP, (b) $i_{w}=5\\mathrm{K}$ using OMAP, (c) $i_{w}=20\\mathrm{K}$ using OMAP. (d) Number of entries in server for $i_{w}=100\\mathrm{K}$ and 30K deletion and variable search.  \n\n# 8 Conclusion  \n\nIn this work, we presented two forward-and-backward private DSE schemes with very efficient search performance, independent of prior deletions. The experimental evaluation shows our schemes outperform prior ones for a variety of settings. Our results leave several possible directions for improvement, e.g., propose schemes with similar search performance but faster updates, or DSE that achieve stronger variants of backward privacy (without compromising search efficiency). Finally, motivated by leakage abuse attacks [7,10,33,34,37,47–49,55,69], it seems very promising to try and adopt leakage-suppression techniques [22, 41, 57, 70] to combine optimal search performance with improved leakage profiles.  \n\n# Acknowledgements  \n\nWe would like to thank the anonymous reviewers for their feedback and Melek Önen for shepherding our paper. This work was supported in part by the Hong Kong Research Grants Council under grant GRF-16200721.  \n\n# References  \n\n[1] Mongodb. http://www.mongodb.com/.   \n[2] OpenSSL: The open source toolkit for SSL/TLS. https://www. openssl.org/.   \n[3] Pixek: An App That Encrypts Your Photos From Camera to Cloud. https://www.wired.com/story/ pixek-app-encrypts-photos-from-camera-to-cloud/.   \n[4] Tyler Akidau, Robert Bradshaw, Craig Chambers, Slava Chernyak, Rafael J. Fernández-Moctezuma, Reuven Lax, Sam McVeety, Daniel Mills, Frances Perry, Eric Schmidt, and Sam Whittle. The Dataflow Model: A Practical Approach to Balancing Correctness, Latency, and Cost in Massive-Scale, Unbounded, Out-of-Order Data Processing. PVLDB, 8:1792–1803, 2015. [5] Gilad Asharov, Ilan Komargodski, Wei-Kai Lin, Kartik Nayak, Enoch Peserico, and Elaine Shi. Optorama: Optimal oblivious RAM. In EUROCRYPT 2020, pages 403–432, 2020.   \n[6] Gilad Asharov, Moni Naor, Gil Segev, and Ido Shahaf. Searchable symmetric encryption: Optimal locality in linear space via two-dimensional balanced allocations. In STOC 2016, page 1101–1114, 2016.   \n[7] Laura Blackstone, Seny Kamara, and Tarik Moataz. Revisiting leakage abuse attacks. NDSS, 2020.   \n[8] Raphael Bost. ∑oϕoς: Forward Secure Searchable Encryption. In ACM CCS, 2016. [9] Raphaël Bost, Brice Minaud, and Olga Ohrimenko. Forward and backward private searchable encryption from constrained cryptographic primitives. In CCS, 2017.   \n[10] David Cash, Paul Grubbs, Jason Perry, and Thomas Ristenpart. Leakage-abuse attacks against searchable encryption. In CCS, 2015.   \n[11] David Cash, Stanislaw Jarecki, Charanjit Jutla, Hugo Krawczyk, Marcel-Catalin Rosu, and Michael Steiner. Highly-Scalable Searchable Symmetric Encryption with Support for Boolean Queries. In CRYPTO, 2013.   \n[12] David Cash, Ruth Ng, and Adam Rivkin. Improved Structured Encryption for SQL Databases via Hybrid Indexing. In ACNS 2021, pages 480–510, 2021.   \n[13] Javad Ghareh Chamani, Dimitrios Papadopoulos, Mohammadamin Karbasforushan, and Ioannis Demertzis. Dynamic searchable encryption with optimal search in the presence of deletions. Cryptology ePrint Archive, Paper 2022/648, 2022.   \n[14] Javad Ghareh Chamani, Dimitrios Papadopoulos, Charalampos Papamanthou, and Rasool Jalili. New constructions for forward and backward private symmetric searchable encryption. In CCS, 2018.   \n[15] Yan-Cheng Chang and Michael Mitzenmacher. Privacy Preserving Keyword Searches on Remote Encrypted Data. In ACNS, 2005.   \n[16] Melissa Chase and Seny Kamara. Structured Encryption and Controlled Disclosure. In ASIACRYPT, 2010.   \n[17] Tianyang Chen, Peng Xu, Wei Wang, Yubo Zheng, Willy Susilo, and Hai Jin. Bestie: Very practical searchable encryption with forward and backward security. In ESORICS 2021, pages 3–23, 2021.   \n[18] Reza Curtmola, Juan Garay, Seny Kamara, and Rafail Ostrovsky. Searchable symmetric encryption: Improved definitions and efficient constructions. In CCS, 2006.   \n[19] Emma Dauterman, Vivian Fang, Ioannis Demertzis, Natacha Crooks, and Raluca Ada Popa. Snoopy: Surpassing the scalability bottleneck of oblivious storage. In SOSP, 2021.   \n[20] Ioannis Demertzis, Javad Ghareh Chamani, Dimitrios Papadopoulos, and Charalampos Papamanthou. Dynamic searchable encryption with small client storage. In NDSS, 2020.   \n[21] Ioannis Demertzis, Dimitrios Papadopoulos, and Charalampos Papamanthou. Searchable encryption with optimal locality: Achieving sublogarithmic read efficiency. CRYPTO, 2018.   \n[22] Ioannis Demertzis, Dimitrios Papadopoulos, Charalampos Papamanthou, and Saurabh Shintre. SEAL: attack mitigation for encrypted databases via adjustable leakage. In USENIX Security 2020.   \n[23] Ioannis Demertzis, Stavros Papadopoulos, Odysseas Papapetrou, Antonios Deligiannakis, Minos Garofalakis, and Charalampos Papamanthou. Practical private range search in depth. TODS, 2018.   \n[24] Ioannis Demertzis, Stavros Papadopoulos, Odysseas Papapetrou, Antonios Deligiannakis, and Minos N. Garofalakis. Practical private range search revisited. In SIGMOD, 2016.   \n[25] Ioannis Demertzis and Charalampos Papamanthou. Fast Searchable Encryption With Tunable Locality. In SIGMOD, 2017.   \n[26] Ioannis Demertzis, Rajdeep Talapatra, and Charalampos Papamanthou. Efficient searchable encryption through compression. PVLDB, 2018.   \n[27] Martin Dietzfelbinger, Anna R. Karlin, Kurt Mehlhorn, Friedhelm Meyer auf der Heide, Hans Rohnert, and Robert Endre Tarjan. Dynamic perfect hashing: Upper and lower bounds. SIAM J. Comput., 23(4):738–761, 1994.   \n[28] Mohammad Etemad, Alptekin Küpçü, Charalampos Papamanthou, and David Evans. Efficient dynamic searchable encryption with forward privacy. PETS, 2018.   \n[29] Sky Faber, Stanislaw Jarecki, Hugo Krawczyk, Quan Nguyen, Marcel Rosu, and Michael Steiner. Rich Queries on Encrypted Data: Beyond Exact Matches. In ESORICS, 2015.   \n[30] Facebook. Myrocks. http://myrocks.io/.   \n[31] Esha Ghosh, Seny Kamara, and Roberto Tamassia. Efficient graph encryption scheme for shortest path queries. In AsiaCCS, 2021.   \n[32] Oded Goldreich and Rafail Ostrovsky. Software protection and simulation on oblivious rams. J. ACM, 43(3):431–473, may 1996.   \n[33] Paul Grubbs, Marie-Sarah Lacharité, Brice Minaud, and Kenny Paterson. Pump up the volume: Practical database reconstruction from volume leakage on range series. In ACM CCS, 2018.   \n[34] Zichen Gui, Oliver Johnson, and Bogdan Warinschi. Encrypted databases: New volume attacks against range queries. In CCS 2019.   \n[35] Ariel Hamlin, Abhi Shelat, Mor Weiss, and Daniel Wichs. Multi-key searchable encryption, revisited. In PKC, 2018.   \n[36] F Hueske. State TTL for Apache Flink: How to Limit the Lifetime of State. https://www.ververica.com/blog/, 2018.   \n[37] Mohammad Saiful Islam, Mehmet Kuzu, and Murat Kantarcioglu. Inference attack against encrypted range queries on outsourced databases. In CODASPY, pages 235–246. ACM, 2014.   \n[38] Seny Kamara and Tarik Moataz. Boolean searchable symmetric encryption with worst-case sub-linear complexity. In EUROCRYPT, 2017.   \n[39] Seny Kamara and Tarik Moataz. Encrypted multi-maps with computationally-secure leakage. IACR, 2018.   \n[40] Seny Kamara and Tarik Moataz. SQL on structurally-encrypted databases. In ASIACRYPT, 2018.   \n[41] Seny Kamara and Tarik Moataz. Computationally volume-hiding structured encryption. In EUROCRYPT, pages 183–213, 2019.   \n[42] Seny Kamara, Tarik Moataz, and Olya Ohrimenko. Structured encryption and leakage suppression. In CRYPTO, 2018.   \n[43] Seny Kamara, Tarik Moataz, Andrew Park, and Lucy Qin. A decentralized and encrypted national gun registry. IEEE S&P, 2021.   \n[44] Seny Kamara and Charalampos Papamanthou. Parallel and dynamic searchable symmetric encryption. In FC 2013, 2013.   \n[45] Seny Kamara, Charalampos Papamanthou, and Tom Roeder. Dynamic Searchable Symmetric Encryption. In CCS, 2012.   \n[46] Kee Sung Kim, Minkyu Kim, Dongsoo Lee, Je Hong Park, and WooHwan Kim. Forward secure dynamic searchable symmetric encryption with efficient updates. In CCS, 2017.   \n[47] Evgenios M Kornaropoulos, Charalampos Papamanthou, and Roberto Tamassia. Data recovery on encrypted databases with k-nearest neighbor query leakage. In IEEE S&P, 2019.   \n[48] Evgenios M Kornaropoulos, Charalampos Papamanthou, and Roberto Tamassia. The state of the uniform: Attacks on encrypted databases beyond the uniform query distribution. In IEEE S&P, 2020.   \n[49] Marie-Sarah Lacharité, Brice Minaud, and Kenneth G Paterson. Improved reconstruction attacks on encrypted data using range query leakage. In IEEE S&P, 2018.   \n[50] Russell W. F. Lai and Sherman S. M. Chow. Forward-secure searchable encryption on labeled bipartite graphs. In ACNS, 2017.   \n[51] Kasper Green Larsen and Jesper Buus Nielsen. Yes, There is an Oblivious RAM Lower Bound! In CRYPTO 2018, pages 523–542, 2018.   \n[52] Xianrui Meng, Seny Kamara, Kobbi Nissim, and George Kollios. GRECS: Graph Encryption for Approximate Shortest Distance Queries. In CCS, 2015.   \n[53] Tatsuya Midorikawa, Akihiro Tachikawa, and Akira Kanaoka. Helping Johnny to Search: Encrypted Search on Webmail System. In AsiaJCIS, 2018.   \n[54] Ian Miers and Payman Mohassel. IO-DSSE: Scaling Dynamic Searchable Encryption to Millions of Indexes By Improving Locality. In NDSS, 2017.   \n[55] Simon Oya and Florian Kerschbaum. Hiding the access pattern is not enough: Exploiting search pattern leakage in searchable encryption. In USENIX Security, 2021.   \n[56] Sarvar Patel, Giuseppe Persiano, and Kevin Yeo. Lower bounds for encrypted multi-maps and searchable encryption in the leakage cell probe model. In CRYPTO 2020, pages 433–463, 2020.   \n[57] Sarvar Patel, Giuseppe Persiano, Kevin Yeo, and Moti Yung. Mitigating leakage in secure cloud-hosted data structures: Volume-hiding for multimaps via hashing. In ACM CCS, pages 79–93, 2019.   \n[58] Rishabh Poddar, Tobias Boelter, and Raluca Ada Popa. Arx: An encrypted database using semantically secure encryption. PVLDB, 2019.   \n[59] Cédric Van Rompay, Refik Molva, and Melek Önen. Multi-user searchable encryption in the cloud. In ISC 2015, 2015.   \n[60] Cedric Van Rompay, Refik Molva, and Melek Onen. Secure and scalable multi-user searchable encryption. In SCC Workshop, 2018.   \n[61] Subhadeep Sarkar, Tarikul Islam Papon, Dimitris Staratzis, and Manos Athanassoulis. Lethe: A tunable delete-aware lsm engine. In SIGMOD, 2020.   \n[62] Dawn Xiaoding Song, David Wagner, and Adrian Perrig. Practical Techniques for Searches on Encrypted Data. In SP, 2000.   \n[63] Emil Stefanov, Charalampos Papamanthou, and Elaine Shi. Practical Dynamic Searchable Encryption with Small Leakage. In NDSS, 2014.   \n[64] Emil Stefanov, Marten Van Dijk, Elaine Shi, Christopher Fletcher, Ling Ren, Xiangyao Yu, and Srinivas Devadas. Path Oram: An Extremely Simple Oblivious Ram Protocol. In CCS, 2013.   \n[65] Shi-Feng Sun, Ron Steinfeld, Shangqi Lai, Xingliang Yuan, Amin Sakzad, Joseph Liu, Surya Nepal, and Dawu Gu. Practical noninteractive searchable encryption with forward and backward privacy. In NDSS, 2021.   \n[66] Xiao Shaun Wang, Kartik Nayak, Chang Liu, TH Chan, Elaine Shi, Emil Stefanov, and Yan Huang. Oblivious data structures. In CCS, 2014.   \n[67] Yun Wang and Dimitrios Papadopoulos. Multi-user collusion-resistant searchable encryption with optimal search time. In AsiaCCS 2021.   \n[68] J. Yang, Z. Liu, J. Li, C. Jia, and B. Cui. Multi-key searchable encryption without random oracle. In INCoS 2014, pages 79–84, 2014.   \n[69] Yupeng Zhang, Jonathan Katz, and Charalampos Papamanthou. All your queries are belong to us: The power of File-Injection attacks on searchable encryption. In USENIX Security 2016.   \n[70] Yongjun Zhao, Huaxiong Wang, and Kwok-Yan Lam. Volume-hiding dynamic searchable symmetric encryption with forward and backward privacy. Cryptology ePrint Archive, Report 2021/786, 2021.  \n\n# A Details of Cryptographic Primitives  \n\nPseudorandom functions. Let $G e n(1^{\\lambda})\\in\\{0,1\\}^{\\lambda}$ be a key generation function, and $F:\\{0,1\\}^{\\lambda}\\times\\{0,1\\}^{\\ell}\\rightarrow\\{0,1\\}^{\\ell^{\\prime}}$ be a pseudorandom function (PRF) family. $F_{k}(x)$ denotes $F(k,x)$ . $F$ is a secure PRF family if for all PPT adversaries Adv, $|\\operatorname*{Pr}[k\\leftarrow G e n(1^{\\lambda});\\mathsf{A d v}^{F_{k}(\\cdot)}(1^{\\lambda})=1]-\\operatorname*{Pr}[\\mathsf{A d v}^{R(\\cdot)}(1^{\\lambda})=1]|\\leq$ $\\nu(\\lambda)$ , where $R:\\{0,1\\}^{\\ell}\\to\\{0,1\\}^{\\ell^{\\prime}}$ is a truly random function. Symmetric-Key Encryption. A symmetric-key encryption scheme $S K E=(K e y g e n,E n c(s k,x),D e c(s k,c))$ consists of the three algorithms:  \n\n• $K e y G e n(\\lambda)$ : A probabilistic algorithm that takes the security parameter $\\uplambda$ as input and outputs a secret-key $s k$ . • $E n c(s k,x)$ : A probabilistic algorithm that takes $s k$ as input key and a plaintext $x.$ . It outputs a ciphertext $c$ . • $D e c(s k,c)$ : A deterministic algorithm that takes as input key $s k$ and ciphertext $c$ . It outputs decrypted plaintext $x$ .  \n\nA symmetric-key encryption scheme is said to be CPAsecure if for all PPT adversaries Adv and any two arbitrary plaintext messages $x_{0}$ and $x_{1}$ , $\\begin{array}{r}{|P r[\\mathsf{A d v}(E n c_{K}(x_{0}))=}\\end{array}$ $1]-P r[\\mathsf{A d v}(E n c_{K}(x_{1}))=1]|\\leq\\nu(\\lambda)$ , where $K\\leftarrow K e y G e n(\\lambda)$ .  \n\n# B DSE Security Game  \n\nFigure 8 shows the ${\\sf R e a l^{D S E}}$ and IdealDSE games for the DSE security definition 1.  \n\n# B.1 Leakage Functions for Backward Privacy  \n\nAssume $Q$ is list with one entry for each operation. The entry for a search and an update is of the form $(u,w)$ and $(u,o p,(w,i d))$ , respectively, where $u$ is the query timestamp (starting from 1), $w$ is the searched keyword, $o p=a d d/d e l$ , and $i d$ is the modified file. For keyword $w$ , $T i m e D B(w)=\\{(u,i d)|(u,a d d,(w,i d))\\in Q\\land$ $\\forall u^{\\prime},(u^{\\prime},d e l,(w,i d))\\notin Q\\}$ returns the list of all timestamp/fileidentifier pairs of keyword $w$ that have been added to $D B$ and have not been subsequently deleted. Finally, the function $D e l H i s t(w)=\\{({u^{a d d}},{u^{d e l}})|\\exists i d:(u^{a d d},a d\\dot{d},(w,i d))\\in$ $Q\\land(u^{d e l},d e l,(w,i d))\\in Q\\}$ returns the history of deleted entries by giving all (insertion timestamp, deletion timestamp) pairs to the adversary. Informally, it shows which deletion corresponds to which addition.  \n\n![](/tmp/output/44_20250325222488/images/554f6c4dd8ef3bbaf5540f2bb71460bc47f0f4145c581e28487cb20073e5a5a2.jpg)  \nFigure 8: Real and ideal experiments for the DSE scheme.  \n\n# C Formal Analysis  \n\n# C.1 Proof of Lemma 1  \n\nLet $s$ be the starting node of the search and recall that $s$ is the root of a full binary tree. We denote by $S$ the set of nodes in $T_{w}$ that belong to the subtree rooted by $s$ . Let $\\nu_{1},\\ldots,\\nu_{l}$ be the roots of the $l$ full binary trees of the MCS forest for leafs $[1,i_{w}]$ , with $l<\\log i_{w}$ . Let $S^{\\prime}$ be the set that contains all the nodes of $T_{w}$ that belong to a subtree rooted by any of the nodes $\\nu_{i}$ . Clearly, since $s$ is a common ancestor of all $\\nu_{i}$ $S^{\\prime}\\subseteq S$ . The set $S^{\\prime\\prime}:=S\\setminus S^{\\prime}$ contains at most $\\log i_{w}$ nodes that are ancestors of some of the leafs in $[1,i_{w}]$ : one for each layer from $s$ to right before the leafs (all parents of $\\nu_{i,}$ ).  \n\nHaving defined these sets, the search process starts from $s$ and accesses all the nodes in $S^{\\prime\\prime}$ and those in $S^{\\prime}$ that have not been removed by some of the previous deletions. The number of nodes in $S$ that are not deleted can be proven to be $\\leq2n_{w}$ by a simple induction on the number of deletions. For 0 deletions, S consists of full binary trees hence $|S|=2i_{w}-1<2n_{w}.$  \n\nsince $n_{w}=i_{w}$ in this case. After $k$ deletions by the inductive hypothesis there will be less than $2(i_{w}-k)$ nodes. Since each deletion removes exactly two nodes from $S$ (the leaf to be deleted and its parent), after deletion $k+1$ , there will be less than $2(i_{w}-k)-2=2(i_{w}-(k+1))$ , i.e., less than twice the result size $(i_{w}-(k+1))$ .  \n\nAs $S^{\\prime}$ and $S^{\\prime\\prime}$ are disjoint, the total number of nodes accessed by a search is upper bounded by $2n_{w}+\\log i_{w}$ .  \n\n# C.2 Proof of Security  \n\nTheorem 1. Assuming $F$ is a secure PRF, SKE is a secure encryption scheme, $O M_{D e l},O M_{T r e e}$ are secure oblivious maps, OSSE is adaptively-secure in the programmable random oracle model according to Definition 1, with $\\mathcal{L}^{S e t u p}(N)=N$ and $\\mathcal{L}^{U p d t}(o p,w,i d)=o p$ and $\\mathcal{L}^{S r c h}(w)=$ $\\left(T i m e D B(w),D e l H i s t(w)\\right)$ .  \n\nProof. We prove the security of OSSE via a sequence of indistinguishable hybrids as follows:  \n\n• Hybrid-0: This is the $\\mathsf{R e a l}^{S S E}$ game defined in Appendix B between the adversary and a challenger.  \n\n• Hybrid-1: This is the same as Hybrid-0 but all encryptions val corresponding to leaf nodes during insertion queries are replaced with $S K E.E n c(s k,0)$ , i.e., encryptions of zero. Since decryption happens only locally by the client, any adversary that can distinguish between these two games can be used to break the CPA security of $S K E$ via a series of standard hybrids. • Hybrid-2: This is the same as Hybrid-1 but all tokens $t k$ for any keyword $w$ and counter $c n t_{w}$ are instead generated uniformly at random from the range of the PRF ${\\cal F}\\{0,1\\}^{\\lambda}$ . More specifically, the challenger initiates a map $T K$ . Every time the code calls for a token $t k$ for a new $w,c n t_{w}$ combination, it gets the result of $T K.\\mathrm{get}((w,c n t_{w}),t k)$ . If the result is null, it chooses $t k$ uniformly at random and stores it with $T K.\\mathrm{put}((w,c n t_{w}),t k)$ , for future reference. The two games can be shown to be indistinguishable due to the security of the PRF via a series of standard hybrids. • Hybrid-3: This is the same as Hybrid-2 but the challenger internally maintains a list $I$ that stores the contents of each query of the adversary ( $w$ for searches, $(\\boldsymbol{w},i d,o p)$ for updates). With this list $I$ , the challenger can at any phase of the game, accurately compute the correct $T_{w}$ tree for every keyword $w$ , including the correct access counter for each node based on the history of the adversary’s queries. This change is internal only so Hybrid-3 is identical to Hybrid-2 for the adversary. • Hybrid-4: This is the same as Hybrid-3 but during setup the oblivious map initializations for $O M_{D e l}$ , $O M_{T r e e}$ are replaced with calls to two simulators $S I M_{O M A P}$ for capacity $N$ . All future oblivious map accesses are emulated by calls to the corresponding simulators $S I M_{O M A P}$  \n\nWhenever the code needs such an access during an update, the challenger recreates the corresponding tree $T_{w}$ for the related keyword $w$ at that phase of the game and calculates the access result from this. Hence, Hybrid-4 is indistinguishable from Hybrid-3 due to the security of the oblivious maps.  \n\n• Hybrid-5: This is the same as Hybrid-4 but all calls to $H_{0}$ are emulated by the challenger as follows. Whenever the code or the adversary calls for an evaluation of $H_{0}$ , the challenger, who maintains a table H0 of all past $H_{0}$ -calls, responds: (i) with a freshly chosen random value from the range of $H_{0}$ if this input has not been requested for evaluation before, in which case it stores it at H0 together with the input, (ii) with the evaluation result stored in H0 if the input has been requested before. Denote by $\\mathsf{B a d}_{1}$ the event where throughout the game the challenger happens to sample the same $H_{0}$ evaluation for different inputs. If $\\mathsf{B a d}_{1}$ takes place the challenger aborts. Clearly, conditioned on not aborting, the view provided to the adversary in Hybrid-4 is identical to that of Hybrid-3. Second, since the range of $H_{0}$ is assumed exponential to the security parameter $\\uplambda$ , whereas the number of $H_{0}$ -calls that will be executed throughout the game is polynomial in $\\uplambda$ (since the adversary is bounded) by the birthday problem the probability of abort is negligible. Hence the games are statistically indistinguishable in the programmable random oracle model.  \n\n• Hybrid-6: This is the same as Hybrid-5 with the following modification. For calls to $H_{0}$ to calculate addr during insertions (Algorithm 1, line 4) and deletions (Algorithm 2, line 20), the challenger samples the evaluation as above but instead stores it to table $\\mathrm{H0^{\\prime}}$ together with the timestamp $1\\leq u\\leq q$ of the operation. For deletions, it stores all $\\log N$ sampled valued in the order in which they are called. Then, for a search query with tokens $t k,t k_{0}$ , let $u_{1},\\ldots,u_{t}$ be the timestamps of all prior updates for the same keyword $w$ , after the latest prior search for the same keyword. The challenger first builds all prior “snapshots” of $T_{w}^{(1)},\\ldots,T_{w}^{(t)}$ . Based on this, it calculates for each update operation, the corresponding involved node labels, access counters, and the order in which they were accesses within the operation (for deletions). For each involved node label lab in these updates let MaxAcc be its maximal access counter in all tree snapshots. The challenger matches each such pair $l a b,a c c$ for $a c c\\in[1,m a x A c c]$ to the corresponding timestamp (and within the timestamped operation, the exact order to which this pair corresponds) and identifies the corresponding evaluation addr in $\\mathrm{H0^{\\prime}}$ . It then removes this entry from $\\mathrm{H0^{\\prime}}$ and inserts to H0 the mapping $(t k,l a b,a c c)\\rightarrow a d d r$ . Note that, if $t k\\neq t k_{0}$ , entries for the latter have already been moved to $\\mathrm{H0}$ . Effectively, this “matches” the evaluations during searched with the ones during previous related updates. Denote by $\\mathsf{B a d}_{2}$ the event where when the challenger moves entries from $\\mathrm{H0^{\\prime}}$ to $_\\mathrm{H0}$ in the above process, it encounters an already filled entry in $_\\mathrm{H0}$ . Since during the protocol execution all $H_{0}$ evaluations during searches follow prior evaluations on the same input during an update, this can only happen if the adversary has posted a query to $H_{0}$ for a token $t k$ it has not yet seen, effectively guessing it. In this hybrid, the challenger modifies its code to check if $\\mathsf{B a d}_{2}$ take place; if that happens it aborts. First, note that unless the challenger aborts the game is identical to that of Hybrid-5. Second, given that tokens are sampled uniformly at random from a domain exponential in $\\uplambda$ , whereas the total number of $H_{0}$ -calls that the adversary can do throughout the game is polynomial in $\\uplambda$ (since the adversary is PPT), the probability of aborting is negligible in $\\uplambda$ , hence the two Hybrids are statistically indistinguishable.  \n\n• Hybrid-7: This is the same as Hybrid-6 but we now also replace $H_{1}$ with a programmable random oracle exactly as we did in Hybrid-5 for $H_{0}$ . By the same reasoning as in that case, this hybrid is indistinguishable from the previous one.  \n\n• Hybrid-8: This is the same as Hybrid-7 but we now manipulate the evaluations of $H_{1}$ during deletions, exactly as we did in Hybrid-6 for $H_{1}$ . More specifically, during deletions we sample the different val uniformly at random and store them at a table $\\mathsf{H}\\mathsf{1}^{\\prime}$ for the time being. During searches, the only difference is that when moving an entry $l a b,a c c$ with evaluation val and a corresponding token $t k$ from $\\mathsf{H}\\mathsf{1}^{\\prime}$ to H1 during a search, tt sets the mapping to $(t k,l a b,a c c)\\to\\nu a l\\oplus(l e f t,l e f t A c c,r i g h t,r i g h t A c c)$ where these four values can again be calculated directly from the series of trees $T_{w}^{1},\\ldots,T_{w}^{t}$ . This ensures subsequent evaluations during the search execution by the server will be consistent with Algorithm 2, lines 20-21. Note that this also eliminates the need for token calculation during updates so this part is removed from the code of the challenger. By the same reasoning as for Hybrid-6, this hybrid is indistinguishable from the previous one.  \n\n• Hybrid-9: This is the same as Hybrid-8 but the challenger changes the list token map $T K$ to map tokens to the search timestamp during they were created. This change is internal only so the game is identical to Hybrid-8 for the adversary.  \n\n• Hybrid-10: This is the same as Hybrid-9 but the challenger changes what the query list $I$ store for each query. If $k$ is an update, $I(k)=\\perp$ . If $k$ is a search for keyword $w$ $\\nu,I(k)=(T i m e D B(w),D e l H i s t(w))$ . We note that the code of the challenger from Hybrid-9 can still be executed solely with this information, as follows. At this point, updates only involve sampling random  \n\nappropriate random value for addr, val and simulating oblivious map accesses. Regarding searches, first the challenger can find which prior operations involve the same keyword from $T i m e D B(w),D e l H i s t(w)$ , directly for the case of prior updates, and indirectly by comparing $T i m e D B(w)$ for past searches in order to reuse token $t k_{0}$ . Second, it can calculate the counters $c n t_{w},i_{w},d_{w}$ directly from $T i m e{\\cal D}B(w),{\\cal D}e l H i s t(w)$ . Third, the functions $T i m e{\\cal D}B(w),{\\cal D}e l H i s t(w)$ map previous insertions and deletions to a unique $T_{w}$ since they reveal when insertions took place and for each deletion specifically which prior insertion it cancels. Finally, from this the challenger can compute all nodes’ access counters which are necessary for running the hybrid’s search code. Again, this change is internal only so Hybrid-8 is identical to Hybrid-9 for the adversary. • Hybrid-11 This is the same as Hybrid-10 but the client only receives $o p$ (instead of $o p,w,i d)$ during updates, and $T i m e D B(w),D e l H i s t(w)$ (instead of $w$ ) during searches. Clearly, this is again indistinguishable to the adversary.  \n\nThe modified challenger’s code for the final hybrid is essentially the code of our simulator in the IdealSSE game as it only takes as input the leakage of Theorem 1. Since we showed that Hybrid-0 and Hybrid 11 provide an indistinguishable view to an adversary playing the DSE adaptive security of Figure 8, the result follows. 口  \n\nWe make two remarks about the above proof. First, even though $T i m e D B(w)$ includes the actual id’s of the documents in $D B(w)$ we never used this information, since our scheme’s goal is to retrieve $D B(w)$ and this result remains always encrypted for the adversary. However, our use of $T i m e(D B)$ also covers our analysis for cases where OSSE is used in applications that want to retrieve the actual documents (see discussion in Section 3). Second, in the above we make the implicit assumption that search queries for non-existent keywords are not repeated; with this restriction, our leakage profile always captures standard search pattern leakage. However, to cover such cases, we should expand our leakage to explicitly include, when such a non-existing keyword was searched previously.  \n\nFinally, the following characterizes the security of our second scheme LLSE. The formal proof is very similar to that of OSSE above and we defer it to the full version [13] due to space limitations.  \n\nTheorem 2. Assuming $F$ is a secure PRF, SKE is a semantically secure encryption scheme, $O M_{D e l},O M_{T r e e}$ are secure oblivious maps, LLSE is adaptively-secure in the programmable random oracle model according to Definition 1, with $\\mathcal{L}^{S e t u p}(N)=N$ and $\\mathcal{L}^{U p d t}(o p,w,i d)=o p$ and $\\mathcal{L}^{S r c h}(w)=(T i m e D B(w),D e l H i s t(w))$ .  "}