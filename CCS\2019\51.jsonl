{"text": "# Fast Actively Secure Five-Party Computation with Security Beyond Abort  \n\n<PERSON><PERSON>ali   \nIndian Institute of Science   \nBangalore, India   \n<EMAIL>   \nCarmit Hazay   \nBa-Ilan University   \nRamat Gan, Israel   \n<EMAIL>   \nArpita <PERSON>†   \nIndian Institute of Science   \nBangalore, India   \n<EMAIL>   \nSwati Singla   \nIndian Institute of Science   \nBangalore, India   \n<EMAIL>  \n\n# ABSTRACT  \n\nSecure Multi-party Computation (MPC) with small population and honest majority has drawn focus specifically due to customization in techniques and resulting efficiency that the constructions can offer. In this work, we investigate a wide range of security notions in the five-party setting, tolerating two active corruptions. Being constant-round, our protocols are best suited for real-time, high latency networks such as the Internet.  \n\nIn a minimal setting of pairwise-private channels, we present efficient instantiations with unanimous abort (where either all honest parties obtain the output or none of them do) and fairness (where the adversary obtains its output only if all honest parties also receive it). With the presence of an additional broadcast channel (known to be necessary), we present a construction with guaranteed output delivery (where any adversarial behaviour cannot prevent the honest parties from receiving the output). The broadcast communication is minimal and independent of circuit size. In terms of performance (communication and run time), our protocols incur minimal overhead over the best known protocol of <PERSON><PERSON> et al. (ACM CCS 2016) that achieves the least security of selective abort. Further, our protocols for fairness and unanimous abort can be extended to $n$ -parties with at most $\\sqrt{n}$ corruptions, similar to Chandran et al. Going beyond the most popular honest-majority setting of three parties with one corruption, our results demonstrate feasibility of attaining stronger security notions for more than one active corruption at an expense not too far from the least desired security of selective abort.  \n\n# CCS CONCEPTS  \n\n• Security and privacy Cryptography;  \n\n# KEYWORDS  \n\nSecure Multiparty Computation; Guaranteed Output Delivery; Fairness; Unanimous Abort; Garbled Circuits  \n\n# ACM Reference Format:  \n\nMegha Byali, Carmit Hazay, Arpita Patra, and Swati Singla. 2019. Fast Actively Secure Five-Party Computation with Security Beyond Abort . In 2019 ACM SIGSAC Conference on Computer and Communications Security (CCS ’19), November 11–15, 2019, London, United Kingdom. ACM, New York, NY, USA, 18 pages. https://doi.org/10.1145/3319535.3345657  \n\n# 1 INTRODUCTION  \n\nSecure Multiparty Computation (MPC) [27, 46, 65] is an area of cryptography that has evolved breathtakingly over the years in its attempt to secure data while computing on it. MPC focuses on the problem of enabling a set of $n$ mutually distrusting parties to perform joint computation on their private inputs in a way that no coalition of $t$ parties can affect the output of computation or learn any additional information beyond what is revealed by the output. In other words, MPC guarantees correctness of computation and privacy of inputs. The literature of MPC has witnessed plethora of works from a theoretical standpoint, however, the focus on building practice-oriented MPC [9, 39, 64] constructs has gained momentum only in the recent years owing to the rising demand for efficiency in real-time networks such as the Internet. The vast literature of MPC can be broadly categorized into dishonest majority [7, 16, 37, 40, 46, 52, 57, 64] and honest majority [10, 11, 14, 15, 55, 61]. While both have received attention in the efficiency studies, designing practical MPC with honest majority is a captivating area of research [6, 24, 25, 43, 55, 60] for the various reasons illustrated below.  \n\nThe paramount benefit of having honest majority enables the computation to achieve stronger security goals such as fairness (adversary obtains output if and only if all honest parties do) and guaranteed output delivery (GOD) (any adversarial behaviour cannot prevent the honest parties from receiving the output) [31]. These properties are desirable in real-life owing to limited time and resource availability, as they bind the parties to participate in the computation and thus keep the adversarial behaviour in check. Furthermore, lack of such strong guarantees can be detrimental in practice. For instance, in real-time applications such as e-commerce and e-auction, an adversary can always cause an abort if the outcome is not in its favour unless a stronger security notion is ensured. In e-voting, the adversary can abort the computation repeatedly, yet learn the outputs each time and use them to rig the election. Apart from enabling stronger security goals, honest-majority allows design of efficient protocols solely using symmetric-key functions. For instance, the necessity of a public-key primitive for realizing oblivious transfer can be replaced with symmetric-key primitives, as exhibited by our protocols and [25]. Further, this setting enables design of information-theoretic protocols [14, 15, 49, 61], besides the computational ones. Thus, achieving stronger security goals is crucial and has driven a lot of research. To elaborate, [35, 36] show constant-round protocols with GOD. The round-optimality of these notions have been studied in [45, 47, 60] and 3 rounds is proven to be necessary. Lately, round-optimal MPC protocols with GOD appeared in [1, 8, 47] relying on either Common Reference String (CRS) or public-key operations, in [2, 4] under super-honestmajority $t<n/4$ and in [60] for the special case of 3-party solely from symmetric-key primitives. The work of [38] shows how to compile honest majority MPC protocol for arithmetic circuits with abort (and several other constraints) into a protocol with fairness while preserving its efficiency. Interestingly, while [31] rules out fairness in dishonest majority, [3, 17, 30, 59] demonstrate its feasibility relying on non-standard techniques such public bulletin boards, secure processors or penalties (via Bitcoin).  \n\nSince inception, the primary focus of MPC has been on generic constructions with $n$ parties. Yet, the regime of practical MPC has seen major breakthroughs in the small-party domain: 3-5. Real-time applications such as Danish Sugar-Beet Auction [22], statistical and financial data analysis [21], email filtering [51], distributed credential encryption [55], Kerberos [6], privacy-preserving machine learning [53], efficient MPC-frameworks such as VIFF [44], Sharemind [20] and ABY-Arithmetic Boolean Yao [54] are crafted for 3 parties with one corruption. The setting of 4, 5 parties with minority corruption has been explored in [24, 25, 49]. The most popular setting of $3/4$ parties with 1 active corruption brings to the table some eloquent custom-made tools such as the use of Yao’s garbled circuits [65] to achieve malicious security [24, 55, 60], spending just 2-3 elements per party in arithmetic circuits [5] and sure-election of one honest party as a trusted party in case the adversary strikes [24, 60]. These techniques rely on the adversary not having an accomplice to cause damage. However, the moment adversary has a collaborator (2 corruptions), these custom-made tools fall apart, thus elevating the challenge of achieving desired security with real-time efficiency. In this paper, we consider MPC for 5 parties (5PC) with 2 corruptions and treat it with securities of unanimous abort, fairness and GOD, at an expense that is not too far from the result of [25] achieving least desired security of selective abort. We present the related work below.  \n\n# 1.1 Related Work  \n\nThe notable works on MPC for small parties come in two flavours– low-latency and high-throughput protocols. Relying on garbled circuits, the former offers constant-round protocols that serve better in high-latency networks such as the Internet. The latter, built on secret sharing tools, aim for low communication (bandwidth), but at the cost of rounds proportional to the depth of the circuit representing the desired function. These primarily cater to lowlatency networks. We focus on the former category in our work.  \n\nThe work most relevant to ours is [25] that proposes a 5PC protocol achieving the weak notion of selective abort against two malicious corruptions. Their customization for 5PC resulted in an efficient protocol for actively-secure distributed garbling of 4 parties, relying solely on the passively-secure scheme of [13], saving $60\\%$ communication than [13] with four corruptions. In the 3-party (3PC), 4-party (4PC) domain, [49, 55] gave a 3PC with selective abort. [49] also gave a 2-round 4PC with GOD. Recently, [24] improved the state-of-the-art with efficient 3PC and 4PC achieving fairness and GOD with minimal overhead over [55]. In the dishonest-majority, the protocol of [29] studies 3PC with two corruptions.  \n\nOrthogonally, recent works [5, 6, 26, 42, 43] in the highthroughput setting with non-constant rounds, show abort security in 3PC with one corruption. The works of [26, 28, 38, 58] additionally include constructs attaining fairness.  \n\n# 1.2 Our Contribution  \n\nIn the regime of low-latency protocols which is of interest to us, the known works [25, 49, 55], despite being in honest majority, trade efficiency for security and settle for weaker guarantees such as selective abort. With 3, 4 parties, [24, 49, 60] demonstrate that fairness, GOD are feasible goals and present protocols with minimal overhead over those achieving weaker notions. Our paper is yet another attempt in this direction, focused on the 5-party setting.  \n\nWe present efficient, constant-round 5PC protocols with honest majority that achieve security notions ranging from unanimous abort to GOD, solely relying on symmetric-key primitives. Being efficient and constant-round, our protocols are best suited for high latency networks such as the Internet. Designed in the Boolean world, our protocols are built on the semi-honest variant of the distributed garbling scheme of [64] while leveraging the techniques of seed distribution and Attested Oblivious Transfer of [25]. The generality of our protocols is such that they can accommodate any passively secure distributed garbling scheme as a building block. Our theoretical findings are backed with implementation results with the choice of benchmark circuits AES-128 and SHA-256.  \n\n5PC with Fairness and Unanimous Abort. In a minimal network of pairwise-secure channels, we achieve fairness and unanimous abort in 5PC with performance almost on par with [25], all consuming 8 rounds. On a technical note, building on [25], we achieve fairness by ensuring a robust output computation phase even when the adversary chooses not to participate in the rest of the output computation on learning the output herself. This is realized using techniques which enforce that, in order to learn the output herself, the adversary must first aid at least one honest party compute the correct output. Further, we employ techniques to allow this honest party to release the output and convince about the correctness of the same to remaining honest parties. Our 5PC with unanimous abort is obtained by simplifying the fair construct such that the adversary can learn the output herself without any aid from honest parties, but if she helps at least one honest party get the output, then that honest party aids fellow honest parties to get the output (as in fair construct).  \n\nBoth our 5PC protocols with fairness and unanimous abort can be extended to $n$ parties under the constraint of $t={\\sqrt{n}}$ corruptions which was established in [25].  \n\n5PC with GOD. Our protocol uses point-to-point channels and a broadcast channel. The latter is inevitable as we use optimal threshold [32]. As broadcast is expensive in real-time, we limit broadcast communication to be minimal and primarily, independent of circuit, input and output size. Our implementation uses a software broadcast based on Dolev-Strong protocol [41]. On the technical side, our protocol relies on 2-robust techniques– 4-party 2-private replicated secret sharing (RSS) scheme for input distribution and seed-distribution of [25] to ensure each party’s role is emulated by two other parties. These strategies ensure that each piece of intermediate data is with a 3-party committee and any wrong-doing by at most 2 parties will ensue conflict. When a conflict occurs, we determine a smaller instance of a 3PC with at most 1 corruption to compute the output robustly. Our technical innovations come from maintaining– (A) input privacy, while making two 3-party committees, one formed by RSS and one by seed-distribution, interact; (B) input consistency across the 3PC and outer 5PC. Due to the use of customized tools for small parties such as RSS, conflict identification and running a smaller 3PC instance, this protocol cannot be scaled to $n$ -parties while retaining the goal of efficiency.  \n\nEmpirical Comparison. A consolidated view of our results is presented below outlining the security achieved, rounds, use of broadcast (BC) and empirical values. The values indicate overhead in maximum runtime latency in LAN , WAN and total communication (CC) over [25] that offers selective abort in 8 rounds. The range is composed over the choice of circuits: AES-128, SHA-256 and the left value in the range corresponds to AES, while the right value indicates SHA. AES is a smaller circuit, with 33616 gates, compared to 236112 gates of SHA. (\\*: The total rounds is computed plugging in the state of the art robust 3PC [24]. The rounds for GOD is stated assuming broadcast channel availability in ours and [24]).  \n\n<html><body><table><tr><td>Security</td><td>Rounds</td><td>BC</td><td>LAN ( ms)</td><td>WAN (s)</td><td>CC (MB)</td></tr><tr><td>unanimous abort</td><td>8</td><td>x</td><td>0.65-2.87</td><td>0.2-0.01</td><td>0.16-0.09</td></tr><tr><td>fairness</td><td>8</td><td></td><td>1.05-10.95</td><td>0.28-0.03</td><td>0.2-0.13</td></tr><tr><td>GOD (honest run)</td><td>6</td><td>√[32]</td><td>3.94-4.92</td><td>1.16-0.82</td><td>0.17-0.07</td></tr><tr><td>GOD(worstcase)</td><td>12*</td><td>[32]</td><td>6.33-19.42</td><td>2.26-2.33</td><td>0.49-6.22</td></tr></table></body></html>  \n\nAll protocols barring the one with GOD maintain the same circuit-dependent communication as [25]. The GOD protocol costs two circuit-dependent communication, one in 5PC and one in 3PC, the latter amongst a smaller set of 3 parties. This is reflected in the cost of worst case run of our GOD. For all other protocols, the overhead comes from extra communication (commitments precisely), dependent only on the input, output size. SHA being a bigger circuit, has its absolute overheads more than AES in most cases but the percentage overheads are better for SHA than AES. The factor of extra communication overhead incurred by our protocols for SHA when compared to AES circuit is far less than the factor of increase in total communication for SHA over AES in [25]. This indicates that the efficiency of our protocols improves for larger circuits.  \n\n# 1.3 Roadmap  \n\nWe introduce the necessary primitives required for our protocols in Section 2. The description of distributed garbling scheme and the building blocks appears in Section 3. Our efficient protocols that achieve fairness and unanimous abort are presented in corresponding sections of 4 and 5. Our protocol for achieving guaranteed output delivery is presented in sections 6. We provide efficiency analysis of our protocols in Section 7. Finally, the security model and functionalities of our protocols appear in Appendix A. The security proofs are deferred to the full version [23].  \n\n# 2 PRELIMINARIES  \n\nWe consider a set of 5 parties $\\mathcal{P}=\\{P_{1},P_{2},P_{3},P_{4},P_{5}\\}$ , where each pair is connected by a pair-wise secure and authentic channel. The presence of a broadcast channel is assumed only for the GOD protocol where it is known to be necessary [32]. We model each party as a non-uniform probabilistic polynomial time (PPT) interactive Turing Machine. We consider a static security model with honest majority, where a PPT adversary $\\mathcal{A}$ can corrupt at most 2 parties at the onset of protocol. Adversary $\\mathcal{A}$ can be malicious in our setting.The computational security parameter is denoted by $\\kappa$ . The security of all our protocols is proved in the standard real/ideal world paradigm. Appendix A elaborates on the functionalities and security definitions. Below we discuss the primitives that we use.  \n\nNon-Interactive Commitment Schemes. A Non-Interactive Commitment Scheme (NICOM) is defined by two PPT algorithms (Com, Open) for the purpose of commitment and opening. We use instantiations based on injective one-way functions that ensure a strong binding even if the adversary chooses the public parameter arbitrarily. For our fair protocol, we need an equivocal NICOM (eNICOM). An eNICOM is defined with four PPT algorithms (eCom, eOpen, eGen, Equiv). eCom, eOpen are defined as in NICOM and eGen, Equiv are used to provide the property of equivocation. The formal definitions and the instantiations appear in Appendix B.  \n\nSecret Sharing Schemes. We use additive sharing and replicated secret sharing (RSS) [33, 50]. For a value $x$ , its $g$ th additive share is noted as $x^{g}$ . We now recall RSS. Consider a secret $x$ , of some finite field $\\mathbb{\\ F}$ to be shared among $n$ parties s.t only $>t$ parties can reconstruct $x$ . A maximal unqualified set is the set of $t$ parties who together cannot reconstruct the secret. A dealer with secret $x$ splits it into additive shares s.t each share corresponds to one maximal unqualified set $\\mathcal{T}_{l},l\\in\\{1,...,\\binom{n}{t}\\}$ . Formally, $\\textstyle x=\\sum_{l\\in[{\\binom{n}{t}}]}x^{l}$ . Each share $x^{l}$ is associated with $\\mathcal{T}_{l}$ (lexicographically wlog) and additive shares are random s.t they sum to $x$ . Each party $P_{i},i\\in[n]$ gets all $x^{l}$ for $P_{i}\\notin\\mathcal{T}_{l}$ . This ensures that $t$ parties alone of any $\\mathcal{T}_{l}$ cannot retrieve $x$ . We use a 4-party RSS with $t=2$ where, each party gets 3 shares and each share is held by 3 parties including the dealer. Reconstruction is done by combining the shares held by any 3 parties. Given only shares of any two parties $\\{P_{i},P_{j}\\}$ , $x$ remains private as xl where $\\mathcal{T}_{l}=\\{P_{i},P_{j}\\}$ is missing from the view.  \n\n# 3 DISTRIBUTED GARBLING AND MORE  \n\nAt the heart of our 5PC lies a 4-party distributed garbling (4DG) and a matching evaluation protocol tolerating arbitrary semi-honest corruptions. Garbling is done distributively amongst the garblers $\\{P_{1},P_{2},P_{3},P_{4}\\}$ and $P_{5}$ enacts the sole evaluator. Our 4DG scheme is a direct simplification of the state-of-the-art actively-secure distributed garbling scheme of [64]. The semi-honest scheme when combined with party-emulation idea of [25], achieves malicious security against 2 corruptions. Specifically, the role of each garbler in the underlying semi-honest 4DG scheme is also enacted by two other fellow garblers. This emulation is achieved via a unique seed distribution (SD) technique that ensures that the seed of a garbler is consistent with two other garblers and all the needed randomness for 4DG is generated from the seed. This helps to detect any wrong-doing by at most two garblers. Interestingly, the seed distribution can further be leveraged to replace the computationallyheavy public-key primitive Oblivious Transfer (OT) in [64] with an inexpensive symmetric-key based alternative called attested OT [25]. While all our protocols for 5PC can be realized with any underlying passively-secure garbling scheme when used with SD and attested OT, we choose the current construction for efficiency. We start with the seed distribution technique.  \n\n# 3.1 Building Blocks  \n\nSeed Distribution (SD). In the 4DG, all randomness required by a garbler $P_{i}$ is generated using a random seed $s_{i}$ . The SD technique involves distributing the seeds among 4 garblers s.t the seed $s_{i}$ generated by $P_{i}$ is held by two other garblers and no single garbler has the knowledge of all 4 seeds. Consequently, any data computed based on $s_{i}$ is done identically by 3 parties who own $s_{i}$ and thus, can be compared for correctness. With at least one honest party in this set of 3 parties, any wrong-doing by at most two parties is detected. The SD functionality $\\mathcal{F}_{\\mathrm{SD}}$ is depicted in $\\mathrm{Fig}1$ and is realized differently in each of our protocols based on the required security guarantee (fairness or GOD). We use $S_{g}$ to denote the set of indices of parties who hold $s_{g}$ as well as the set of indices of the seeds held by party $P_{g}$ . Note that both these sets are identical– for instance, $S_{1}=\\left\\{1,3,4\\right\\}$ indicates that parties $P_{1},P_{3},P_{4}$ hold $s_{1}.S_{1}$ also indicates that $P_{1}$ holds $s_{1},s_{3},s_{4}$ .  \n\nLet $S_{i}$ , $i\\in[4]$ be $S_{1}=\\{1,3,4\\}$ , $S_{2}=\\{2,3,4\\}$ , $S_{3}=\\{1,2,3\\}$ , ${\\cal S}_{4}=$ $\\{1,2,4\\}$ . Let $c$ be the set of corrupt parties. Corrupted parties $P_{j}\\in C$ may send the trusted party (Input, ${s}_{j}/\\perp)$ as instructed by the adversary. On message (Input, ∗) from garbler $P_{g}\\in\\mathcal{P}\\backslash C$ and (Input, $\\{s_{j}/\\perp\\}_{j\\in C}\\stackrel{\\cdot}{,}$ ) from adversary, sample $s_{i}$ on behalf of every honest $P_{i}$ and send $s_{g}$ (or $\\bot$ as given by adversary) to each party in $S_{g}$ .  \n\n# Figure 1: Functionality $\\mathcal{F}_{\\mathrm{SD}}$  \n\nAttested Oblivious Transfer (AOT). The AOT protocol [25] can be viewed as an OT between a sender and a receiver with an additional help from two other parties called “attesters”. These “attesters\" aid in ensuring correctness of the OT protocol by attesting inputs of the sender and the receiver. AOT functionality is recalled in Fig 2.  \n\n# 3.2 The semi-honest 4DG and Evaluation  \n\nA distributed garbled circuit (DGC) is prepared together by all garblers in a distributed manner. Each wire $\\boldsymbol{w}$ in our 4DG scheme is associated with a mask bit $\\lambda_{w}\\in\\{0,1\\}$ and each garbler $P_{g}$ holds a share $\\lambda_{w}^{g}$ s.t $\\lambda_{w}=\\oplus_{g\\in[4]}\\lambda_{w}^{g}$ . Each $P_{g}$ samples two keys $k_{w,0}^{g}$ ,  \n\n$P_{s}$ acts as sender, $P_{r}$ acts as receiver and $P_{a_{1}},P_{a_{2}}$ act as attesters. – On input message (Sen, $m_{0},m_{1})$ ) from $P_{s}$ , record $(m_{0},m_{1})$ and send (Sen, $m_{0},m_{1})$ to $P_{a_{1}}$ and $P_{a_{2}}$ and Sen to the adversary. – On input message (Rec, $b_{.}$ ) from $P_{r}$ , where $b\\in\\{0,1\\}$ , record $^b$ and send (Rec, $b_{,}$ ) to $P_{a_{1}}$ and $P_{a_{2}}$ and Rec to the adversary. – On input message (Att, $m_{0}^{j},m_{1}^{j},b^{j})$ from $P_{a_{j}},j\\in[2]$ , if (Sen, sid, $^*$ , $^{*)}$ and $({\\mathsf{R e c}},*)$ have not been recorded, ignore this message; otherwise, record $(m_{0}^{a_{j}},m_{1}^{a_{j}},b^{a_{j}})$ and send Att to the adversary. – On input message Output from the adversary, if $(m_{0},m_{1},b)\\neq$ $(m_{0}^{a_{1}},m_{1}^{a_{1}},b^{a_{1}})$ or $(m_{0},m_{1},b)\\neq(m_{0}^{a_{2}},m_{1}^{a_{2}},b_{a_{2}})$ , send (Output, ⊥) to $P_{r}$ ; else send (Output, $m_{b}$ ) to $P_{r}$ . – On input message abort from the adversary, send (Output, $\\perp$ ) to $P_{r}$ .  \n\n$k_{w,1}^{g}=k_{w,0}^{g}\\oplus\\Delta^{g}$ for each wire $w$ , with global offset $\\Delta^{g}$ . Thus, each super-key of a wire has 4 keys contributed by 4 garblers.  \n\nDefinition 3.1. A super-key of a wire is a set of 4 keys, each contributed by one garbler i.e., $\\{k_{w,0}^{g}\\}_{g\\in[4]}$ indicates the 0-superkey on wire $\\boldsymbol{w}$ and $\\{k_{w,1}^{g}\\}_{g\\in[4]}$ indicates the 1-super-key on $\\boldsymbol{w}$ .  \n\nFree-XOR is enabled by setting the mask and keys for the output wire of an XOR gate as the XOR of masks and keys of its input wires. A garbled AND gate, on the other hand, comprises of 4 super-ciphertexts (super-CT), one for each row of truth table. A super-CT is made up of 4 CTs, each of which is contributed by one garbler. Each CT hides a share of a super-key on the output wire such that during evaluation, 4 decrypted messages of a superCT together would give the desired super-key on the output wire. In order to hide the actual output of intermediate gates from an evaluator, we enable point and permute. The mask bit $\\lambda_{w}$ acts as the permutation bit for wire $w$ . Thus, for an AND gate with input wires $u$ , v, output wire $w$ and their corresponding masks $\\lambda_{u}$ , $\\lambda_{v},\\lambda_{w}$ , if $x_{u},x_{v}$ denote the actual values on wires $u,v$ respectively, then the evaluator sees super-keys $k_{u,b_{u}}^{g},k_{v,b_{v}}^{g}$ where $b_{u},b_{v}$ defined as $(b_{u}=x_{u}\\oplus\\lambda_{u})$ ), $(b_{v}=x_{v}\\oplus\\lambda_{v})$ denote the blinded bits. The evaluator then decrypts the super-CT positioned at row $(b_{u},b_{v})$ and obtains the output super-key $\\{k_{w,0}^{g^{-}}\\oplus\\Delta^{g}(x_{u}x_{v}\\oplus\\lambda_{w})\\}_{g\\in[4]}$ that corresponds to the blinded (masked) bit $x_{u}x_{v}\\oplus\\lambda_{w}$ on wire $\\boldsymbol{w}$  \n\nDefinition 3.2. A blinded or masked bit of a bit $x_{w}$ on a wire w is the XOR of $x_{w}$ with mask bit $\\lambda_{w}$ on wire $w$ i.e. $b_{w}=x_{w}\\oplus\\lambda_{w}$ .  \n\nInterpreting row $(b_{u},b_{v})$ as $\\gamma=2b_{u}+b_{v}+1$ and recasting the above, we see that the super-CT at row $\\gamma$ for $\\gamma\\in$ [4] encrypts the super-key $\\{k_{w,0}^{g}\\oplus\\Delta^{g}((b_{u}\\oplus\\lambda_{u})(b_{v}\\oplus\\lambda_{v})\\oplus\\lambda_{w})\\}_{g\\in[4]}.$ In 4DG, the super-CTs as above for an AND gate are prepared distributedly amongst the garblers, using the additive shares of the mask bits and keys held by each garbler corresponding to the input and output wires of the gate. We achieve this in a two-step process. First, we generate the additive sharing of each key belonging to the super-key to be encrypted in each row. Second, for each row, a garbler encrypts the additive shares it holds of each key of the corresponding super-key (obtained in the first step) in the CT that it contributes for the super-CT of that row. A CT for row $\\gamma$ has the format of one-time pad where the pad is calculated using a double-keyed PRF with keys corresponding to row $\\gamma$ .  \n\nDefinition 3.3. A super-ciphertext for a given row γ $\\cdot\\left(\\gamma=2b_{u}+b_{v}+\\right.$ 1), of an AND gate with input wires $u,v$ , output wire $w$ , is a set of  \n\n4 CTs, $\\{c_{\\gamma}^{g}\\}_{g\\in[4]}$ , where $P_{g}$ contributes $c_{\\gamma}^{g}$ that encrypts its additive share of each key in $\\{k_{w,0}^{g}\\oplus\\Delta^{g}((b_{u}\\oplus\\lambda_{u})(b_{v}\\oplus\\lambda_{v})\\oplus\\lambda_{w})\\}_{g\\in[4]}.$  \n\nTo compute the additive sharing of super-key $\\{k_{w,0}^{g}\\oplus\\Delta^{g}((b_{u}\\oplus$ $\\lambda_{u})(b_{v}\\oplus\\lambda_{v})\\oplus\\lambda_{w})\\}_{g\\in[4]}$ for all rows (i.e. all possibilities of $\\left(b_{u},b_{v}\\right))$ , we compute the additive sharing of the following in sequence, starting with the additive shares of $\\lambda_{u},\\lambda_{v},\\lambda_{w}$ : (A) $\\lambda_{u}\\lambda_{v}$ (for row 1 i.e. $\\gamma=1$ and $b_{u}=b_{v}=0_{\\qquad}$ ), $\\lambda_{u}\\overline{{\\lambda_{v}}}$ (for $\\gamma=2$ and $b_{u}=0,b_{v}=1)$ , $\\overline{{\\lambda_{u}}}\\lambda_{v}$ (for $\\gamma=3$ and $b_{u}=1,b_{v}=0$ ) and $\\overline{{\\lambda_{u}}}\\overline{{\\lambda_{v}}}$ (for $\\gamma=4$ and $b_{u}=1$ , $b_{v}=1);({\\mathrm B})\\lambda_{1}=\\lambda_{u}\\lambda_{v}\\oplus\\lambda_{w},\\lambda_{2}=\\lambda_{u}\\overline{{\\lambda_{v}}}\\oplus\\lambda_{w},\\lambda_{3}=\\overline{{\\lambda_{u}}}\\lambda_{v}\\oplus\\lambda_{w},\\lambda_{4}=$ $\\overline{{\\lambda_{u}}}\\overline{{\\lambda_{v}}}\\oplus\\lambda_{w};(\\mathrm{C})\\Delta^{g}\\lambda_{\\gamma}$ for all $g,\\gamma\\in[4]$ and lastly (D) $k_{w,0}^{g}\\oplus\\Delta^{g}\\lambda_{\\gamma}$ for all $g,\\gamma\\in$ [4]. (B) and (D) require linear operations, thus can be done locally by each garbler. However, for (A) and (C), additive sharing of a product needs to be computed which requires interaction among garblers. This is done via OTs, which we explain below. Also, in (A), it is known how to tweak shares of $\\lambda_{u}\\lambda_{v}$ locally to get the shares of remaining products [13], thus computing the sharing of $\\lambda_{u}\\lambda_{v}$ alone suffices. We now explain how the additive sharing of 1) $\\lambda_{u}\\lambda_{v}$ and 2) $\\Delta^{g}\\lambda_{\\gamma}$ for any $\\gamma\\in[4]$ is computed.  \n\nTo compute 1), each garbler $P_{g}$ locally computes $\\lambda_{u}^{g}\\lambda_{v}^{g}$ . In addition, each pair of parties $P_{g},P_{g^{\\prime}}$ for $g\\neq g^{\\prime}$ run an OT with $P_{g}$ as sender, holding $(r,r\\oplus\\lambda_{u}^{g})$ and $P_{g^{\\prime}}$ as receiver, holding $\\lambda_{v}^{g^{\\prime}}$ to generate 2-out-of-2 additive sharing of $\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}.P_{g}$ outputs its share as $r$ denoted by $[\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}]_{S}$ and $P_{g^{\\prime}}$ outputs its share as the OT output $r\\oplus\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}$ denoted by $[\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}]_{R}$ (We use $[\\cdot]_{S},[\\cdot]_{R}$ to denote the shares of sender and receiver of OT respectively). Each garbler $P_{g}$ now computes its share, $\\lambda_{u v}^{g}$ , of the product $\\lambda_{u v}=\\lambda_{u}\\lambda_{v}$ as the sum of its local product $\\lambda_{u}^{g}\\lambda_{v}^{g}$ and the shares obtained from OTs either as a sender or as a receiver i.e., $\\lambda_{u v}^{g}=\\lambda_{u}^{g}\\lambda_{v}^{g}\\oplus(\\oplus_{g\\neq g^{\\prime}}[\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}]_{S})\\oplus$ $(\\oplus_{g\\neq g^{\\prime}}[\\lambda_{u}^{g^{\\prime}}\\lambda_{v}^{g}]_{R})$ .  \n\nNext, to compute 2), where $\\Delta^{g}$ belongs to $P_{g}$ and $\\Delta^{g}\\lambda_{\\gamma}~=$ $\\Delta^{g}(\\lambda_{\\gamma}^{1}\\oplus\\lambda_{\\gamma}^{2}\\oplus\\lambda_{\\gamma}^{3}\\oplus\\lambda_{\\gamma}^{4})$ , each garbler $P_{g}$ first locally computes $\\Delta^{g}\\lambda_{\\gamma}^{g}$ and then for each cross-term $\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}},g\\neq g^{\\prime},P_{g}$ acts as a sender with each $P_{g^{\\prime}}$ as receiver in an OT to get their respective shares $[\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}}]_{S}$ and $[\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}}]_{R}$ . Finally, the share of $P_{g}$ for the product $\\Delta^{g}\\lambda_{\\gamma}$ is set to the following sum: $\\Delta^{g}\\lambda_{\\gamma}^{g}\\oplus(\\oplus_{g^{\\prime}\\neq g}[\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}}]_{S})$ , while the share of each $P_{g^{\\prime}}$ is set to $[\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}}]_{R}$ . We now present the functionality $\\mathcal{F}_{\\mathrm{GC}}$ formally in Fig 3. Partitioning the set of all super-CTs into its 4 constituent CTs, we can view the GC as $G C^{1}\\parallel\\bar{G}C^{2}\\parallel G C^{3}\\parallel G C^{4}$ where $g$ th partition is contributed by garbler $P_{g}$ .  \n\nEvaluation of the DGC. Starting with the masked bits of all inputs and corresponding super-keys, the evaluator $P_{5}$ evaluates the DGC in topological order, with XOR gate evaluated using free-XOR. For an AND gate with input wires $u,v$ and output wire $w,P_{5}$ , with input super-keys $\\{(k_{u,b_{u}}^{g},k_{v,b_{v}}^{g})\\}_{g\\in[4]}$ and blinded input bits $b_{u},b_{v}$ , decrypts $(b_{u},b_{v})\\mathrm{th}$ row’s super-CT to obtain the super-key corresponding to blinded output bit $x_{u}x_{v}\\oplus\\lambda_{w}$ and the blinded output bit itself. The blinded bits for output wires give clear output when XORed with their respective masks.  \n\n![](/tmp/output/51_20250326144796/images/49c7296d2214ebc3ab384620bc2ce59de11aaf59eb5a8faff6dd3c7950917a03.jpg)  \nFigure 3: Functionality $\\mathcal{F}_{\\mathrm{GC}}$  \n\n# 3.3 Distributed Garbling with AOT and SD  \n\nAs iterated before, we assume that all randomness required by a party $P_{g}$ for 4DG is generated using a random seed $s_{g}$ . The SD then enables a party-emulation technique where the seed $s_{g}$ of $P_{g}$ is given to exactly two other garblers in $S_{g}$ who can now emulate the role of $P_{g}$ . Thus, each partition of GC, $G C^{g}$ is generated by 3 garblers holding $s_{g}$ , offering security against at most two corrupt garblers. This also preserves input privacy as: (i) when two garblers are corrupt (and together hold all seeds), the evaluator is surely honest and protects input privacy; (ii) when a garbler and the evaluator are corrupt, one seed remains hidden, assuring input privacy. The SD results in a prime gain in the underlying semi-honest 4DG– replacing standard OTs with 1-round AOTs: The standard OTs used to compute each cross-term $\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}}$ , $\\Delta^{g}\\lambda_{\\gamma}^{g^{\\prime}}\\left(g\\neq g^{\\prime}\\right)$ in the additivesharing of $\\lambda_{u}\\lambda_{v},\\Delta^{g}\\lambda_{\\gamma}$ respectively, are replaced with AOTs. The SD further allows each AOT to be run s.t the attesters hold both seeds that the sender and receiver mutually-exclusively hold. This implies that the attesters are aware of the inputs of both sender and receiver at the onset, thus leading to a one-round instantiation of AOT. Note that the party-emulation technique does not increase the number of OTs required to three times the underlying semihonest 4DG but instead keeps it the same, since SD ensures that, for each garbler $P_{i}$ , OTs are needed in the computation of every $\\lambda_{u}^{g}\\lambda_{v}^{g^{\\prime}},\\bar{\\Delta^{g}}\\lambda_{\\gamma}^{g^{\\prime}}(g\\neq g^{\\prime})$ only when one of $g,g^{\\prime}$ is not in $S_{i}$ .  \n\nFor clarity, below we demonstrate, how a particular product share $\\lambda_{u v}^{1}$ (of $\\lambda_{u}\\lambda_{v.}$ ) is computed by parties in $S_{1}$ $(\\{P_{1},P_{3},P_{4}\\})$ , utilizing AOT and SD. The share $\\lambda_{u v}^{1}$ consists of summands as listed in the first column of the table below. We explain how $P_{1}$ computes each summand. Except $\\lambda_{u}^{1}\\lambda_{v}^{1}$ , the remaining summands correspond to cross-terms that $P_{1}$ originally obtained via OT either as sender or receiver. Now, all summands that correspond to $P_{1}$ enacting a sender $(\\lambda_{u}^{1}\\lambda_{v}^{g},g\\ne1)$ can be sampled from $s_{1}$ , as the sender’s share is a random bit. For the summands where $P_{1}$ enacts receiver $(\\lambda_{u}^{g}\\lambda_{v}^{1},g\\ne1)$ , AOT is needed only for the summand, $\\lambda_{u}^{2}\\lambda_{v}^{1}$ that involves s2 which $P_{1}$ does not own, while for other terms, $P_{1}$ can locally compute its share with the knowledge of both seeds. As for the AOT, $P_{1}$ acts as receiver with seed $s_{1},P_{2}$ acts as sender with seed $s_{2}$ , and $\\{P_{3},P_{4}\\}$ act as attesters with $\\{s_{1},s_{2}\\}$ . Similarly, $\\{P_{3},P_{4}\\}$ can compute the summands of $\\lambda_{u v}^{1}$ as indicated in the table.  \n\n<html><body><table><tr><td></td><td>P :(S1, S3, S4)</td><td>P3 :(S1,S2, S3)</td><td>P4 :(S1, S2, S4)</td></tr><tr><td>,[]s [ls,1s</td><td>local</td><td>local</td><td>local</td></tr><tr><td>[2A]R</td><td>F4AOT(P2，P1，{P3，P4})</td><td>local</td><td>local</td></tr><tr><td>[lR</td><td>local</td><td>local</td><td>F4AOT(P2,P4，{P1,P3})</td></tr><tr><td>[A]R</td><td>local</td><td>F4AOT(P2,P3，{P1,P4})</td><td>local</td></tr></table></body></html>\n\nOur final garbling and evaluation protocols appear in Figs 4-5.  \n\nEfficiency of 4DG. Our 4DG is superior to the state-of-the-art [13] computationally while retaining their communication efficiency. Concretely, for 4DG, [13] needs 4 PRF computations per CT of the super-CT whereas our scheme needs 1 PRF computation per CT. Since, the number of PRFs computed depends on the number of parties, this difference is significant for large $n$ i.e. $O(n^{2})$ versus $n$ PRF computation per super-CT.  \n\nAttested OT Instantiation. For the attested OT functionality $\\mathcal{F}_{4\\mathsf{A O T}}$ defined in Fig 2, we now provide a standalone instantiation. The sender of the AOT, $P_{s}$ having inputs $m_{0},m_{1}$ samples random $r_{0},r_{1}\\gets\\{0,1\\}^{\\kappa}$ and generates the commitments: $(\\mathsf{c}_{0},\\mathsf{o}_{0})\\gets$ $\\mathsf{C o m}(\\mathsf{p p},m_{0})$ , $(\\mathsf{c}_{1},\\mathsf{o}_{1})\\gets\\mathsf{C o m}(\\mathsf{p p},m_{1})$ . $P_{s}$ sends $(m_{0},r_{0},m_{1},r_{1})$ to the attesters and $(\\mathsf{p p},\\mathsf{c}_{0},\\mathsf{c}_{1})$ to the receiver. The receiver $P_{r}$ sends the choice bit $b$ to the attesters. The attesters exchange the copy of message received from $P_{s},P_{r}$ amongst themselves for correctness. If verified, they use $(m_{0},r_{0},m_{1},r_{1})$ to compute the commitments $(\\mathsf{p p},\\mathsf{c}_{0},\\mathsf{c}_{1})$ and send the same to the receiver. One of the attesters, say $P_{a_{1}}$ also sends the opening corresponding to $c_{b}$ to $P_{r}$ . If the verification fails, the attesters send $\\bot$ to $P_{r}$ . The receiver $P_{r}$ then checks if all the copies of commitments received are the same. If not, aborts. Else, $P_{r}$ uses the opening of $c_{b}$ to obtain $m_{b}$ .  \n\nWhen coupled with seed distribution, the standalone realization of $\\mathcal{F}_{4\\mathsf{A O T}}$ can be simplified as follows: The attesters are chosen s.t they possess the inputs (derived from seed) of both sender and receiver. For instance, when $P_{s}=P_{1},P_{r}=P_{2}$ , the attesters are $P_{3},P_{4}$ and the inputs of the sender are derived from the seed $s_{1}$ while the input of the receiver is derived from seed $s_{2}$ (both seeds are with $P_{3},P_{4})$ . Thus, $P_{s}$ , now sends $(\\mathsf{p p},\\mathsf{c o},\\mathsf{c}_{1})$ to $P_{r}$ and the attesters send $\\mathsf{H}((\\mathsf{p p},\\mathsf{c}_{0},\\mathsf{c}_{1}))$ to $P_{r}$ . Also, $P_{a_{1}}$ sends opening corresponding to commitment $c_{b}$ . All these steps can be done in only one round and hence AOT in our garbling scheme needs only one round. This process is formally depicted in Fig 6.  \n\nThe protocol realization specific to god is presented in Fig 7. This protocol is same as $\\Pi_{4\\mathsf{A O T}}$ , except that the sender’s and attesters’ messages are broadcast to enable the identification of conflict in case of mismatching messages. Thus the protocol either outputs the OT message to the receiver or identifies a $3\\mathrm{PC}\\:\\mathcal{P}^{3}$ for all.  \n\n# 3.4 Correctness of 4DG  \n\nLemma 3.4. The protocols Garble and Eval are correct.  \n\nCommon Inputs: Circuit $C$ that computes $f$  \n\nPrimitives and Notation: A double-keyed PRF F [13]. $S_{g}$ denotes the   \nindices of parties who hold $s_{g}$ as well as the indices of seeds held by $P_{g}$ .   \nOutput: Each party $P_{g},g\\in[4]$ outputs $G C^{j}$ , $j\\in S_{g}$ or $\\bot$ .   \nSampling Phase: Each $P_{g},g\\in[4]$ samples $\\Delta^{j}$ from $\\mathsf{s}_{j},j\\in S_{g}$ . Also,   \nthe following is done for each wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ in $C$ corresponding to seed $s_{j}$ :   \n– If $\\boldsymbol{\\mathbf{\\mathit{w}}}$ is not an output wire of XOR gate, sample $\\lambda_{w}^{j}$ and $k_{w,0}^{j}$ from $s_{j}$ Set $k_{w,1}^{j}=k_{w,0}^{j}\\oplus\\Delta^{j}$   \n– If $\\boldsymbol{w}$ is an output wire of XOR gate with input wires $u,v_{\\scriptscriptstyle\\mathrm{~\\AA~}}$ , set $\\lambda_{w}^{j}= $ $\\lambda_{u}^{j}\\oplus\\lambda_{v}^{j},k_{w,0}^{j}=k_{u,0}^{j}\\oplus k_{v,0}^{j}$ k jv ,0 and k jw , $k_{w,1}^{j}=k_{w,0}^{j}\\oplus\\Delta^{j}$ .   \nThe mask and super-key pair for a wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ is defined as $\\lambda_{w}=\\oplus_{g\\in[4]}\\lambda_{w}^{g}$   \nand $\\left(\\{k_{w,0}^{g}\\}_{g\\in[4]},\\{k_{w,1}^{g}\\}_{g\\in[4]}\\right)$ . Run in parallel for every AND gate in with input wires $u$ , $^{v}$ and output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ :   \nR1: Product Phase I: Define $\\lambda_{u v}=\\lambda_{u}\\lambda_{v}=(\\oplus_{g\\in[4]}\\lambda_{u}^{g})(\\oplus_{g\\in[4]}\\lambda_{v}^{g})$ .   \nLikewise define $\\lambda_{u\\overline{{{v}}}}$ , $\\lambda_{\\overline{{{u}}}v}$ , $\\lambda_{\\overline{{{u}}}\\overline{{{v}}}}$ that can be derived from shares of $\\lambda_{u v}$ .   \nEach garbler $P_{g}$ computes $\\lambda_{u v}^{j}$ of $\\lambda_{u v}$ for every $j\\in S_{g}$ as below:   \n– locally compute $\\lambda_{u}^{j}\\lambda_{v}^{j}$ . For each $k\\neq j$ , sample $[\\lambda_{u}^{j}\\lambda_{v}^{k}]_{S}$ from seed $s_{j}$ .   \n– for every $k\\in S_{g}$ , locally compute $[\\lambda_{u}^{k}\\lambda_{v}^{j}]_{R}=[\\lambda_{u}^{k}\\lambda_{v}^{j}]_{S}\\oplus\\lambda_{u}^{k}\\lambda_{v}^{j}$ with the knowledge of $s_{j}$ and $s_{k}$ .   \n– for each $k\\not\\in S_{g}$ , obtain $[\\lambda_{u}^{k}\\lambda_{v}^{g}]_{R}$ from $\\mathcal{F}_{4\\mathsf{A O T}}$ as receiver with input $\\lambda_{v}^{g}$ and $P_{k}$ as sender with inputs $([\\lambda_{u}^{k}\\lambda_{v}^{g}]_{S},[\\lambda_{u}^{k}\\lambda_{v}^{g}]_{S}\\oplus\\lambda_{u}^{k})$ derived from $s_{k}$ . Further, for each $k\\not\\in S_{g},j\\not=g$ , obtain $[\\lambda_{u}^{k}\\lambda_{v}^{j}]_{R}$ from $\\mathcal{F}_{4\\mathsf{A O T}}$ as receiver with input $\\lambda_{v}^{j}$ , and sender $P_{s}$ , $s=[4]\\setminus\\{g,j,k\\}$ with inputs $([\\lambda_{u}^{k}\\lambda_{v}^{j}]_{S},[\\lambda_{u}^{k}\\lambda_{v}^{j}]_{S}\\oplus\\lambda_{u}^{k})$ derived from $s_{k}$ .   \n– compute $\\lambda_{u v}^{j}=\\lambda_{u}^{j}\\lambda_{v}^{j}\\oplus(\\oplus_{i\\neq j}[\\lambda_{u}^{j}\\lambda_{v}^{i}]_{S})\\oplus(\\oplus_{i\\neq j}[\\lambda_{u}^{i}\\lambda_{v}^{j}]_{R})$ .   \nDefine $\\lambda_{1}=\\lambda_{u}\\lambda_{v}\\oplus\\lambda_{w}$ , $\\lambda_{2}=\\lambda_{u}\\overline{{{\\lambda_{v}}}}\\oplus\\lambda_{w},\\lambda_{3}=\\overline{{{\\lambda_{u}}}}\\lambda_{v}\\oplus\\lambda_{w},\\lambda_{4}=$   \n$\\overline{{\\lambda_{u}}}\\overline{{\\lambda_{v}}}\\oplus\\lambda_{w}$ . Every $P_{g}$ computes $j\\mathrm{th}$ share $\\lambda_{1}^{j}$ of $\\lambda_{1}$ for all $j\\in S_{g}$ as   \n$\\lambda_{u v}^{j}\\oplus\\lambda_{w}^{j}$ . Similarly, it computes the shares for $\\lambda_{2},\\lambda_{3},\\lambda_{4}$ .   \nR2: Product Phase II: $P_{g}$ computes share $[\\Delta^{j}\\lambda_{\\gamma}]_{j}$ (jth additive share)   \nof $\\Delta^{j}\\lambda_{\\gamma}$ for every $\\gamma\\in[4]$ and $j\\in S_{g}$ as follows:   \n– locally compute $\\Delta^{j}\\lambda_{\\gamma}^{j}$ . For every $k\\neq j$ , sample $[\\Delta^{j}\\lambda_{\\gamma}^{k}]_{S}$ from $s_{j}$ .   \n– compute $[\\Delta^{j}\\lambda_{\\gamma}]_{j}=\\Delta^{j}\\lambda_{\\gamma}^{j}\\oplus_{k\\neq j}[\\Delta^{j}\\lambda_{\\gamma}^{k}]_{S}$ .   \n$P_{g}$ computes $[\\Delta^{k}\\lambda_{\\gamma}]_{j}$ of $\\Delta^{k}\\lambda_{\\gamma}$ for each $k\\neq j,\\gamma\\in[4],j\\in S_{q}$ as:   \n◦ For every $k\\in S_{g}$ , compute $[\\Delta^{k}\\lambda_{\\gamma}]_{j}=[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{R}$ locally from the knowledge of $s_{j}$ and $s_{k}$ .   \n◦ For $k\\notin S_{g},j=g$ , obtain $[\\Delta^{k}\\lambda_{\\gamma}^{g}]_{R}$ from $\\mathcal{F}_{4\\mathsf{A O T}}$ acting as receiver with input $\\lambda_{\\gamma}^{g}$ and with $P_{k}$ as sender whose inputs are $[\\Delta^{k}\\lambda_{\\gamma}^{g}]_{S}$ and $[\\Delta^{k}\\lambda_{\\gamma}^{g}]_{S}\\oplus\\Delta^{k}$ derived from $s_{k}$ . Set $[\\Delta^{k}\\lambda_{\\gamma}]_{j}=[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{R}$ .   \n$\\circ\\operatorname{For}k\\notin S_{g},j\\neq g$ , obtain $[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{R}$ from F4AOT acting as receiver with input $\\lambda_{\\gamma}^{j}$ and $P_{s},s=[4]\\backslash\\{g,j,k\\}$ as sender with inputs $[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{S}$ , $[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{S}\\oplus\\Delta^{k}$ (from sk ). Set $[\\Delta^{k}\\lambda_{\\gamma}]_{j}=[\\Delta^{k}\\lambda_{\\gamma}^{j}]_{R}$ .   \nSuper-CT Construction Phase: For each $j\\in S_{g},P_{g}$ constructs $c_{\\gamma}^{j}$ for   \n$\\gamma\\in$ [4], as in $\\mathcal{F}_{\\mathrm{GC}}$ (Fig 3) and outputs   \n$G C^{j}=\\{\\{c_{\\gamma}^{j}\\}_{\\gamma\\in[4]}\\}_{\\forall\\mathrm{\\check{A}N D g a t e s}}||\\{\\mathsf{H}(k_{w,0}^{g}),\\mathsf{H}(k_{w,1}^{g})\\}_{\\forall}$ output wires w.  \n\n# Figure 4: Protocol Garble()  \n\nProof. To prove the lemma we argue that the super-key encrypted in the super-CT of a row decrypts to the correct super-key when evaluated on the blinded inputs corresponding to that row. Consider an AND gate with input wires $u$ , $\\boldsymbol{v}$ and output wire $\\boldsymbol{w}$ with corresponding masks $\\lambda_{u},\\lambda_{v}$ and $\\lambda_{w}$ respectively. Let the blinded inputs $b_{u},b_{v}$ received for evaluation have values $b_{u}=b_{v}=0$ . This means γ = 1 (row 1). We prove that bw and {kдw,bw }д ∈[4] are correctly computed given $b_{u},b_{v}$ and super-keys (ku,bu , kv,bv )}д ∈[4]. For simplicity we consider $\\lambda_{w}=0$ . The values $b_{u}=b_{v}=0$ imply $x_{u}~=~\\lambda_{u}$ and $x_{v}~=~\\lambda_{v}$ . Since, $\\lambda_{w}\\mathrm{~=~}0$ , $\\lambda_{\\gamma}\\:=\\:\\lambda_{1}\\:=\\:\\lambda_{u}\\lambda_{v}$ . This  \n\nInputs: $P_{5}$ holds $G C=G C^{1}||G C^{2}||G C^{3}||G C^{4}$ , blinded bit $b_{w}$ , the   \ncorresponding super-key $\\{k_{w,b_{w}}^{g}\\}_{g\\in[4]}$ for every input wire $\\boldsymbol{\\mathbf{\\nabla}}w$ and mask   \n$\\lambda_{w}$ for every output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ .   \nOutput: $P_{5}$ outputs $y=C(x)$ where $x$ is the actual input or $\\bot$ .   \nEvaluation: Evaluation is done topologically. For a gate with input wires   \n$u,v$ and output wire $w,P_{5}$ has $(\\bar{b_{u}},\\{\\bar{k_{u,b_{u}}^{g}}\\}_{g\\in[4]}),\\overbar(b_{v},\\{k_{v,b_{v}}^{g}\\}_{g\\in[4]})$   \n––  FFoorr  XAONRD  ggaattee,, $P_{5}$ sseettss $b_{w}=b_{u}\\oplus b_{v}$ $\\{k_{w,b_{w}}^{g}=k_{u,b_{u}}^{g}\\oplus k_{u,b_{v}}^{g}\\}_{g\\in[4]}$ . $P_{5}$ $\\gamma=2b_{u}+b_{v}+1$ $b_{w}=\\oplus_{g\\in[4]}\\lambda_{\\gamma}^{g}$ and kд $k_{w,b_{w}}^{g}=k_{w}^{g}\\oplus(\\oplus_{g^{'}\\neq g}[\\Delta^{g}\\lambda_{\\gamma}]_{g^{'}})$ by decrypting every CT $c_{\\gamma}^{g}$ in γ th super-CT as: $(\\lambda_{\\gamma}^{g}||\\big\\{[\\Delta^{g^{\\prime}}\\lambda_{\\gamma}]_{g}\\big\\}_{g^{\\prime}\\neq g}||k_{w}^{g}):=\\mathsf{F}_{k_{u,b_{u}}^{g},k_{v,b_{v}}^{g}}(j||g)\\oplus c_{\\gamma}^{g}.$   \nFor an output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ , $P_{5}$ assigns $\\mathbf{Y}:=\\{k_{w,b_{w}}^{g}\\}_{g\\in[4]}$ and checks if the   \nhash on $g$ th key in $\\mathbf{Y}$ verifies to $\\mathsf{H}(k_{w,b_{w}}^{g}),g\\in[4]$ . If so, $P_{5}$ outputs   \n$y_{w}:=b_{w}\\oplus(\\oplus_{g\\in[4]}\\lambda_{w}^{g}) $ for every output wire $\\boldsymbol{w}$ . Else $P_{5}$ aborts.  \n\n# Figure 5: Protocol Eval()  \n\n$P_{s},P_{r}$ denote the sender and receiver respectively. $P_{a_{1}},P_{a_{2}}$ are attesters.   \nAll are distinct parties.   \nInputs: $P_{s}$ holds $m_{0},m_{1}$ , $P_{r}$ holds choice bit $^b$ .   \nOutput $P_{r}$ outputs $m_{b}/\\perp$ .   \nPrimitives: A secure NICOM (Com, Open) (Appendix B).   \n– $P_{s}$ samples pp and random $r_{0},r_{1}\\gets\\{0,1\\}^{\\kappa}$ (derived from $s_{i}$ , $i\\in S_{s}\\backslash S_{r})$ and computes $(\\mathsf{c}_{0},\\mathsf{o}_{0})\\gets\\mathsf{C o m}(\\mathsf{p}\\mathsf{p},m_{0})$ , $\\left(\\mathbf{c}_{1},\\mathbf{o}_{1}\\right)\\gets$ $\\mathsf{C o m}(\\mathsf{p p},m_{1})$ . $P_{s}$ sends $(\\mathsf{p p},\\mathsf{c}_{0},\\mathsf{c}_{1})$ to $P_{r}$ . $P_{a_{1}},P_{a_{2}}$ who know $(r_{0},r_{1})$ (since they know $s_{i}$ ) also compute $(\\mathsf{c}_{0},\\mathsf{o}_{0})\\gets\\mathsf{C o m}(\\mathsf{p}\\mathsf{p},m_{0}),(\\mathsf{c}_{1},\\mathsf{o}_{1})$ $\\leftarrow\\mathsf{C o m}(\\mathsf{p p},m_{1})$ and each send $\\mathsf{H}((\\mathsf{p p},\\mathsf{c}_{0},\\mathsf{c}_{1}))$ to $P_{r}$ (The exact realization of the functionality $\\mathcal{F}_{4\\mathsf{A O T}}$ involves $P_{s}$ and $P_{r}$ sending $(r_{0},m_{0},r_{1},m_{1})$ and $^b$ respectively to $P_{a_{1}}$ and $P_{a_{2}}$ who in turn exchange their copies received from $P_{s},P_{r}$ for correctness).   \n– $P_{r}$ has $^b$ (derived using $s_{j},j\\in S_{r}\\setminus S_{s})$ which is known to $P_{a_{1}},P_{a_{2}}$ (since they know sj ). $P_{a_{1}}$ (wlog) sends $_{0}{_{b}}$ to $P_{r}$ .   \n(Local Computation by $P_{r}$ ): If the commitment sent by $P_{s}$ and the hash   \nvalues sent by $P_{a_{1}},P_{a_{2}}$ do not match, then $P_{r}$ outputs $\\bot$ . Else, output   \n$m_{b}=\\mathrm{Open}(\\mathrm{c}_{b},\\mathrm{o}_{b})$ .  \n\n# Figure 6: Protocol $\\Pi_{4\\mathsf{A O T}}(P_{s},P_{r},\\{P_{a_{1}},P_{a_{2}}\\})$ for Garble  \n\n$P_{s},P_{r}$ denote the sender and receiver respectively. $P_{a_{1}},P_{a_{2}}$ are attesters. $P_{a}$ denotes the auditor. All are distinct parties. Inputs: $P_{s}$ holds $m_{0},m_{1}$ , $P_{r}$ holds choice bit $^b$ . Notations $\\mathcal{P}^{3}$ is the 3PC committee with at most 1 corruption. Output $P_{r}$ outputs $m_{b}/\\mathcal{P}^{3}$ . All other parties output $\\perp/\\mathcal{P}^{3}$ . Primitives: A secure NICOM (Com, Open) (Appendix B). – $P_{s}$ samples pp and random $r_{0},r_{1}\\gets\\{0,1\\}^{\\kappa}$ (derived from $s_{i}$ , $i\\in S_{s}\\backslash S_{r})$ and computes $({\\mathsf{c}}_{0},{\\mathsf{o}}_{0})\\gets{\\mathsf{C o m}}({\\mathsf{p}}{\\mathsf{p}},m_{0})$ ), $\\left(\\mathbf{c}_{1},\\mathbf{o}_{1}\\right)\\gets$ $\\mathsf{C o m}(\\mathsf{p p},m_{1})$ . $P_{s}$ broadcasts (pp, $\\mathsf{c}_{0},\\mathsf{c}_{1})$ . $P_{a_{1}},P_{a_{2}}$ who know $(r_{0},r_{1})$ (since they know $s_{i}$ ) also compute $(\\mathsf{c}_{0},\\mathsf{o}_{0})\\gets\\mathsf{C o m}(\\mathsf{p p},m_{0})$ , $(\\mathbf{c}_{1},\\mathbf{o}_{1})$ $\\leftarrow\\mathsf{C o m}(\\mathsf{p p},m_{1})$ and each broadcast $(\\mathsf{c}_{0},\\mathsf{c}_{1})$ . – $P_{r}$ has $^b$ (derived using $s_{j},j\\in S_{r}\\setminus S_{s})$ which is known to $P_{a_{1}},P_{a_{2}}$ (since they know sj ). $P_{a_{1}}$ (wlog) sends $_{0}{_{b}}$ to $P_{r}$ . If the broadcast values sent by $P_{s},P_{a_{1}},P_{a_{2}}$ do not match, each $P_{\\gamma},\\gamma\\in$ [5] sets $\\mathcal{P}^{3}:=\\{a_{1},r,a\\}$ . Output $\\mathcal{P}^{3}$ . Computation by $P_{r}$ : If no $\\mathrm{o}_{b}$ is received or $\\mathrm{Open}(\\mathsf{c}_{b},\\mathsf{o}_{b})=\\perp$ , broadcast conflict with $P_{a_{1}}$ . All parties set $\\mathcal{P}^{3}:=\\{s,a_{2},a\\}$ and output $\\mathcal{P}^{3}$ . Else, $P_{r}$ outputs $m_{b}=\\mathrm{Open}(\\mathrm{c}_{b},\\mathrm{o}_{b})$ and the remaining parties output ⊥.  \n\n$$\n\\mathbf{{Figure7:Protocol}}\\Pi_{4\\mathrm{AOTGOD}}(P_{s},P_{r},\\{P_{a_{1}},P_{a_{2}}\\},P_{a})\n$$  \n\nmeans that $\\mathrm{g}(\\lambda_{u},\\lambda_{v})=\\mathrm{g}(x_{u},x_{v})$ where $\\mathbf{g}$ is the AND gate function. Thus, the encrypted super-key must be $\\{k_{w,\\mathrm{g}(x_{u},x_{v})}^{g}\\}_{g\\in[4]}$ as $\\Delta^{g}\\lambda_{1}=\\Delta^{g}\\mathrm{g}(x_{u},x_{v})$ (thus $\\lambda_{1}=\\mathrm{g}(x_{u},x_{v}))$ for each garbler $P_{g}$ . Now, we show that on decryption of the super-CT in row $\\gamma=1$ , the evaluator obtains kw,g(xu ,xv )}д ∈[4]. The plaintext of super-CT of row 1 on unmasking the one-time pad of PRF appears as follows:  \n\n$$\n\\begin{array}{r l r}&{}&{\\{(\\lambda_{1}^{1}||\\{[\\Delta^{g^{\\prime}}\\lambda_{1}]_{1}\\}_{g^{\\prime}\\neq1}||k_{w,0}^{1}\\oplus[\\Delta^{1}\\lambda_{1}]_{1}),}\\ &{}&{(\\lambda_{1}^{2}||\\{[\\Delta^{g^{\\prime}}\\lambda_{1}]_{2}\\}_{g^{\\prime}\\neq2}||k_{w,0}^{2}\\oplus[\\Delta^{2}\\lambda_{1}]_{2}),}\\ &{}&{(\\lambda_{1}^{3}||\\{[\\Delta^{g^{\\prime}}\\lambda_{1}]_{3}\\}_{g^{\\prime}\\neq3}||k_{w,0}^{3}\\oplus[\\Delta^{3}\\lambda_{1}]_{3}),}\\ &{}&{(\\lambda_{1}^{4}||\\{[\\Delta^{g^{\\prime}}\\lambda_{1}]_{4}\\}_{g^{\\prime}\\neq4}||k_{w,0}^{4}\\oplus[\\Delta^{4}\\lambda_{1}]_{4})\\}}\\end{array}\n$$  \n\nThe evaluator computes $b_{w}~=~\\oplus_{g\\in[4]}\\lambda_{1}^{g}~=~\\mathrm{g}(x_{u},x_{v})$ and the super-key as $\\{(k_{w,0}^{g}\\oplus[\\Delta^{g}\\lambda_{1}]_{g})\\oplus(\\oplus_{g^{\\prime}\\ne g}[\\Delta^{g}\\lambda_{1}]_{g^{\\prime}})\\}_{g\\in[4]}=\\{k_{w,0}^{g}\\oplus$ $\\Delta^{g}\\lambda_{1}\\}_{g\\in[4]}$ . Since $\\Delta^{g}\\lambda_{1}~=~\\Delta\\mathrm{g}(x_{u},x_{v})$ , the super-key reduces to kдw,g xu ,xv }д ∈[4] as desired. Similarly, correctness for the remaining rows of super-CT and for any choice of $\\lambda_{w}$ can be proved. □  \n\n# 4 5PC WITH FAIRNESS  \n\nRelying on pairwise-secure channels, we outline a symmetric-key based 5PC with fairness, tolerating 2 malicious corruptions with performance almost on par with the state-of-the-art [25] with selectiveabort and round complexity of 8. Starting with the overview of [25], we enumerate the challenges involved in introducing fairness into it and then describe techniques to tackle them.  \n\nIn [25], the garblers perform a one-time SD, which can be used for multiple executions. The evaluator $P_{5}$ splits her input additively among $P_{2},P_{3},P_{4}$ who treat the shares as their own input. Garbling is done using the passively secure scheme of [13] topped with techniques of SD and AOT (Section 3). For the transfer of super-keys wrt each input wire $w$ of each garbler $P_{g}$ , the remaining garblers send the mask shares not held by $P_{g}$ $(\\lambda_{w}^{j},j\\notin S_{g})$ on $\\boldsymbol{w}$ to $P_{g}$ who after verifying the shares for correctness (checking for equality), computes the blinded bit $b_{w}=x_{w}\\oplus\\lambda_{w}$ $(x_{w}$ is the input on $\\boldsymbol{w}$ ). Now, $P_{g}$ can send 3 out of 4 keys in the super-key $k_{w,x_{g}\\oplus\\lambda_{w}}^{j},j\\in S_{g}$ for $b_{w}$ to $P_{5}$ . However, to enable $P_{5}$ learn the fourth key for $b_{w}$ that corresponds to the seed held by remaining co-garblers, $P_{g}$ cannot simply send $b_{w}$ to the co-garblers, as it would leak $P_{g}$ ’s input when two of the garblers are corrupt (and hold all seeds and thus the mask $\\lambda_{w}$ ). [25] overcomes this subtle case as: $P_{g}$ splits $b_{w}$ as $b_{w}=\\oplus_{l\\in[4]\\backslash\\{g\\}}b_{l}$ and sends each share to exactly one cogarbler. Each co-garbler now sends key for the share she received to $P_{5}$ who XORs the 3 key-shares to get the desired $4^{\\mathrm{th}}$ key. The property of free-XOR is crucial in ensuring that XOR of key-shares gives the key on blinded input. A breach in the above solution is that $P_{g}$ colluding with $P_{5}$ can learn both super-keys for $w$ leading to multiple evaluations of $f$ . This is captured by the following attack: $P_{g}$ sets $b_{l}=0$ , $b_{l^{\\prime}}=1$ and sends them to co-garblers $P_{l}$ , $P_{l^{\\prime}}$ respectively. As a result, $P_{5}$ receives 0-key from $P_{l}$ , 1-key from $P_{l^{\\prime}}$ and XOR of these values leaks the global offset and thus both keys wrt the seed $P_{g}$ does not own. Now $P_{g}$ who already owns 3 seeds can use both 0-key and 1-key of the $4^{\\mathrm{th}}$ key to obtain multiple evaluations of $f$ . This is tackled by having $P_{g}$ and one of her cogarblers individually provide additive shares of $0^{\\kappa}$ that are XORed with key-shares before sending to $P_{5}$ . Finally, $P_{5}$ assembles the XOR shares and uses the $4^{\\mathrm{th}}$ key for evaluation. On evaluation, $P_{5}$ sends the output super key Y to all garblers, who then compute the output using output mask shares, that are exchanged and verified at the end of garbling phase.  \n\nThe prime challenge to introduce fairness in the protocol of [25] is for the case of a corrupt $P_{5}$ , who either sends $\\mathbf{Y}$ selectively to garblers or sends an invalid/no Y after learning the output herself on successful evaluation of DGC. This can be tackled using the natural techniques in the output phase as: (a) The garblers withhold the shares of mask bits on the output wires until a valid Y is received from $P_{5}$ . (b) To further prevent a corrupt $P_{5}$ from selectively sending $\\mathbf{Y}$ to garblers, we enforce the garbler who received valid Y from $P_{5}$ to, in turn, send the same $\\mathbf{Y}$ to her co-garblers. Nevertheless, both the above solutions still violate fairness. In solution (a), a corrupt garbler, $P_{g}$ can send an incorrect share of the mask bit on receiving Y, thus creating chaos for the honest receiver who fails to decide its true value, while $P_{g}$ herself learns the output using shares received from honest co-garblers. In solution (b), two colluding garblers can convince the honest garblers of any Y using their knowledge of all seeds, even if the honest $P_{5}$ aborts during evaluation. This is easily fixable with broadcast, however, without broadcast, a convincing strategy that Y indeed originated from $P_{5}$ is necessary.  \n\nWe tackle the concerns in solution (a) using the commit-then-open technique. In detail, garblers are forced to commit to the shares of mask bit on each output wire in advance to bar them from sending inconsistent values later and violating fairness. Each commitment is sent by 3-parties who own the corresponding seed which are then compared for correctness by each receiver prior to evaluation. The collision-resistant property of hash is used as a proofing mechanism to tackle the concerns in solution (b). Concretely, $P_{5}$ computes hash on a random value proof in the garbling phase and sends the resulting hash, H(proof) to all garblers who in turn exchange H(proof) amongst themselves for consistency. The value proof is sent as a proof to the garblers along with Y post evaluation. This technique is reminiscent of the one used in [24]. The above techniques ensure that a colluding garbler and $P_{5}$ cannot compute the output $y$ without the aid of at least one honest garbler. An honest garbler reveals shares on the mask bits owned by her only on receiving valid (Y, proof) from some party. This handles the concern in solution (b) by ensuring that Y was not impostered upon by two colluding garblers as they cannot forge a valid proof.  \n\n# 4.1 The construction  \n\nWe present the formal protocol in Fig 8. The garblers perform a one-time SD as in [25], which can be used for multiple runs. Circuit garbling is done as in Fig 4. The input keys sent by garblers define their committed inputs. The case of evaluator’s input and transfer of input keys is dealt as in [25]. In addition, we enforce each garbler to generate commitments on the shares of output wire masks wrt each seed she owns and allow agreement on these commitments by all parties. Also, $P_{5}$ samples a random proof and sends H(proof) to the garblers who agree on the hash value or abort. Then, $P_{5}$ evaluates the GC and sends (Y, proof) to all. Each garbler checks if (Y, proof) is valid. If so, it sends (Y, proof) and the openings corresponding to the commitments on mask bit shares of output wires to all. Finally, when a garbler has enough valid openings for commitments on mask bit shares of output wires, she computes the required output.  \n\nThe equivocal commitment eNICOM is used to commit on the output mask shares to handle a technicality that arises in the proof. Namely, when one garbler and $P_{5}$ are corrupt, the adversary, on behalf of $P_{5}$ can decide to abort as late as when Y needs to be sent to garblers. Hence, the simulator is also forced to act on the adversary’s behalf and invoke the functionality after this step. Nevertheless, the simulator needs to simulate the prior rounds with no clue of the output, which includes transfer of DGC, super-keys, commitments on output mask shares. To tackle this, the simulator uses eNICOM to commit to dummy values at the onset and later equivocates to output mask shares (set based on the output obtained after invoking the functionality) if corrupt $P_{5}$ sends Y to at least one honest garbler.  \n\nTo keep the eNICOM trapdoor hidden from the adversary and available to the simulator, we need it to be distributed among 3 parties. Although convenient, the public parameter for eNICOM cannot be derived from the seeds, as it would trivially arm a corrupt garbler (with the knowledge of 3 seeds) to equivocate. Further, due to the symmetry of eNICOM, equivocation seems infeasible for the simulator if the trapdoor is distributed into only three parts. Hence, we distribute the trapdoor and thus public parameter into four parts (held by three parties) to keep the binding property intact in the real world while allowing the simulator (acting on behalf of 3 honest parties) to perform equivocation. We demonstrate below for each $g\\in$ [4], how $\\mathbf{e}{\\mathbf{p}}{\\mathbf{p}}^{g}(=\\oplus_{l\\in[4]}\\mathbf{e}{\\mathbf{p}}{\\mathbf{p}}^{g l})$ for the output mask bits corresponding to $s_{g}$ is chosen by the parties. We note that we could opt for a random-oracle based scheme and use its programmability to enable equivocality. But this would make the proof rely on nonstandard assumption, and not injective one-way functions.  \n\n<html><body><table><tr><td>P1</td><td></td><td>P2</td><td>P3</td><td>P4</td></tr><tr><td>epp1</td><td>epp11,epp12</td><td></td><td>epp13</td><td>epp14</td></tr><tr><td>zdda</td><td></td><td>epp21,epp22</td><td>epp23</td><td>epp24</td></tr><tr><td>epp3</td><td>epp31</td><td>epp32</td><td>epp33, epp34</td><td></td></tr><tr><td>epp4</td><td>epp41</td><td>epp42</td><td></td><td>epp43 , epp44 </td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>  \n\nOptimizations. We propose the optimizations below to boost the efficiency of fair: all optimizations of [25] can be applied to our protocol. More concretely, majority of communication in the garbling phase is due to the number of AOT invocations. This is optimized with the use of batch AOTs. Batch AOTs allow the sender to send both commitments while the attesters send only hash on all the commitments. The NICOM instantiation (Appendix B) based on the ideal cipher model can be used to obtain faster commitments in practice. Each $G C^{g}$ , $g\\in[4]$ , is sent by exactly one owner while the rest send only $\\mathsf{H}(G C^{g}).P_{5}$ verifies the hash values before evaluation. For implementation, eNICOM, NICOM are instantiated with random-oracle based commitment. Also, communication in eNICOM is saved by generating commitment on the concatenation of mask bit shares of all wires rather than on each bit individually.  \n\n# 4.2 Properties  \n\nLemma 4.1. The protocol fair is correct.  \n\nProof. The input of $P_{5}$ is well defined by the shares sent to $P_{2},P_{3},P_{4}$ . The 3 keys for each input wire owned by the garblers, along with the $4^{\\mathrm{th}}$ key sent as XOR shares, define their committed  \n\nInput and Output: Party $P_{i}\\in\\mathcal{P}$ has input $x_{i}$ and outputs $y=C(x_{1},x_{2},x_{3},x_{4},x_{5})$ or $\\bot$ .  \n\nCommon Inputs: The circuit $C(x_{1},x_{2},x_{3},x_{4},\\oplus_{j\\in\\{2,3,4\\}}x^{5j})$ that computes $f(x_{1},x_{2},x_{3},x_{4},x_{5})$ and takes $x_{1}$ , $x_{2}$ , $x_{3}$ , $x_{4}$ and shares $\\{x^{5j}\\}_{j\\in\\{2,3,4\\}}$ as inputs, each input, their shares are from $\\{0,1\\}$ (instead of $\\{0,1\\}^{\\ell}$ for simplicity) and output is of the form $\\{0,1\\}^{\\ell}$ .  \n\nNotation: $S_{i}$ denotes indices of the parties who hold $s_{i}$ as well as indices of the seeds held by $P_{i}$  \n\nPrimitives: A NICOM (Com, Open), an eNICOM (eGen, eCom, eOpen, Equiv), Garble (Fig 4), Eval (Fig 5), Collision Resistant Hash H (Appendix B). Seed Distribution Phase (one-time): $P_{g}$ chooses random seed $s_{g}\\in_{R}\\{0,1\\}^{\\kappa}$ , and sends $s_{g}$ to the other two parties in $S_{g}$ who in turn exchange with each other and abort if their versions do not match.  \n\nEvaluator’s Input sharing Phase: $P_{5}$ secret shares its input as $x_{5}=x^{52}\\oplus x^{53}\\oplus x^{54}$ . $P_{5}$ sends $x^{5j}$ to $P_{j}$ (wlog).  \n\nSetup of public parameter for Equivocal Commitment. For $\\exp{\\mathfrak{p}}^{g}$ , $g\\in[4]$ of eNICOM, each $P_{j}$ , $j\\in S_{g}$ samples $\\exp{\\mathfrak{p}}^{g j}$ from fresh randomness (not from any of the seeds he holds ) and sends to all. $P_{g}$ additionally samples $\\exp{g l}$ , $l\\in[4]\\backslash S_{g}$ and sends to all. Each party computes $\\mathrm{epp}^{g}=\\oplus_{j\\in[4]}\\mathrm{epp}^{g j}$ . $P_{l}\\in\\mathcal{P}$ forwards $\\exp^{g}$ , $g\\in[4]$ to all. Each $P_{i}\\in\\mathcal{P}$ aborts if any of $\\exp{\\mathfrak{p}}^{g}$ received mismatch.  \n\n# Transfer of Equivocal Commitments.  \n\n– Each $P_{g},g\\in[4]$ runs the Sampling Phase of Garble $(C)$ and computes the following commitments for every circuit output wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ using randomness from $\\boldsymbol{s}_{j}$ , $j\\in S_{g}$ : $\\{(\\mathrm{c}_{w}^{j},\\mathrm{o}_{w}^{j})\\gets\\mathrm{eCom}(\\mathrm{epp}^{j},\\lambda_{w}^{j})\\}_{j\\in S_{g}\\cdot}P_{g}$ sends $\\{(\\mathsf{e p p}^{j},\\mathsf{c}_{w}^{j})\\}_{j\\in S_{g}}$ to all.  \n\n$P_{i}\\in\\mathcal{P}$ aborts if it receives mismatched copies of $(\\mathtt{e p p}^{j},\\mathtt{c}_{w}^{j}),j\\in[4]$ for some output wire $\\boldsymbol{w}$  \n\n# Garbling, Masked input bit and Key Transfer Phase.  \n\nFor circuit input wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ held by $P_{g},g\\in$ [4] corresponding to input bit $x_{w}$ , each $P_{l},l\\in[4]\\setminus\\{g\\}$ sends $\\lambda_{w}^{j},j\\in S_{l}$ to $P_{g}.P_{g}$ aborts if it   \nreceives mismatched copies for some $\\lambda_{w}^{j}$ . Else, $P_{g}$ computes $\\lambda_{w}=\\oplus_{j\\in[4]}\\lambda_{w}^{j}$ and $b_{{\\pmb w}}=x_{{\\pmb w}}\\oplus\\lambda_{{\\pmb w}}.P_{g}$ sends $(b_{w},\\{k_{w,b_{w}}^{j}\\}_{j\\in S_{g}})$ to $P_{5}$ . To send $k_{w,b_{w}}^{j},j\\in[4]\\backslash S_{g}$ o (mn obti thse ld bayn $P_{g}$ a) ntdo $P_{5}$ , it does the f wing (The case for key of o lsl.ot tahned $P_{5}^{\\prime}s$ input share  by isf ehnedlsd $P_{g}$ tiso .ndled similarly): $\\circ~P_{g}$ chooses rand $b_{l}$ d r om $\\beta_{l}\\in\\{0,1\\}^{\\kappa}$ $b_{w}=\\oplus_{l\\in[4]\\backslash\\{g\\}}b_{l}$ $0^{\\kappa}=\\oplus_{l\\in[4]\\backslash\\{g\\}}\\beta_{l}.P_{g}$ $b_{l},\\beta_{l}$ $P_{l}$ $\\circ$ One garbler other than $P_{g}$ chooses $\\delta_{l}\\in\\{0,1\\}^{\\kappa}$ s.t $0^{\\kappa}=\\oplus_{l\\in[4]\\backslash\\{g\\}}\\delta_{l}$ and sends $\\delta_{l}$ to $P_{l}$ . $\\circ\\:\\:P_{l}$ sends $K_{l}=k_{w,b_{w}^{l}}^{j}\\oplus\\beta_{l}\\oplus\\delta_{l}$ to $P_{5}$ who sets k w,bw := ⊕l Kl .   \n– For input wire $\\boldsymbol{w}$ correswponding to $P_{5}$ ’s input shares, let $\\{k_{w,0}^{g},k_{w,1}^{g}\\}_{g\\in[4]}$ be the keys derived from seeds $\\{s_{g}\\}_{g\\in[4]}.\\operatorname{Each}P_{g},g\\in[4]$ computes commitments on these as: for $b\\in\\{0,1\\},j\\in S_{g}$ , $(c_{w,b}^{j},\\sigma_{w,b}^{j})\\gets\\mathsf{C o m}(\\mathsf{p p}^{j},k_{w,b}^{j})$ using ${\\mathsf{p p}}^{j}$ and randomness derived from $s_{j}$ and sends $\\{\\boldsymbol{\\mathrm{pp}}^{j},c_{w,b}^{j}\\}$ to $P_{5}.P_{g}$ also sends $o_{w,b_{w}}^{j}$ to $P_{5}$ if it holds $b_{w}.P_{5}$ aborts if it receives either different copies of commitments or invalid opening for   \nany wire. Otherwise, $P_{5}$ recovers the super-keys for $b_{w}$ , namely, $\\{k_{w,b_{w}}^{g}\\}_{g\\in[4]}$ . Let $\\mathbf{X}$ to be the set of super-keys obtained.   \nGarble $(C)$ is run. Each $P_{g}$ , $g\\in[4]$ sends $\\{G C^{j}\\}_{j\\in S_{g}}$ to $P_{5}$ . If $P_{5}$ finds conflicting copies, it aborts.   \nEvaluation and Output Phase.  \n\n$P_{5}$ runs Eval to evaluate $G C$ using $\\mathbf{X}$ and obtains $\\mathbf{Y}$ and $(y_{w}\\oplus\\lambda_{w})$ for all output wires $\\boldsymbol{\\mathbf{\\nabla}}w$ . $P_{5}$ sends (Y, proof) to all. For $g\\in$ [4], $j\\in S_{g}$ , if $k_{w,b_{w}}^{j}$ of $\\mathbf{Y}$ for some output wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ does not match with either $(k_{w,0}^{j},k_{w,1}^{j})$ or the three keys k jw ,bw in Y do not map to the same $b_{w}$ or if proof does not verify with $\\mathsf{H}(\\mathsf{p r o o f})$ received before, $P_{g}$ does nothing. Else, $P_{g}$ sends (Y, proof) to all other garblers and $\\{\\circ_{w}^{j}\\}_{j\\in S_{g}}$ to all. $P_{5}$ checks if valid $\\{\\circ_{w}^{j}\\}_{j\\in S_{g}}$ received from each $P_{g}$ . If so, $P_{5}$ computes $y_{w}=(y_{w}\\oplus\\lambda_{w})\\oplus(\\oplus_{l\\in[4]}\\lambda_{w}^{l})$ for output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ and thus outputs $y$ . If received valid (Y, proof) and $\\{\\boldsymbol{\\mathrm{o}}_{w}^{j}\\}_{j\\in S_{g}}$ from a co-garbler $P_{g}$ , $P_{\\alpha},\\alpha~\\in$ [4] computes $y$ by unmasking all $\\lambda_{w}$ . Also, if sent nothing before, send (Y, proof) to co-garblers, $\\{\\circ_{w}^{l},\\circ_{w}^{j}\\}_{l\\in S_{\\alpha},j\\in S_{g}}$ to all. If no $y$ computed yet and received valid (Y, proof), $\\{\\circ_{w}^{l},\\circ_{w}^{j}\\}_{l\\in S_{\\alpha},j\\in S_{g}}$ from co-garbler $P_{\\alpha}$ $\\mathrm{~\\boldmath~{~o~}~}_{w}^{j}$ was sent by $P_{g}$ to $P_{\\alpha}$ before), compute $y$ upon unmasking all $\\lambda_{w}$ . Likewise, if $P_{5}$ has not computed $y$ yet and received valid $\\{\\mathrm{o}_{w}^{l}$ , $\\underline{o}_{w}^{j}\\}_{l\\in S_{\\alpha},j\\in S_{g}}$ from $P_{\\alpha}$ $\\mathrm{~\\boldmath~{~o~}~}_{w}^{j}$ was sent by $P_{g}$ to $P_{\\alpha}$ before), $P_{5}$ computes $y$ by unmasking all $\\lambda_{w}$ .  \n\n# Figure 8: Protocol fair  \n\ninputs. Evaluation is done on committed inputs. The correctness of $\\mathbf{Y}$ and thus $y$ follows from the correctness of garbling scheme (Figs 4, 5) presented in Lemma 3.4. □  \n\nTheorem 4.2. Our fair protocol consumes at most 8 rounds.  \n\nProof. The proof establishment phase and setting up of public parameter for eNICOM consume 2 rounds each and can be overlapped. Further, round 1 of these two phases can be overlapped with distribution of $P_{5}$ ’s input and round 1 of masked input bit computation and key transfer phase. These together consume a total of 3 rounds. The key transfer is started prior to Garble. More precisely, garbling can begin alongside round 3 of key transfer phase. The transfer of GC and keys to $P_{5}$ take 1 round. Finally, evaluation and output phase need at most 3 rounds, thus settling the protocol in 8 rounds. If Y is received by all honest garblers in round 1 of output phase itself, then 7 rounds suffice. The seed distribution phase is one-time and hence is not counted for round complexity. □  \n\nTheorem 4.3. Assuming one-way permutations, the protocol of fair securely realizes $\\mathcal{F}_{\\mathrm{fair}}$ (Fig. 17) in the standard model against a malicious adversary that corrupts at most two parties.  \n\nThe proof of the theorem appears in the full version [23]  \n\n# 4.3 $n$ -party Extension of fair  \n\nThe technique of achieving fairness for 5 parties can be extended to $n$ parties tolerating $t<{\\sqrt{n}}$ corruptions by modifying only the output phase of fair (Fig 8). We first recall the conditions involved in seed distribution for n-parties elaborated in [25] to better understand the extension tolerating $t={\\sqrt{n}}$ corruptions. The seed distribution needs to satisfy the following properties:  \n\nPrivacy: No t − 1 garblers should hold all the seeds. This is to ensure input privacy of honest garblers when $t-1$ garblers and the evaluator collude.   \nAttested OT For each pair of seeds $\\mathsf{s}_{i},\\mathsf{s}_{j}$ , there must be a garbler who holds both $\\mathsf{s}_{i},\\mathsf{s}_{j}$ . This party will act as an attester in the corresponding AOT.   \nCorrectness Every seed should be held by at least $t+1$ garblers. This is necessary for correctness of the computed DGC.  \n\nAll the above properties collectively imply that for any corruption scenario, the honest garblers together must hold all the seeds. Specifically, from correctness: each seed $s_{i}$ that is supposed to be held by at least $t+1$ garblers is sure to end up in the hands of an honest garbler in the worst case corruption scenario of $t$ corrupt garblers. To achieve fairness for the case of $n$ parties, all steps of the protocol fair remain the same except the output phase. For the extension, we consider that $P_{1},...,P_{n-1}$ are garblers and $P_{n}$ is the evaluator. On a high level, the output phase involves 3 rounds where in round $1,P_{n}$ sends (Y, proof) to all garblers and the remaining two rounds are used to exchange (Y, proof) with co-garblers and openings for the commitments on mask-shares belonging to output wires with all and thus fairly compute the output.  \n\nRound 1: The evaluator sends (Y, proof) to the garblers.   \nRound 2: If the received (Y, proof) from the evaluator is valid, each garbler $P_{g}$ forwards (Y, proof) and openings for the commitments on output mask shares wrt the seeds she holds.   \nRound 3: If received valid (Y, proof) and valid openings from subset of garblers s.t the openings received and the output mask shares already held by party $P_{\\alpha}$ are sufficient to reconstruct $\\lambda_{w}$ for every output wire $\\boldsymbol{w}$ , then $P_{\\alpha}$ computes output $y$ using the output masks. If sent nothing before, $P_{\\alpha}$ forwards (Y, proof) and the accumulated openings to all. Local Computation: If no $y$ computed yet and received valid (Y, proof) and openings received from subset of garblers so far are enough to reconstruct $\\lambda_{w}$ for every output wire $\\boldsymbol{w}$ , then party $P_{\\beta}$ computes output $y$ using the output masks.  \n\n# Figure 9: Output Phase for $n$ -party fairness  \n\nEach honest party computes the output only if openings for commitments wrt every seed is received by the end of round 3. A naive way to distribute the openings in the last two rounds is to allow an honest garbler to forward the openings possessed by her (and if received any other) when a valid (Y, proof) is received. This technique however, leads to fairness violation in the following scenario: suppose the evaluator and $t-1$ garblers are corrupt and $P_{n}$ does not communicate with any honest garbler in round 1, However in round 2, few of the corrupt garblers send (Y, proof) to one set of honest parties (chosen selectively s.t the openings of this set of honest parties and those held by the adversary are enough to compute the output). These honest parties forward all the accumulated openings in round 3 and thus the adversary gets the output. Further, in round 3, the adversary can also choose to send the openings to the other complementary set of honest parties on behalf of all the corrupt parties who have not sent anything yet, thus ensuring that other complimentary set gets the output while the first set aborts. To tackle this, we impose a restriction on the garbler $P_{g}$ who communicates for the first time in round 3 of the output phase as: Forward all the openings accumulated until round 2 only if, the openings received in round 2 together with those held by $P_{g}$ are sufficient to reconstruct the output. This condition eliminates the dependency of $P_{g}$ on shares received in round 3 to compute the output and ensures that the adversary, in order to compute the output herself, must aid at least one honest party compute the output. Thus, even if one honest party is able to compute the output at the end of round 2, then that honest party releases all the openings in round 3 sufficient to help all honest parties compute the output. This concludes the intuition. The formal protocol is presented in Fig 9.  \n\n# 5 5PC WITH UNANIMOUS ABORT  \n\nBy simplifying fair, we present a 5PC achieving unanimous abort, relying on a network of pairwise-private channels with performance on par with [25] and having the round complexity to 8. Specifically, we eliminate the stronger primitive of eNICOM used to commit on output mask shares, owing to weaker security. However, we still need to address the case of a corrupt $P_{5}$ selectively sending $\\mathbf{Y}$ to honest garblers. Unanimous abort can be trivially achieved if Y is broadcast by $P_{5}$ instead of being sent privately but since broadcast increases assumptions and is expensive in real-time networks, we enforce the garbler who receives a valid Y from $P_{5}$ to forward the same to her co-garblers. However, this technique does not suffice on its own, since in case of a colluding garbler and the evaluator, $P_{5}$ may not send Y to any honest party and at the same time, the corrupt garbler may send (Y, proof) only in the last round, to one honest garbler. To tackle this, we ensure that an honest garbler accepts Y from a co-garbler only if the co-garbler gives a valid proof that she received Y from $P_{5}$ only in the previous round. Further, to ensure that Y indeed originated from $P_{5}$ (and was not forged by two corrupt garblers), we reuse the technique described in fair. The formal protocol appears in Fig 10. Similar to our fair protocol, this protocol can also be extended for arbitrary $n$ parties.  \n\n# 5.1 $n$ -party Extension of ua  \n\nTo achieve unanimous abort for the case of $n$ parties, all steps of the protocol ua remain the same except the output phase. The seeddistribution is done as explained in Appendix 4.3. For the extension, we consider that $P_{1},...,P_{n-1}$ are garblers and $P_{n}$ is the evaluator. On a high level, the output phase involves 3 rounds where in round $1,P_{n}$ sends $(\\mathbf{Y},{\\mathsf{p r o o f}}_{n}.$ ) to all garblers and the remaining two rounds are used to exchange the Y and proofs to compute the output.  \n\nEach honest party computes the output only if $t+1$ proofs are received by the end of round 3. This is done to prevent the adversary from remaining silent in first two rounds but selectively sending Y to few honest parties only in the last round and them naively accepting the output without any confirmation about fellow honest parties. Thus, an honest garbler who has not sent anything until the end of round 2, forwards Y and the received proofs (along with own proof) in round 3 only if at least $t$ valid proofs are received by the end of round 2. This ensures that all honest parties are in agreement about the output acceptance at the end of round 3. In detail, if one honest party decides to accept the output by the end of round 2 due to the availabilty of $t$ proofs, then all honest parties will also  \n\nInputs, Common Inputs, Output and Notation $:$ Same as in fair().   \nPrimitives: A NICOM (Com, Open), Garble (Figs. 4), Eval (Fig. 5).   \nSeed Distribution Phase (one-time)and Evaluator’s Input Sharing   \nPhase are same as in fair().   \nProof Establishment Phase: $P_{i}$ , $i\\in[5]$ chooses proof $\\dot{\\mathbf{\\rho}}_{i}$ from the do  \nmain of a hash function H, computes and sends $\\mathsf{H}(\\mathsf{p r o o f}_{i})$ to all parties.   \nEach party, $P_{j},j\\in[5]\\setminus\\{i\\}$ in turn sends the copy of $\\mathsf{H}(\\mathsf{p r o o f}_{i})$ received   \nto the remaining parties. $P_{j}$ aborts if the $\\mathsf{H}(\\mathsf{p r o o f}_{i})$ received from the   \nremaining parties does not match with her own copy received from $P_{i}$ .   \nElse, $P_{j}$ accepts $\\mathsf{H}(\\mathsf{p r o o f}_{i})$ to be the agreed upon hash.   \nSetup of public parameter and Transfer of Equivocal Commit  \nments are not present in this protocol but instead for each output wire $\\boldsymbol{w}$ ,   \neach $P_{j},j\\in S_{g}$ sends $\\lambda_{w}^{g}$ in clear to all. Each party $P_{i}~\\in~\\mathcal{P}$ aborts   \nif the three copies of $\\lambda_{w}^{g}$ received do not match. Else, $P_{i}$ computes   \nλw = ⊕д 4 λw .   \nGarbling, Masked input bit, Key Transfer Phase are same as in fair().   \nEvaluation and Output Phase:   \n– $P_{5}$ runs Eval to evaluate $G C$ using X and obtains $\\mathbf{Y}$ and $(y_{w}\\oplus\\lambda_{w})$ for all output wires $\\boldsymbol{w}$ . $P_{5}$ sends $\\mathrm{\\bf{Y}},$ proof) to all. $P_{5}$ locally computes $y_{w}=(y_{w}\\oplus\\lambda_{w})\\oplus_{l\\in[4]}\\lambda_{w}^{l}$ for each output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ .   \noutput w $P_{g},g\\in[4],j\\in S_{g}$ with either $k_{w,b_{w}}^{j}$ of $\\mathbf{Y}$ for some $\\boldsymbol{\\mathbf{\\mathit{w}}}$ does not match $(k_{w,0}^{j},k_{w,1}^{j})$ , k jw ,1) or the three keys k jw,bw , j ∈ Sд in Y do not map to the same bw or proof5 fails, then do nothing. Else for each output wire $\\boldsymbol{w}$ , compute $y_{w}$ unmasking $\\lambda_{w}$ . Send $(\\mathbf{Y},{\\mathsf{p r o o f}}_{5}$ , proof $_g$ ) to the co-garblers. – If received valid $(\\mathbf{Y},{\\mathsf{p r o o f}}_{5}$ , proof $_g$ ) from a co-garbler $P_{g}$ $,P_{\\alpha},\\alpha~\\in$ [4] computes $y$ unmasking $\\lambda_{w}$ . Also if sent nothing before, send (Y, proof $5$ , proof $_g$ , proof $\\overset{\\cdot}{\\alpha}$ ) to all. If no output $y$ is computed yet and received valid $(\\mathbf{Y},{\\mathsf{p r o o f}}_{5}$ , proof $_g$ , proof $_{\\alpha}$ ) from co-garbler $P_{\\alpha}$ (proof $_g$ indicates $(\\mathbf{Y},{\\mathsf{p r o o f}}_{5}$ , proof $_g$ ) was received from $P_{g}$ ), garbler $P_{\\gamma}$ obtains $\\left(y_{w}\\oplus\\lambda_{w}\\right)$ from $\\mathbf{Y}$ , unmasks $\\lambda_{w}$ and computes $y$ .  \n\n# Figure 10: Protocol ua()  \n\naccept the output at the end of round 3 due to the availability of at least $t+1$ proofs which implies that an honest party has accepted Y i round 2. This completes the intuition. We formally present the $n$ -party extension for unanimous abort in Fig 11.  \n\nTheorem 5.2. Assuming one-way permutations, our protocol ua securely realizes the functionality $\\mathcal{F}_{\\mathrm{uAbort}}$ (Fig. 16) in the standard model against a malicious adversary that corrupts at most two parties.  \n\nThe proof of the theorem appears in the full version [23].  \n\n# 6 5PC WITH GOD  \n\nWith fair as the starting point, we elevate the security and present a constant-round 5PC with GOD relying only on symmetric-key primitives. We assume a necessary broadcast channel besides pairwiseprivate channels for our corruption threshold owing to the result of [32]. Our protocol reduces to an honest-majority 3PC with GOD in some cases. With the assumption of broadcast channel, our protocol takes 6 rounds when no 3PC is invoked and stretches up to 12 rounds when packed with the 3PC of [24] in the worst case.  \n\nThe Construction. We achieve GOD by tackling abort scenarios when parties are in conflict. Specifically, we eliminate a corrupt party and transit to a smaller world of 3 parties with at most one corruption to complete computation. We retain the setup of four garblers $\\{P_{1},P_{2},P_{3},P_{4}\\}$ and $P_{5}$ as the evaluator. On a high level, our protocol starts with a robust input and (one-time) SD, followed by garbling, transfer of GC, blinded inputs and corresponding superkeys to $P_{5}$ and concludes with GC evaluation by $P_{5}$ and output computation by all. The key technique to achieve robustness lies in the use of tools: 4-party 2-private RSS and SD to ensure that each phase of the protocol is robust against any malicious wrongdoing. While using a passively-secure 4DG as the underlying building block, there exist cases where it seems improbable to publicly identify a corrupt party due to the presence of 2 active corruptions. Instead, when the adversary strikes, we establish and eliminate the parties in conflict publicly (of which one is ensured to be corrupt) and rely on the remaining parties with at most one corruption to robustly compute the output. The essence of our protocol lies in tackling the threats to input privacy and correctness that arise during the transfer of masked inputs and corresponding super-keys. To begin with, the input and seed distributions are robust. Each input-share/seed is owned by a committee of 3 parties (as dictated by RSS/seed-distribution). To ensure consistent distribution, we force the dealer (of input-share/seed) to commit to the data publicly and open privately rather than relying on private communication alone. Parties who receive the same RSS share/seed cross-check with each other to agree either on a publicly committed value or a default value when no correct openings are dealt. The shares distributed as per RSS in input distribution are now deemed as parties’ new inputs and the circuit is augmented with XOR gates at input level which take these shares as inputs. The formal protocols for input and seed distribution appear in Fig. 12 and 13 respectively. The techniques to identify a pair of conflicting parties (in order to eliminate a corrupt party) differ based on the communication being either public or private. Public data sent by a party involves the transfer of: (a) GC partition wrt each seed owned by the party, (b) shares of output wire masks wrt each seed owned by the party, (c) shares of input wire masks wrt the seeds not owned by the wire owner, (d) masked input values for the input-shares not owned by the evaluator. Each of these values can be broadcasted by the 3 parties owning the respective seed (for cases (a)-(c)) or input-share (for case (d)). Any mismatch in the 3 broadcasted copies leads to  \n\n![](/tmp/output/51_20250326144796/images/afee7c0a87dd07cada6258c6149d1b8486609da4f4567d7c5364ccf57362ddb2.jpg)  \n\n$$\n\\mathbf{Figure12:ProtocolinputGOD}_{i}\n$$  \n\nNotation: $S_{1}=\\{1,3,4\\}$ , $S_{2}=\\{2,3,4\\}$ , $S_{3}=\\left\\{1,2,3\\right\\}$ , $S_{4}=\\{1,2,4\\}$ . Output: Each party Pj , j ∈ Sд outputs sд .   \nR1: $P_{g}$ chooses random seed $s_{g}\\in_{R}\\{0,1\\}^{\\kappa}$ , samples ${\\mathsf{p p}}^{g}$ and computes $(\\mathrm{c}_{g},\\mathrm{o}_{g})\\gets\\mathrm{Com}(\\mathrm{pp}^{g},\\mathsf{s}_{g}).P_{g}$ broadcasts $(\\mathsf{p p}^{g},\\mathsf{c}_{g})$ and sends $_{o_{g}}$ privately to each $P_{j},j\\in S_{g}$ .   \nR2: If no $_{o_{g}}$ received or $\\mathrm{Open}({\\mathrm{pp}}^{g},\\mathrm{c}_{g},\\mathrm{o}_{g})=\\perp,P_{j}$ sets $\\O_{g}=\\bot,P_{j}$ forwards $_{o_{g}}$ to $P_{k}$ , $k\\in S_{g}$ .   \n(Local Computation by $P_{j}$ :) Accept $_{o_{g}}$ sent by $P_{k}$ , if $\\left|\\mathrm{Open}({\\mathsf{p p}}^{g},\\mathrm{c}_{g},\\mathrm{o}_{g})\\right.\\neq\\mathrm{\\Phi}\\perp$ and the $_{o_{g}}$ received earlier from $P_{g}$ was set to $\\bot$ . If the opening still remains $\\bot$ , agree on default seed $s_{g}$ .  \n\n$$\n\\mathbf{Figure13:ProtocolseedGOD}_{g}\n$$  \n\nelection of a 3-party committee $\\mathcal{P}^{3}$ that becomes the custodian for completing computation. The primary reason for adopting broadcast in the above cases is to aid in unanimous agreement about the conflicting parties. Else, if we rely on private communication alone, an honest receiver may always receive mismatching copies and fail to convince all honest parties about the wrongdoing. Further, input privacy is preserved when masked input is broadcast in case (d) for the shares not owned by evaluator (instead owned by 3 garblers), since the adversary (corrupting the evaluator and one garbler) lacks knowledge of one seed needed to learn the underlying input-share.  \n\nPrivate communication includes the transfer of super-key for input wires wrt masked input shares to $P_{5}$ . The natural solution is to have the garblers, owning the respective input share, send keys privately to $P_{5}$ corresponding to the seeds they own. The private transfer alone, however, allows corrupt parties to send incorrect keys which goes undetected by $P_{5}$ . We resolve this using the standard trick of commit-then-open. All garblers publicly commit to both keys on each input wire for the seeds they possess, where any conflict is dealt as in the public message. The commitments wrt each seed are generated by the three seed owners using randomness derived from the same seed, turning public verification to plain equality checking. When no public conflict arises, only the garblers holding the actual input share send the relevant openings to $P_{5}$  \n\nSince each input-share is owned by at least two garblers (the other may be the evaluator), they together hold all parts of the correct super-key to be opened, hence all openings can be communicated. However, this step may not be robust in case of a corrupt garbler sending incorrect (or no) opening privately which can be realised only by $P_{5}$ . In such case, $P_{5}$ raises a conflict against the garbler who sent a faulty opening and a 3-party set is identified for 3PC which excludes $P_{5}$ and the conflicting garbler.  \n\nFurther, input consistency is threatened when the adversary gets the output in the 5PC, yet makes the honest parties receive output via 3PC which now needs to adhere to the inputs committed in the outer 5PC protocol. This occurs when a corrupt $P_{5}$ computes the output, yet does not disclose to the garblers and the related 3PC instance invoked must ensure input consistency to bar the adversary from learning multiple evaluations of $f$ . This creates a subtle issue when in the elected 3PC, only one party say $P_{\\alpha}$ holds a share $x^{i j}$ (the other two owners of $x^{i j}$ are eliminated). A potentially corrupt $P_{\\alpha}$ can use a different $x^{i j}$ causing the 3PC to compute on a different input $x_{i}$ of $P_{i}$ than what was used in the 5PC, thus obtaining multiple evaluations of $f$ . Custom-made to the robust 3PC of [24], we tackle this having the RSS dealer $P_{i}$ distribute $\\boldsymbol{x}^{i j}+\\mathsf{r}^{i j}$ and $\\mathrm{\\Delta}_{\\mathrm{r}}i j$ instead of just $x^{i j}$ for each share in the inputdistribution phase. When a 3PC is invoked, the 3-parties who hold opening of $\\bar{x^{i j}}+\\mathsf{r}^{i j}$ and $\\mathrm{\\Delta}_{\\mathrm{r}}i j$ hand them over respectively to the two parties in the 3PC who do not hold $x^{i j}$ . With such a modification, now each input share in the elected 3PC is either held by at least two parties or by one party in which case it is XOR-shared between the remaining two. This is in line with the 3PC of [24] that offers consistency for inputs, either held by at least two parties or by one party in which case it is XOR-shared between the remaining two. In the 3PC of [24], two parties, say $P_{\\alpha},P_{\\beta}$ act as garblers and the third party, say $P_{\\gamma}$ acts as an evaluator. The garblers use common randomness to construct the same Yao’s GC [12] individually. Since at most one party can be corrupt, a comparison of GCs received from the garblers allows $P_{\\gamma}$ to conclude its correctness. For key transfer, the garblers perform commitments on all keys for the input wires in a permuted order and send openings for the shares they own to $P_{\\gamma}$ . This suffices since, for an input share not held by $P_{\\gamma}$ , it is available with both garblers and thus, $P_{\\gamma}$ can verify if both the openings received for such a share are same. The use of permutation here further ensures that $P_{\\gamma}$ does not learn the actual value of the input key that she has the opening for. However, for input shares held by $P_{\\gamma}$ , no permutation is used to allow $P_{\\gamma}$ to verify if the correct opening has been received.  \n\nIn 5PC, it is easy to check that the evaluator colluding with a garbler can’t cheat with a wrong super-key for the output, as no single garbler possesses all seeds. The AOT protocol, used in Garble, is aptly modified to tackle conflicts and elect a 3PC instance (Fig. 7). Our 3PC appears in Fig. 14. The main protocol appears in Fig. 15.  \n\nOptimizations. To improve efficiency, the garbling process is optimized similar to fair. When a conflict is identified prior to sending of GC, election of 3PC instance and its execution are set in motion immediately, thus enabling the protocol to terminate faster. To minimize the overhead of broadcast and make it independent of input, output and circuit size, we replace each broadcast message $m$ with the collision-resistant hash of the message, $\\mathsf{H}(m)$ , while sending $m$  \n\nInputs: Party $P_{k}$ has $(\\mathsf{c}_{i j},\\mathsf{c}_{i j}^{\\prime})$ for $i\\in[5],j\\in$ [6] and $(\\mathbf{o}_{i l},\\mathbf{o}_{i l}^{\\prime})$ for   \n$i\\in$ [5], $l\\in$ [6], $k\\notin\\mathcal{T}_{l}$ .   \nCommon Inputs: The circuit $\\begin{array}{r}{C(\\oplus_{j\\in[6]}x^{1j},\\oplus_{j\\in[6]}x^{2j},\\oplus_{j\\in[6]}x^{3j},}\\end{array}$   \n$\\oplus_{j\\in[6]}x^{4j},\\oplus_{j\\in[6]}x^{5j})$ that computes $f(x_{1},x_{2},x_{3},x_{4},x_{5})$ , each input,   \ntheir shares and output are from $\\{0,1\\}$ .   \nNotation: $\\mathcal{P}^{3}=\\{P_{\\alpha},P_{\\beta},P_{\\gamma}\\}$ is the chosen 3PC Committee.   \nOutput: $y=C(x_{1},x_{2},x_{3},x_{4},x_{5})$ .   \nInput Setup for 3PC: For each $x^{i j}$ , if just one party, say $P_{\\alpha}\\in\\mathcal{P}^{3}\\cap\\mathcal{X}_{i j}$ ,   \nthe following is done: every party in $\\chi_{i j}$ sends $_{0i j}$ for ${\\boldsymbol{x}}^{i j}\\oplus{\\mathsf{r}}_{i j}$ and $0_{i j}^{\\prime}$   \nfor $\\mathrm{~r~}_{i j}$ to $P_{\\beta}$ and $P_{\\gamma}$ respectively, each of which in turn recovers the   \nrespective share using one valid opening.   \n3PC Run: Run a robust 3PC [24] secure against one active corruption   \nwith $\\{P_{\\alpha},P_{\\beta}\\}$ as garblers and $P_{\\gamma}$ as the evaluator. – The input of each party is $x^{i j}/x^{i j}\\oplus{\\sf r}^{i j}/{\\sf r}^{i j}.P_{\\gamma}$ does not XOR-share its input as in the protocol of [24]. – Inside the 3PC, for inputs not known to $P_{\\gamma}$ , the garblers send commitments on both keys in random permuted order with randomness drawn from the common randomness of garblers. For other inputs, the commitments are sent without permutation. For $x^{i j}$ , not known to $P_{\\gamma}$ and held by both $P_{\\alpha},P_{\\beta}$ and on receiving the opening for keys $P_{\\gamma}$ , checks if the opened keys are same from both garblers. For $x^{i j}$ known to $P_{\\gamma}$ , it checks if they correspond to bit $x^{i j}$ by checking whether $x^{i j}$ th commitment was opened or not. The case when all 3 parties hold $x^{i j}$ is subsumed in the above case.   \n– For $x^{i j}$ held by $P_{\\gamma}$ while $\\boldsymbol{x}^{i j}\\oplus\\mathsf{r}^{i j}$ and $\\mathrm{~\\boldmath~r~}^{i j}$ held by $P_{\\alpha}$ and $P_{\\beta}$ respectively, $P_{\\gamma}$ (who knows $\\boldsymbol{x}^{i j}\\oplus\\boldsymbol{r}^{i j}$ and $\\mathrm{\\Delta}_{\\mathrm{r}}i j$ too) checks if the openings obtained from $P_{\\alpha}$ and $P_{\\beta}$ indeed correspond to $\\boldsymbol{x}^{i j}\\oplus\\mathsf{r}^{i j}$ and $\\mathrm{\\Delta}_{\\mathrm{r}}i j$ respectively. If so, he XORs the keys to obtain the key for $x^{i j}$ . – For $x^{i j}$ held by $P_{\\alpha}$ , while $x^{i j}\\oplus r^{i j}$ held by $P_{\\beta}$ and $\\mathrm{\\Delta}_{\\mathrm{r}}i j$ held by $P_{\\gamma}$ , $P_{\\alpha}$ sends key-openings wrt $x^{i j}+r^{i j},\\mathsf{r}^{i j}$ and $P_{\\beta}$ sends key-opening wrt $\\boldsymbol{x}^{i j}\\oplus\\mathrm{\\sfr}^{i j}$ . $P_{\\gamma}$ checks if the opening wrt $\\mathrm{~\\boldmath~r~}^{i j}$ is correct and if the opened keys wrt $\\boldsymbol{x}^{i j}\\oplus\\mathrm{\\sfr}^{i j}$ (sent by $P_{\\alpha},P_{\\beta})$ are the same. If so, the keys of $r^{i j}$ XORed with $\\boldsymbol{x}^{i j}\\oplus\\mathrm{\\sfr}^{i j}$ top obtain key wrt $x^{i j}$ . Compute similarly if $\\boldsymbol{x}^{i j}\\oplus\\mathsf{r}^{i j}$ is held by $P_{\\gamma}$ .   \nRest of 3PC is run with keys for all RSS shares $x^{i j}$ and the output of 3PC   \nis sent to each $P_{i}\\in\\mathcal{P}$ who outputs the majority of three $y$ ’s received.  \n\n# Figure 14: Protocol god3PC  \n\nprivately to the recipient. For instance, in DGC, ${\\sf H}(G C^{i}),i\\in[4]$ is broadcasted by parties who own $G C^{i}$ whereas, $G C^{i}$ is sent to $P_{5}$ by one of the parties in $S_{i}$ privately. Similarly, for sending output super-key, ${\\mathsf{H}}(\\mathbf{Y})$ is broadcasted by $P_{5}$ and $\\mathbf{Y}$ is sent via pairwise channels and so on. With this optimization in broadcast, we elaborate how any conflict is resolved with the following examples (all our broadcast messages fall under one of these examples): (1) Consider $m$ as the GC fragment $G C^{1}$ , held by $P_{1},P_{3},P_{4}$ due to SD. Each of $P_{1},P_{3},P_{4}$ broadcasts $\\mathsf{H}(G C^{1})$ . If the hashes mismatch for two parties say $P_{1},P_{3}$ , then god3PC is run with $P_{2},P_{4},P_{5}$ . Else, if all the broadcast hashes are in agreement, then $P_{1}$ will send $G C^{1}$ privately to $P_{5}$ . Now if $P_{5}$ is honest and finds that the received $G C^{1}$ is not consistent with the hash that was successfully broadcasted and agreed, then $P_{5}$ broadcasts a conflict with $P_{1}$ and a 3PC instance with $P_{2},P_{3},P_{4}$ is run. Else if $P_{5}$ is corrupt and raises a false conflict with $P_{1}$ , even then god3PC with $P_{2},P_{3},P_{4}$ is run. In both cases, one corrupt party is surely eliminated and god3PC contains at most one corruption. (2) Let $m$ be the mask share $\\lambda_{w}^{1}$ on output wire $\\boldsymbol{w}$ that is held by $P_{1},P_{3},P_{4}$ due to SD. Each of $P_{1},P_{3},P_{4}$ broadcasts $\\mathsf{H}(\\lambda_{w}^{1})$ . If the hashes of say $P_{1},P_{3}$ mismatch, then god3PC is formed amongst the remaining parties, $P_{2},P_{4},P_{5}$ . Else, if all hashes are in agreement, then $P_{1},P_{3},P_{4}$ privately send $\\lambda_{w}^{1}$ to each party. We consider the receiver $P_{2}$ for explanation. This step is robust since, if the hashes are in agreement, there will always exist one valid pre-image among the private messages received by $P_{2}$ . This is due to the presence of one honest sender. Hence $P_{2}$ uses this valid value for computation.  \n\n# 6.1 Properties  \n\nLemma 6.1. An elected 3PC has at most one corruption.  \n\nProof. We argue that a corrupt party is eliminated in a conflict. Suppose $P_{i},P_{j}$ are in conflict. This could be due to either (i) mismatch in the public message broadcast by $P_{i},P_{j}$ or (ii) one of $P_{i},P_{j}$ raised a conflict against the other for an incorrect private message. In case (i), each message is result of either robust input or seed distribution and hence if both were honest, the broadcast messages would be identical. In case (ii), each message involves an opening for the commitments agreed on in public message and neither $P_{i}$ nor $P_{j}$ would raise a conflict if valid opening was received. Also, in both the above cases, each message is checked for correctness before proceeding further and thus the conflict could not have been the result of adversary’s doing in the previous steps. This implies that at least one of $P_{i},P_{j}$ is corrupt. Thus, an elected 3PC in either case would contain parties $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{i},P_{j}\\}$ . Since one of $P_{i},P_{j}$ is surely corrupt, at most one corrupt party can be present in $\\mathcal{P}^{3}$ . □  \n\nLemma 6.2. The output $y$ computed in the god3PC instance corresponds to the committed inputs.  \n\nProof. In case of conflict in god, a 3PC instance with at most one corruption is formed (Lemma 6.1). To ensure the input consistency in the 3PC, every agreed upon RSS share xij in inputGOD, is made available in 3PC to at least two parties or when held by one party, it is XOR shared between the remaining two. With this arrangement of input shares, the robust 3PC of [24] is guaranteed to preserve input consistency. This ensures that computation in 3PC is performed on the inputs committed in inputGOD. □  \n\n# Theorem 6.3. The protocol god is correct.  \n\nProof. We argue that the output $y$ computed corresponds to the unique inputs committed by each $P_{i},i\\in$ [5] in input $\\mathrm{GOD}_{i}$ A corrupt party either commits to an input or a default value is assumed as per inputGOD. The honest parties are established to have committed to their inputs by the end of round 1 in inputGOD. An honest $P_{\\alpha}$ obtains the output either by decoding the output super-key Y or via the output of god3PC (as a participant in god3PC or recipient from the 3PC committee). In the latter case, correctness follows from Lemma 6.2 and correctness of god3PC. We argue for the former case. Let an honest $P_{\\alpha}$ obtains output from Y broadcast by $P_{5}$ . This implies that the adversary behaved honestly in the entire execution and input keys opened by a corrupt garbler correspond to committed inputs only. Otherwise, a conflict would be raised to elect a 3PC, which contradicts our assumption that the output was obtained on decoding Y. Thus, the output always corresponds to the committed inputs in inputGOD. The correctness of evaluation follows from the correctness of the garbling scheme. □  \n\nInputs and Output: Party $P_{i}\\in\\mathcal{P}$ has $x_{i}$ . Each party outputs $y=C(x_{1},x_{2},x_{3},x_{4},x_{5})$ .  \n\nCommon Inputs: The circuit $C(\\oplus_{j\\in[6]}x^{1j}$ , $\\oplus_{j\\in[6]}x^{2j}$ , $\\oplus_{j\\in[6]}x^{3j}$ , $\\oplus_{j\\in[6]}x^{4j}$ , $\\oplus_{j\\in[6]}x^{5j})$ that takes the RSS shares as inputs and computes $f(x_{1},x_{2},x_{3}$ , $\\mid x_{4},x_{5})$ , each input, their shares are from $\\{0,1\\}$ (instead of $\\{0,1\\}^{\\ell}$ for simplicity) and output is from $\\{0,1\\}^{\\ell}$ .  \n\nNotation: $S_{i}$ denotes the indices of the parties who hold $s_{i}$ as well as the indices of the seeds held by $P_{i}$ . $\\chi_{i j}$ denotes the set of parties that holds the $j^{\\mathrm{th}}$ share of $P_{i}$ ’s input $x^{i j}$ . $\\mathcal{P}^{3}$ is the identified 3PC committee.  \n\nPrimitives: A NICOM (Com, Open), inpu $\\mathrm{GOD}_{i}$ (Fig. 12), $\\mathsf{s e e d G O D}_{g}$ (Fig. 13), Garble (Fig. 4), Eval (Fig. 5) and Π4AOTGOD (Fig. 7).   \nInput and Seed Distribution Phase. Run inpu $\\mathrm{tGOD}_{i}$ and seed $\\mathsf{j}\\mathrm{OD}_{g}$ for every $P_{i}\\in\\mathcal{P}$ and $P_{g}$ , $g\\in[4]$ respectively in parallel.  \n\nGarbling Phase. Garble $(C)$ is run where ΠAOTGOD (Fig 7) is used instead of $\\mathcal{F}_{4\\mathsf{A O T}}$ . Each $P_{g}$ , $g\\in[4]$ broadcasts $\\{G C^{j}\\}_{j\\in S_{g}}$ . Each party runs god3PC with $\\mathcal{P}^{3}$ when any instance of $\\Pi_{4\\mathrm{AOTGOD}}$ returns $\\mathcal{P}^{3}$ or with $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{\\alpha},P_{\\beta}\\}$ when $(P_{\\alpha},P_{\\beta})$ with $\\alpha,\\beta\\in S_{g}$ for some $g\\in[4]$ broadcasts different $G C^{g}$ (in the optimized version, we broadcast only a hash of GC).  \n\n# Masked input bit and Key Transfer Phase.  \n\n# In parallel to the R1 of Garbling phase,  \n\n◦ For each output wire w $,P_{g},g\\in[4]$ broadcasts $\\lambda_{w}^{j},j\\in S_{g}$ . Every party runs god3PC with $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{\\alpha},P_{\\beta}\\}$ , if parties $P_{\\alpha},P_{\\beta}$ holding seed $s_{g}$ i.e. $\\{\\alpha,\\beta\\}\\in S_{g}$ broadcast different copies of $\\lambda_{w}^{g}$ for some output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ and $g$ . (Tie break deterministically if multiple pairs are in conflict.) Otherwise, every party reconstructs $\\lambda_{w}=\\oplus_{g\\in[4]}\\lambda_{w}^{g}$ for every output wire $\\boldsymbol{w}$ .   \n◦ For every input wire $\\boldsymbol{\\mathbf{\\mathit{w}}}$ corresponding to input $x_{w}=x^{i j}$ held by three garblers, for each $P_{g}\\in\\mathcal{X}_{i j}$ : each garbler $P_{h}$ , $h\\neq g$ , broadcasts $\\lambda_{w}^{l},l\\in$ $S_{h}\\setminus S_{g}$ . (If $\\chi_{i j}$ includes evaluator, then each garbler $P_{h}$ , $h\\in[4]$ broadcasts $\\lambda_{w}^{l},l\\in S_{h})$ . Every party runs god3PC with $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{\\alpha},P_{\\beta}\\}$ , if there are parties $P_{\\alpha},P_{\\beta}$ with $\\{\\alpha,\\beta\\}\\in S_{l}$ broadcasting different copies $\\lambda_{w}^{l}$ for some wire $\\boldsymbol{\\mathbf{\\nabla}}w$ . Otherwise, $P_{g}$ , the owner of the input wire $\\boldsymbol{\\mathbf{\\nabla}}w$ uses $\\lambda_{w}^{l}$ to compute $\\lambda_{w}=\\oplus_{l\\in[4]}\\lambda_{w}^{l}$ .   \nIn parallel to R2 of Garbling phase, for circuit input wire $\\boldsymbol{\\mathbf{\\nabla}}w$ corresponding to input $x_{w}=x^{i j}$ held by three garblers, each $P_{\\alpha}\\in\\mathcal{X}_{i j}$ computes   \n$b_{w}=x_{w}\\oplus\\lambda_{w}$ and broadcasts $b_{w}$ . Every party runs god3PC with $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{\\alpha},P_{\\beta}\\}$ , if there are parties $P_{\\alpha},P_{\\beta}$ with $\\{\\alpha,\\beta\\}\\in X_{i j}$ broadcasting   \ndifferent copies of $b_{w}$ . Otherwise, $P_{5}$ uses $b_{w}(=x_{w}\\oplus\\lambda_{w})$ for evaluation. For circuit input wire $\\boldsymbol{w}$ corresponding to input $x_{w}=x^{i j}$ held by two   \ngarblers and $P_{5}$ , $P_{5}$ already knows $b_{w}$ as $\\lambda_{w}$ was computed by $P_{5}$ in the previous step.   \nFor every input wire $\\boldsymbol{\\mathbf{\\nabla}}w$ , let $\\{k_{w,0}^{g},k_{w,1}^{g}\\}_{g\\in[4]}$ denote the super-key derived from seeds $\\{s_{g}\\}_{g\\in[4]}$ . Each $P_{g},g\\in[4]$ computes commitments as: for   \nb ∈ {0, 1}, j ∈ Sд , (c jw ,b , o jw ,b ) $(c_{w,b}^{j},\\sigma_{w,b}^{j})\\gets\\mathrm{Com}(\\mathrm{pp}^{j},k_{w,b}^{j})$ and broadcasts $\\{\\mathsf{p p}^{j},c_{w,b}^{j}\\}.P_{g}$ sends the opening $\\sigma_{w,b_{w}}^{j}$ to $P_{5}$ if it also holds $b_{w}$ . Every   \nparty runs god3PC with $\\mathcal{P}^{3}$ with $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{\\alpha},P_{\\beta}\\}$ if $(P_{\\alpha},P_{\\beta})$ with $\\alpha$ , $\\beta\\in S_{i}$ for some i and input wire $\\boldsymbol{w}$ broadcast different commitments.   \nOtherwise, $P_{5}$ tries to recover the super-key for $b_{w}$ , namely, $\\{k_{w,b_{w}}^{g}\\}_{g\\in[4]}$ using the openings received. If no valid openings received for some key, broadcasts a conflict with a garbler who sent invalid opening and subsequently every party runs god3PC with the remaining three parties as   \n$\\mathcal{P}^{3}$ . Otherwise, let $\\mathbf{X}$ to be the set of super-keys obtained.  \n\n# Evaluation and Output Phase.  \n\n$-\\quad P_{5}$ runs Eval to evaluate $\\mathrm{^c}$ using $\\mathbf{X}$ and obtains $\\mathbf{Y}$ and $(y_{w}\\oplus\\lambda_{w})$ for all output wires $\\boldsymbol{\\mathbf{\\mathit{w}}}$ . For each output wire $\\boldsymbol{\\mathbf{\\nabla}}w$ , $P_{5}$ computes $y_{w}=(y_{w}\\oplus$ λw $)\\oplus_{g\\in[4]}\\lambda_{w}^{g}$ and thus $y$ . Finally, $P_{5}$ outputs $y.P_{5}$ broadcasts Y. $P_{g}$ the t hgroede 3kPeCys $\\mathcal{P}^{3}$ $\\mathcal{P}^{3}=\\mathcal{P}\\backslash\\{P_{1},P_{5}\\}$ $k_{w,b_{w}}^{j}$ . Otherwдise, each ga $\\boldsymbol{w}$ obt $j\\in S_{g}$ by comparing each $(k_{w,0}^{j},k_{w,1}^{j})$ $k_{w,b_{w}}^{j},j\\in S_{g}$ $\\mathbf{Y}$ $b_{w}$ $P_{g}$ $(y_{w}\\oplus\\lambda_{w})$ key in $\\mathbf{Y}$ with the two key labels for each $\\boldsymbol{w}$ and computes $y_{w}=(y_{w}\\oplus\\lambda_{w})\\oplus_{g\\in[4]}\\lambda_{w}^{g}$ . Finally, $P_{g}$ outputs .  \n\n# Figure 15: Protocol god  \n\nTheorem 6.4. Assuming one-way permutations, protocol god securely realizes the functionality $\\mathcal{F}_{\\mathrm{god}}$ (Fig. 18) in the standard model against an active adversary that corrupts at most two parties.  \n\nThe proof of the theorem appears in the full version [23]. Since the inputs are defined prior to Garble in all our protocols, we do not require the adaptive notion of the proof.  \n\n# 7 EMPIRICAL RESULTS  \n\nIn this section, we elaborate the empirical results of our protocols. We use the circuits of AES-128 and SHA-256 as benchmarks. We begin with the details of the setup environment, both hardware and software and then give a detailed comparison of efficiency.  \n\nHardware Details We provide experimental results both in LAN and WAN (high latency) settings. For the purpose of LAN, our system specifications include a 32GB RAM; an Intel Core $i7-7700\\mathrm{-}4690$ octa-core CPU with $3.6\\mathrm{GHz}$ processing speed with AES-NI support from the hardware. For WAN, we have employed Microsoft Azure D4s_v3 cloud machines with instances located in West US, South India, East Australia, South UK and East Japan. The average bandwidth measured using the iperf testing tool corresponds to $169M b p s$ . The slowest link has a round trip time of 277 ms between East Australia and South UK. RTT denotes the time required to send a packet from source to destination and subsequently an acknowledgment back from destination to source. But the transfer of a packet involves only one way communication from source to destination. So the delay we consider is half of RTT which is 138.5 ms for our slowest link (between garblers $P_{3},P_{4})$ . The maximum delays for each garbler for one way communication are: $P_{1}$ : 102 ms, $P_{2}$ 101 ms, $P_{3}$ : 138 ms, $P_{4}$ : 138.5 ms. For the evaluator, the maximum delay is close to 112 ms. The tables indicate the average delay for the role of garbler which turns out to be between $114-120$ ms.  \n\nSoftware Details. For efficiency, the technique of free-XOR is enabled and the implementation is carried out using libgarble library licensed under GNU GPL license. This library leverages the use of AES-NI instructions provided by the underlying hardware.We additionally use openSSL $1.02\\mathrm{g}$ library for SHA to instantiate our commitments. The operating system used is Ubuntu 16.04 (64-bit). Our code follows the standards of $\\mathrm{C}{+}{+}11$ and multi-threading is enabled on all cores for improved results. Communication is done using sockets whose maximum size is set to $1~\\mathsf{M B}$ and a connection is established between every pair of parties to emulate a network consisting of pair-wise private channels.  \n\nTable 1: The total computation time (Total CT), maximum latency in LAN run-time (LAN) and WAN run-time (WAN) and total communication (Total CC) of all parties for [25] and our protocols using Garble. The figures in brackets indicate the increase for the worst case run of god.   \n\n\n<html><body><table><tr><td></td><td colspan=\"4\">Total CT( ms)</td><td colspan=\"4\"></td><td colspan=\"4\">WAN( s)</td><td colspan=\"4\">Total CC(MB)</td></tr><tr><td>Circuit</td><td>[25]</td><td>ua</td><td>fair</td><td>god</td><td>[25]</td><td>ua</td><td>fair</td><td>god</td><td>[25]</td><td>ua</td><td>fair god</td><td></td><td>[25] ua</td><td>fair</td><td>god</td><td></td></tr><tr><td>AES-128</td><td>96.81</td><td>100.53</td><td>100.9</td><td>101.06 (+ 3.15)</td><td>25.01</td><td>25.66</td><td>26.06</td><td>28.95 (+ 2.39)</td><td>2.54</td><td>2.74</td><td>2.82</td><td>3.7 (+ 1.1)</td><td>29.55</td><td>29.71</td><td>29.75</td><td>29.72 (+ 0.32)</td></tr><tr><td>SHA-256</td><td>1078.99</td><td>1080.31</td><td>1086.18</td><td>1090.47(+ 33.02)</td><td>290.38</td><td>293.25</td><td>301.33</td><td>295.3 (+ 14.5)</td><td>4.78</td><td>4.79</td><td>4.81</td><td>5.6 (+ 1.51)</td><td>389.12</td><td>389.2</td><td>389.24</td><td>389.19 (+ 6.15)</td></tr></table></body></html>  \n\nTable 2: Computation time (CT), LAN run-time (LAN), WAN runtime (WAN) and Communication (CC) for [25], ua and fair for $g\\in[4]$ .   \n\n\n<html><body><table><tr><td rowspan=\"2\"></td><td rowspan=\"2\">Protocol</td><td colspan=\"2\">CT(ms)</td><td colspan=\"2\">LAN( ms)</td><td colspan=\"2\">WAN(s)</td><td colspan=\"2\">CC(MB)</td></tr><tr><td>Pg</td><td>P5</td><td>P g</td><td>P5</td><td></td><td>P5</td><td></td><td>P5</td></tr><tr><td rowspan=\"4\">128 AES-1</td><td>[25](withGarble)</td><td>20.84</td><td>13.45</td><td>25.01</td><td>21.45</td><td>2.54</td><td>0.99</td><td>7.38</td><td>0.031</td></tr><tr><td>[25](with[13])</td><td>24.4</td><td>14.17</td><td>28.56</td><td>22.17</td><td>2.58</td><td>1.0</td><td>7.38</td><td>0.03</td></tr><tr><td>ua fair</td><td>21.72</td><td>13.65</td><td>25.66</td><td>21.85</td><td>2.74</td><td>0.99</td><td>7.42</td><td>0.039</td></tr><tr><td></td><td>21.79</td><td>13.74</td><td>26.06</td><td>22.3</td><td>2.82</td><td>1.10</td><td>7.43</td><td>0.039</td></tr><tr><td rowspan=\"4\">256 SHA-25</td><td>[25](with Garble)</td><td>247.69</td><td>88.23</td><td>290.38</td><td>236.53</td><td>3.44</td><td>4.78</td><td>97.26</td><td>0.062</td></tr><tr><td>[25](with[13])</td><td>259.99</td><td>103.54</td><td>302.6</td><td>254.21</td><td>3.58</td><td>4.8</td><td>97.26</td><td>0.06</td></tr><tr><td></td><td>247.89</td><td>88.75</td><td>293.25</td><td>241.51</td><td>3.69</td><td>4.79</td><td>97.28</td><td>0.078</td></tr><tr><td>fair</td><td>249.35</td><td>88.78</td><td>301.33</td><td>242.66</td><td>3.78</td><td>4.81</td><td>97.29</td><td>0.078</td></tr></table></body></html>  \n\nTable 3: Computation time (CT), LAN run-time (LAN) and Communication (CC) and Broadcast (BC) for protocol god for $g\\in[4].P_{g^{\\prime}}$ is the garbler and $P_{\\gamma}$ is the evaluator for worst case 3PC run.   \n\n\n<html><body><table><tr><td>Circuit</td><td colspan=\"2\">CT( ms)</td><td colspan=\"2\">LAN( ms)</td><td colspan=\"2\">WAN( s)</td><td colspan=\"2\">CC(MB)</td><td colspan=\"2\">BC( KB)</td></tr><tr><td></td><td>Pg(Pg) 21.93</td><td>Ps(P) 13.34</td><td>Pg(Pg)</td><td>Ps(Py)</td><td>Pg(Pg)</td><td>Ps(P)</td><td>Pg(Pg)</td><td>Ps(P)</td><td>Pg(Pg)</td><td>P5(Py)</td></tr><tr><td>AES-128</td><td>(+1.12)</td><td>(+0.91)</td><td>28.95 (+2.39)</td><td>24.19 (+2.1)</td><td>3.70 (+1.02)</td><td>1.76 (+1.1)</td><td>7.41 (+0.15)</td><td>0.032 （+0.002）</td><td>10.416 (+4.03)</td><td>10.064 (+4.06)</td></tr><tr><td>SHA-256</td><td>249.91 (+11.63)</td><td>90.83 (+9.76)</td><td>295.3 (+14.5)</td><td>241.83 (+11.9)</td><td>4.5 (+1.42)</td><td>5.6 (+1.51)</td><td>97.27 (+3.074)</td><td>0.064 (+0.004)</td><td>10.416 (+4.03)</td><td>10.064 (+4.06)</td></tr></table></body></html>  \n\nWe compare our results in the high-latency network with the relevant ones. The state of the art 3PC [24, 55] and 4PC [24] with honest majority achieving various notions of security, incur significantly less overhead compared to our setting since they tolerate one corruption which aids in usage of inexpensive Yao’s garbled circuits [12] and fewer rounds. Thus, the closest result to our setting is [25] and below we make a detailed comparison with it. For fair analysis, we instantiate the protocol of [25] in our environment and use the semi-honest 4DG scheme (Section 3) in place of [13] that they rely on. However, we also instantiate [25] with the 4DG scheme of [13] to emphasize the saving in computation time that occurs with the use of Garble in place of [13]. We highlight the following parameters for analysis: computation time (CT)– the time spent computing across all cores, runtime ( $\\mathrm{CT+}$ network time) in terms of LAN, WAN and communication (CC). The network time emphasizes the influence of rounds and data size taking into account the proximity of servers. The tables highlight average values distinctly for the role of a garbler $(P_{g},g\\in[4])$ and the evaluator $(P_{5})$ . The results for [25], ua, fair appear in Table 2. Table 3 depicts the results for god. While achieving stronger security than in [25], ua and fair incur an overhead of at most $0.2\\mathsf{M B}$ overall for both circuits over [25]. The overhead in both protocols is a result of the proof of origin of output super-key Y and exchange of Y among garblers. Additionally, in fair, the commit-then-open trick on output mask bits constitutes extra communication. For the necessary robust broadcast channel in god, we use Dolev Strong [41] to implement authenticated broadcast and fast elliptic-curve based schemes [18] to realize public-key signatures therein. These signatures have a one-time setup to establish public-key, private-key for each party. We do the same for robust 3PC of [24] for empirical purposes.  \n\nWhen instantiated with DS broadcast, the round complexity for honest run of GOD is 12 (in the presence of 4 broadcasts) and the shown WAN overhead in Table 3 over [25] captures this inflation in rounds. For the sake of implementation of all protocols (including [25] for fair comparison), we have adopted parallelization wherever possible. Next, if we observe god, Table 3 indicates that the pairwise communication (CC) of god protocol is almost on par with that of [25] in Table 2 (and less than fair). This is because, the honest run of our god is almost same as [25] except for the input commit routine and the use of broadcast. The input commit routine can be parallelized with the process of garbling to minimize number of interactions. This implies that the majority overhead is mainly due to the use of broadcast. The implementation of DS broadcast protocol is done by first setting up public-key, private key pair for each party involved. Each message sent by the broadcast sender is then agreed upon by the parties by running 3 $\\left(t{+}1\\right)$ rounds. If multiple independent broadcasts exist in one round, they are run parallelly. Also, any private communication that can be sent along with the broadcast data is also parallelized for improved round complexity. The broadcast communication is kept minimal and independent of the circuit, input and output size. As a result, the total data to be broadcasted constitutes only $1.73~{\\mathsf{K B}}$ of the total communication. In the honest run, when the adversary does not strike, the overall overhead amounts to a value of at most $1.2s$ in WAN over [25]. The worst case run in god occurs when the adversary behaves honestly throughout but only strikes in the final broadcast of Y and a 3PC instance is run from that point. In this case, the overall WAN overhead is at most $2.5s$ over [25]. This overhead is justified considering the strength of security that the protocol offers when compared to [25]. Also, the overheads in LAN and communication are quite reasonable.  \n\nIn fair, the overhead of $0.2\\mathsf{M B}$ higher than the honest run of god is a result of commitments on output wire masks and circulation of Y and proof proof in the output phase. Also, fair protocol involves 3 sequential rounds for output phase compared to single communication of Y by P5 in [25] and in god. Note that in the LAN setting, RTT is of the order of $\\mu s$ for one packet send. Our observations show that, in the LAN setting, RTT sensitively scales with data size whereas in WAN, RTT hardly varies for small increase in communication. For instance, we have noted that, in LAN, the average RTT for 1 KB, 8 KB, 20 KB, 80 KB is $280\\mu s$ , $391\\mu s$ , $832\\mu s$ , $1400\\mu s$ respectively, whereas in WAN, RTT for these data sizes does not vary. This implies that two transfers of 1 KB data consumes less time than a single transfer of $20\\mathsf{K B}$ data in LAN. All the above reasons collectively justify the slight variation in the LAN time. Having said that, we believe that WAN being a better comparison measure in terms of both communication data and round complexity, aptly depicts the overhead of all our protocols over [25].  \n\nTable 1 provides a unified view of the overall maximum latency in terms of each parameter and total communication of all protocols implemented with Garble. The bracketed values indicate the additional overhead involved in the worst case run of god. Note that the overhead for SHA-256 is higher compared to AES-128. This difference maps to the circuit dependent communication involving the inputs and output. Since SHA is a huge circuit compared to AES, the increase is justified. However, the percentage overheads get better for SHA compared to AES. Besides, the factor of additional communication overhead incurred by our protocols for SHA when compared to AES is far less than the factor of increase in the total communication for SHA over AES in [25] thus implying that the performance of our protocols improves with larger circuits. Further, based on our observation and in [25], using AOT instead of OT extension eliminates the expensive public key operations needed even for the seed OTs between every pair of garblers. Further, AOT needs just 1 round whereas OT extension needs more. All these factors lead to the improvement of our Garble over [64] which relies on large number of Tiny OTs [57] to perform authentication.  \n\n# REFERENCES  \n\n[1] Prabhanjan Ananth, Arka Rai Choudhuri, Aarushi Goel, and Abhishek Jain. 2018. Round-Optimal Secure Multiparty Computation with Honest Majority. In CRYPTO. 395–424.   \n[2] Prabhanjan Ananth, Arka Rai Choudhuri, Aarushi Goel, and Abhishek Jain. 2019. Two Round Information-Theoretic MPC with Malicious Security. In EUROCRYPT.   \n[3] Marcin Andrychowicz, Stefan Dziembowski, Daniel Malinowski, and Lukasz Mazurek. 2014. Secure Multiparty Computations on Bitcoin. In IEEE Symposium on Security and Privacy. 443–458.   \n[4] Benny Applebaum, Zvika Brakerski, and Rotem Tsabary. 2019. Degree 2 is Complete for the Round-Complexity of Malicious MPC. 504–531.   \n[5] Toshinori Araki, Assi Barak, Jun Furukawa, Tamar Lichter, Yehuda Lindell, Ariel Nof, Kazuma Ohara, Adi Watzman, and Or Weinstein. 2017. Optimized HonestMajority MPC for Malicious Adversaries - Breaking the 1 Billion-Gate Per Second Barrier. In IEEE Symposium on Security and Privacy. 843–862.   \n[6] Toshinori Araki, Jun Furukawa, Yehuda Lindell, Ariel Nof, and Kazuma Ohara. 2016. High-Throughput Semi-Honest Secure Three-Party Computation with an Honest Majority. In SIGSAC. 805–817.   \n[7] Gilad Asharov, Abhishek Jain, Adriana López-Alt, Eran Tromer, Vinod Vaikuntanathan, and Daniel Wichs. 2012. Multiparty Computation with Low Communication, Computation and Interaction via Threshold FHE. In EUROCRYPT. 483–501.   \n[8] Saikrishna Badrinarayanan, Aayush Jain, Nathan Manohar, and Amit Sahai. 2018. Secure MPC: Laziness Leads to GOD. IACR Cryptology ePrint Archive 2018 (2018), 580.   \n[9] Assi Barak, Martin Hirt, Lior Koskas, and Yehuda Lindell. 2018. An End-to-End System for Large Scale P2P MPC-as-a-Service and Low-Bandwidth MPC for Weak Participants (CCS ’18). 695–712.   \n[10] Donald Beaver, Silvio Micali, and Phillip Rogaway. 1990. The Round Complexity of Secure Protocols (Extended Abstract). In STOC. 503–513.   \n[11] Zuzana Beerliová-Trubíniová and Martin Hirt. 2008. Perfectly-Secure MPC with Linear Communication Complexity. In TCC. 213–230.   \n[12] Mihir Bellare, Viet Tung Hoang, and Phillip Rogaway. 2012. Foundations of garbled circuits. In CCS. 784–796.   \n[13] Aner Ben-Efraim, Yehuda Lindell, and Eran Omri. 2016. Optimizing Semi-Honest Secure Multiparty Computation for the Internet. In CCS. 578–590.   \n[14] Michael Ben-Or, Shafi Goldwasser, and Avi Wigderson. 1988. Completeness Theorems for Non-Cryptographic Fault-Tolerant Distributed Computation (Extended Abstract). In STOC. 1–10.   \n[15] Eli Ben-Sasson, Serge Fehr, and Rafail Ostrovsky. 2012. Near-Linear Unconditionally-Secure Multiparty Computation with a Dishonest Minority. In CRYPTO. 663–680.   \n[16] Rikke Bendlin, Ivan Damgård, Claudio Orlandi, and Sarah Zakarias. 2011. Semihomomorphic Encryption and Multiparty Computation. In EUROCRYPT. 169– 188.   \n[17] Iddo Bentov and Ranjit Kumaresan. 2014. How to Use Bitcoin to Design Fair Protocols. In CRYPTO. 421–439.   \n[18] Daniel J. Bernstein, Niels Duif, Tanja Lange, Peter Schwabe, and Bo-Yin Yang. 2012. High-speed high-security signatures. Journal of Cryptographic Engineering (2012), 77–89.   \n[19] John Black. 2006. The Ideal-Cipher Model, Revisited: An Uninstantiable Blockcipher-Based Hash Function. In Fast Software Encryption, Matthew Robshaw (Ed.). 328–340.   \n[20] Dan Bogdanov, Sven Laur, and Jan Willemson. 2008. Sharemind: A Framework for Fast Privacy-Preserving Computations. In ESORICS. 192–206.   \n[21] Dan Bogdanov, Riivo Talviste, and Jan Willemson. 2012. Deploying Secure MultiParty Computation for Financial Data Analysis - (Short Paper). In FC. 57–64.   \n[22] Peter Bogetoft, Dan Lund Christensen, Ivan Damgård, Martin Geisler, Thomas P. Jakobsen, Mikkel Krøigaard, Janus Dam Nielsen, Jesper Buus Nielsen, Kurt Nielsen, Jakob Pagter, Michael I. Schwartzbach, and Tomas Toft. 2009. Secure Multiparty Computation Goes Live. In FC. 325–343.   \n[23] Megha Byali, Carmit Hazay, Arpita Patra, and Swati Singla. 2019. Fast Actively Secure Five-Party Computation with Security Beyond Abort. Cryptology ePrint Archive, Report 2019/863. https://eprint.iacr.org/2019/863.   \n[24] Megha Byali, Arun Joseph, Arpita Patra, and Divya Ravi. 2018. Fast Secure Computation for Small Population over the Internet (CCS ’18). 677–694.   \n[25] Nishanth Chandran, Juan A. Garay, Payman Mohassel, and Satyanarayana Vusirikala. 2017. Efficient, Constant-Round and Actively Secure MPC: Beyond the Three-Party Case. In CCS. 277–294.   \n[26] H. Chaudhari, A. Choudhury, A. Patra, and A. Suresh. 2019. ASTRA: Highthroughput 3PC over Rings with Application to Secure Prediction. https://eprint. iacr.org/2019/450. In IACR Cryptology ePrint Archive.   \n[27] David Chaum, Ivan Damgård, and Jeroen Graaf. 1987. Multiparty Computations Ensuring Privacy of Each Party’s Input and Correctness of the Result. In CRYPTO. 87–119.   \n[28] Koji Chida, Daniel Genkin, Koki Hamada, Dai Ikarashi, Ryo Kikuchi, Yehuda Lindell, and Ariel Nof. 2018. Fast Large-Scale Honest-Majority MPC for Malicious Adversaries. In CRYPTO. 34–64.   \n[29] Seung Geol Choi, Jonathan Katz, Alex J. Malozemoff, and Vassilis Zikas. 2014. Efficient Three-Party Computation from Cut-and-Choose. In CRYPTO. 513–530.   \n[30] Arka Rai Choudhuri, Matthew Green, Abhishek Jain, Gabriel Kaptchuk, and Ian Miers. 2017. Fairness in an Unfair World: Fair Multiparty Computation from Public Bulletin Boards. In CCS. 719–728.   \n[31] Richard Cleve. 1986. Limits on the Security of Coin Flips when Half the Processors Are Faulty (Extended Abstract). In STOC. 364–369.   \n[32] Ran Cohen and Yehuda Lindell. 2014. Fairness versus Guaranteed Output Delivery in Secure Multiparty Computation. In ASIACRYPT. 466–485.   \n[33] R. Cramer, I. Damgård, and Y. Ishai. 2005. Share Conversion, Pseudorandom Secret-Sharing and Applications to Secure Computation. In TCC. 342–362.   \n[34] Giovanni Di Crescenzo, Yuval Ishai, and Rafail Ostrovsky. 1998. Non-Interactive and Non-Malleable Commitment. In STOC. 141–150.   \n[35] Ivan Damgård and Yuval Ishai. 2005. Constant-Round Multiparty Computation Using a Black-Box Pseudorandom Generator. In CRYPTO. 378–394.   \n[36] Ivan Damgård and Yuval Ishai. 2006. Scalable Secure Multiparty Computation. In CRYPTO. 501–520.   \n[37] Ivan Damgård and Claudio Orlandi. 2010. Multiparty Computation for Dishonest Majority: From Passive to Active Security at Low Cost. In CRYPTO. 558–576.   \n[38] Ivan Damgård, Claudio Orlandi, and Mark Simkin. 2018. Yet Another Compiler for Active Security or: Efficient MPC Over Arbitrary Rings. In CRYPTO. 799–829.   \n[39] I. Damgård, V. Pastro, N. P. Smart, and S. Zakarias. 2012. Multiparty Computation from Somewhat Homomorphic Encryption. In CRYPTO, R. Safavi-Naini and R. Canetti (Eds.). 643–662.   \n[40] Ivan Damgård, Valerio Pastro, Nigel P. Smart, and Sarah Zakarias. 2012. Multiparty Computation from Somewhat Homomorphic Encryption. In CRYPTO. 643–662.   \n[41] Danny Dolev and H. Raymond Strong. 1983. Authenticated Algorithms for Byzantine Agreement. SIAM J. Comput. (1983).   \n[42] H. Eerikson, C. Orlandi, P. Pullonen, J. Puura, and M. Simkin. 2019. Use your Brain! Arithmetic 3PC For Any Modulus with Active Security. IACR Cryptology ePrint Archive (2019).   \n[43] Jun Furukawa, Yehuda Lindell, Ariel Nof, and Or Weinstein. 2017. HighThroughput Secure Three-Party Computation for Malicious Adversaries and an Honest Majority. In EUROCRYPT. 225–255.   \n[44] Martin Geisler. 2007. Viff: Virtual ideal functionality framework. http://viff.dk.   \n[45] Rosario Gennaro, Yuval Ishai, Eyal Kushilevitz, and Tal Rabin. 2002. On 2-Round Secure Multiparty Computation. In CRYPTO.   \n[46] Oded Goldreich, Silvio Micali, and Avi Wigderson. 1987. How to Play any Mental Game or A Completeness Theorem for Protocols with Honest Majority. In STOC. 218–229.   \n[47] S. Dov Gordon, Feng-Hao Liu, and Elaine Shi. 2015. Constant-Round MPC with Fairness and Guarantee of Output Delivery. In CRYPTO. 63–82.   \n[48] Thomas Holenstein, Robin Künzler, and Stefano Tessaro. 2011. The equivalence of the random oracle model and the ideal cipher model, revisited. In STOC. 89–98.   \n[49] Yuval Ishai, Ranjit Kumaresan, Eyal Kushilevitz, and Anat Paskin-Cherniavsky. 2015. Secure Computation with Minimal Interaction, Revisited. In CRYPTO. 359–378.   \n[50] Mitsuru Ito, Akira Saito, and Takao Nishizeki. 1989. Secret sharing scheme realizing general access structure. Electronics and Communications in Japan (Part III: Fundamental Electronic Science) (1989).   \n[51] John Launchbury, Dave Archer, Thomas DuBuisson, and Eric Mertens. 2014. Application-Scale Secure Multiparty Computation. In Programming Languages and Systems. 8–26.   \n[52] Yehuda Lindell, Benny Pinkas, Nigel P. Smart, and Avishay Yanai. 2015. Efficient Constant Round Multi-party Computation Combining BMR and SPDZ. In CRYPTO. 319–338.   \n[53] Eleftheria Makri, Dragos Rotaru, Nigel P. Smart, and Frederik Vercauteren. 2017. PICS: Private Image Classification with SVM. IACR Cryptology ePrint Archive 2017 (2017), 1190.   \n[54] Payman Mohassel and Peter Rindal. 2018. ABY3: A Mixed Protocol Framework for Machine Learning. IACR Cryptology ePrint Archive 2018 (2018), 403.   \n[55] Payman Mohassel, Mike Rosulek, and Ye Zhang. 2015. Fast and Secure Threeparty Computation: The Garbled Circuit Approach. In CCS. 591–602.   \n[56] Moni Naor. 1991. Bit Commitment Using Pseudorandomness. J. Cryptology 4, 2 (1991), 151–158.   \n[57] Jesper Buus Nielsen, Peter Sebastian Nordholt, Claudio Orlandi, and Sai Sheshank Burra. 2012. A New Approach to Practical Active-Secure Two-Party Computation. In CRYPTO.   \n[58] Peter Sebastian Nordholt and Meilof Veeningen. 2018. Minimising Communication in Honest-Majority MPC by Batchwise Multiplication Verification. In ACNS. 321–339.   \n[59] Rafael Pass, Elaine Shi, and Florian Tramèr. 2017. Formal Abstractions for Attested Execution Secure Processors. In EUROCRYPT. 260–289.   \n[60] Arpita Patra and Divya Ravi. 2018. On the Exact Round Complexity of Secure Three-Party Computation. In CRYPTO. 425–458.   \n[61] Tal Rabin and Michael Ben-Or. 1989. Verifiable Secret Sharing and Multiparty Protocols with Honest Majority (Extended Abstract). In STOC. 73–85.   \n[62] Phillip Rogaway and Thomas Shrimpton. 2004. Cryptographic Hash-Function Basics: Definitions, Implications, and Separations for Preimage Resistance, SecondPreimage Resistance, and Collision Resistance. In FSE. 371–388.   \n[63] C. E. Shannon. 1949. Communication theory of secrecy systems. The Bell System Technical Journal 28, 4 (1949), 656–715.   \n[64] Xiao Wang, Samuel Ranellucci, and Jonathan Katz. 2017. Global-Scale Secure Multiparty Computation. In CCS. 39–56.   \n[65] Andrew Chi-Chih Yao. 1982. Protocols for Secure Computations (Extended Abstract). In FOCS. 160–164.  \n\n# A FUNCTIONALITIES AND SECURITY MODEL  \n\nA function negl $(\\kappa)$ is said to be negligible in $\\kappa$ if for every positive polynomial $p(\\cdot)$ , there exists an $n_{0}$ such that for all $n>n_{0}$ , it holds that $\\begin{array}{r}{\\mathsf{n e g l}(n)<\\frac{1}{\\rho(n)}}\\end{array}$ < p 1n . A probability ensemble X = {X (a, n)} a 0,1 ∗;n N is an infinite sequence of random variables indexed by a and $n\\in\\mathbb N$ . Two ensembles $X=\\{X(a,n)\\}_{a\\in\\{0,1\\}^{*};n\\in\\mathbb{N}}$ and $Y=\\{Y(a,n)\\}_{a\\in\\{0,1\\}^{*};n\\in\\mathbb{N}}$ are said to be computationally indistinguishable, denoted by $X\\stackrel{c}{\\approx}Y$ , if for every PPT algorithm $D$ , there exists a negligible function negl(.) such that for every $a\\in\\{0,1\\}^{*}$ and $n\\in\\mathbb{N}$ , $|\\operatorname*{Pr}[D(X(a,n))=1]-\\operatorname*{Pr}[D(Y(a,n))=1]|\\leq\\mathsf{n e g l}(n)$ .  \n\nThe security of our protocols is proven based on the standard real/ideal world paradigm i.e. it is examined by comparing the adversary’s behaviour in a real execution to that of an ideal execution considered to be secure by definition (in presence of an incorruptible trusted third party (TTP)). In an ideal execution, each participating party sends its input to TTP over a perfectly secure channel, the TTP computes the function using these inputs and sends respective output to each party. Informally, a protocol is said to be secure if an adversary’s behaviour in the real protocol (where no TTP exists) can be simulated in the above described ideal computation.  \n\nBelow we present the ideal functionalities in the standard security model for unanimous abort, fair and GOD in Figs 16,17 and 18 respectively.  \n\nEach honest party $P_{i}$ $\\zeta_{i}\\in[5])$ sends its input $x_{i}$ to the functionality. Corrupted parties may send arbitrary inputs as instructed by the adversary. When sending the inputs to the trusted party, the adversary is allowed to send a special abort command as well.   \nInput: On message (Input, $x_{i}$ ) from $P_{i}$ , do the following: if (Input, $^*$ ) message was received from $P_{i}$ , then ignore. Otherwise record $x_{i}^{\\prime}=x_{i}$ internally. If $\\boldsymbol{x}_{i}^{\\prime}$ is outside of the domain for $P_{i}$ , consider $x_{i}^{\\prime}=$ abort. Output to the adversary: If there exists $i\\in[5]$ such that $x_{i}^{\\prime}=\\mathsf{a b o r t},$ send (Output, $\\perp$ ) to all the parties. Else, send (Output, $y,$ ) to the adversary, where $y=f(x_{1}^{\\prime},x_{2}^{\\prime},x_{3}^{\\prime},x_{4}^{\\prime},x_{5}^{\\prime})$ .   \nOutput to honest parties: Receive either continue or abort from the adversary. In case of continue, send $y$ to all honest parties. In case of abort send $\\bot$ to all honest parties.  \n\n# Figure 16: Ideal Functionality $\\mathcal{F}_{\\mathrm{uAbort}}$  \n\nEach honest party $P_{i}$ $i\\in[5])$ sends its input $x_{i}$ to the functionality. Corrupted parties may send arbitrary inputs as instructed by the adversary. When sending the inputs to the functionality, the adversary is allowed to send a special abort command as well.   \nInput: On message (Input, $x_{i}$ ) from $P_{i}$ , do the following: if (Input, $*$ ) message was received from $P_{i}$ , then ignore. Otherwise record $x_{i}^{\\prime}=x_{i}$ internally. If $\\boldsymbol{x}_{i}^{\\prime}$ is outside of the domain for $P_{i}$ , consider $x_{i}^{\\prime}=$ abort. Output: If there exists $i\\in$ [5] such that $x_{i}^{\\prime}=$ abort, send (Output, ⊥) to all the parties. Else, send (Output, $y$ ) to party $P_{i}$ for every $i\\in$ [5], where $y=f(x_{1}^{\\prime},x_{2}^{\\prime},x_{3}^{\\prime},x_{4}^{\\prime},x_{5}^{\\prime})$ .  \n\n# Figure 17: Ideal Functionality $\\mathcal{F}_{\\mathrm{fair}}$  \n\nEach honest party $P_{i}$ $(i\\in[5])$ ) sends its input $x_{i}$ to the functionality. Corrupted parties may send arbitrary inputs.   \nInput: On message (Input, $x_{i}$ ) from a party $P_{i}$ $i\\in[5])$ ), do the following: if (Input, $^*$ ) message was already received from $P_{i}$ , then ignore. Else record $x_{i}^{\\prime}=x_{i}$ internally. If $\\boldsymbol{x}_{i}^{\\prime}$ is outside of the domain for $P_{i}$ , set $\\boldsymbol{x}_{i}^{\\prime}$ to be some predetermined default value.   \nOutput: Compute $y~=~f(x_{1}^{\\prime},x_{2}^{\\prime},x_{3}^{\\prime},x_{4}^{\\prime},x_{5}^{\\prime})$ and send (Output, $y$ ) to party $P_{i}$ for every $i\\in[5]$ .  \n\n# Figure 18: Ideal Functionality $\\mathcal{F}_{\\mathrm{god}}$  \n\n# B PRIMTIVES  \n\n# B.1 Non-Interactive Commitment Scheme  \n\nWe use Non-Interactive Commitment Scheme (NICOM) characterized by two PPT algorithms (Com, Open) and are defined as:  \n\n– Com outputs commitment $c$ and corresponding opening information o, given a security parameter $\\kappa$ , a common public parameter pp, message $x$ and random coins $r$ .   \n– Open outputs the message $x$ given $\\kappa$ , pp, a commitment $c$ and corresponding opening information o.  \n\n– Correctness: For all values of public parameter pp, message $x\\in M$ and randomness $r\\in{\\mathcal{R}}$ , if $(c,o)\\gets\\mathsf{C o m}(x;r)$ then $\\mathrm{Open}(c,o)=x$ .  \n\n– Hiding: For all PPT adversaries $\\mathcal{A}$ , all values of pp, and all $x,x^{'}\\in\\mathcal{M}$ , the difference $|\\mathsf{P r}_{(c,o)\\leftarrow\\mathsf{C o m}(x)}[\\mathcal{R}(c)~=~1]~-$ $\\mathsf{P r}_{(c,o)\\leftarrow\\mathsf{C o m}(x^{'})}[\\mathcal{A}(c)=1]|$ is negligible.   \n– Binding: A PPT adversary $\\mathcal{A}$ outputs $(c,o,o^{'})$ such that $\\mathrm{Open}(c,o)\\neq\\mathrm{Open}(c,o^{'})$ and $\\perp\\not\\in\\{\\mathrm{Open}(c,o),\\mathrm{Open}(c,o^{'})\\}$ with negligible probability over uniform choice of pp and random coins of $\\mathcal{A}$ . Instantiations. In the random oracle model, the commitment   \nscheme is:   \n$-\\mathsf{C o m}(x;r)$ sets $c=H(x||r)$ , $o=\\left(x\\vert\\vert r\\right)$ where $c,o$ refer to the commitment and opening respectively. The pp can be empty.   \n$\\boldsymbol{-\\mathrm{\\nabla{Open}}}(c,o=(x||r))$ returns $x$ if $H(o)=c$ and $\\bot$ otherwise. For the purpose of all empirical results, the random oracle can be   \ninstantiated using a hash function. Alternatively, based on one-way   \npermutation, we present an instantiation of NICOM(Com, Open)   \nused theoretically in our protocols as: Let $f:\\{0,1\\}^{n}\\to\\{0,1\\}^{n}$   \nbe a one-way permutation and $h:\\{0,1\\}^{n}\\rightarrow\\{0,1\\}$ be a hard-core   \npredicate for $f$ . Then the bit-commitment scheme for $x$ is:   \n– $\\mathrm{~-~}\\operatorname{Com}(x,r)$ sets $\\boldsymbol{c}=(f(r),\\boldsymbol{x}\\oplus h(r))$ where $r\\in_{R}\\{0,1\\}^{n}$ and $o=$ $(x||r)$ .   \n$\\boldsymbol{-\\mathrm{\\nabla{Open}}}(c,o=(x||r))$ returns $x$ if $c=(f(r),x\\oplus h(r))$ , else $\\bot$ . We provide bit and string based instantiations for   \n$\\mathsf{N I C O M}(\\mathsf{C o m},\\mathsf{O p e n})$ [25] based on block ciphers that are   \nsecure in the ideal cipher model [19, 48, 63] and are used in our   \nAOTs for efficiency. The bit commitment scheme is as follows:   \n$\\mathrm{~-~}\\mathsf{C o m}({b,r})$ sets $\\boldsymbol{c}~=~F_{k}(\\boldsymbol{r})\\oplus\\boldsymbol{r}\\oplus b^{n}$ where $b^{n}~=~\\vert\\vert_{i\\in[n]}b$ and $F:\\{0,1\\}^{n}\\times\\{0,1\\}^{n}\\to\\{0,1\\}^{n}$ is a random permutation parametrized by key $k$ . Also, $o=\\left(r||b\\right)$ .   \n$\\boldsymbol{\\mathbf{\\ell}}-\\mathbf{\\ell}\\mathbf{O}\\mathbf{pen}(c,o=(r||b))$ returns $b$ if $c=F_{k}(r)\\oplus r\\oplus b^{n}$ and $\\bot$ otherwise.   \nHowever, this bit commitment scheme is not secure for string com  \nmitments. Hence we describe the following secure instantiation:   \n– ${\\mathrm{Com}}(m,r)$ sets $c=F_{k}(r)\\oplus r\\oplus F_{k}(m)\\oplus m\\mathrm{~s.t~}F:\\{0,1\\}^{n}\\times\\{0,1\\}^{n}\\rightarrow$ $\\{0,1\\}^{n}$ is a random permutation parametrized by key $k$ and $o=\\left(r||m\\right)$ .   \n$\\boldsymbol{-}\\operatorname{Open}(c,o=(r||m))$ returns $b$ if $c=F_{k}(r)\\oplus r\\oplus F_{k}(m)\\oplus m$ , else ⊥.  \n\n# B.2 Equivocal Commitment Scheme  \n\nFor the fair protocol, we use an Equivocal Non-Interactive Commitment Scheme (eNICOM) characterized by four PPT algorithms (eCom, eOpen, eGen, Equiv). The algorithms eCom, eOpen are as defined in NICOM. The algorithms eGen, Equiv are defined as:  \n\n– $\\mathsf{\\Psi}^{\\mathrm{eGen}(1^{\\kappa})}$ returns a public parameter and a corresponding trapdoor (epp, t). The parameter epp is used by both eCom and eOpen and trapdoor $t$ is used for equivocation.   \n– Equi $\\prime(c,o^{\\prime},x,t)$ returns an o s.t $x\\gets{\\mathrm{eOpen}}({\\mathrm{epp}},c,o)$ when invoked on commitment $c$ , its opening $o^{\\prime}$ , the desired message $x$ (to which equivocation is required) and the trapdoor $t$ .  \n\nAn eNICOM should satisfy the following properties:  \n\n– Correctness: For all pairs of public parameter and trapdoor, $(\\mathrm{epp},t)\\gets\\mathrm{eGen}(1^{\\kappa})$ , message $x\\in\\mathcal{M}$ and randomness $r\\in{\\mathcal{R}}$ , if $(c,o)\\gets\\mathrm{eCom}(x;r)$ then $\\mathtt{e O p e n}(c,o)=x$ .   \n– Hiding: For all $(\\mathsf{e p p},t)\\gets\\mathsf{e G e n}(1^{\\kappa})$ , all PPT adversaries $\\mathcal{A}$ and all $x,x^{\\prime}\\in{\\mathcal{M}}$ , the difference $|\\mathsf{P r}_{(c,o)\\gets\\mathrm{eCom}(x)}[\\mathcal{A}(c,o)=1]-$ $\\mathsf{P r}_{(c,o)\\gets\\mathrm{eCom}(x),o\\gets\\mathsf{E q u i v}(c,x,t)}\\mathscr{A}(c,o)=1|$ is negligible   \n– Binding: For all $(\\mathrm{epp},t)\\gets\\mathrm{eGen}(1^{\\kappa})$ , a PPT adversary $\\mathcal{A}$ outputs $(c,o,o^{'})$ s.t $\\mathrm{eOpen}(c,o)\\neq\\mathrm{eOpen}(c,o^{'})$ and $\\perp\\not\\in$ $\\{\\mathrm{eOpen}(c,o),\\mathrm{eOpen}(c,o^{'})\\}$ with negligible probability.  \n\nInstantiation: We can use the equivocal bit commitment scheme of [34] in the standard model, based on Naor’s commitment scheme [56] for bits. Let ${\\mathrm{G}}:\\{0,1\\}^{n}\\to\\{0,1\\}^{4n}$ be a pseudorandom generator. The commitment scheme for bit $^b$ is:  \n\n$\\mathtt{-e G e n}(1^{\\kappa})$ sets $\\begin{array}{r l r}{(\\mathbf{e}\\mathrm{pp},t_{1},t_{2},t_{3},t_{4})\\quad}&{{}=\\quad}&{((\\sigma,\\mathbf{G}(r_{1}),\\mathbf{G}(r_{2}),}\\end{array}$ $\\mathrm{G}(r_{3}),\\mathrm{G}(r_{4})),r_{1},r_{2},r_{3},r_{4})$ , where $ \\sigma~=~{\\o G}(r_{1})\\oplus{\\o G}(r_{2})\\oplus$ $\\mathrm{G}(r_{3})\\oplus\\mathrm{G}(r_{4}).t=||_{i\\in[4]}t_{i}$ is the trapdoor.   \n$\\mathrm{~-~}\\mathrm{e}\\mathrm{Com}(\\boldsymbol{x};\\boldsymbol{r})$ sets $c=\\mathbf{G}(s_{1})\\overset{}{\\oplus}\\mathbf{G}(s_{2})$ if $x=0$ , else $c=\\mathbf{C}(s_{1})\\oplus$ ${\\bf G}(s_{2})\\oplus\\sigma$ and sets $o=\\left(x\\|r\\right)$ where $r=s_{1}||s_{2}$ .   \n– eOpen( $c,o=\\left(\\boldsymbol{x}||\\boldsymbol{r}\\right))$ returns $x$ if $\\boldsymbol{c}=\\mathbf{G}(s_{1})\\oplus\\mathbf{G}(s_{2})\\oplus\\boldsymbol{x}\\cdot\\boldsymbol{\\sigma}$ (where denotes multiplication by a constant), else returns $\\bot$ .   \n– Equiv $(c={\\bf G}(r_{1})\\oplus{\\bf G}(r_{2}),\\bot,x,(t_{1},t_{2},t_{3},t_{4}))$ returns $o=\\left(x\\vert\\vert r\\right)$ where $r=t_{1}||t_{2}$ if $x=0$ , else $r=t_{3}||t_{4}$ . The entire trapdoor $t=(t_{1},t_{2},t_{3},t_{4})$ is required for equivocation.  \n\nFor empirical purposes, we rely on the random oracle based scheme presented before with the property of equivocation and is realized using a hash function.  \n\n# B.3 Collision Resistant Hash [62]  \n\nConsider a hash function family ${\\sf H}=\\mathcal{K}\\times\\mathcal{L}\\rightarrow\\boldsymbol{y}$ . The hash function $\\mathsf{H}$ is said to be collision resistant if for all probabilistic polynomial-time adversaries $\\mathcal{A}$ , given the description of $\\mathsf{H}_{k}$ where $k\\in_{R}\\mathcal{K}_{:}$ , there exists a negligible function negl() such that $\\operatorname*{Pr}[(x_{1},x_{2})\\leftarrow{\\mathcal{A}}(k):(x_{1}\\neq x_{2})~/$ ∧ $\\mathsf{H}_{k}(x_{1})=\\mathsf{H}_{k}(x_{2})]\\leq\\mathsf{n e g l}(\\kappa).$ , where $m={\\mathsf{p o l y}}(\\kappa)$ and $x_{1},x_{2}\\in_{R}\\{0,1\\}^{m}$ .  "}