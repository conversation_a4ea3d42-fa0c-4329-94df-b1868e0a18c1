{"text": "# INTRUSION-TO<PERSON>RANCE USING  \n\nFINE-GRAINF<PERSON>GMENTATION-SCATTERING  \n\nJean<PERSON><PERSON> Yves DESWARTE David POWELL  \n\nLAAS/CNRS-INRIA 7 avenue du <PERSON> 31077 TOULOUSE Cédex FRANCE  \n\n# ABSTRACT  \n\nTheconsequences of faults and intrusions in computer networks areofever-increasing importance; new efficient techniques are needed to deal with them.The SATURNE [DES 86] project aims atsolutionsforarchitecturesbasedonthe conceptof fault-and intrusion-tolerancerather thanavoidancetechniques.The subjectofthe paperconcerns the security of files.Filesare partitioned and then fragmented before   being stored in different,distributed archive sites in order  to tolerate intrusions into some of  these sites. The choice among different fragmentation cryptographicmethods isdiscussed,relatedto different kinds of possible attacks.A simple and fast  solution is proposed that  nevertheless ensuresahighleveloffilesecurityand authenticity.  \n\n# INTRODUCTION  \n\nSociety is becoming more and more  dependent oncomputersystems in allaspectsof modernlife. We not only use computers to do our accounting or tomakeourwashingmachineswork butwe alsohave totrust them to ensure thatouraircraftdonot fallout oftheskyandtolook afterour companies'marketing strategies and our countries' defense systems. Thus, we are (or should be) forcedto design and implement computingsystems that are dependable [LAP 85], i.e. systems in which users can place justifiable confidence.  \n\nIn the same way, the defenses of a computing system with respect to intrusions may be basedon avoidance and/or tolerance techniques.At the same time as more and more \"sensitive\" applications are being developed（i.e.concerning confidential data)，there is an increasing number of users having (hopefully-legitimate) access to both widearea and local-area computer networks. This means that it is becoming more and more difficult and lessefficientto use onlyintrusionavoidance mechanisms like identification, access control, protection... It would thus seem appropriate to develop intrusiontolerance techniques:rather than prevent a malicious or unintentional intruder from accessing confidential data, it is better to make the accessibledata meaningless tohim. Cryptographyisaclassicexampleofsucha technique. Distributed systems offer a new way to tolerate intusions,as discussed in this paper.In the same way as for fault handling, intrusion avoidanceandtolerancemechanismsarenot exclusive, but are complementary.  \n\nComputingsystem dependability can be adversely affected byboth （accidental)faults and (malicious) intrusions.Thetwo underlying concepts（availability and security） may beseen not asseparate, butasjointproblemsas discussed in a recent paper by Randell and Dobson [ran 86].  \n\nClassically, faults  are tackled by the complementaryapproaches offault-avoidanceand fault-tolerance.Fault-avoidancetechniquesare aimedatminimizing the probability thatfaults occurwhereasfault-tolerancetechniquesseekto ensure correct  system operation in spite of faults.  \n\nA combined LAAS/INRIA project called  SATURNE iscurrentlyunder way that aims toinvestigate newtechniquesforensuring bothfault- and intrusion-tolerance in distributed computing systems built around localarea networks[DES 86]. Two novel techniques are currently being explored: \"saturation\"and \"fragmentation-scattering\"  \n\nThe saturation technique aims to provide user-transparent  fault-tolerance based on errorcompensation or  error-masking whilst ensuring minimumerrorlatency（thedelaybetweenfault occurrence and error manifestation). The basic idea isthat any active process in thesystem will be  executed in as many sites as possible so that process redundancy and processor activity are maximized.  \n\nThe aimof thefragmentation-scattering technique is toprovide a distributed file management system that is both reliable (with respect to faults) and secure (with respect to intrusions). Theprincipleistocut every sensitive file into several fragments, which are then stored on different archive sites, in such a waythatan intruder cannotobtain complete file, even if he gets access to an archive site.  \n\nThis  paper is  concerned  with the latter technique which may be seen as a complement, rather than a substitute, for the more classicai data security techniques.It is divided into three main parts:  \n\n- the first part deals with the principles of fragmentation-scattering;a summary of the system design from a security point of view is given and a new approach is exposed that provides a finer grain of file fragmentation allowing increased flexibility,   \n- the second part is devoted to fragmentation techniques that can be implemented either by software or specialized (but simple) hardware and which, due to the added value of fragmentation-scattering, should provide a degree of confidentiality comparable with or better than that of more classical cryptographic techniques,   \n\"the third and last part analyzes the various threats to which the system must resist in order to determine the features and  properties  of such new  security policies.  \n\n# 1. PRINCIPLES OF  \n\n# FRAGMENTATION-SCATTERING  \n\nFragmentation and scattering hasbeen studied within the context of a fault- and intrusiontolerant distributed file system [FRA 85]. First, weshallexamine the environment in whichthese concepts are to be implemented. Secondly， the principlesofthe technique are establishedand then the management policy is discussed. Finally, a flexible approachtofragmentation1s given.  \n\nAuserhasfullauthorityovertheobjects stored in his machine and during his session (login time)，nobody else can work on this site.  \n\nThe system contains three kinds of sites whichareinterconnectedthrougha broadcast network (fig.1):  \n\nuser sites,which are personal workstations   \nthat constitute the physical domains of their users,   \narchive sites, designed for longduration data storage，while the user is not logged-in,   \nsecurity_sites, designed to manage user   \nlog-intothe esystem and todealwith   \nexclusions.  \n\n![](/tmp/output/6_20250326032926/images/2a7e68d426e3c1b4c9e334680b2cd9d485bed9b0e6516b532397b6163c00a867.jpg)  \nFig.1 - System environment  \n\n# I.1.SYSTEM ENVIRONMENT  \n\n# I.2.PRINCIPLE OF THE METHOD  \n\nThe basis of the fragmentation and scattering techniqueis to cut every sensitive file into several fragments in such a way that one or more fragments(butnotall）areinsufficientto reconstitute the file.These fragments arethen stored in geographically distributed archive sites,to ensure that an intruder cannot obtain allthe fragments of the same file(except ifhe has almost overall controlofthewhole distributed system). The fragmentation itself is carriedout by the user site so as toavoidthe transmissionof theentirefileover the communication channel (otherwise, eavesdropping couldannihilatetheaddedadvantagesofthe technique).On the other hand，in order to obtain afault-tolerance capability (availability), several copies of  each fragment are  stored on different sites; this aspect of the project  is developed in [DEs 86].  \n\nBefore studyingdetailed methods for fragmenting files,it is interesting to examine the general policy of file management in this distributed system.  \n\n# I.3.FILE MANAGEMENT.  \n\nSomeofthefile managementoperationsare carried out in the user site,others are remotely executedeitherby securityor archive sites.All thefilemanagement operations areexecutedby user-transparentsystem facilities.To ensurea high level of security，files are never available in  any site other than the connected user  site. Thus, data sent by the user  is always  in a fragmentedformwith nopossibilityofrecognizing the different fragments derived from a given file. Consequently，the fragmentation and the naming of the fragments must be executed inthe usersite.  \n\n# I.3.1 User site operations.  \n\nThe   fragmentationoperation is largely discussed in the sequel of this paper; it is done with a fragmentation key, provided by the security sites after they have authorized access to the file。  \n\nThe names given to the different fragments are generated by cryptographic methods using the fragmentation key, such that no information can be derived from these names. Because they are locally generated, nothing  prevents two users  from generating fragments with identical names.In this case,the archive sites can see that a name has alreadybeen used and may inform theusersite that the name must be changed.Another solution to this problem is to store the fragment with a duplicatedname andto trust intheerrordetection capabilities of the system when the file is reassembled; fragments with the same name are thenretrieveduntil a correct one isobtained. The relative efficiency of these twosolutions depends on the probability of such occurrences, and has not yet been studied.The original file is reconstituted with the same key that has been used for fragmentation.  \n\n# I.3.2.Security site operations  \n\nThe security aspects such as key management, authentication of the users, file access rights associated  to  the  user, etc... are the responsibilityof the securitysites.Although they are called security sites, an intrusion in oneofthesesites must not be abletoaccess sensitive information. Consequently, _threshold schemes are used for the different keys [SHA79], [DAV 8o].The different image values of a key are scattered among the security sites. When a file is required by a user, the different image values of the fragmentation key are sent by the security sites to the user site which combines these values to reconstitute the key. At the same time， the securitysitessendthe accessrightstothe archive sites thus permitting (or not） the request tobe satisfied.To recognize which user sends a request, an activator is provided by the security sites (also based on threshold schemes） which is then used by the user site at the time of a fragments request. Exclusion is also managed by the security sites， based on majority voting algorithms [THo 79].  \n\n# I.3.3.Archive site operations  \n\nAt the end of a session,the archiving of the file is executed by sending the fragments over the communicationchannel,fromtheusersites tothe archive sites.Every archive site decides whether or not it should be saved locally, depending on a distributed  algorithmensuring security and availability,based on the principles discussed previously. Only ciphered names are visible; an archivesite cannot determine where thefragment comes from.Because the fragments are anonymous, allthe fragments of a single file maybestored inonearchivesite.This maybeavoidedby partitioning these sites into several classes,in order to enforce scattering. This is a compromise betweenunmarked sites and theestablishmentof archive tables determined by the user site and based on the fragmentation key [FRA 85].  \n\n# I.4.GENERAL APPROACH OF FRAGMENTATION  \n\nThemain goalof fragmentation istohide the sensitive information contained in the files, when the latter is not in the user site. Security isthus ensured but authenticity must also be provided: the information that the user obtains from the archive sites must be exactly the same as that he previously stored.The modification of a fragment（duetoanintruderortophysical faults） must be detected， the faulty fragment identifiedand a correct one obtained by faulttolerance mechanisms as discussed in [DES 86]. The fragmentation is carried out in the local site and the operation must be transparent to the user. The fragmentation must be executed guickly enoughin order to prevent a high overhead. Moreover, no information may be obtained externally by simply observingthe different fragments.Forexample, the lengths of the different fragments must not convey any information on their own.Sensitive files have different characteristics (program, data...） and very different lengths. The algorithm must be able to deal with these disparities. A solution to these requirements is discussedbelow in detail. The purpose is first to determine a general scheme for the fragmentation, compatible with the constraints.  \n\n# I.4.1. Fixed number of fragments  \n\nThis method comes first to mind. A file  is splitinto Nfragments;the length of a fragment is proportional to the length of the file.The secrecy depends on this number N.The bigger it is, the least significant is the information included in one fraction. The time taken by  the operation increases with N.This is the trade-off between the degree of secrecy and the speed of the algorithmto achieve it.With thismethod，the lengthof every fragment depends on the length of the file. Consequently, an intruder would know that two fragments probably belong to the same file by just comparing their length. This is one ofthe things we want to avoid.Another drawback is the need to get all the file，even if only a partofitisrequired.  \n\n# I.4.2.Fixed length of fragments  \n\nThis methoddoes notenableittobeknownif two fragments come from the same file just by comparing their lengths,but the choice of a fragment length is difficult， due to the large differences in lengths of files. Moreover，to obtain a file,one still has to get all the fragments.There may be many fragments for a long file, due to the small size of one fragment imposed by the necessity for dealing also with small files.  \n\n# I.4.3. Partitioning and fragmentation  \n\nThe two methods exposed above are not very suitable for managing files with very different lengths. The solution proposed is to first cut every file intoblocksoffixed size (partitioning). Bvery block may then be fragmented in a fixed way (number and lengths). The files are padded out to reach a size equal to a multiple of ablocksize(fig.2). All the fragments so obtained will have the same length, which may be chosen equal to that of a packet sent on the communication channel，or a quantum in the mass storage, for example. These  choices  may also improve the speed of access to information.  \n\nAnotheradvantage is that one does not need toget all the file， instead blocks can be retrieved independently. So, many users may work on the same file with exclusive rights for writing if they operate on different blocks. This is very importanttoimprove the quality of service ina databasesystem.  \n\nThe method is iilustrated below:  \n\n![](/tmp/output/6_20250326032926/images/27a093bd737c033ea072cbb1c71b7b8e30b25bcc8ae2446ffd2bb9eec11a108c.jpg)  \nFig.2-A file partitioned into 7 blocks  \n\nThis first step is done sequentially without cryptography. An overhead is induced : part of the last block is filled with padding informationbut is nevertheless treated as precious data. The mean overhead is half a block, and is of course a large overhead for small files. The shorter a block is, the smaller is the overhead,but the longer is the management time, mainly due to fragment storage time. Now the probiem is to deal with the blocks; this is much more simple, however, than dealing with files of arbitrary length. Secrecyand authenticity wiil be introduced at this step.  \n\n# 11. APPROPRIATE  \n\n# FRAGMENTATION ALGORITHMS  \n\nA very simple fragmentation scheme is to just spread the data among the fragments; however in this case, an intruder having several fragments could derive the whole block byguessing the missing data.Cryptographic methods must thus be used in conjunction with the fragmentation. The questionthat one maythenaskis:why use fragmentation since cryptographic techniques must still be employed ? Our purpose is to use simple but fast ciphering algorithms that，with the help of fragmentation and scattering, can nevertheless reach and even pass beyond the level of security brought by strong but much longer algorithms.  \n\n# II.1.CIPHERING AND FRAGMENTATION  \n\nOnemay imagine different schemes to realize these operations together. Some of them are discussed below.  \n\n# II.1.1.Fragmentation of ciphered block  \n\nThe block is first ciphered and thefragments are obtained from this ciphered block. The fragmentation itself uses a fixed schemethat does not depend on the key (fig.3). There is a real scattering ofthedata.  \n\n![](/tmp/output/6_20250326032926/images/6dad613b012530add124812af35edda1094dc36374ba3c59ade25297de356975.jpg)  \n\nThis method is interesting because there are nologicalrelations betweenthedata items of one fragment. One  can  take  advantage  of the unavailability of several fragments by choosing an appropriate ciphering method,as discussed in paragraphII.2.  \n\n# II.1.2.Ciphering the fragments  \n\nThe fragmentation  is carried out before ciphering(fig.4).  \n\n![](/tmp/output/6_20250326032926/images/52c968e81d33921d9c6daa8dd4ce679a3380355ed2295f57ec264e37f2def261.jpg)  \n\nThe ciphered fragments do not depend on each other. This seems less efficient than the previous method, although the data of a fragment is spread over the whole block.  \n\n# II.1.3. Threshold schemes  \n\nThe idea is to use a threshold scheme for the block,, as it is already done for numbers such as keys (I.3.2). The problem is the size needed for storage that is here multiplied by the number of \"image\" blocks. Nevertheless, this solution is not to be rejected if fault-tolerance mechanisms are concurrently developed.The classical way to ensure fault-tolcrance is toderive severalcopies of one block. With the use of threshold schemes, one can ensure both fault-tolerance and intrusiontolerance capabilities [FRA 85]， by deriving more image blocks than necessary. The relative overhead in terms of storage place, in comparison with identical  copies solution is not necessarily large. We think that this may be an interesting question to explore.  \n\n# II.1.4.Which choice ?  \n\nWe have briefly discussed some methods for mixing fragmentation and ciphering. Some of them are rejected as inefficient from a scattering point of view. The most interesting are the fragmentation of ciphered blocks and the threshold schemes detailed for blocks. We have mainly studied the first approach and it is detailed below.  \n\n# II.2,DETAILED METHODS  \n\nThe choiceofthecipher used before fragmentation must make the ciphertext of each fragment dependent on the others. This may be realized by using a stream cipher. With such a cipher, the key used to cipher a quantum of plaintext changes every time. If used in Cipher Feedback Mode (CFB） [DEN 83],[MEY 82], the preceding ciphered text is necessary to determine the foliowing plaintext (fig.5). This introduces secrecy if any quantum of text is missing.Another method, not very different，is Cipher Block Chaining (CBC), in which the plaintext is chained with a part of the preceding cipher text (fig.6).  \n\n$\\mathtt{M_{i}}$ :quantum of plaintext，K : key (constant) ${\\sf c_{i}}$ : quantum of ciphertext, $\\mathtt{K_{i}}$ : stream key E:cryptographicfunction.  \n\n![](/tmp/output/6_20250326032926/images/ec6075b3f5efe3276c7a718ffc6271bf8f04867356d826a5401810c71f4d4395.jpg)  \nFig.5 - Stream cipher in Cipher Feedback Mode  \n\n![](/tmp/output/6_20250326032926/images/57beb5a622d1c4041c66b020329a2fafd5515c3d6ec2a1dc2aee057fa605a89c.jpg)  \nFig.6 - Cipher Block Chaining  \n\nThese methods aliow authentication of the fragments.because if one quantum is modified,all the following deciphered text is changed. Once the cipheredblock is obtained.the fragmentation is executed: fragment number j contains the fixed quanta (bits, bytes, words...） number i such that ${\\dot{\\mathbf{J}}}={\\dot{\\mathbf{1}}}$ (mod N)，N being the number of fragments. We  have  imagined some examples based on  these principlesand one of them has beenimplemented, tosee the time needed forthese algorithms to be executed by software.  \n\n# II.2.1. First example  \n\nWe propose a stream cipher algorithm in CFB, but the quantum is no longer a bit but a word of 16 bits. The stream key Ki is derived from the fragmentation key and the R preceding  cipher words. Then,  the plaintext is simply exclusiveored with the stream key(fig.7).  \n\n![](/tmp/output/6_20250326032926/images/ecd8e3a05ae6961b2687c3f4a51470c06b067ed299690679f9c357ed6842295a.jpg)  \nFig.7 - Enciphering words of 16 bits  \n\nA register contains the R preceding cipher words; a non-linear transformation, depending on the fragmentation key, is used to get the stream key (16 bits). This method is efficient in the case of a missing ciphered fragment, because the key depends on the R consecutive ciphered words, and they are not all available. In the beginning, the register is partly filled with known words and completed with a secret initialization. The length of the secret part may be chosen by the user. (It may be inexistent, the secret being contained in the key K only). The fact that the plaintext is ciphered word-by-word increases the speed of the algorithm, comparedtobit ciphering. The non-linear transformation of the register into a word $\\upkappa_{\\mathrm{i}}$ is the heart of the algorithm. A non-1inear method must be used， to ensure protection againstmathematical cryptanalyses. Such a function is given below, as an example.  \n\nLet $\\texttt{N}=\\texttt{8}$ The fragmentation key is a byte (8 bits).  \n\nThe word number n of the register is: -unchanged if the bit number n of the key K is O. - rotated if the bit number n of the key K is 1.  \n\nThen, the 8 results are exclusive-ored to get $\\mathtt{\\mathbf{K_{i}}}$  \n\nThis  transformation, althoughnot very complicated,is non linear. Of course，it would not be so strong if it were not associated with the  fact that ‘some fragments  are  missing. Nevertheless, if only one fragment is missing, one still needs to do a mean $\\yen20000$ trials to guess which word is missing in the register, and this, everyR deciphered words.Even if some words of plaintextmay sometimes be guessed thanks to the context， it is not always possible and cryptanalysis seems difficult,even in this very optimistic case.It is obvious that if onlyone fragment is available,i.e. one word among R, cryptanalysis is not possible.  \n\n# II.2.2.Second example  \n\nThis algorithm isbasedon the same principles, but is simpler.  \n\n![](/tmp/output/6_20250326032926/images/95ffd86376c494523d4947924974163b50a42412f096c9714b16896d6a501d8c.jpg)  \nFig.8  \n\nThe  preceding  cipher   word ${\\tt C}_{\\mathrm{i}\\mathrm{~-~}1}$ isleft unchangedif thelastbitofthe rotatedkeyisO andis rotatedbyone bit if thelastbitofthe rotated key is 1. The stream key $\\mathtt{K_{i}}$ isderived fromthe preceding stream key and whatis generated by the ciphering box. The first word is ciphered with a key derived from an initialization key, which  is secret. This method answers the requirements we have previously discussed. It is simpler and faster than the first example but seemsweaker.  \n\nThis second example has been implemented on a small machine (Intel 8088 with 4.7 MHz clock). in assembly language.  In these conditions, ' the fragmentation time is about 220 ms for a 8 Kbyte block, split into 8 fragments. Indeed, this is rather long, compared with hardware operation, but is about the same duration needed for disk access on this sort of machine.  \n\n# II.3.CONCLUSION  \n\nThe use of appropriate methods to cipher the blocks and obtain the fragments allows a simpler and faster way to obtain a high level of secrecy, due to the assumption that not all the fragments are available. Hence, it is possible to realize thefragmentation by software while keeping the operation time acceptably short.  \n\n# III. SECURITY ANALYSIS  \n\nAlthough it is difficult to guess all the ideas an intruder may have, we propose to analyze here the possible threats to system security. The two aspects of security must be treated : privacy and authenticity. The system is made of user sites, system sites (archive and security) and a communication channel. What are the consequences of intrusions in any of these parts ?  \n\n# III.1.USER SITES  \n\nDuring the session time, all the sensitive data is present in the site and an intrusion during this time could be catastrophic.It is not possible to make the data available for the authorized  user  and not for  an  intruder. Consequentiy, This kind of attack must be avoided by physical protections and, toreducethe possibilities of an opponent, the  physical dimensions of the user site must be as small as possible. When the work on a file is terminated, all the temporary files at the user site must be deleted. Moreover, at the end of a session not only the files but all the keys used during the session must be deleted. No information can thus be obtained by intrusion in the user site after the end of a session, even by an authorized user, because he cannot obtain the fragmentation key of previoususers（except if he has rights tothis information).Whatever an intruder does in a user site,the sensitive data is always delivered by remote distributed sites that he does not control, andthathecanonlyaccessifthesystem (securitysites）recognize him as an authorized user.  \n\n# III.2、ARCHIVE AND SECURITY SITES  \n\nFragmentationandnaming ofthedifferent fragmentsare carried out in the user site.Data isonlyavailableoutside the usersiteina fragmented form. This is particularly the case for the archive sites and the security sites.If we want to tolerate intrusions in such a site,by an intruder stealing some storage disks or by a programmerworking in one of these sites，only some of the fragments or some of the image values ofthekeysmustbeavailable.  \n\nIf the distributed algorithms responsible for the scattering of information can be proven, then the security of the system from a privacy point of view is based on the strength of the fragmentation aigorithms of the files and the threshoidschemes for the keys. Concerning the files, an intruder canobtain nothing else but the fragmentsstored in the archive site he has violated.Cryptanalysis is verydifficult if not impossiblesincethe namesofthefragmentsarecipheredandthe fragments residing in one archive site do notall come from the same file.Even a deciphered fragment willcontainverylittle useful information.The only chance for an intruder to obtain informationistoviolate many (geographically distributed)archive sites.  \n\nOn the other hand，the modification or destructionofthedatainanarchivesite (fragments)or a security site (key images）is alwayspossible.One hastoimplement faulttolerancemechanisms to deal with these problems. But this is not sufficient. The fragmentation algorithmsmustalso have authenticity capabilities. This is the case for the examples we have given in paragraph Il.2.  \n\n# III.3. COMMUNICATION CHANNEL  \n\nCommunicationisa sensitive point ofthe system because there is no geographic scattering of information. Particular care should be taken to prevent eavesdropping. However, eavesdropping can be tolerated if the necessary precautions are taken.  \n\nIncreased security can be based on the fact that an eavesdroppercannotrecognize the fragments of a single file, among the whole flow ofdata. Consequently, the fragments exchanged between the archive sites and the user sites should not be sent sequentially in their \"natural\" order and must be anonymous. This is ensured by adequate naming (II.3).The fragmentsmustbe transmitted at sufficiently spaced intervalsand theflowof data must alwaysbesufficientto drownthe fragments ofa single file in theflow of other messages.If necessary,this flow must be artificially increased.  \n\n# III.4.STRENGTH OF FRAGMENTATION  \n\nThe confidentiality of the system depends mainly on the algorithms used to derive the different fragments. It is difficult to compare this  method  with classical  ciphering. The difficulty  of cryptanalysis depends on the relative amount of available data. For instance, we can ensure that with the fragmentation examples given above (Il.2)，if only one fragment (outof 8) is available, it is impossible to retrieve the original block. In the worst case, when an intruder has obtained all the fragments, he then faces a classical cryptanalysis problem.It is always possible to use strong algorithms (such as DES) before the fragmentation.Consequently，one can always ensure a higher level of securitythan with classical ciphering methods.  \n\nThe interesting question is whether simple andfast algorithmswith theadded value of fragmentationarebetterthan strongandslow methods without fragmentation.A quantitative evaluation of strength for fragmented data would be of great interest but seems difficuit due to theirrelevance of usual tools for evaluating the probability of successful intrusion.We intend to pursue this point further.  \n\n# IV. CONCLUSION  \n\nFragmentation and scattering is an original method for protecting data against intrusions and faults in a distributed architecture.It is based on tolerance and may be used in addition to avoidance techniques. Distributed systems introduce new problems but also provided new solutions. Thedifferentattackswhich are possible inclassical systemsbased on cryptographyarenot relevant here if access to all of the fragments is made impossible. This is thekey of the method.  Once defined. the fragmentation of the blocks could be done bya special， but simplechip to achievevery fast operation. Particular caremust be taken concerning the communication channel.A comparison with ciassical cryptography is difficult but should be attempted. Nevertheless, if the time constraints are not too strict，a higher level of security is ensured.  \n\n# REFERENCES  \n\nBAN 85 J.S.BANINO, J.C.FABRE, M.GUILLEMONT, G.MORISSET, M.ROZIER: \"Some fault tolerance aspects in the CHORUS distributed system\" Proc. 5th Int. Conf. on Dist. Comp. Systems, Denver (USA), May 1985,PP.430-437  \n\nDAV80 G.I.DAVIDA， R.A.DEMILLO，R.J.LIPTON \"protecting share cryptographic keys\", 1980 Symposium on Security and Privacy, 0akland, April 1980, pp.100-102  \n\nDEN83 DENNING D.E. \"Cryptography__and data security\", Addison-Wesley. 1983  \n\nDES 86 Y.DESWARTE, J.C.FABRE, J.C.LAPRIE, D.POWELL ： \"A saturation network to tolerate faults and intrusions\", Proc of the 5th IEEE symposium on Reliability in Distributed Software and Database Systems, Los Angeles, Jan 1986, pp.74-81   \nFRA85 J.FRAGA, D.POWELL ： \"A fault and intrusion-tolerant file system\" Proc. 3rd Int. Cong. on Comp. Security (IFIP/Sec'85), Dublin (Ireland), Aug. 1985, pp.203-218   \nLAP 85 J.C.LAPRIE: \"Dependable computing and fault-tolerance\", Proc. of the 15th Symposium on Fault-Tolerant Computing Systems (FTCS-15),Ann Arbor. June 1985, pp.2-11   \nMEY 82 C.H.MEYER, S.M.MATYAS : \"Cryptography\", Wiley-Interscience 1982   \nRAN 86 B.RANDELL, J.E.DOBSON : \"Reliability and security issues in distributed computing systems\" Proc of the 5th IEEE symposium on Reliability in Distributed Software and Database Systems, Los Angeles, Jan. 86, pp.113-118   \nRUS 83 J.RUSHBY, B.RANDELL ： \"A distributed secure system\", IEEE Computer, July 83, pp.55-67   \nSHA 79 A.SHAMIR: \"How to share a secret\" Communications of the ACM. Vol 22, nb 11, November 1979, pp 612-613   \nTHO79 R.H.THOMAS ： \"A majority consensus approach to concurrency control for multiple copy databases\" ACM transactions Database Systems, 4.2 (June 1979). Pp 180-209  "}