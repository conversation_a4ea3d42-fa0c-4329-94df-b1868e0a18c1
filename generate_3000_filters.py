#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成3000个filters组合的脚本
覆盖CCS(2007-2022)、NDSS(1996-2023)、SP(1980-2022)、USENIX_Security(2006-2023)
"""

import csv
import random

# 定义技术领域和关键词
tech_domains = {
    "密码学": ["RSA", "AES", "椭圆曲线", "哈希函数", "数字签名", "同态加密", "零知识证明", "量子密码", "后量子密码", "格密码"],
    "网络安全": ["防火墙", "入侵检测", "DDoS防护", "VPN", "SSL/TLS", "DNS安全", "BGP安全", "SDN安全", "5G安全", "WiFi安全"],
    "系统安全": ["访问控制", "权限管理", "沙箱", "虚拟化安全", "容器安全", "操作系统安全", "内核安全", "BIOS安全", "固件安全", "启动安全"],
    "Web安全": ["XSS", "SQL注入", "CSRF", "会话管理", "认证授权", "API安全", "Web应用防火墙", "内容安全策略", "同源策略", "HTTPS"],
    "移动安全": ["Android安全", "iOS安全", "移动应用安全", "移动设备管理", "移动恶意软件", "应用商店安全", "移动支付安全", "位置隐私", "移动通信安全", "NFC安全"],
    "AI安全": ["对抗样本", "模型窃取", "成员推理", "差分隐私", "联邦学习", "机器学习安全", "深度学习安全", "神经网络鲁棒性", "AI伦理", "算法公平性"],
    "区块链": ["智能合约", "共识算法", "DeFi安全", "NFT安全", "加密货币", "区块链隐私", "侧链", "跨链", "区块链扩容", "DAO安全"],
    "物联网": ["IoT设备安全", "工业控制系统", "车联网安全", "智能家居", "传感器安全", "边缘计算", "RFID安全", "无线传感网", "智能电网", "医疗设备安全"],
    "云计算": ["云存储安全", "多租户安全", "虚拟机安全", "容器编排", "微服务安全", "无服务器安全", "云原生安全", "混合云安全", "云合规", "云取证"],
    "隐私保护": ["匿名通信", "Tor网络", "混合网络", "k-匿名", "l-多样性", "t-接近性", "本地差分隐私", "同态加密", "安全多方计算", "隐私计算"]
}

# 定义问题类型
problem_types = [
    "如何设计安全的{}系统？",
    "{}中的隐私泄露风险如何防范？",
    "如何提升{}的安全性能？",
    "{}面临的主要威胁有哪些？",
    "如何实现{}的安全认证？",
    "{}的漏洞检测方法有哪些？",
    "如何构建可信的{}环境？",
    "{}的安全评估标准是什么？",
    "如何防范{}中的攻击？",
    "{}的安全架构如何设计？"
]

# 定义作者姓名
authors = [
    "David Wagner", "Dawn Song", "Adrian Perrig", "Dan Boneh", "Vitaly Shmatikov",
    "Helen Wang", "XiaoFeng Wang", "Wenke Lee", "Giovanni Vigna", "Christopher Kruegel",
    "Somesh Jha", "Barton Miller", "Trent Jaeger", "Patrick McDaniel", "William Enck",
    "Ahmad-Reza Sadeghi", "N. Asokan", "Srdjan Capkun", "Stefan Savage", "Vern Paxson",
    "Niels Provos", "Thorsten Holz", "Lorenzo Cavallaro", "Tudor Dumitras", "Mathias Payer",
    "Cristina Nita-Rotaru", "Radu State", "Roberto Perdisci", "Michalis Polychronakis", "Angelos Keromytis"
]

# 定义会议和年份范围
conferences = {
    "CCS": list(range(2007, 2023)),
    "NDSS": list(range(1996, 2024)),
    "SP": list(range(1980, 2023)),
    "USENIX_Security": list(range(2006, 2024))
}

# 定义论文标题模板
paper_templates = [
    "{}: A Comprehensive Analysis",
    "Towards Secure {} Systems",
    "Breaking {} Security: New Attack Vectors",
    "Defending Against {} Attacks",
    "Privacy-Preserving {} Techniques",
    "Scalable {} Security Solutions",
    "Automated {} Vulnerability Detection",
    "Machine Learning for {} Security",
    "Formal Verification of {} Protocols",
    "Real-world {} Security Assessment"
]

def generate_filters():
    filters = []
    no = 451  # 从当前最后一个编号开始
    
    # 为每个技术领域生成filters
    for domain, keywords in tech_domains.items():
        for keyword in keywords:
            for conf, years in conferences.items():
                for year in years:
                    # 生成论文标题
                    paper_title = random.choice(paper_templates).format(keyword)
                    author = random.choice(authors)
                    problem = random.choice(problem_types).format(keyword)
                    
                    # 模式1: 年份+关键字
                    query_zh = f"帮我查询{year}年{keyword}安全技术方向的所有论文"
                    query_en = f"Help me find all papers on {keyword} security technology from {year}"
                    filters.append([no, query_zh, query_en, paper_title])
                    no += 1
                    
                    # 模式2: 作者+关键字
                    query_zh = f"帮我查询{author}在{keyword}安全研究方向的论文"
                    query_en = f"Help me find papers by {author} on {keyword} security research"
                    filters.append([no, query_zh, query_en, paper_title])
                    no += 1
                    
                    # 模式3: 会议+关键字
                    query_zh = f"{conf}中涉及到{keyword}技术的论文有哪些"
                    query_en = f"What papers in {conf} involve {keyword} technology"
                    filters.append([no, query_zh, query_en, paper_title])
                    no += 1
                    
                    # 模式4: 会议+问题
                    query_zh = f"{conf}中针对\"{problem}\"这一问题的解决方案"
                    query_en = f"Solutions for \"{problem}\" in {conf}"
                    filters.append([no, query_zh, query_en, paper_title])
                    no += 1
                    
                    # 模式5: 会议/年份+关键字+问题
                    query_zh = f"{conf}/{year}中{keyword}领域针对\"{problem}\"的研究"
                    query_en = f"Research on \"{problem}\" in {keyword} field at {conf}/{year}"
                    filters.append([no, query_zh, query_en, paper_title])
                    no += 1
                    
                    # 控制总数量
                    if no >= 3000:
                        return filters
    
    return filters

if __name__ == "__main__":
    # 读取现有的filters
    existing_filters = []
    try:
        with open('filters_test.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            existing_filters = list(reader)
    except FileNotFoundError:
        existing_filters = [['no', 'query{zh}', 'query{en}', 'answer']]
    
    # 生成新的filters
    new_filters = generate_filters()
    
    # 合并并写入文件
    all_filters = existing_filters + new_filters
    
    with open('filters_test_3000.csv', 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(all_filters)
    
    print(f"已生成{len(all_filters)-1}个filters组合")
