{"text": "# Introduction to Secure Collaborative Intelligence (SCI) Lab  \n\nPu Duan   \nAnt Group   \nSunnyvale, CA, USA   \n<EMAIL>  \n\n# ABSTRACT  \n\nWith the rapid development of technology, user privacy and data security are drawing much attention over the recent years. On one hand, how to protect user privacy while making use of customers’ data is a challenging task. On the other hand, data silos are becoming one of the most prominent issues for the society. How to bridge these isolated data islands to build better AI and BI systems while meeting the data privacy and regulatory compliance requirements has imposed great challenges.  Secure Collaborative Intelligence (SCI) lab at Ant Group dedicates to leverage multiple privacy-preserving technologies on AI and BI to solve these challenges. The goal of SCI lab is to build enterprise-level solutions that allow multiple data owners to achieve joint risk control, joint marketing, joint data analysis and other cross-organization collaboration scenarios without compromising information privacy or violating any related security policy. Compared with other solution providers, SCI lab has been working with top universities and research organizations to build the first privacy-preserving open platform for collaborative intelligence computation in the world. It is the first platform that combines all three cutting-edge privacypreserving technologies, secure multi-party computation (MPC), differential privacy (DP) and trusted execution environment (TEE) that are based on cryptography, information theory and computer hardware respectively, on multi-party AI and BI collaboration scenarios. During multi-party collaboration, all inputs, computations and results are protected under specific security policy dedicatedly designed for each data owner. At this time, the platform has been applied to various business scenarios in Ant group and Alibaba Group, including joint lending, collaborative data analysis, joint payment fraud detection, etc. More than 20 financial organizations, have been benefited from the secure data collaboration and computing services provided by SCI lab.  \n\n# Author Keywords  \n\nMPC, Differential Privacy, TEE, Artificial Intelligence, Business Intelligence  \n\n# BIOGRAPHY  \n\nDr. Pu Duan has. been a twenty-year veteran on cryptography, information security and networking security. He received his B.E. from Xian Jiaotong University in 2001. From 2002 to 2005, he was a researcher in Nanyang Technology University in Singapore, working on research of elliptic curve cryptography and ID-based digital signature and authentication protocols. From 2006 he started his Ph.D. research in Department of Computer Science and Engineering at Texas A&M University, dedicating to protocol design of cryptographic privacypreserving attribute matching and computation protocols. He joined Cisco in 2011 after graduating from Texas A&M University, led the research and development of new cryptographic algorithms for TLS.13 on cisco firewall product. Currently Dr. Duan works in SCI lab as a senior staff engineer, leadings the team on research and implementation of privacypreserving technologies for our platform.  \n\n![](/tmp/output/163_20250326103392/images/25c67465cbcf34546f19308fc01a5788eaa59d1acd7924c4a5ab9797a8a7693c.jpg)  "}