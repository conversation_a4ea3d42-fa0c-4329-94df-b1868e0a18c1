{"text": "# Fuzzy Labeled Private Set Intersection with Applications to Private Real-Time Biometric Search  \n\n<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Georgia Institute of Technology  \n\nhttps://www.usenix.org/conference/usenixsecurity21/presentation/uzun  \n\nThis paper is included in the Proceedings of the 30th USENIX Security Symposium. August 11–13, 2021 978-1-939133-24-3  \n\nOpen access to the Proceedings of the 30th USENIX Security Symposium is sponsored by USENIX.  \n\n# Fuzzy Labeled Private Set Intersection with Applications to Private Real-Time Biometric Search  \n\nErkam Uzun <PERSON>   \nGeorgia Institute of Technology Georgia Institute of Technology Vladimir <PERSON>   \nGeorgia Institute of Technology Georgia Institute of Technology  \n\nWenke Lee Georgia Institute of Technology  \n\n# Abstract  \n\nThe explosive growth of biometrics use (e.g., in surveillance) poses a persistent challenge to keep biometric data private without sacrificing the apps’ functionality.  \n\nWe consider private querying of a real-life biometric scan (e.g., a person’s face) against a private biometric database. The querier learns only the label(s) of a matching scan(s) (e.g. a person’s name), and the database server learns nothing.  \n\nWe formally define Fuzzy Labeled Private Set Intersection (FLPSI), a primitive computing the intersection of noisy input sets by considering closeness/similarity instead of equality.  \n\nOur FLPSI protocol’s communication is sublinear in database size and is concretely efficient. We implement it and apply it to facial search by integrating with our fine-tuned toolchain that maps face images into Hamming space.  \n\nWe have implemented and extensively tested our system, achieving high performance with concretely small network usage: for a 10K-row database, the query response time over WAN (resp. fast LAN) is $146\\mathrm{ms}$ (resp. $47\\mathrm{ms}$ ), transferring 12.1MB; offline precomputation (with no communication) time is 0.94s. FLPSI scales well: for a 1M-row database, online time is 1.66s (WAN) and 1.46s (fast LAN) with 40.8MB of data transfer in online phase and 37.5s in offline precomputation. This improves the state-of-the-art work (SANNS) by $9-25\\times$ (on WAN) and $1.2-4\\times$ (on fast LAN).  \n\nOur false non-matching rate is $0.75\\%$ for at most 10 false matches over 1M-row DB, which is comparable to underlying plaintext matching algorithm.  \n\n# 1 Introduction  \n\nRecent advances in deep learning (DL)-based biometric identification have made possible real-time identification of persons in footage collected by surveillance equipment. The trend toward real-time surveillance in public and private places (e.g., streets, city halls, airports, retail stores, pharmacies, gas stations etc.) has immense benefits for public safety or customer convenience. However, adoption of these technologies come at a significant privacy cost, which raises serious objections.  \n\nTo our knowledge, existing biometrics surveillance systems have the following major challenges. First, vendors store and process the collected biometric data on their server in plaintext for the ease of deployment and practicality. Second, people cannot opt-out of these systems, since video footage (or any captured faces) are directly uploaded to a remote server.  \n\nIdentifying “persons of interest” may be warranted [65], but tracking everybody else in the process may open the doors to illegitimate surveillance and certain human right abuses [67]. In response, privacy stakeholders are pressing for a moratorium on permanent adoption of these systems, and in fact they have already succeeded in banning facial surveillance in several countries and U.S. states [10, 64, 66].  \n\nIn this paper, we propose a middle ground solution, privacypreserving biometric search. Here the server $\\mathcal{S}$ holding a large biometric database with corresponding labels (e.g., identities) should learn nothing about the query or the result, while the querier (client $C$ ) should learn nothing about the database besides the label(s) of the query’s match(es).  \n\nA similar problem of exact private match is extensively studied in a variety of scenarios (e.g., contact list discovery and online dating services), and can be achieved via (labeled) private set intersection (LPSI), a standard primitive [16,17,41, 49]. Even though the state-of-the-art CHLR18 [16] achieves a practical efficiency with communication costs sublinear to DB size, LPSI cannot directly be applied to our problem because it targets exact matches, while biometrics are noisy (e.g., due to lighting, imprecise scans, etc.).  \n\nWe introduce FLPSI: a fuzzy LPSI protocol for fast privacypreserving biometric search. We address a number of technical challenges in protocol/definition design and formal proofs.  \n\nTo our knowledge, none of the prior work related to fuzzy matching achieves practical efficiency for real-time surveillance, mainly because of communication growing (at least) linearly with database size (see Sect. 2 and Sect. 11.5 for the related work). For example, two protocols of the state-of-theart (SANNS [15]) require 1.7-5.4 GB communication and 15.1-41.7 sec. online response times over WAN per query over a million-row database.  \n\nWe follow a much more scalable approach that reduces our fuzzy matching problem to an easier exact-matching subproblems that could be solved with communication cost sublinear in DB size, by leveraging optimizations of the state-of-theart (L)PSI techniques [16, 17]. The crux of our solution is twofold. First, we translate the closeness (e.g., in Euclidean space) of two biometrics into a t-out-of- $T$ set-based matching without sacrificing accuracy. That is, we encode a given biometric input into a set of $T$ items, s.t. the two sets will likely have at least $t$ exactly common items iff the biometrics are of the same person. Second, we build an efficient threshold set-matching protocol from fully homomorphic encryption (FHE), garbled circuits (GC) and $t$ -out-of- $T$ secret sharing, and solve several challenges in definitional approach.  \n\n# 1.1 Summary of Our Contributions  \n\n• We describe and formally define the functionality and security of Fuzzy Labeled Private Set Intersection (FLPSI). We build a FLPSI protocol using the AES blockcipher, homomorphic encryption, garbled circuits and $t$ -out-of- $T$ secret sharing. We prove the security in the semi-honest model. • We show how to interpret closeness (e.g., in Euclidean space) between biometric inputs as t-out-of-T exact set-item matchings without sacrificing the accuracy. • We give simulation-based FLPSI security definition (prior definitions of fuzzy primitives are game-based). • We introduce a number of optimizations, in addition to the prior (L)PSI techniques we use. • We extensively evaluate our protocol in different settings. We achieve 1.66s online running time over WAN with 40.8MB transfer per query over a million-row database. • We systematically compare our design with prior art, and outperform all of them in their best settings, often by several orders of magnitude both in run time and communication. For example, on the largest dataset (of 10M records), we speed up by a factor of $3.33\\times$ and decrease the overall data communication by a factor of up to $48{-}452\\times$ compared to the two protocols of the state-of-the-art, SANNS [15]. • We highlight sublinear and concretely very small network use of our solution. In contrast with most other related work, our solution will scale on very small-bandwidth networks.  \n\n# 2 Related Work  \n\nAs noted above, (L)PSI protocols [16, 17, 41, 49] and other exact-match protocols are inapplicable in our setting.  \n\nFreedman et al. [29] informally introduced the problem of private fuzzy search as an application of their private matching protocol. They proposed a basic construction, and left improving its efficiency as future work.  \n\nWe now discuss state of the art techniques in fuzzy search.  \n\nThreshold Matching. The works [11, 18, 76] are based on threshold t-out-of-T matching outlined in [29]. These constructions incur (at least) linear in DB size and concretely inefficient communication and computation. We compare them in detail in Sect. 11.5 and Fig. 12 and show that they do not scale to a million-row DB.  \n\nA related line of work uses threshold over Euclidean or Hamming distance or cosine similarity between players’ vectors [4, 5, 23, 34, 47, 53, 75]. While these works are generally faster than [11,18,76], our solution is still orders of magnitude more efficient. We provide detailed performance comparison in Sect. 11.5 and Fig. 11.  \n\nOur solution, also based on threshold $t\\cdot$ -out-of-T matching, must overcome the technical difficulties of i) high variability (and hence high distance) of feature vectors of the same person, and ii) large size of extracted feature vectors (hence high costs). We resolve both by carefully integrating fine-tuned random private subsampling of the feature vectors prior to computing threshold match (see Sect. 5).  \n\nNearest Neighbor Search (NNS). A related line of work, albeit solving a different problem from the privacy perspective, is secure NNS. Indeed, NNS may (and is expected to) return match(es) for a person who is not present in DB; hence NNS does not meet our security requirements. However, we compare to NNS solutions as well, as they are close enough in spirit to our application scenario, and they are faster than prior work on private fuzzy search discussed above. Note, we do not consider outsourcing-based NNS [72, 78, 79, 81, 82] as they require a third party who learns the query ( [52] requires two non-colluding servers). State-of-the-art optimized NNS [15] (SANNS) relies on a fast network connection (up to 56 gigabit/s) for efficiency as they transfer 1.7-5.4 GB to run a 10-NNS over a database of a million entries (6.1-57.7 GB over a database of 10 million entries). Hence, SANNS is not practical enough for real-time tasks at scale. We give a detailed comparison to SANNS in Sect. 11.5.  \n\nFinally, we mention, but do not discuss in detail, work on fuzzy searchable encryption [6,43], as they address a different setting where the querier owns the data stored on an untrusted server (i.e. non-private DB).  \n\n# 3 Overview and Technical Details  \n\nHere we review existing non-private (plaintext) fuzzy matching algorithms and building privacy protection into them.  \n\n# 3.1 Plaintext Fuzzy Matching  \n\nExisting facial surveillance systems, informally, work as follows. A client $c$ captures facial images of people from a surveillance video footage, then transmits the biometric data to a server $\\mathcal{S}$ with transport encryption, and $\\mathcal{S}$ receives the data in plaintext. Then, the server uses a DL system to turn raw biometric readings into embedding vectors with a (probabilistic) guarantee that two such vectors will be close in Euclidean distance iff they are from the same person. If the server finds such a close biometric entry in its database, it returns the label (e.g, identity) of the entry to the client. Otherwise, it returns “no match” result to the client. In our evaluation, we used FaceNet [55], which is the state-of-the-art DL system achieving at most $0.67\\%$ for 10 false matches per query over 1M-row DB. See Fig. 7.  \n\n![](/tmp/output/211_20250325173461/images/2aed4218fb31d7227f79f936880e008abdeb423a3f753c1a90a6e14c0b6daa85.jpg)  \nFigure 1: Overview of FLPSI. For clarity, subsampling is depicted without AES encryption and 2PC.  \n\nPrivacy concerns. Clearly, since the data is typically processed in plaintext by the server, it achieves maximal privacy, while the client achieves none. Next, we discuss achieving maximal client privacy as well.  \n\n# 3.2 Private Fuzzy Matching  \n\nOur goal is to build a protocol that reveals labels of query matches only to $\\boldsymbol{c}$ , while maintaining confidentiality of $\\displaystyle{C^{*}}$ ’s query and $\\mathcal{S}$ ’s database. To achieve this, $\\boldsymbol{c}$ and $\\mathcal{S}$ can locally compute DL embeddings from their biometric data, then apply standard MPC tools to compute Euclidean (or cosine similarity) distance between the $\\boldsymbol{c}$ ’s query and each of the $\\mathcal{S}$ ’s database items [4, 5, 23, 34, 53]. However, this does not scale (cf Fig. 11). Our much more scalable approach is based on a t-out-of-T matching scheme, described in detail next. Fig. 1 shows a high-level overview of our FLPSI protocol.  \n\nBinary encoding. To accommodate t-out-of- $T$ matching, we first address the incompatibility between DL embeddings (operating in Euclidean space) and the crypto components (operating in Hamming space) of our protocol. (Operating in Hamming space, e.g., computing closeness is much cheaper in MPC). To do this, parties additionally apply a space mapping function, which is based on locality-sensitive hashes [13, 36, 38, 51], to convert the embedding vectors into bio-bit vectors $(x_{i}$ and $y$ ) with the desired property (they are Hamming-close if they originate from the biometrics of the same person). This is also used as a dimension reduction process in scalable image search applications [42, 46, 77]. We note that there are different DL-based algorithms that generate binary representations directly from the raw data [21, 39, 70].  \n\nWe omit exploring the best algorithm, and refer to [71] for a comprehensive survey. For lack of space, we present our space mapping technique from [68] in Appx. A. We will refer to the set of functions converting biometric data into bio-bit vectors, as ${}^{\\leftarrow}E n c o d e(.){}^{,}$ .  \n\n$\\boldsymbol{c}$ and $\\mathcal{S}$ proceed as follows after encoding their biometric data into bio-bit vectors $y$ (held by $C$ ) and $x_{i}\\in\\mathcal{X}$ (held by $S$ ). • Subsampling: generate $T$ random subsamples of $y$ and each $x_{i}$ bio-bit vectors (in the same way, e.g., $x_{21}=x_{2}\\wedge$ $m a s k_{1}$ ), s.t. at least $t$ of them will be the same iff $y$ and a $x_{i}$ belong to the same person (if bio-bit vectors are Hammingclose, this can be achieved whp); • Secret sharing: generate $t$ -out-of- $T$ secret shares of the label $l_{i}$ (e.g., identity) of each $x_{i}\\in\\mathcal{X}$ (each share is associated with a subsample of $x_{i}$ ), s.t. any $t$ shares can reconstruct $l_{i}$ ; • STLPSI: interactively execute a private t-out-of- $T$ matching protocol (Set Threshold LPSI, or STLPSI) on the $\\boldsymbol{C}$ ’s subsample set and the $\\mathcal{S}$ ’s sets of (subsample, secret share) pairs1: the label $l_{i}$ of an $x_{i}\\in\\mathcal{X}$ is revealed to $\\boldsymbol{c}$ iff at least $t$ of the subsamples of $y$ and $x_{i}$ are equal (which means $\\boldsymbol{c}$ obtains shares of matching subsamples of $x_{i}$ ).  \n\nOur private matching achieves false positive and negative rates equal to the state-of-the-art plaintext algorithms.  \n\n# 3.2.1 Our Solutions to Technical Challenges  \n\nNow we discuss the most interesting technical challenges.  \n\nSubsample confidentiality As described above, $c$ learns the subsamples (and respective subsampling masks), which may help $\\boldsymbol{c}$ learn something additional about database. For example, in case of a false-positive match, the semi-honest $c$ will now learn with confidence positions in bio-bit vector, thereby learning the original biometric, which may not be included in the result set. Further, it may be the case (and publicly known) that faces in $\\mathcal{S}$ ’s database are similar (e.g. manifested by certain positions of the bio-bit vectors being equal). A match from $\\displaystyle{C^{*}}$ s query will inform the malicious $c$ how to set the bits of his next query so as to improve his chance of “hitting” a face in database (a false match).  \n\nWe can resolve this by operating over encrypted subsamples only. For this, $\\mathcal{S}$ chooses the random subsampling/projection masks and an AES encryption key $k_{S}$ . Then $\\mathcal{S}$ via MPC allows $c$ to compute the AES-encryptions of masked projections on the $\\displaystyle{C}^{\\mathrm{{\\prime}}}$ ’s query bio-bit vector $y$ , while keeping the projection masks and $k_{S}$ secret from $\\boldsymbol{c}$ . The server efficiently computes AES-encryptions of masked projections on its large database non-interactively in $O(|\\boldsymbol{x}|)$ . Note that $\\mathcal{S}$ has to refresh these masks and keys for each execution.  \n\nConcealing partial matches in single execution. (L)PSI protocols (e.g., [16, 17, 41, 49]) do not directly implement the above STLPSI functionality since they, by design, reveal partial (below-threshold $t$ ) matches.We resolve this by building effient STLPSI from t-out-of- $T$ secret sharing and FHE, based on prior (L)PSI works (e.g., CHLR18 [16]).  \n\nConcealing partial matches in repeated executions. This subtle issue arises when generated shares are not refreshed between queries, and $\\boldsymbol{c}$ may collect threshold $t$ shares across queries. We resolve this by carefully resetting secret shares, subsampling masks and keys in each execution.  \n\nNovel definitional approach. In MPC, the preferred simulation-based security definitions offer clean and composable guarantees. At the same time, they require precise specification of ideal-world behavior, which we (as a community) do not know how to achieve for biometric functions. Because of this, biometric authentication definitions are usually game-based and not composable, but which allow to bound, rather than precisely specify adversary success.  \n\nOne of our contributions is a novel definitional approach (see Section 7.1), which allows the best of both worlds: our definition is indeed simulation-based, and yet we bound adversary success rather than exactly specifying it. Our definition is generic and incorporates optional leakage, which is often needed for efficient sublinear protocols. We believe this definitional approach can serve as a template in defining primitives in the biometric space.  \n\n# 3.2.2 Trust Assumptions and Threat Model  \n\n• $\\boldsymbol{c}$ and $\\mathcal{S}$ locally apply binary encoding to their biometric data. We assume they own the same DL model that is trained on a public dataset and not tailored to any particular user from either party. Hence, we consider membership inference [58] or model inversion [27] attacks to be out of our scope.   \n• Considering our motivating application, we do not discuss here how the query biometric is obtained; we note that face detection in video footage is an easy and solved problem [69].   \n• We prove our 2PC (one $\\boldsymbol{c}$ and one $\\mathcal{S}$ ) in the semi-honest model (parties follow the protocol specification). In particular, parties do not corrupt their inputs (e.g., via a pixel perturbing attack [62]). This models natural scenarios in  \n\npractice, as well as serves as a stepping stone to stronger models, such as handling malicious adversaries. Of course, many practical applications require protection against active cheating. Indeed, the biometric information served by $\\mathcal{S}$ may be highly sensitive, and hence a possibility of data exfiltration by a malicious $\\boldsymbol{c}$ would preclude offering the search service to a broader audience. We leave malicious security as important future work.  \n\nResilience Against Certain Malicious Behaviour. While our protocol is in the SH model, we informally discuss several natural malicious attacks, their impact and mitigation.  \n\nFirstly, $\\mathcal{S}$ can exclude its DB entries from search results simply by sending encryptions of random values. This can be also achieved by appropriate input substitution, and therefore is not an attack in a standalone execution setting. In general, efficiently ensuring that a malicious $\\mathcal{S}$ is unable to omit entries in its DB is a hard technical problem.  \n\nFurther, $c$ can try to learn DB by querying random bitvectors or brute-forcing arbitrary biometric inputs. Bruteforcing is a well-understood attack, which is mitigated by rate-limiting. Querying bit-vectors is not helping $\\boldsymbol{c}$ , since the bit-vector search space is larger than the space of faces.  \n\n# 4 Definition of FLPSI  \n\nIn this section, we define a general syntax for a fuzzy labeled PSI. We start with the notion of closeness (fuzzy matching), adopted from [6].  \n\nDefinition 4.1. Closeness Domain. We say that $\\Lambda=(\\mathcal{D},\\mathrm{Cl})$ is a closeness domain if $\\mathcal{D}$ is a set, and $\\mathrm{Cl}$ is the (partial) closeness function that takes any $x,y\\in{\\mathcal{D}}$ and outputs a member of $\\{\\mathtt{c l o s e},\\mathtt{f a r}\\}$ , so that Cl is symmetric (i.e., $\\operatorname{Cl}(x,y)=\\operatorname{Cl}(y,x))$ .  \n\nThere are no requirements on the output of close for pairs that are “near” (i.e. points that neither close nor far).  \n\nDefinition 4.2. Fuzzy Labeled PSI (FLPSI). FLPSI protocol is defined for a closeness domain $(\\mathcal{D},\\mathrm{Cl})$ and a label space $\\mathcal{L}\\mathcal{S}$ by the interactive algorithm $(C,S)$ , where the client $\\boldsymbol{c}$ inputs a query $q\\in\\mathcal{D}$ , and the server $\\mathcal{S}$ inputs a database $D b=\\{(d_{1},l_{1})..,(d_{N},l_{N})\\}$ , where items $d_{i}\\in\\mathcal{D}$ and labels $l_{i}\\in\\mathcal{L}\\mathcal{S}$ . At the end, $c$ outputs a set $R$ , and $\\mathcal{S}$ outputs $\\perp$ . FLPSI must satisfy the following correctness and security properties:  \n\nCorrectness. We use ε-correctness instead of a perfect correctness, as it is common for biometrics-matching systems to have errors, e.g., false matches $(\\varepsilon_{1})$ and non-matches $(\\varepsilon_{2})$ . Then, it requires that, for $q$ and $D b$ , we have an output set $R$ consisting only of pairs $(q,l_{i})$ such that for each $i\\in[N]^{2}$ ; if $\\mathbf{Cl}(q,d_{i})=\\mathbf{far}$ , then $\\mathrm{Pr}[(q,l_{i})\\not\\in R]\\geq1-\\varepsilon_{1}$ ; if $\\operatorname{Cl}(q,d_{i})=\\mathsf{c l o s e}$ , then $\\mathrm{Pr}[(q,l_{i})\\in R]\\geq1-\\mathfrak{\\varepsilon}_{2}$ , where $(d_{i},l_{i})\\in D b$ .  \n\nIn our construction the domain $\\mathcal{D}$ refers to facial biometrics in a surveillance scenario. Hence, $\\operatorname{Cl}(q,d_{i})=\\mathbf{f}\\mathsf{a r}$ refers to each of $q,d_{i}$ coming from different people, while $\\mathbf{Cl}(q,d_{i})=$ close refers to both of them belonging to the same person. The label space $\\mathcal{L}\\mathcal{S}$ refers to people’s identities or other info. Hence, the client learns the information corresponding to the person in its query, if the photo(s) of this person is in the database.  \n\nSecurity of FLPSI is formally defined in Sect. 7.1 in the simulation paradigm by specifying the ideal functionality. We stress that we separately require $\\Pi_{\\mathrm{FLPSI}}$ to satisfy the above correctness requirement. We present this low-level technical definition discussion together with the proofs in Sect. 7.1, and focus on the protocol description next.  \n\n# 5 Building Blocks of FLPSI  \n\nIn this section, we discuss the ideas behind our protocol and its building blocks (presented formally in Sect. 6).  \n\n# 5.1 Binary Encoding  \n\nOur construction starts with a binary encoding step, where the client and server locally turn each of their raw biometric inputs (e.g., facial photos) $q,d_{i}\\in{\\mathcal{D}}$ , for $i\\in[N]$ , into bio-bit vectors $y=E n c o d e(q)$ and $x_{i}=E n c o d e(d_{i})$ , respectively, so that if there is a $q,d_{i}$ pair of the same person, then $y,x_{i}$ are likely close wrt Hamming distance.  \n\n# 5.2 Subsampling and 2PC  \n\nNow the client and server could apply random projections for each bit vector $y$ and $x_{i}$ , respectively. This outputs a set of subsampled bit vectors, $\\mathcal{Y}=\\left\\{y_{1},...,y_{T}\\right\\}$ and $\\mathcal{X}_{i}=\\{x_{i1},...,x_{i T}\\}$ , with the property that if $y$ and $x_{i}$ are close, then some subsamples of them will likely be the same [12, 20, 68].  \n\n$\\mathcal{S}$ hides the subsampling projections from $c$ to avoid it reconstructing the inputs in the database (see Sect. 3.2.1). To do this, $\\mathcal{S}$ chooses a 128-bit AES blockcipher key $k_{S}$ and generates the projection masks $\\{m a s k_{1},...,m a s k_{T}\\}$ . Note that $\\mathcal{S}$ can locally compute its subsample set $\\mathcal{X}_{i}$ for each of its bio-bit vector $x_{i}$ s.t. $x_{i j}=A E S_{k_{S}}(x_{i}\\wedge m a s k_{j})$ , where $j\\in[T]$ .  \n\nNext both parties execute a 2PC protocol $\\left(C_{A E S},S_{A E S}\\right)$ . This protocol privately computes each $y_{j}\\in\\mathcal{Y}$ s.t. $y_{j}=$ $A E S_{k_{S}}(y\\wedge m a s k_{j})$ for $\\boldsymbol{c}$ , s.t. it can learn matched encrypted subsamples without learning the projection masks and $k_{S}$ .  \n\nWe implement this 2PC protocol as Yao’s Garbled Circuits $(\\mathbf{G}\\mathbf{C})^{3}$ using the EMP toolkit [73]. We use subsamples and encrypted subsamples interchangeably, by referring $\\mathcal{Y}$ , throughout the paper.  \n\n# 5.3 Secret Sharing  \n\nOur construction will use STLPSI (introduced in the next section) along with a $t$ -out-of- $T$ secret sharing scheme, whose syntax, correctness and security we now define.  \n\nDefinition 5.1. t-out-of-T Secret Sharing. A t-out-of-T secret sharing scheme is defined by algorithms (KS,KR) for sharing and reconstructing a secret. The domain of secrets is $\\{0,1\\}^{\\bar{\\mathcal{K}}}$ , where $\\mathcal{K}$ is some parameter. KS takes a secret $s$ and outputs a set $\\left\\{s s_{1},...,s s_{T}\\right\\}$ of shares. KR takes as input a set of shares $\\left\\{s s_{1},...,s s_{d}\\right\\}$ and returns an integer $s\\in\\{0,1\\}^{\\mathcal{K}}$ if $d\\geq t$ , $o r\\perp i f d<t$ .  \n\nCorrectness. For correctness we demand that for any $s\\in$ $\\{0,1\\}^{\\mathcal{K}}$ , any set $S S\\in[\\mathsf{K S}(s)]$ , and $S S_{i}\\subseteq S S$ , where $\\left|S S_{i}\\right|\\geq t$ , it holds that $\\mathsf{K R}(S S_{i})=s$ with probability 1.  \n\nPrivacy. For privacy we demand that for any $s\\in\\{0,1\\}^{\\mathcal{K}}$ and set $S S\\in[\\mathsf{K S}(s)]$ it holds that any subset $S S_{i}\\subseteq S S$ of size $|S S_{i}|<t$ does not give any information about $s$ , i.e., its probability distribution is independent of $s$ .  \n\nIn our protocol, $\\mathcal{S}$ generates $t$ -out-of- $T$ secret shares for the label $l_{i}\\in\\mathcal{L}\\mathcal{S}$ associated with the $i^{t h}$ entry in its database. Note that $\\mathcal{S}$ attaches an agreed-upon token $0^{\\lambda}$ , where $\\uplambda$ is a security parameter, to each label $l_{i}$ before secret sharing it. Then, $\\boldsymbol{c}$ can verify if any set of $t$ shares (obtained via a single STLPSI execution) correctly reconstruct a valid label. Overall, for a given label $l_{i}\\in\\mathcal{L}\\mathcal{S}$ , $\\mathcal{S}$ generates $\\{s s_{i1},...,s s_{i T}\\}\\gets\\mathsf{K S}(0^{\\lambda}\\mid\\mid l_{i})$  \n\n# 5.4 Set Threshold LPSI (STLPSI)  \n\nThough prior steps prepare the inputs to accommodate a t-outof-T matching, existing private $t$ -out-of-T matching schemes are not practical for a real-time surveillance (see Sect. 11.5.1). We require small communication (e.g., under $128\\mathrm{MB},$ ), to support server with a large (1M rows) database and a WAN or less channel to the client.  \n\nA closely related LPSI primitive does achieve above performance [16], but cannot be plugged in directly as a building block to FLPSI, since it does not implement t-out-of-T matching (LPSI reveals below-threshold $t$ matching to the client). It is, however, possible to combine with an appropriate carefully designed secret sharing scheme to achieve this feature as well.  \n\nFor modularity and because STLPSI is a useful primitive, we formalize it and implement it based on prior techniques, mostly drawn from CHLR18. We integrate a number of optimizations specific to our setting, such as different bucketing, removing now redundant preprocessing steps and the use of cuckoo hashing in CHLR18. We prove security of the resulting protocol (Theorem 5.1).  \n\n# 5.4.1 Formal Definition of STLPSI  \n\nIn this section, we formally define a general syntax and correctness for a private $t\\cdot$ -out-of- $T$ matching protocol.  \n\nDefinition 5.2. Set Threshold Labeled Private Set Intersection (STLPSI). STLPSI protocol is defined by the input space MS, label space $\\mathrm{\\mathcal{S}\\mathcal{S}^{4}}$ , threshold values $t,T\\in$ $\\mathbb{N}$ and the interactive algorithm $(\\boldsymbol{C},\\boldsymbol{S})$ . $\\boldsymbol{c}$ inputs a query set $\\mathcal{Y}=\\{y_{1},...,y_{T}\\}\\subset\\mathcal{M}\\mathcal{S}$ , and $\\mathcal{S}$ inputs database sets $\\mathcal{X}=\\left\\{\\mathcal{X}_{1},\\ldots,\\mathcal{X}_{N}\\right\\}$ , where $\\chi_{i}=\\{x_{i1},...,x_{i T}\\}\\subset\\mathcal{M}\\mathcal{S}$ , and $S S=$ $\\{S S_{1},\\ldots,S S_{N}\\}$ , where $S S_{i}=\\{s s_{i1},...,s s_{i T}\\}\\subset{\\mathcal{S}}{\\mathcal{S}}$ . At the end, $c$ outputs a set $R$ , while $\\mathcal{S}$ outputs $\\perp$ .  \n\nCorrectness. We require that $R=\\{r_{1},\\ldots,r_{N}\\}$ s.t. for each $i\\in[N]$ , let $r_{i}^{\\prime}=\\{(x_{i j},s s_{i j}):x_{i j}=y_{j}\\in\\mathcal{Y},s s_{i j}\\in S S_{i}\\}_{j\\in[T]}$ , then we have that $r_{i}=\\left(r_{i}^{\\prime},l_{i}\\right)$ iff $|r_{i}^{\\prime}|\\geq t$ , otherwise $r_{i}=\\emptyset$ . That is, through the set $R$ , $c$ learns such tuples $(x_{i j},s s_{i j})$ and the label $l_{i}$ associated with them iff it gets at least $t$ exactly matching items between sets $\\mathcal{Y}$ and $\\mathcal{X}_{i}$ .  \n\nNow we define the security of STLPSI. Let Π be an STLPSI protocol, defined according to Def. 5.2. The ideal functionality $\\mathcal{F}_{\\mathrm{STLPSI}}$ is defined in Fig. 2. In Appx. B, we recall the standard definition of securely realizing ideal functionality in the semihonest model [24], formulated as Def. B.1 for the 2PC case. Now we can put these together to define security of STLPSI.  \n\nDefinition 5.3. STLPSI Security. We say that a protocol $\\Pi_{\\mathtt{S T L P S I}}=(C,S)$ , defined w.r.t. input space MS and label space SS, is a secure STLPSI protocol (in the semi-honest model) with no leakage if ΠSTLPSI securely realizes (cf. Def. B.1) functionality $\\mathcal{F}_{\\mathrm{STLPSI}}$ of Fig. 2.  \n\n# 5.4.2 Constructing STLPSI Protocol  \n\nFor clarity, we first explain how a private $t\\cdot$ -out-of- $T$ matching works on two sets, $\\mathcal{Y}$ from the client and $\\boldsymbol{\\chi}_{i}\\in\\boldsymbol{\\chi}$ from the server (each with $T$ items) in a strawman design. Then, we introduce our optimizations for an efficient construction.  \n\nStrawman design. First, $c$ and $\\mathcal{S}$ agree on an FHE scheme, where $\\boldsymbol{c}$ generates (public, secret) keys $(p k,s k)$ and sends $p k$ to $\\mathcal{S}$ . $c$ also homomorphically encrypts each set item $y_{j}\\in\\mathcal{Y}$ into a ciphertext $[[y_{j}]]$ and sends to $\\mathcal{S}$ . Then, $\\mathcal{S}$ computes $\\left[\\left[z_{i j}\\right]\\right]=r\\times\\left(\\left[\\left[y_{j}\\right]\\right]-x_{i j}\\right)+s s_{i j}$ under encryption5, where $r\\in_{R}\\mathcal{P}$ and is refreshed for each computation, and $s s_{i j}$ is the secret share (of label $l_{i}$ ) associated with $x_{i j}$ . $\\mathcal{S}$ sends the ciphertext $\\mathbb{[}z_{i j}\\mathbb{]}$ to $c$ . Recall that secret shares are uniformly sampled item Js inK SS (equal to MS). Notice, $z_{i j}=s_{i j}$ iff $y_{j}=x_{i j}$ , Otherwise, $z_{i j}$ is random on SS. Now, it is guaranteed that $\\boldsymbol{c}$ can reconstruct the label $l_{i}$ iff it gets at least $t$ shares of $l_{i}$ (see Def. 5.1). Otherwise, $\\boldsymbol{c}$ learns nothing and cannot distinguish possible below-threshold $t$ matches.  \n\nOptimizations. Applying above evaluation for each DB item does not scale to large DBs (e.g., of a million sets). We adopt the following optimizations from the (L)PSI literature for compressing DB items and reducing the circuit depth in FHE evaluations [9, 16, 17, 28, 29, 48, 49, 60]. With the exception of bucketing, we closely follow CHLR18 [16] in applying the following optimizations:  \n\nPolynomial interpolation is used instead of the above strawman to generate $\\mathbb{[}z_{i j}\\mathbb{]}$ . To do this, $\\mathcal{S}$ interpolates an $N\\cdot$ degree polynomial $P_{j}$ ,  Jby uKsing ( $\\langle\\mathrm{item}\\rangle$ , ⟨secret share⟩) pairs $(x_{i j},s s_{i j})$ s.t. $P_{j}(x_{i j})=s s_{i j}$ . Since $P_{j}(y)=\\alpha_{N}y^{N}+...+\\alpha_{1}y+$ $\\upalpha_{0}$ , where the $\\upalpha_{i}$ could be pre-computed by $\\mathcal{S}$ in the offline phase, $P_{j}$ is homomorphically evaluated in ${\\cal O}(\\log N)$ multiplicative depth given $[[y]]$ . Further, a single $\\mathbb{[}z_{i j}\\mathbb{]}$ now encodes a secret share corresp JonKding to any of the  JmatKching $x_{i j}$ .  \n\nBucketing. Prior exact matching works use different methods (e.g., cuckoo hashing, bloom filters) to bucketize the DB items, so that the query item needs to be compared only with DB items in the same bucket. In our protocol, since each of $T$ set items are generated through different LSH projections, each projection is interpreted as a bucket (with $N$ items). Note that bucketing is a standard PSI technique [50], also used in CHLR18. It improves asymptotic performance, but concretely is costly, as buckets must be padded with dummy element for security. Crucially, this additional bucketing is not needed in our application. As noted above, projections already define the buckets within which the search is performed, and they need not be padded.  \n\nWe combine bucketing with windowing, described next.  \n\nWindowing. Interpolating polynomials over buckets doesn’t scale to large $N$ values (e.g., a million). If $c$ sends the encryptions of y2 , y2 , y2 , $y^{2^{0}}\\bar{,y}^{2^{1}},y^{2^{2}},...,y^{2^{\\overset{.}{\\mathrm{log}}N}}$ , $\\mathcal{S}$ can homomorphically compute all necessary powers of $y$ in $O(\\log\\log N)$ multiplicative depth. This technique decreases $c\\leftarrow S$ communication cost by a factor of $N$ , while increasing the $c\\to S$ cost by a factor of $\\log N$ , which has a small impact on overall communication since $c$ only holds a set of $T$ items.  \n\nSplitting. To speed-up homomorphic evaluations, $\\mathcal{S}$ splits each bucket into $a$ partitions, s.t. it interpolates a $\\frac{N}{a}$ -degree polynomial per partition. This reduces the multiplicative depth to $O(\\log\\log\\frac{N}{a})$ , and the number of $y$ powers ( $\\boldsymbol{c}$ sends to $S$ ) to $\\log{\\frac{N}{a}}$ , but increases the $c\\leftarrow S$ communication by a factor of $a$ as $\\mathcal{S}$ sends results for all partitions to $\\boldsymbol{c}$ .  \n\nBatching. This is a well-known technique in FHE to enable Single Instruction Multiple Data (SIMD) on ciphertexts. For more details and example applications, we refer  \n\nInputs: $c$ inputs set $\\mathcal{Y}=\\{y_{1},...,y_{T}\\}\\subset\\mathcal{M}\\mathcal{S}$ ; $\\mathcal{S}$ inputs   \n$\\mathcal{X}=\\{\\mathcal{X}_{1},...,\\mathcal{X}_{N}\\}$ , where $\\mathcal{X}_{i}=\\{x_{i1},...,x_{i T}\\}\\subset\\mathcal{M}\\mathcal{S}$ , and $S S=$   \n$\\{S S_{1},\\ldots,S S_{N}\\}$ , where $S S_{i}=\\{s s_{i1},...,s s_{i T}\\}\\subset{8}{\\mathcal{S}}$ is the secret   \nshares of $0^{\\lambda}\\parallel l_{i}$ s.t. $\\uplambda$ is a param., $l_{i}\\in\\mathcal{L}\\mathcal{S}$ .   \n1. [FHE parameters] Parties agree on FHE parameters $(m,m_{p},m_{c t},\\mathcal{P})$ for an IND-CPA secure FHE scheme, and on threshold $(t)$ , split $\\textstyle B={\\frac{N T}{m a}}$ NmTa ) and windowing (w ∈ $\\{2^{1},2^{2},\\dots,2^{\\log B}\\}$ parameters. Then, $c$ generates (public, secret) FHE keys $(p k,s k)$ , then sends $p k$ to $\\mathcal{S}$ .   \n2. [Pre-process $\\chi$ and SS] $\\mathcal{S}$ bucketizes $\\chi$ into a table and splits each bucket (column) into $a$ partitions, then interpolates a $B$ - degree polynomial $P_{k}$ for each partition, s.t. $P_{k}(x_{i j})=s s_{i j}$ , where $s s_{i j}$ is the secret share associated with $x_{i j}$ . Then, $\\mathcal{S}$ batches coefficients at corresponding row (of lenght $m$ ) of the table into a plaintext polynomial $p_{k}^{\\ell}$ , where $\\ell\\in B,k\\in a$ .   \n3. [Compute encrypted query from $\\mathcal{Y}]$ $c$ concatenates its input set $\\frac{m}{T}$ times and batches into a plaintext polynomial. Then, it homomorphically encrypts (by using $p k$ ) each windowing power $(w)$ of this plaintext polynomial into the ciphertexts $[[y_{w}]] $ , then sends them to $\\mathcal{S}$ .   \n4. [JHoKmomorphic intersection] $\\mathcal{S}$ , under encryption, i) expands the received $[[y_{w}]] $ to $\\{\\mathbb{[{y_{0}}]},\\mathbb{[{y_{1}}]},\\dots,\\mathbb{[{y_{B}}]}\\}$ ; ii) evaluates $\\left[\\left[z_{k}\\right]\\right]=$ $\\sum_{\\ell=0}^{B}\\mathbb{\\left[y_{\\ell}\\right]}.p_{k}^{\\ell}$ f oJr eKach $k\\in a$ , JandK sendJs allK ciphertexts $\\mathbb{I}z_{k}\\mathbb{I}$ toK $c$ after applying noise flooding and modulus switching on them.   \n5. [Decrypt and get result] $c$ decrypts (by using $s k$ ) ciphertexts $\\mathbb{I}z_{k}\\mathbb{I}$ to obtain result vector $z_{k}$ (of length $m$ ) for each partition $k\\in a$ . Now, each item of $z_{k}$ will be the evaluation of $P_{k}(y_{j})=$ $s s_{i j}$ iff there is any $x_{i j}=y_{j}$ in partition $k$ of corresponding bucket, otherwise the item will be a random element in $\\mathbb{F}_{\\mathcal{P}}$ .   \n6. [Reconstruct label] $c$ runs KR algorithm on each $\\binom{T}{t}$ combination of consecutive $T$ items of $z_{k}$ , and gets a reconstruction result $s_{i}$ . We have $r_{i}^{\\prime}=\\{(x_{i j},s s_{i j}):x_{i j}=y_{j}\\in\\mathcal{Y},s s_{i j}\\in$ $S S_{i}\\}_{j\\in[T]}$ . Then $s_{i}=0^{\\uplambda}\\parallel l_{i}$ and $r_{i}=\\left(r_{i}^{\\prime},l_{i}\\right)$ iff $|r_{i}^{\\prime}|\\geq t$ , otherwise $s_{i}$ is a random element from $\\mathbb{F}_{\\mathcal{P}}$ (see Def. 5.1) and $r_{i}=\\emptyset$ . $0^{\\lambda}$ validates a label $l_{i}$ in DB.   \nOutput: $c$ outputs a result set $R=\\{r_{1},...,r_{N}\\}$ , where each $r_{i}\\neq\\emptyset$  \n\nis recovered in Step $6.5$ outputs $\\bot$ .  \n\n# Figure 3: STLPSI protocol ΠSTLPSI  \n\n[8, 17, 30, 31, 60]. In this work, we specifically use SIMD batching from FHE library SEAL [56]. To accommodate it, $\\mathcal{S}$ groups coefficients, associated with the same powers of $y_{1},y_{2},\\dotsc,y_{T}$ from different buckets, into vectors of length $m$ . Since $m$ is parameterized as $m\\gg T$ , $\\mathcal{S}$ also concatenates coefficients from $\\frac{m}{T}$ partitions. This means batching $\\frac{m}{T}$ sets into a single vector, that decreases each partition size to NmTa . Finally, $\\boldsymbol{c}$ concatenates its set $\\frac{m}{T}$ times and batches into a plaintext polynomial, then it computes all windowing powers of it and sends encryptions of them to $\\mathcal{S}$ . Overall, batching decreases i) the FHE multiplicative depth to $\\begin{array}{r}{O(\\log\\log\\frac{N T}{m a})}\\end{array}$ ; ii) the number of $y$ powers ( $\\boldsymbol{c}$ sends to $S$ ) to $\\log{\\frac{N T}{m a}}$ ; and iii) $c\\leftarrow S$ communication by a factor of $\\frac{m}{T}$ .  \n\nNoise flooding. $\\mathcal{S}$ re-randomizes the returned ciphertexts by adding fresh encryptions of zero with large noise [22].  \n\nThis results in increased FHE parameters. See Sect. 11.2.  \n\nModulus switching. This is a technique that FHE scheme allows to reduce the complexity of a ciphertext at some degrees [9]. In our design, $\\mathcal{S}$ performs SEAL’s modulus switching on encrypted results before sending them to $\\boldsymbol{c}$ .  \n\nAfter receiving the evaluation results, the client decrypts each of them to $\\frac{m}{T}$ sets (each with $T$ results). Then, it runs the reconstruction algorithm KR from Def. 5.1 on $\\binom{T}{t}$ combinations of each set and obtains a label $l_{i}$ iff at least $t$ query subsamples match with the ones from $i^{t h}$ database set.  \n\n# 5.4.3 Full Protocol and Security Proof of STLPSI  \n\nOur STLPSI protocol is formally presented in Fig. 3  \n\nTheorem 5.1. In the presence of a semantically secure fully homomorphic encryption and t-out-of-T secret sharing schemes, the ΠSTLPSI protocol of Fig. 3 is a secure (in the semi-honest model) STLPSI protocol with no leakage if each of the server’s input sets of labels $S S_{i}\\in S S$ for $i\\in[N]$ are: • randomly sampled (and unknown to $c$ ) $t\\cdot$ -out-of- $T$ Shamir’s secret shares of $0^{\\lambda}\\parallel l_{i}$ , where $\\uplambda$ is a security parameter and $l_{i}\\in\\mathcal{L}\\mathcal{S}$ is the label associated with $i^{t h}$ database record; • the domain of secret shares SS is equal to the domain $\\mathbb{F}_{\\mathcal{P}}$ of the underlying fully homomorphic encryption scheme.  \n\nProof. The intuition for the protocol security is presented above in Sect. 5.4.2. For space, we formally prove security of our protocol ΠSTLPSI w.r.t. Def. 5.3 in Apx. C.1. 口  \n\n# 6 FLPSI Protocol  \n\nIn Sect. 3.2 we present the technical intuition of our ΠFLPSI protocol. Fig. 4 formalizes that discussion and presents $\\Pi_{\\mathrm{FLPSI}}$ for the closeness domain $(\\mathcal{D},\\mathrm{Cl})$ and label space $\\mathcal{L}\\mathcal{S}$ . The protocol uses the building blocks AES blockcipher, t-out-of- $T$ secret sharing scheme (KS, KR), 2PC protocol $\\left(C_{A E S},S_{A E S}\\right)$ , and STLPSI protocol $\\left(C_{S T L P S I},S_{S T L P S I}\\right)$ .  \n\nIn the protocol, Encode is an algorithm that generates $\\mathcal{L}$ -bit bit vector for an input from $\\mathcal{D};k_{S}$ is 128-bit AES blockcipher key; $T$ is number of subsamples; $t$ is matching threshold; $\\uplambda$ is a security parameter; and $\\wedge$ is “logical and” operation, used in subsampling function, i.e., $A E S_{k_{S}}(y\\wedge m a s k_{j})$ .  \n\nThe outputs of both $\\mathcal{S}$ ’s subsampling in Step 3 and $\\left(C_{A E S},S_{A E S}\\right)$ (Step 5), and the input items of $C_{S T L P S I}$ and SSTLPSI should be in the same domain MS. Moreover, the output of secret sharing KS (Step 4) and the input labels of SSTLPSI should be from the same domain SS.  \n\n# 6.1 Instantiating FLPSI Protocol  \n\nWe now discuss specific instantiations of $\\Pi_{\\mathrm{FLPSI}}$ building blocks, tailored for our use case. Discussion of low-level protocol and subprotocol parameters is presented in Sect. 11.2.  \n\nInputs: $c$ inputs query $q\\in\\mathcal{D}$ ; $\\mathcal{S}$ inputs a database $D b=$   \n$\\{(d_{1},l_{1}),...,(d_{N},l_{N})\\}$ , where each $d_{i}\\in\\mathcal{D}$ and label $l_{i}{\\in}\\mathcal{L}\\mathcal{S}$ .   \n1. [Encode] Parties agree on function Encode : $\\mathcal{D}\\rightarrow\\{0,1\\}^{\\mathcal{L}}$ . $c$ computes $y=E n c o d e(q)$ , and $\\mathcal{S}$ computes $x_{i}=E n c o d e(d_{i})$ for each $i\\in[N]$ .   \n2. [Init] The server $\\mathcal{S}$ samples an AES key $k_{\\mathcal{S}}\\stackrel{\\mathcal{S}}{\\leftarrow}\\{0,1\\}^{128}$ and $T$ projection masks $\\{m a s k_{1},\\dots,m a s k_{T}\\}$ .   \n3. [Server’s local subsampling] The server generates subsample set $\\mathcal{X}_{i}=\\{x_{i1},...,x_{i T}\\}$ , where $x_{i j}\\in\\mathcal{M}\\mathcal{S}$ such that $x_{i j}=$ $A E S_{k_{S}}(x_{i}\\wedge m a s k_{j})$ for each $i\\in[N]$ and $j\\in[T]$ .   \n4. [Secret sharing] $\\mathcal{S}$ generates a secret share set $S S_{i}={}$ $\\{s s_{i1},\\dots,s s_{i T}\\}\\xleftarrow{\\mathrm{s}}\\mathsf{K}\\mathsf{S}(0^{\\uplambda}\\mid\\mid l_{i})$ for each $i\\in[N]$ , where $s s_{i j}\\in\\mathcal{S}\\mathcal{S}$ , $0^{\\lambda}$ is an agreed token, and $l_{i}$ is the $i^{t h}$ label.   \n5. [Client’s 2PC oblivious subsampling] $c$ and $\\mathcal{S}$ run $\\left(C_{A E S},S_{A E S}\\right)$ , where $\\mathit{C_{A E S}}$ inputs $y,S_{A E S}$ inputs $k_{S}$ and $\\{m a s k_{1},\\dots,m a s k_{T}\\}$ . Then, $\\mathit{C_{A E S}}$ learns the subsample set $\\mathcal{Y}=\\{y_{1},...,y_{T}\\}$ , where $y_{j}\\in\\mathcal{M}\\mathcal{S}$ s.t. $y_{j}=A E S_{k_{S}}(y\\wedge m a s k_{j})$ for each $j\\in[T]$ , and $S_{A E S}$ learns $\\bot$ .   \n6. [STLPSI execution] $c$ and $\\mathcal{S}$ run $\\'{\\cal C}_{S T L P S I},S_{S T L P S I})$ , where $C_{S T L P S I}$ inputs $\\mathcal{Y}$ , and $\\mathcal{S}_{S T L P S I}$ inputs $\\mathcal{X}=\\{\\mathcal{X}_{1},...,\\mathcal{X}_{N}\\}$ , and $S S=\\{S S_{1},\\ldots,S S_{N}\\}$ . At the end, $\\mathcal{S}$ learns $\\bot$ , and $c$ learns a set $R$ as per Definition 5.2. I.e., we have $r_{i}^{\\prime}=\\{(x_{i j},s s_{i j}):x_{i j}=$ $y_{j}\\in\\mathcal{Y},s s_{i j}\\in S S_{i}\\}_{j\\in[T]}$ for each $i\\in[N]$ , and $r_{i}=\\left(r_{i}^{\\prime},l_{i}\\right)$ iff $|r_{i}^{\\prime}|\\geq t$ , otherwise $r_{i}=\\emptyset$ . Then, $R=\\{r_{1},...,r_{N}\\}$ .   \nOutput: For each $r_{i}\\in R$ , $r_{i}\\neq\\emptyset$ , $c$ outputs $l_{i}$ label recovered in   \nStep 6. $\\mathcal{S}$ outputs $\\perp$ .  \n\n# Figure 4: FLPSI protocol ΠFLPSI  \n\nIn the binary encoding step, $c$ and $\\mathcal{S}$ locally i) encode their raw biometrics $\\overset{\\cdot}{q}$ and $d_{i}$ , resp.) into embedding vectors, by using the state-of-the-art DL model (e.g., FaceNet [55]); and ii) translate the Euclidean space into Hamming, by using SuperBit Locality Sensitive Hash (SBLSH) [38] (see Appx. A), that converts DL embeddings into bio-bit vectors $y$ and $x_{i}$ , resp.).  \n\nNext, following Sect. 5.2, $c$ and $\\mathcal{S}$ generate their subsample sets $\\mathcal{T}$ and $\\mathcal{X}_{i}$ , resp.). Recall that, $\\mathcal{S}$ generates each projection mask, by randomly choosing $\\mathcal{N}_{s b}$ ones, which essentially turns all other bits into a constant zero.  \n\nBefore each protocol execution, $\\mathcal{S}$ regenerates the projection masks, AES encryption key $k_{S}$ , and secret shares of each label $l_{i}$ for each DB record (by using $t\\cdot$ -out-of- $T$ Shamir’s secret sharing scheme [57]), so that $\\mathcal{S}$ ensures that $c$ is not able to use any information seen in a prior execution.  \n\nFinally, parties run STLPSI protocol in Fig. 3, where $\\boldsymbol{c}$ inputs its subsamples, and $\\mathcal{S}$ inputs subsamples and their associated secret shares for each DB record. At the end, $\\mathcal{S}$ learns nothing and $c$ learns the label $l_{i}$ (and matching items) of $i^{t h}$ database record iff the $i^{t h}$ record has at least $t$ matching subsamples with the $\\displaystyle{C^{*}}$ ’s set.  \n\nCorrectness. The protocol works correctly with small false matching $(\\varepsilon_{1})$ and non-matching $(\\varepsilon_{2})$ error probabilities. Since we can only empirically estimate these errors, we defer this analysis to Sect. 11.2. In summary, we target to get maximum error rates of $\\mathfrak{E}_{1}=0.001$ and $\\mathfrak{E}_{2}=0.01$ for the smallest DB.  \n\n# 7 Security Analysis of FLPSI Protocol  \n\nWe start our analysis by formally defining security of FLPSI.   \nWe then state and prove security in Sect. 7.2.  \n\n# 7.1 Security Definition of FLPSI  \n\nSECURITY DEFINITION DISCUSSION. Following the common approach to modeling security of 2PC, we use the idealreal paradigm and consider static security against a semihonest adversary that can corrupt at most one participant.  \n\nWe need to define an ideal functionality for FLPSI and what it means for a protocol to securely realize it. An ideal functionality takes inputs from the parties, computes the desired parties’ outputs, and returns them to the parties, along with the corresponding leakage (if any). We require that the view of each party in real protocol’s execution is indistinguishable from the view produced by the ideal-world simulator.  \n\nThis is a common general approach, which, unfortunately, does not fit FLPSI and many natural biometric functionalities. The difficulty we are facing is that the ideal functionality must precisely match what happens in the real world. In particular, the parties’ outputs should have the same distribution in both worlds on all inputs. In our case this would mean that the ideal functionality specifies the exact probability of any two close elements correctly identifying as close by the protocol, as well as the probability of far elements correctly identifying as far. This is unrealistic to achieve for real-world settings, such as facial biometrics we focus on.  \n\nThis complication is avoided in game-based definitions, where no ideal functionality is defined, and hence there is no need to explicitly specify it. Indeed, security there can be defined as an adversary unable to succeed (e.g., learn something forbidden) with probability above a certain threshold. However, they will not guarantee that absolutely nothing additional is revealed, and such protocols are not freely composable – these are features of ideal-real style definitions.  \n\nOur approach. We reconcile the yin and yang and achieve the best of both by defining the ideal FLPSI functionality via a reference to a real FLPSI protocol $\\Pi_{\\mathrm{FLPSI}}$ . Namely, we will say that ideal functionality outputs whatever the real $\\Pi_{\\mathrm{FLPSI}}$ formally outputs. Recall, in our case (cf Def. 4.2), it is the set $R$ or pairs $(q,l_{i})$ . While at the first glance this may seem a tautology, this approach does provide a guarantee that nothing beyond the explicit protocol output is revealed. Now we are in a good place, since we can easily control explicit protocol output by specifying the correctness property. Indeed, Def. 4.2 requires (modulo errors bounded by ε) that the $\\boldsymbol{c}$ outputs close labels, and in particular does not output far labels or any other information it may have learned from the view of execution.  \n\nIn sum, the correctness requirement of Def. 4.2 guarantees that $\\Pi_{\\mathrm{FLPSI}}$ ’s syntactic output only contains allowed records; the simulation-based component guarantees that no additional information (beyond the above output) is revealed. Put together, this makes a composable and usable security definition. We are not aware of this definitional approach being used in prior work, and believe it can be broadly useful, especially working with biometrics.  \n\n![](/tmp/output/211_20250325173461/images/30d71cfb188d08c84ac89dafc43747faafac5ac822e30aa33f450eb3afad1786.jpg)  \nFigure 5: The Ideal Functionality $\\mathcal{F}_{\\mathrm{FLPSI}}^{\\mathcal{L},\\Pi}$  \n\nWe caution the reader that the correctness requirement – and hence our definition – are not perfect. A “bad” protocol may exploit the allowed small manipulations of probability of returning each particular record and leak unauthorized information to $c$ . However, in FLPSI (in contrast, e.g., to MPC of approximations [26]), correctness condition is restrictive and severely limits possible leakage: we return input DB records which cannot be modified to leak. Moreover, our definition can be further tightened, e.g. by correctness requiring that a record return probability does not change if some other DB record changes. Formal exploration of this new definitional style for fuzzy MPC is an interesting future research direction.  \n\nWe parameterize the security definition with a leakage profile describing leakage of information to the parties. This is common in the searchable encryption but is somewhat novel for MPC-style definitions. Our construction will have very limited leakage to $\\boldsymbol{c}$ : a measure of closeness of $\\displaystyle{C^{*}}$ s input with $\\mathcal{S}$ ’s entry it matched; no leakage in case of no match.  \n\nFORMAL FLPSI SECURITY DEFINITION. Let $\\Pi$ be an FLPSI protocol for a closeness domain $(\\mathcal{D},\\mathrm{Cl})$ and label space $\\mathcal{L}\\mathcal{S}$ , defined according to Def. 4.2. Let $\\mathcal{L}=\\{\\mathcal{L}_{C},\\mathcal{L}_{S}\\}$ be the leakage profile describing leakage to $\\boldsymbol{c}$ and $\\mathcal{S}$ . The ideal functionality $\\mathcal{F}_{\\mathrm{FLPSI}}$ is defined in Figure 5. Then, the security of FLPSI is formally defined as follows.  \n\nDefinition 7.1. FLPSI Security. We say that a protocol $\\Pi_{\\mathtt{F L P S I}}=(C,S)$ defined w.r.t. the closeness domain $(\\mathcal{D},\\mathrm{Cl})$ is a secure FLPSI protocol (in the semi-honest model) with leakage profile $\\mathcal{L}=\\{\\mathcal{L}_{C},\\mathcal{L}_{S}\\}$ , if ΠFLPSI securely realizes (cf. Def. B.1) functionality F FLL,PΠSIFLPSI of Fig. 5.  \n\n# 7.2 Security Theorem and Proof of FLPSI  \n\nWe now formally state the security theorem of FLPSI construction, instantiated with secure 2PC protocols $\\left(C_{A E S},S_{A E S}\\right)$ and $\\left(C_{S T L P S I},S_{S T L P S I}\\right)$ , and Shamir’s $t$ -out-of- $T$ secret sharing scheme. We also state the leakage profile of $\\Pi_{\\mathrm{FLPSI}}$ , then prove the security theorem of it in the semi-honest model.  \n\nTheorem 7.1. Assume AES is a pseudorandom function (PRF). In the presence of secure (in the semi-honest model) 2PC protocols $\\left(C_{A E S},S_{A E S}\\right)$ and $\\left(C_{S T L P S I},S_{S T L P S I}\\right)$ , and a tout-of-T secret sharing scheme, the ΠFLPSI protocol of Fig. 4 is a secure (in the semi-honest model) Fuzzy LPSI protocol with leakage profile $\\mathcal{L}=\\{\\mathcal{L}_{C},\\mathcal{L}_{S}=\\bot\\}$ .  \n\nLeakage $\\mathcal{L}_{S}$ to $\\mathcal{S}$ in $\\Pi_{\\mathrm{FLPSI}}$ . There is no leakage to $\\mathcal{S}$ . Leakage $\\mathcal{L}_{C}$ to $\\boldsymbol{c}$ in ΠFLPSI. ΠFLPSI reveals to the client a measure of quality of the match with the database entry (i.e., the number of (obliviously) encrypted matching subsamples). In case of multiple matches, the client also learns which (obliviously) encrypted subsamples matched (i.e. were common) across the different matched database entries.  \n\nRecall that our initial privacy goal is to achieve client privacy, which is satisfied by revealing and leaking nothing to the server. Furthermore, we emphasize the leakage to the client is strictly less (and in fact much less) than the client learning the matching database entry(ies) (or its bit vector) of the server. It is easy to see that this leakage is inferred from the matching entry(ies) held by the server. Inspecting the relevant portion of the proof, it is easy to see that the $\\mathrm{Sim}_{C}$ actions informed by leakage can be easily performed without $\\mathcal{L}_{C}$ , and with the knowledge of the matching database entries of the server.  \n\nIn a typical scenario, where parties share all the information/data about matches (e.g., photos, name, age, etc. of a person of interest) with each other, this leakage does not have any security impact on the desired system.  \n\nProof. For lack of space, we formally prove the security of our main protocol $\\Pi_{\\mathrm{FLPSI}}$ w.r.t. Def. 7.1 in Apx. C.2. 口  \n\n# 8 Complexity Analysis of FLPSI  \n\nIn Apx. D, we explain our communication and computation complexity in detail. In summary, FLPSI has $\\mathrm{O}(\\textstyle{\\frac{\\mathrm{NT}}{\\mathrm{mB}}}\\ell)$ and $\\mathrm{O}({\\frac{\\mathrm{NT}}{m}})$ communication and computation complexities, respectively. In the notations, $N$ is database size, $T$ is number of subsamples, $m$ is SIMD vector size, $B$ is the size of each DB split and $\\ell$ is the length of an item in MS and SS. Note that $m B$ could be parameterized to be (almost) equal to $N$ (see Sect. 11.2) if we are not considering a database of, e.g., hundreds of millions of people. Then, our communication complexity would approximate to $\\mathrm{O}(T\\ell)$ in practice.  \n\n# 9 Environment and Implementation Details  \n\nWe use an Azure F72s_v2 instance, which has 72 virtual cores equivalent to that of $2.7\\:\\mathrm{GHz}$ Intel Xeon Platinum 8168 CPU and $144\\mathrm{GB}$ of RAM each. We also have two sets of experiments: for fast and slow network connections between $c$ and $\\mathcal{S}$ . While the former has $500\\mathrm{MB/s}$ connection with 0.5 ms latency, the latter is having $40\\mathrm{MB/s}$ with $34~\\mathrm{ms}$ latency. We use Ubuntu 18.04 in this instance. Note that, even though, our design does not require a fast network connection or high number of threads, we use above environment for creating an identical comparison setting with the state-of-the-art [15].  \n\nWe implement our protocol on top of the homomorphic encryption library SEAL v3.5 [56], through Brakerski/FanVercauteren (BFV) scheme [25]. To extract embedding vectors from facial images, we use the Python implementation of FaceNet6 (with the Inception-Resnet-V1 architecture [63]) after aligning faces, as recommended in [80].  \n\n# 10 Optimizing FLPSI Implementation  \n\nIn addition to applying optimization tricks to compress the database and reduce the homomorphic multiplication depth in STLPSI, as explained in Sect. 5.4.2, we further optimize our protocol for better performance and accuracy as follows.  \n\nNoise reduction (NR) in binary encoding. Inspired by [7, 59, 68], the client can extract multiple face samples from a short surveillance video in order to perform noise removal. This can be done very seamlessly at some specific application scenarios. Since people cannot be completely in the same pose throughout a video recording, $c$ can treat each individual frame in a video as a different sample. On the other hand, $\\mathcal{S}$ can capture multiple samples per person more conveniently since it may have a controlled environment unlike $\\boldsymbol{c}$ .  \n\nIn this optimization, both parties take bit vectors, generated in the binary encoding step (from Sect. 5.1) through multiple biometric readings, and majority vote over each bit. If a certain amount of them agree (e.g., at least 90 percent), they keep it. Otherwise, they cancel (zero-out) it. After eliminating noisy bits, the residual bit vector is given to the subsampling layer.  \n\nSubsample compression. Since we use AES blockcipher with 128-bit key $k_{S}$ , we can compress its inputs to 128 bits to avoid multiple rounds of block-ciphering. This will reduce the online communication and computation costs of the 2PC subsampling protocol from Sect. 5.2. To do this, we can effectively compress a subsample as it mostly contains zero bits, e.g., only 14-out-of-256 bits are ones in our setting, as follows. We split the bio-bit vector into 128-bit of chunks, and evenly subsample each chunk (e.g., 7-out-of-128) without colliding subsampled bits across the chunks. For instance, if $8^{t h}$ bit is subsampled in the first chunk, we do not subsample $8^{t h}$ bit of the second chunk. Finally, we compute the bit-wise XOR of all chunks as the compressed output.  \n\nOptimizing STLPSI (load balancing). We introduce a new optimization to balance the loads across the buckets in $\\mathcal{S}$ ’s reconstructed database (see Sect. 5.4.2). We decrease the number of partitions, as argued next. Note that a certain subsample(s) may be the same for too many DB entries, while the rest are shared by less of them. Also notice, it is not mathematically possible to build a (Lagrange or Newton) interpolation polynomial over such (item, secret share) pairs, where any two items are the same [44]. That is why $\\mathcal{S}$ has to put each of the colliding subsamples into distinct partitions, and thus there is an unavoidable lower bound on the number of partitions, and accordingly, on the computation and communication costs. In STLPSI, before building the database, $\\mathcal{S}$ truncates such (subsample, secret share) pairs after reaching a certain collision threshold, which balances the load of the each bucket of its constructed coefficient table. In Sect. 11.4, we empirically show the impact of this optimization on the overall costs.  \n\n<html><body><table><tr><td>Par.</td><td>Description</td><td>Value</td></tr><tr><td>T</td><td>matching threshold numberofsubsamples</td><td>2 64</td></tr><tr><td>C qu Nsb</td><td>length of bio-bit-vectors consistency thresholdratio number of subsampled bits</td><td>256 0.9 14</td></tr><tr><td>ks P 入</td><td>S's key for a AES blockcipher prime mod. of domain Fp security param. for token O^</td><td>{0,1}128 8519681 [log P]=23</td></tr><tr><td>N</td><td>numberofdatabase entry</td><td>[10K-10M]</td></tr></table></body></html>\n\nFigure 6: List of parameters and their fixed values.  \n\n# 11 Evaluation  \n\nIn this section, we extensively evaluate our protocol, and then systematically compare it to the prior art. Note that we achieve our results by applying all optimizations.  \n\n# 11.1 Datasets  \n\nEvaluation datasets. We use a DL model that is pre-trained on the MSCeleb1M dataset, including over 8 million unconstrained facial images of around 100 thousand identities [33].  \n\nQuery set. We use the YouTube Faces (YTF) benchmark dataset, that contains noisy collections of unconstrained facial videos from 1,595 public figures [74]. Since the preprocessing may use multiple biometric scans per person to generate reliable bio-bit vectors, we randomly pick (at most) ten frames each for $\\boldsymbol{c}$ and $\\mathcal{S}$ to test ε errors. We assume $c$ always queries these 1,595 people over any size of DB in our experiments.  \n\nDatabase set. We generate photo-realistic synthetic faces to create large-scale databases since there is no such big public datasets. We use StyleGAN [40] to create databases of 10 thousand (Face-10K), 100 thousand (Face-100K) and one million (Face-1M) identities along with the YTF identities (with isolated samples from the query set).  \n\nComparison datasets. For our comparative analysis, we use AT&T [54] and Deep1B [3] datasets, which are used in prior art. Note that we use these datasets in the same way as they are used in the prior art. AT&T7 includes 400 facial images from 40 people, where 8 faces of each (320 in total)  \n\n<html><body><table><tr><td rowspan=\"2\">#of false matches</td><td colspan=\"2\">FRR(%)for Plaintext /FLPSI</td></tr><tr><td>Face-10K</td><td>Face-100K Face-1M</td></tr><tr><td>1</td><td>2.89/2.95 2.93/2.97</td><td>2.99/3.01</td></tr><tr><td>2</td><td>1.62/1.65</td><td>1.86/1.95 2.13/2.18</td></tr><tr><td>3</td><td>1.26/1.32</td><td>1.64/1.66 1.97/2.01</td></tr><tr><td>4</td><td>1.06/1.14 1.39/1.42</td><td>1.55/1.56</td></tr><tr><td>5</td><td>0.92/1.01 1.14/1.18</td><td>1.18/1.25</td></tr><tr><td>6</td><td>0.81/0.85 0.94/0.97</td><td>1.06/1.12</td></tr><tr><td>7</td><td>0.72/0.77 0.83/0.86</td><td>0.92/0.94</td></tr><tr><td>8</td><td>0.56/0.59 0.74/0.79</td><td>0.87/0.92</td></tr><tr><td>9</td><td>0.53/0.58 0.69/0.74</td><td>0.73/0.79</td></tr><tr><td>10</td><td>0.51/0.56 0.58/0.63</td><td>0.67/0.75</td></tr></table></body></html>\n\nFigure 7: FRRs of underlying plaintext matching system and FLPSI protocol for at most 10 false matches per query errors.  \n\nare kept as database items and 2 faces of each are queried. Deep1B contains a billion image descriptors (each 96 dimension vector), which is generated by passing images through a deep neural network [3]. We use the original query set, which includes 10 thousand data points, published by the authors8. And, we conduct queries over two subsets of Deep1B that consist of randomly selected one million and 10 million entries (labeled as Deep1B-1M and Deep1B-10M, respectively). We treat Deep1B descriptors as embedding vectors in our pipeline since it is not a facial dataset.  \n\n# 11.2 Parameters  \n\nIn the following, we introduce the parameters and our parameter selection process. Note that once we fix our parameters, we use them without changing across different experiments.  \n\nε-correctness errors. These refer to the errors in the ε- correctness of FLPSI. Recall from the Sect. 4 that, $\\varepsilon_{1}$ infers the false matches, and $\\varepsilon_{2}$ infers the false non-matches (or, false rejection rate − FRR). Interpreting in our context, false matches denotes the number of different identities obtained other than the queried one, while false non-matches standing for the number of “not exist” results in response to querying existing people in the database.  \n\nIn our experiments, we target to get at most 10 false matches and $1\\%$ false non-match rate for any of the database sizes, which meets accuracy requirement of the commercial systems [2, 32, 45].  \n\nParameter choices for the targeted errors. In the following, we summarize our parameter searching method to find the ones achieving the targeted errors.  \n\nIn Fig. 6, in addition to $t$ and $T$ , we enumerate and describe all parameters $(\\mathcal{L},\\mathfrak{r}_{r b},\\mathcal{N}_{s b})$ required in DL, SBLSH and NR steps, which affect the errors. We first search the parameters for the plaintext baseline to see if we can obtain the targeted errors without enabling privacy-preserving blocks. We search (following Apx. E) and fix our parameters to the values in Fig. 6, then use them for all the experiments below.  \n\nParameter choices for privacy-preserving blocks. The parameters of BFV scheme are three integers $(m_{p},m_{c t},\\mathcal{P})$ where $m_{p}$ is the polynomial modulus degree , $m_{c t}$ is the ciphertext modulus and $\\mathcal{P}$ is the plaintext modulus [14]. We initialize $m_{p}=2^{13}$ , $m_{c t}=218$ bits and $\\mathcal{P}=8519681$ to always achieve at least a 128-bit security level as recommended in [14]. These parameters allow us to perform a standard noise flooding operation as part of our STLPSI protocol (see Sect. 5.4.2). The LWE estimator9 by Albrecht et al. [1] suggests $128-131$ bits security level for this setting. We switch the ciphertext modulus from 218 to 55 bits in the modulusswitching step to decrease the communication size from $\\mathcal{S}$ to $c$ . For the parameters such as standard deviation error and secret key distribution we use the default values of SEAL. We set the SIMD vector size to $m=8192$ , and the size of the token $0^{\\lambda}$ to 23 bits (at most), which is the same length of the labels of database records.  \n\nAchieved errors for the fixed parameters. After fixing the parameters, we measure the errors of end-to-end FLPSI protocol to see if it holds our ε-correctness requirement. Fig. 7 shows the FRRs per query for the targeted false-matches (at most 10 per query for any DB size). Note that these error rates have implications on the confidentiality of DB, and nothing relevant to the query data, which is the first privacy goal of our protocol. As mentioned before, revealing false matches (e.g., within industrial standards [2, 32, 45]) to the client is allowed since it is unavoidable in desired application. Having said that, though FLPSI slightly increases the FRR errors compared to underlying plaintext system (due to the reason explained in Sect. 10), it still holds the correctness for all settings.  \n\n# 11.3 Costs of FLPSI  \n\nFig. 8 shows experimental results of FLPSI protocol. For each database size $N$ , it presents the storage needs and preprocessing times for the offline phase, total online communication overhead, and end-to-end online computation times for different number of threads (Th). We report total response times for the fast and slow network configurations, introduced in Sect. 9. For clarity, we discuss the results of a single query over Face-1M dataset in the following. We average over 100 queries for the FLPSI results.  \n\n# 11.3.1 Offline Preprocessing Cost of FLPSI  \n\nWe run a one-time initialization phase to compile the DB from facial images. We do not include this cost in our summary tables. Our protocols refresh $t$ -out-of- $T$ secret sharings and AES blockcipher key $k_{S}$ (both held by $S$ ) per query. This is performed solely by $\\mathcal{S}$ in expectation of the query. This cost is easily amortized (run concurrently) with an actively executing query, and we report it as an offline cost. In our experiments, $\\mathcal{S}$ needs at most $501\\mathrm{MB}$ of storage and 37.5 sec. to pre-compute and buffer a copy of constructed database of 1M entries. We include buffer reading time in the following online evaluations.  \n\nFigure 8: FLPSI results (per query). The best computation times are in bold-face, and the best computation speed-ups are measured against the single-threaded results. Total response times are reported under the last two columns for fast/slow networks.   \n\n\n<html><body><table><tr><td rowspan=\"3\">Database</td><td colspan=\"2\">Offline</td><td rowspan=\"3\">Online comm.</td><td colspan=\"8\">Online response time (milliseconds)</td></tr><tr><td rowspan=\"2\">Storage (MB)</td><td rowspan=\"2\">Preprocess time (s.) (MB)</td><td rowspan=\"2\"></td><td colspan=\"7\">Computation time with different number of threads</td></tr><tr><td>Th=1 8</td><td>16</td><td>32</td><td>64 72</td><td>Sp-up</td><td></td><td>Bestquery fast slow</td></tr><tr><td>Face-10K</td><td>5</td><td>0.94</td><td>12.1</td><td>523</td><td>93</td><td>68</td><td>46</td><td>57</td><td>56</td><td>11.4x</td><td>47 146</td></tr><tr><td>Face-100K</td><td>51</td><td>4.07</td><td>20.4</td><td>4457</td><td>635</td><td>376</td><td>257</td><td>241</td><td>186</td><td>24.0x</td><td>187 386</td></tr><tr><td>Face-1M</td><td>501</td><td>37.5</td><td>40.8</td><td>43956</td><td>5944</td><td>3058</td><td>1828</td><td>1647</td><td>1355</td><td>32.4x</td><td>1455 1655</td></tr></table></body></html>  \n\n<html><body><table><tr><td>Step</td><td>Party</td><td>Run time percent</td></tr><tr><td>Building encrypted query Homomorphic evaluation Decrypting query results Extracting matches</td><td>C S C</td><td>3.66% 91.6% 3.79%</td></tr></table></body></html>\n\nFigure 9: Run time percent. of steps in a query over Face-1M.  \n\n# 11.3.2 Online Communication Cost  \n\nWe have a fixed $\\mathbf{\\Lambda}^{\\prime}8.5\\mathbf{MB}$ per query) communication cost from obliviously extracting the subsamples of a single biobit-vector of the client through the 2PC $\\left(C_{A E S},S_{A E S}\\right)$ protocol. Hence, this cost is independent from the database size. FLPSI achieves at most ${\\bf40.8~M B}$ per query communication cost, which shows that we are not relying on the fast network connection for efficiency. The last two columns of Fig. 8 show that data communications increase from 100 to $300~\\mathrm{{ms}}$ (at most) even if we switch from the fast to slow network connection. This is our major advantage compared to prior art (see Sect. 11.5). Hence, we can conclude that FLPSI is compatible with the existing network infrastructures of potential clients in the desired surveillance scenario.  \n\n# 11.3.3 Online Computation Cost  \n\nEven in the single-threaded execution scenario, FLPSI achieves promising performance (at most 44 seconds). Given that, since we spend most of the time for homomorphically evaluating the polynomials on the server side, as presented in Fig. 9, we can use multi-threading to speed up this computation. Note that setting up a powerful server could be more applicable than providing fast network connections (e.g., in gigabit scale) for every client. Using 72 threads achieves $32.4\\times$ faster computation compared to using a single thread. Moreover, since $\\mathcal{S}$ concurrently evaluates partitions, which could be less than the number of threads for small databases,  \n\n<html><body><table><tr><td rowspan=\"2\">Database</td><td colspan=\"2\">Communication</td><td colspan=\"2\">Response time (fast/slow)</td></tr><tr><td>(MB)</td><td>Saving</td><td>(seconds)</td><td>Speed up</td></tr><tr><td>Face-10K</td><td>72</td><td>6×</td><td>2.12/2.33</td><td>4.1×/3.7x</td></tr><tr><td>Face-100K</td><td>528</td><td>26x</td><td>17.8/21.7</td><td>4.0×/4.7x</td></tr><tr><td>Face-1M</td><td>2124</td><td>52x</td><td>189/199</td><td>4.3×/4.5x</td></tr></table></body></html>  \n\nFigure 10: FLPSI per query results taken without load balancing the server’s buckets. Data communications are reduced by saving factors, and response times are improved by speed up factors with optimizations.  \n\ncomputation time does not decrease linearly (or increases) as $\\mathcal{S}$ uses more threads.  \n\nBest end-to-end timing: In Fig. 8, we show the best achievable response times for each of the database sizes at the last two columns. Overall, by using multi-threading, FLPSI can privately search a single person over a database of a million people in 1.46 sec. and 1.66 sec. with fast and slow network connections, respectively. To the best of our knowledge, this is the fastest response time compared to prior art, with similar functionality, in a desired application scenario.  \n\n# 11.4 Impact of Load Balancing Optimization  \n\nIn the following, we explain how we decrease the overall communication and computation costs, through the optimization from the Sect. 10. To do this, we repeat the experiments without applying this optimization, whose results are presented in Fig. 10. Then, we compare them with those in Fig. 8. For clarity, we only report total communication overheads and single threaded response times. To show the impact of our optimization, we also report the achieved saving factors in communication and speed ups in computation costs, by comparing optimized and non-optimized results. Overall, we reduce the communication overheads up to $52\\times$ and speed up the response times up to $4.3/4.5\\times$ on fast/slow networks.  \n\n# 11.5 End-to-end Comparison with Prior Art  \n\nIn this section, we systematically compare FLPSI with previous private fuzzy matching protocols. Considering their functionality and security guarantees for our application scenario, we group prior art in two categories: i) threshold matching and ii) $k$ -nearest neighbor search. In (i), as in our work, $\\mathcal{S}$ may return empty result (depending on the $\\varepsilon_{1}$ error) to $c$ if no close entry exists in the database. In (ii), $\\mathcal{S}$ always guarantees to return $k$ database entries to $c$ regardless of the query. While (ii) is a different functionality, we compare our work with protocols in both categories, as the state-of-the-art (SANNS [15]) in (ii) is also faster than protocols in (i), and is the fastest among protocols “close enough in spirit”.  \n\n<html><body><table><tr><td rowspan=\"2\">Protocol</td><td colspan=\"2\">Communication</td><td colspan=\"2\">Resp. time (fast)</td></tr><tr><td>(MB)</td><td>Saving</td><td>(sec.)</td><td>Speed up</td></tr><tr><td>FLPSI</td><td>0.39</td><td></td><td>0.014</td><td></td></tr><tr><td>Yasuda et al. [75]</td><td>9.92</td><td>25.5x</td><td>1.70</td><td>121x</td></tr><tr><td>Huang et al. [34]</td><td>17.9</td><td>46.0x</td><td>6.08</td><td>434×</td></tr><tr><td>Osadchy et al. [47]</td><td>35.2</td><td>90.3x</td><td>99.2</td><td>7086x</td></tr><tr><td>Blanton et al.[5]</td><td>2.8</td><td>7.18x</td><td>9.37</td><td>669x</td></tr><tr><td>Barni et al. [4]</td><td>9.11</td><td>23.4x</td><td>16.0</td><td>1110x</td></tr><tr><td>Sadeghiet al.[53]</td><td>2.8</td><td>7.18x</td><td>15.5</td><td>1286x</td></tr><tr><td>Erkin et al.[23]</td><td>7.3</td><td>18.7x</td><td>18.0</td><td>1143x</td></tr></table></body></html>\n\nFigure 11: Comparing FLPSI with existing distance thresholding protocols. Communication costs and response times per query over AT&T database. †Costs are scaled for AT&T database based on reported results in cited works.  \n\nAs discussed earlier, we do not compare with exact matching protocols (e.g., (L)PSI protocols from [16, 17, 41, 49]), as they do not support fuzzy matches. We solve a much harder problem than exact matching.  \n\n# 11.5.1 Comparison to Threshold Matching Approaches  \n\nAs discussed in Sect. 2, prior art either a) applies thresholding to computed Euclidean (or Hamming and cosine similarity) distance [4, 5, 23, 34, 47, 53, 75], or b) runs t-out-of- $T$ matching [11,18,29,76] between query and database (feature) vectors. Though they satisfy the functionality requirement and security guarantees for our application scenario, none of them propose a practically applicable system for a real-time surveillance task.  \n\nDistance thresholding approaches. Fig. 11 compares concrete costs of FLPSI to prior work [4, 5, 23, 34, 47, 53, 75]. Note that the cited works report communication and computation costs linear in the database size. They achieve between 1.7-99.2 sec. response times and $2.8{-}35.2\\mathrm{MB}$ network overheads per query over AT&T database.  \n\nFurther, majority of them do not satisfy our ε-correctness requirements. We achieve $\\mathbf{121-7086}\\times$ faster response time $14\\mathrm{ms}$ . per query) and ${\\bf7.18}–{\\bf90.3}\\times\\bf{]}$ less communication for the same database, while meeting our ε-correctness requirements. Note that we consider single threaded execution for all works, but could not execute them in the exact same environment. However, since all run on similar clock speeds, our achieved speed-ups would slightly vary on the same environment.  \n\nt-out-of-T matching approaches. Systems [11,18,76] (referred as $\\mathrm{{CH}_{1}}^{10}$ , YSPW, CEC, resp.) are existing, secure, t-outof- $T$ protocols. Fig. 12 compares asymptotic communication and computation complexity of [11, 18, 76] to our system. FLPSI behaves better both in computation and communication than $\\mathrm{{CH}_{1}}$ , YSPW, and CEC protocols, as both of their communication and computation complexities are linear in database size. Further, computation and communication of CEC [11] are linear also with the domain size. In concrete terms, CEC reports 3GB communication for a database of $100T$ -dimension vectors, where each vector item could be one of 4 distinct letters. Thus, CEC does not scale for our case (FLPSI operates in a domain with over $2^{23}$ integers). $\\mathrm{CH}_{1}$ [18] and YSPW [76] do not report concrete costs.  \n\n<html><body><table><tr><td>Protocol</td><td>Communication</td><td>Computation</td></tr><tr><td>FLPSI CEC [11] YSPW[76] CH1 [18]</td><td>(1)O~() O(N|Fp|e) O(NT²0) O(NTE)</td><td>O(NT) O(N(|Fp|+T)T) O(N(poly(T)+T2T/))</td></tr></table></body></html>\n\nFigure 12: Comparing FLPSI with existing $t$ -out-of- $T$ protocols that are still considered secure. Only the dominant terms are kept for all protocols. $\\ell$ is the size of a ciphertext in the chosen encryption scheme. $\\mathrm{T}_{\\mathrm{\\varepsilon}}^{\\prime}$ is the time needed for all homomorphic operations in a single cycle.  \n\n# 11.5.2 Comparison to kNNS Approaches  \n\nWe emphasize that “k-nearest neighbor search” protocols solve a somewhat related, yet different problem, and do not meet the security guarantees we consider. Nevertheless, we compare them to FLPSI because we wish to present a broader perspective and to illustrate that our work is more efficient not only than protocols for our exact problem, but than any prior work “close enough in spirit.”  \n\nkNNS is related to FLPSI. Before discussing performance, we briefly explain the relevance of kNNS to our setting. Indeed, a protocol returning a nearest neighbor could be used to construc a (leaky) FLPSI, e.g. as follows: $c$ and $\\mathcal{S}$ run 1NNS. $c$ obtains the output and checks if it meets the threshold of FLPSI before returning it (causing leakage to $c$ if it does not). To search and return multiple matches, $\\boldsymbol{c}$ and $\\mathcal{S}$ could either proceed iteratively, increasing $k$ by a small amount, or guess a larger $k$ and risk higher leakage.  \n\nPerformance comparison. We compare our design with Chen et al. [15]’s two protocols since, to our knowledge, they are the fastest protocols compared to all other kNNS approaches [19, 35, 37, 61], which do not use a trusted thirdparty in their pipelines.  \n\n[15] show (at least) $8{-}31\\times$ faster response times compared to optimally implemented prior art. They propose an optimized linear scan (SANNS-linear) and an approximate search (SANNS-approx) protocols, which are built upon additive homomorphic encryption, garbled circuits and oblivious read only memory, to conduct secure kNNS over large databases.  \n\nFigure 13: Comparing FLPSI to two protocols of SANNS [15]. Best achieved response times are reported for fast/slow networks.   \n\n\n<html><body><table><tr><td rowspan=\"2\">Protocol</td><td colspan=\"4\">Deep1B-1M</td><td colspan=\"4\">Deep1B-10M</td></tr><tr><td>Communication</td><td></td><td>Response time (fast/slow)</td><td></td><td>Communication</td><td></td><td>Response time (fast/slow)</td><td></td></tr><tr><td></td><td>Total</td><td>Saving</td><td>(seconds)</td><td>Speed up</td><td>Total</td><td>Saving</td><td>(seconds)</td><td>Speed up</td></tr><tr><td>FLPSI</td><td>40.8MB</td><td></td><td>1.46/1.66</td><td></td><td>128MB</td><td></td><td>12.7/13.5</td><td></td></tr><tr><td>SANNS-linear</td><td>5.39GB</td><td>132x</td><td>5.79/41.7</td><td>3.97/25.1x</td><td>57.7GB</td><td>452x</td><td>73.1/446</td><td>5.76/33.0x</td></tr><tr><td>SANNS-approx</td><td>1.72GB</td><td>42x</td><td>1.70/15.1</td><td>1.16/9.09x</td><td>6.07GB</td><td>48x</td><td>5.27/41.8</td><td>0.41/3.10x</td></tr></table></body></html>  \n\nTo conduct an almost identical comparison, we evaluate FLPSI on the same Azure instances with the same fast/slow network connections, as introduced in Sect. 9, and over the same image datasets: Deep1B-1M and Deep1B-10M.  \n\nCommunication and computation costs. Fig. 13 compares total communication overheads and the best achieved response times through the fast/slow networks for the both database sizes. Due to our sublinear communication, FLPSI decreases required bandwidth by $132{\\scriptstyle-452\\times}$ and $\\mathbf{42-48\\times}$ (depending on the database size) compared to SANNS’s linear and approximate protocols, respectively. This implies significant improvement in wall-clock time, especially on slower networks. In fact, SANNS outperforms FLPSI only on Deep1B-10M dataset, with fast network connection, and via its approximate algorithm. For instance, the best response time of SANNS-approx protocol increases from 1.7 to 15.1 sec. as we switch the network from fast to slow connection. Similarly, SANNS-linear’s performance decreases even more in the same situation, as it has more data overhead than their approximate protocol. On the other hand, FLPSI preserves its performance regardless of the network connection, as it has $128\\mathrm{MB}$ of communication overhead even for a database of 10 million entries. Overall, we achieve up to ${\\bf5.8}/33\\times$ and $\\mathbf{1.2/9.1}\\times$ faster response times compared to SANNS’s linear and approximate protocols, respectively, on the fast/slow networks.  \n\n# 12 Conclusions  \n\nWe define FLPSI, fuzzy labeled private set intersection, and propose an efficient construction. In FLPSI, client $c$ holds a biometric query and server $\\mathcal{S}$ holds a labeled biometric database, where labels may be, e.g., persons’ identities. In FLPSI, $\\boldsymbol{c}$ learns the label iff the query is in the database, and $\\mathcal{S}$ will learn nothing. Our definitional approach uniquely combines the properties of game-based and simulation-based definitions, and can be useful in other settings.  \n\nDesigning an efficient protocol for FLPSI is challenging mainly due to the need to manage the noisiness of biometric data. We realize FLPSI in the semi-honest model from a blockcipher, garbled circuits, secret sharing, and fully homo  \n\nmorphic encryption.  \n\nFLPSI achieves sublinear communication cost relative to the database. Our experiments show that our solution scales well to massive datasets including up to 10 million entries. Additionally, our comparative results show that i) FLPSI achieves up to $48{-}452\\times$ less communication cost and ii) up to $3.1/33\\times$ faster response times compared to protocols from the stateof-the-art on a database of 10 million entries. Notably, FLPSI has a major advantage over prior art by not relying on high speed network connection for efficiency.  \n\n# References  \n\n[1] M. R. Albrecht, R. Player, and S. Scott. On the concrete hardness of learning with errors. Journal of Mathematical Cryptology, 9(3):169– 203, 2015.   \n[2] Android Open Source Project. Biometric security, 2020. https: //source.android.com/security/biometric/measure.   \n[3] A. Babenko and V. Lempitsky. Efficient indexing of billion-scale datasets of deep descriptors. In IEEE CVPR, 2016.   \n[4] M. Barni, T. Bianchi, D. Catalano, M. Di Raimondo, R. Donida Labati, P. Failla, D. Fiore, R. Lazzeretti, V. Piuri, F. Scotti, et al. Privacypreserving fingercode authentication. In MM&Sec, 2010.   \n[5] M. Blanton and P. Gasti. Secure and efficient protocols for iris and fingerprint identification. In ESORICS. Springer, 2011.   \n[6] A. Boldyreva and N. Chenette. Efficient fuzzy search on encrypted data. In International Workshop on FSE. Springer, 2014.   \n[7] K. W. Bowyer, K. Hollingsworth, and P. J. Flynn. Image understanding for iris biometrics: A survey. CVIU, 110(2):281–307, 2008.   \n[8] Z. Brakerski, C. Gentry, and S. Halevi. Packed ciphertexts in lwe-based homomorphic encryption. In International Workshop on Public Key Cryptography, pages 1–13. Springer, 2013.   \n[9] Z. Brakerski, C. Gentry, and V. Vaikuntanathan. (leveled) fully homomorphic encryption without bootstrapping. TOCT, 6(3):1–36, 2014.   \n[10] Business Insider. https://www.businessinsider.com/senate-b ill-sanders-merkley-ban-corporate-facial-recognition -without-consent-2020-8.   \n[11] I. Calapodescu, S. Estehghari, and J. Clier. Compact fuzzy private matching using a fully-homomorphic encryption scheme, Aug. 29 2017. US Patent 9,749,128.   \n[12] R. Canetti, B. Fuller, O. Paneth, L. Reyzin, and A. Smith. Reusable fuzzy extractors for low-entropy distributions. In EUROCRYPT, 2016.   \n[13] M. S. Charikar. Similarity estimation techniques from rounding algorithms. In STOC, 2002.   \n[14] M. Chase, H. Chen, J. Ding, S. Goldwasser, S. Gorbunov, J. Hoffstein, K. Lauter, S. Lokam, D. Moody, T. Morrison, et al. Security of homomorphic encryption. HomomorphicEncryption.org, Tech. Rep, 2017.   \n[15] H. Chen, I. Chillotti, Y. Dong, O. Poburinnaya, I. Razenshteyn, and M. S. Riazi. SANNS: Scaling up secure approximate k-nearest neighbors search. In USENIX Security, 2020.   \n[16] H. Chen, Z. Huang, K. Laine, and P. Rindal. Labeled psi from fully homomorphic encryption with malicious security. In CCS, 2018.   \n[17] H. Chen, K. Laine, and P. Rindal. Fast private set intersection from homomorphic encryption. In CCS, 2017.   \n[18] L. Chmielewski and J.-H. Hoepman. Fuzzy private matching. In ARES, 2008.   \n[19] D. Demmler, T. Schneider, and M. Zohner. Aby-a framework for efficient mixed-protocol secure two-party computation. In NDSS, 2015.   \n[20] Y. Dodis, L. Reyzin, and A. Smith. Fuzzy extractors: How to generate strong keys from biometrics and other noisy data. In EUROCRYPT, 2004.   \n[21] Z. Dong, C. Jing, M. Pei, and Y. Jia. Deep cnn based binary hash video representations for face retrieval. Pattern Recognition, 81, 2018.   \n[22] L. Ducas and D. Stehlé. Sanitization of fhe ciphertexts. In Annual International Conference on the Theory and Applications of Cryptographic Techniques, pages 294–310. Springer, 2016.   \n[23] Z. Erkin, M. Franz, J. Guajardo, S. Katzenbeisser, I. Lagendijk, and T. Toft. Privacy-preserving face recognition. In PETS, 2009.   \n[24] D. Evans, V. Kolesnikov, and M. Rosulek. A pragmatic introduction to secure multi-party computation. FnT Privacy and Security, 2, 2018.   \n[25] J. Fan and F. Vercauteren. Somewhat practical fully homomorphic encryption. IACR Cryptology ePrint Archive, 2012:144, 2012.   \n[26] J. Feigenbaum, Y. Ishai, T. Malkin, K. Nissim, M. J. Strauss, and R. N. Wright. Secure multiparty computation of approximations. ACM Trans. Algorithms, 2(3):435–472, July 2006.   \n[27] M. Fredrikson, S. Jha, and T. Ristenpart. Model inversion attacks that exploit confidence information and basic countermeasures. In CCS, pages 1322–1333, 2015.   \n[28] M. J. Freedman, Y. Ishai, B. Pinkas, and O. Reingold. Keyword search and oblivious pseudorandom functions. In TCC, 2005.   \n[29] M. J. Freedman, K. Nissim, and B. Pinkas. Efficient private matching and set intersection. In EUROCRYPT, 2004.   \n[30] C. Gentry, S. Halevi, and N. P. Smart. Homomorphic evaluation of the aes circuit. In Annual Cryptology Conference, pages 850–867. Springer, 2012.   \n[31] R. Gilad-Bachrach, N. Dowlin, K. Laine, K. Lauter, M. Naehrig, and J. Wernsing. Cryptonets: Applying neural networks to encrypted data with high throughput and accuracy. In International Conference on Machine Learning, pages 201–210. PMLR, 2016.   \n[32] P. Grother, P. Grother, M. Ngan, and K. Hanaoka. Face recognition vendor test (frvt) part 2: Identification. NIST, 2019.   \n[33] Y. Guo, L. Zhang, Y. Hu, X. He, and J. Gao. Ms-celeb-1m: A dataset and benchmark for large-scale face recognition. In ECCV, 2016.   \n[34] Y. Huang, D. Evans, J. Katz, and L. Malka. Faster secure two-party computation using garbled circuits. In USENIX, pages 331–335, 2011.   \n[35] Y. Huang, L. Malka, D. Evans, and J. Katz. Efficient privacy-preserving biometric identification. In NDSS, 2011.   \n[36] P. Indyk and R. Motwani. Approximate nearest neighbors: towards removing the curse of dimensionality. In Proceedings of the thirtieth annual ACM symposium on Theory of computing, pages 604–613, 1998.   \n[37] P. Indyk and D. Woodruff. Polylogarithmic private approximations and efficient matching. In TCC, 2006.   \n[38] J. Ji, J. Li, S. Yan, B. Zhang, and Q. Tian. Super-bit locality-sensitive hashing. In NIPS, pages 108–116, 2012.   \n[39] R. Ji, H. Liu, L. Cao, D. Liu, Y. Wu, and F. Huang. Toward optimal manifold hashing via discrete locally linear embedding. IEEE Transactions on Image Processing, 26(11):5411–5420, 2017.   \n[40] T. Karras, S. Laine, and T. Aila. A style-based generator architecture for generative adversarial networks. In CVPR, pages 4401–4410, 2019.   \n[41] V. Kolesnikov, R. Kumaresan, M. Rosulek, and N. Trieu. Efficient batched oblivious prf with applications to private set intersection. In CCS, 2016.   \n[42] B. Kulis and K. Grauman. Kernelized locality-sensitive hashing for scalable image search. In IEEE ICCV, pages 2130–2137, 2009.   \n[43] M. Kuzu, S. Islam, and M. Kantarcioglu. Efficient similarity search over encrypted data. In IEEE ICDE, 2012.   \n[44] E. Meijering. A chronology of interpolation: from ancient astronomy to modern signal and image processing. Proc. IEEE, 2002.   \n[45] Microsoft. Biometric requirements, 2020. https://docs.microso ft.com/en-us/windows-hardware/design/device-experience s/windows-hello-biometric-requirements.   \n[46] M. Norouzi, D. J. Fleet, and R. R. Salakhutdinov. Hamming distance metric learning. In Advances in neural information processing systems, pages 1061–1069, 2012.   \n[47] M. Osadchy, B. Pinkas, A. Jarrous, and B. Moskovich. Scifi-a system for secure face identification. In IEEE S&P, 2010.   \n[48] B. Pinkas, T. Schneider, G. Segev, and M. Zohner. Phasing: Private set intersection using permutation-based hashing. In USENIX, 2015.   \n[49] B. Pinkas, T. Schneider, C. Weinert, and U. Wieder. Efficient circuitbased psi via cuckoo hashing. In EUROCRYPT, 2018.   \n[50] B. Pinkas, T. Schneider, and M. Zohner. Faster private set intersection based on $\\{\\mathrm{OT}\\}$ extension. In 23rd {USENIX} Security Symposium ({USENIX} Security $I4.$ ), pages 797–812, 2014.   \n[51] M. Raginsky and S. Lazebnik. Locality-sensitive binary codes from shift-invariant kernels. Advances in neural information processing systems, 22:1509–1517, 2009.   \n[52] M. S. Riazi, B. Chen, A. Shrivastava, D. S. Wallach, and F. Koushanfar. Sub-linear privacy-preserving search with untrusted server and semihonest parties. CoRR, 2016.   \n[53] A.-R. Sadeghi, T. Schneider, and I. Wehrenberg. Efficient privacypreserving face recognition. In ICISC, 2009.   \n[54] F. S. Samaria and A. C. Harter. Parameterisation of a stochastic model for human face identification. In IEEE WACV, 1994.   \n[55] F. Schroff, D. Kalenichenko, and J. Philbin. Facenet: A unified embedding for face recognition and clustering. In IEEE CVPR, 2015.   \n[56] Microsoft SEAL (release 3.5). https://github.com/Microsoft/S EAL, Aug. 2020. Microsoft Research, Redmond, WA.   \n[57] A. Shamir. How to share a secret. Commun. ACM, 1979.   \n[58] R. Shokri, M. Stronati, C. Song, and V. Shmatikov. Membership inference attacks against machine learning models. In IEEE S&P, 2017.   \n[59] S. Simhadri, J. Steel, and B. Fuller. Cryptographic authentication from the iris. In ISC, pages 465–485. Springer, 2019.   \n[60] N. P. Smart and F. Vercauteren. Fully homomorphic simd operations. Designs, codes and cryptography, 71(1):57–81, 2014.   \n[61] E. M. Songhori, S. U. Hussain, A.-R. Sadeghi, and F. Koushanfar. Compacting privacy-preserving $\\mathbf{k}$ -nearest neighbor search using logic synthesis. In IEEE DAC, 2015.   \n[62] J. Su, D. V. Vargas, and K. Sakurai. One pixel attack for fooling deep neural networks. IEEE TEVC, 2019.   \n[63] C. Szegedy, S. Ioffe, V. Vanhoucke, and A. A. Alemi. Inception-v4, inception-resnet and the impact of residual connections on learning. In AAAI, volume 4, page 12, 2017.   \n[64] The Guardian. https://www.theguardian.com/technology/202 0/aug/11/south-wales-police-lose-landmark-facial-rec ognition-case, June 2020.   \n[65] The Intercept. https://theintercept.com/2018/05/30/face-r ecognition-schools-school-shootings/, Dec. 2020.   \n[66] The NYT. https://www.nytimes.com/2020/01/18/technology/ clearview-privacy-facial-recognition.html, June 2020.   \n[67] The Verge. Moscow’s facial recognition system can be hijacked. http s://www.theverge.com/2020/11/11/21561018/moscows-facia l-recognition-system-crime-bribe-stalking, Dec. 2020.   \n[68] E. Uzun, C. Yagemann, S. Chung, V. Kolesnikov, and W. Lee. Cryptographic key derivation from biometric inferences for remote authentication. In ASIACCS, 2021.   \n[69] P. Viola and M. J. Jones. Robust real-time face detection. International journal of computer vision, 57(2):137–154, 2004.   \n[70] Y. Vizilter, V. Gorbatsevich, A. Vorotnikov, and N. Kostromov. Realtime face identification via cnn and boosted hashing forest. In IEEE CVPR Workshops, pages 78–86, 2016.   \n[71] J. Wang, T. Zhang, N. Sebe, H. T. Shen, et al. A survey on learning to hash. IEEE TPAMI, 40(4):769–790, 2017.   \n[72] Q. Wang, S. Hu, K. Ren, M. He, M. Du, and Z. Wang. Cloudbi: Practical privacy-preserving outsourcing of biometric identification in the cloud. In ESORICS, 2015.   \n[73] X. Wang, A. J. Malozemoff, and J. Katz. EMP-toolkit. https://gi thub.com/emp-toolkit, 2016.   \n[74] L. Wolf, T. Hassner, and I. Maoz. Face recognition in unconstrained videos with matched background similarity. In IEEE CVPR, 2011.   \n[75] M. Yasuda. Secure hamming distance computation for biometrics using ideal-lattice and ring-lwe homomorphic encryption. Information Security Journal: A Global Perspective, 26(2):85–103, 2017.   \n[76] Q. Ye, R. Steinfeld, J. Pieprzyk, and H. Wang. Efficient fuzzy matching and intersection on private datasets. In ISISC, 2009.   \n[77] X. Yi, C. Caramanis, and E. Price. Binary embedding: Fundamental limits and fast algorithm. In ICML, pages 2162–2170, 2015.   \n[78] J. Yuan and S. Yu. Efficient privacy-preserving biometric identification in cloud computing. In IEEE INFOCOM, 2013.   \n[79] C. Zhang, L. Zhu, and C. Xu. Ptbi: An efficient privacy-preserving biometric identification based on perturbed term in the cloud. IS, 2017.   \n[80] K. Zhang, Z. Zhang, Z. Li, and Y. Qiao. Joint face detection and alignment using multitask cascaded convolutional networks. IEEE Signal Processing Letters, 23(10):1499–1503, 2016.   \n[81] L. Zhu, C. Zhang, C. Xu, X. Liu, and C. Huang. An efficient and privacy-preserving biometric identification scheme in cloud computing. IEEE Access, 6:19025–19033, 2018.   \n[82] Y. Zhu, Z. Wang, and J. Wang. Collusion-resisting secure nearest neighbor query over encrypted data in cloud, revisited. In IEEE/ACM IWQoS, 2016.  \n\n# A Super-Bit Locality Sensitive Hashing  \n\nThough the Euclidean space of DL accurately captures the statistical properties of the raw input data, unfortunately, even the two consequent biometric scans of a person will not result the same embeddings due to fuzzy/noisy nature of biometrics. In order to accommodate $t$ -out-of- $T$ matching, both parties translate the Euclidean space into Hamming, by using SuperBit Locality Sensitive Hash (SBLSH) [38]. SBLSH is built on top of Sign-Random-Projection LSH (SRP-LSH) [13] , which turns input vectors into one-bit hash s.t. if two input vectors are close in angular distance, it is likely that their SRPLSH will be the same. In particular, SRP-LSH is defined as $h_{\\nu}(x)=s g n(\\nu^{T}x)$ , where $x$ and $\\nu$ are $d$ -dimensional vectors, and $s g n(.)$ is the sign function (i.e. 1 if the input is greater than or equal to 0, otherwise 0). Note, $x$ is the input (e.g., embedding vector), and $\\nu$ is sampled with normal distribution.  \n\nSBLSH demonstrated that SRP-LSH can be used to turn $x$ into $\\mathcal{L}$ -bit codes. It independently samples $\\left\\{\\nu_{1},...,\\nu_{\\mathscr{L}}\\right\\}$ vectors, then for each $i\\in[\\mathcal{L}]$ , it calls $h_{\\nu_{i}}(x)$ , and thus generates $\\mathcal{L}$ -bit codes. For details, we refer readers to [13, 38].  \n\n# B Securely Realizing Ideal Functionality  \n\nIn this section, we recall the standard definition of securely realizing ideal functionality in the semi-honest model [24], formulated for the 2PC case in Def. B.1.  \n\nDefinition B.1. Securely Realizing Ideal Functionality. We say that a real-world protocol Π securely realizes an idealworld functionality $\\mathcal{F}$ in the presence of static semi-honest adversaries if there exists a simulator Sim such that, for every corrupt party $P_{i},i\\in\\{1,2\\}$ and all valid inputs $x_{1},x_{2}$ , the  \n\ndistributions $\\mathrm{Real}_{\\Pi}(\\mathbf{\\kappa},P_{i};x_{1},x_{2})$ and Ideal $\\mathcal{F},\\sin\\big(\\mathbf{K},P_{i};x_{1},x_{2}\\big)$   \nare computationally indistinguishable $(i n\\kappa)$ . Real and Ideal   \nensembles are defined as follows: $\\mathtt{R e a l}_{\\Pi}(\\kappa,P_{i};x_{1},x_{2})\\colon r u n\\Pi$ with security parameter κ, where   \neach party $P_{i}$ runs honestly using private input $x_{i}$ . Let $V_{i}$ denote   \nthe final view of party $P_{i}$ , and let $y_{1},y_{2}$ be the final outputs of   \nthe two parties. Output $\\left\\{V_{i},\\left(y_{1},y_{2}\\right)\\right\\}$ . $\\operatorname{Ideal}_{F,\\operatorname{Sim}}(\\kappa,P_{i};x_{1},x_{2})\\colon L e t(y_{1},y_{2})\\gets\\mathcal{F}(x_{1},x_{2})$ . Output   \n$\\{\\mathrm{Sim}(P_{i},(x_{i},y_{i})),(y_{1},y_{2})\\}$ .  \n\n# C Proving the Security of STLPSI and FLPSI  \n\nIn this section, we formally prove the Theorem 5.1 and Theorem 7.1. We specifically describe ideal world simulators $\\mathrm{Sim}_{C}$ and $\\mathrm{Sim}_{S}$ emulating the views $\\mathrm{VIEW}_{C}$ and $\\mathsf{V I E W}_{S}$ of $\\boldsymbol{c}$ and $\\mathcal{S}$ in the real execution for both protocols. Recall, the player’s view includes its input, output, randomness, and the messages it received. The (challenging part of the) task of the simulators is to emulate the received messages in a consistent manner. Recall, a simulator Sim takes as input the simulated player’s input, output and leakage (if there is any). In the following, we formally present the required simulators and the proofs of the indistinguishability of the simulated and real views.  \n\n# C.1 Proving the Security Theorem of ΠSTLPSI  \n\nIt is immediate that $\\Pi_{\\mathrm{sTLPSI}}$ correctly computes the set intersection and the associated labels if the underlying Shamir’s secret reconstruction succeeds, i.e. when there are at least $t$ intersecting items between a query and database set (cf. Def. 5.1). Now, we formally prove the Theorem 5.1.  \n\nFor the ease of exposition, we assume that the simulator/protocol is parameterized by $(t,T,\\lambda,m,m_{p},m_{c t},\\mathcal{P},a,B)$ , which are fixed and public (see Sect. 11.2), and that $t$ -out-of- $T$ secret sharing scheme (Sect. 5.3) is used.  \n\n# C.1.1 Simulating the Client  \n\nRecall, $\\mathrm{Sim}_{C}$ takes the client’s query set $\\mathcal{Y}=\\left\\{y_{1},\\dots,y_{T}\\right\\}$ and the output of the real execution (labels of the matching database sets $X_{i}$ and corresponding (item, share) pairs, if at least $t$ of them matched). To construct $\\mathrm{Sim}_{C}$ , we first describe the real view that needs to be simulated.  \n\nReal view of $C.$ . In Steps 1-3 and 5-6, $\\boldsymbol{c}$ receives no messages, and thus $\\mathrm{Sim}_{C}$ does nothing to simulate them.  \n\nIn Step 6, $c$ attempts to reconstruct a label $l_{i}$ (and succeeds if there is a matching one. As required by the security of STLPSI, the client should not learn any below-threshold $t$ matches. STLPSI achieves this since the server takes a set of secret shares (for each label) as inputs, and again, Shamir’s secret sharing scheme guarantees the indistinguishability of each individual share (or any below-threshold $t$ combinations of them) from a random item in the share domain SS, which is the same with the agreed FHE scheme’s domain $\\mathbb{F}_{\\mathcal{P}}$ .  \n\nAlso note that we assume the server randomly re-generates different set of secret shares for each label before each execution of STLPSI protocol. This prevents a serious leakage, an adversary combining (possible) below-threshold $t$ shares, which are obtained across distinct executions, to sum up enough shares reconstructing a label.  \n\nIn Step 3, $c$ computes and sends $\\log B$ homomorphic ciphertexts to $\\mathcal{S}$ . In Step 4, $c$ receives back a homomorphic ciphertexts, each of which is an encryption of a degree- $\\cdot m$ FHE plaintext polynomial. Crucially, the ciphertexts sent to $\\boldsymbol{c}$ by $\\mathcal{S}$ are rerandomized with high noise (noise flooding), to hide the history of ciphertext construction.  \n\nConstructing the client’s simulator. We need to simulate the output of Step 4, homomorphic evaluations of intersection functions. Hence, $\\mathrm{Sim}_{C}$ is defined as follows.  \n\nRecall the $\\mathrm{Sim}_{C}$ has the output of the real execution. Hence, if the output is empty (there is no match), $\\mathrm{Sim}_{C}$ generates $a$ vectors, where each of them includes $m$ random items from the agreed FHE scheme’s domain $\\mathbb{F}_{\\mathcal{P}}$ . And, if the output has a matching label $l_{i}$ , $\\mathrm{Sim}_{C}$ inserts its associated shares into the corresponding vector indices (which are also obtained from the output) instead of random items from $\\mathbb{F}_{\\mathcal{P}}$ . Then, $\\mathrm{Sim}_{C}$ batches each of these $a$ vectors into a FHE plaintext polynomial and homomorphically encrypts it into a ciphertext. The ciphertext is then noise-flooded with the same noise distribution as used in the protocol. This ensures that the noise distribution in the simulated ciphertext is indistinguishable from that of the real execution. $\\mathrm{Sim}_{C}$ then applies modulus switching with the same parameters as in the real execution. The resulting $a$ ciphertexts serve as a simulation of the client’s view. By the IND-CPA security assumption on the agreed FHE scheme, this view is indistinguishable from the client’s view $\\mathrm{VIEW}_{C}$ in the real execution of $\\Pi_{\\mathrm{sTLPSI}}$ .  \n\n# C.1.2 Simulating the Server  \n\nSimulating the server is straightforward. In Step 4, $\\mathcal{S}$ receives $\\log B$ ciphertexts, where each of them is an encryption of a degree- $\\cdot m$ FHE plaintext polynomial. $\\mathrm{Sim}_{S}$ generates new encryptions of zero in place of the encryptions in this step. By the IND-CPA security of the agreed FHE scheme, this view is indistinguishable from the server’s view $\\mathsf{V I E W}_{\\mathcal{S}}$ in the real execution of ΠSTLPSI.  \n\n# C.2 Proving the Security Theorem of ΠFLPSI  \n\nIn this section, we prove Theorem 7.1 of our main protocol.  \n\n# C.2.1 Simulating the Client  \n\nDescribing the client’s view. After describing $\\mathrm{VIEW}_{C}$ , we explain what the simulator does to simulate the view and why this works. The simulator $\\mathrm{Sim}_{C}$ ’s inputs are a query $q\\in\\mathcal{D}$ , the leakage $\\mathcal{L}_{C}$ , and the output of the real execution (label(s) of the matched biometric(s), if any match occurred).  \n\nLet $q$ be the client’s query biometric data, and $y$ be the output bio-bit vector, computed through DL, SBLSH and NR in the preprocessing. Only $y$ is used in the rest of the protocol.  \n\nThe preprocessing stage (Step 1) and Steps 2-4 are noninteractive and the client receives no messages. Hence, $\\mathrm{Sim}_{C}$ does nothing to simulate these steps.  \n\nIn Step 5, $c$ and $\\mathcal{S}$ run MPC, where $c$ inputs $y$ , and $\\mathcal{S}$ inputs $k_{S}$ and $\\{m a s k_{1},\\dots,m a s k_{T}\\}$ . Then, $c$ gets subsample set $\\mathcal{Y}=\\left\\{y_{1},...,y_{T}\\right\\}$ s.t. $y_{j}=A E S_{k_{S}}(y\\wedge m a s k_{j})$ . $\\boldsymbol{c}$ receives MPC messages here, which (by the security of the underlying MPC protocol) carry no information and are simulated by the simulator guaranteed by the MPC protocol. However, the output of the MPC is something that $c$ obtains in its view, and we need to simulate it.  \n\nIn Step 6, the client submits the encrypted subsamples $\\mathcal{Y}=\\left\\{y_{1},\\dots,y_{T}\\right\\}$ , received from Step 5, to the STLPSI protocol and gets the set of shares (and identities of the corresponding matching encrypted subsamples) as output, if there is a match (which means there are at least $t$ matches). If there was no match, $\\boldsymbol{c}$ receives the empty set from the STLPSI protocol. Because $\\mathrm{Sim}_{C}$ is given the output, it will know whether STLPSI returns empty. However, in case a match is returned in the FLPSI protocol, we do not know how many subsamples matched. We cannot simulate this (without leakage), as it depends, e.g., on how close ${\\cal C}^{\\mathrm{{\\prime}}}$ ’s and $\\mathcal{S}$ ’s matching bio-bit vectors are. Thus, this information (the number of matched subsamples in case of a match) constitutes leakage $\\mathcal{L}_{C}$ , and $\\mathrm{Sim}_{C}$ will use it for the simulation. We again emphasize that this leakage is strictly less (and in fact much less) than $\\boldsymbol{c}$ learning the matching bio-bit vector (or the original biometric) of $\\mathcal{S}$ .  \n\nConstructing the client’s simulator. We need to simulate the output and input of the STLPSI call in Step 6. STLPSI inputs from the client is the set of elements (simulated by random elements in the range of the AES function and which are further used in the simulation of the AES step, described next). STLPSI output to the client is a set of labels and allows to reconstruct the output of $\\Pi_{\\mathrm{FLPSI}}$ , together with the corresponding matched set elements. The $\\Pi_{\\mathrm{FLPSI}}$ output (which is given to $\\mathrm{Sim}_{C}$ as input) indicates if there was a match (or matches) and specifies the corresponding label(s).  \n\nIf no match was achieved, $\\mathrm{Sim}_{C}$ sets the simulated output of STLPSI to be empty.  \n\nIf there was a single match over the database, $\\mathrm{Sim}_{C}$ knows the label to be returned in STLPSI. It also uses leakage $\\mathcal{L}_{C}$ to determine how many subsamples should be returned in STLPSI. It then uses the received label $l_{i}$ to generate the simulated secret shares input into STLPSI and obtained in the matched subsamples. $\\mathrm{Sim}_{C}$ then randomly chooses the set elements (from the AES outputs it simulated) to be the ones resulting in matches.  \n\nThe case of multiple matches is handled similarly. The only interesting difference is in simulating how many subsamples should be returned for each label $l_{i}$ . This is established with the help of the leakage $\\mathcal{L}_{C}$ .  \n\nHaving constructed the simulated input and output of $\\Pi_{\\mathtt{S T L P S I}}$ , $\\mathrm{Sim}_{C}$ uses the client-side simulator guaranteed by the security of $\\Pi_{\\mathrm{sTLPSI}}$ , to simulate the messages exchanged as part of the Step 6. Note that the input of the protocol is distributed according to the requirements of Theorem 5.1, and hence simulation goes through.  \n\nWe need to simulate messages received in the MPC call of Step 5. The output of the MPC call is the $T$ random elements chosen by $\\mathrm{Sim}_{C}$ as described above. The input to the MPC call is the client’s input $y$ , which is also given to $\\mathrm{Sim}_{C}$ . Thus, the real-world messages generated by the MPC subprotocols called in Step 5 are simulated by running the client-side simulator provided by the MPC protocol.  \n\nThis completes the description of the simulator $\\mathrm{Sim}_{C}$ . As noted above, the discussion included in the view description and the simulator construction is a direct argument of the indistinguishability of the simulated and real views.  \n\n# C.2.2 Simulating the Server  \n\nSimulating $\\mathcal{S}$ is significantly easier as it does not learn anything or receive any leakage in the protocol execution. The only protocol messages received by $\\mathcal{S}$ are those of the calls to MPC and STLPSI in Steps 5 and 6. $\\mathrm{Sim}_{S}$ simulates inputs to both calls simply by following the protocol on its input, and there are no outputs to $\\mathcal{S}$ in these steps. Thus, the messages received by $\\mathcal{S}$ in these steps are simulated by the corresponding server-side simulators of the MPC and STLPSI.  \n\nThis completes the description of the simulator $\\mathrm{Sim}_{S}$ . The argument of the indistinguishability of the simulated and real views is immediate.  \n\n# D Complexity Analysis of FLPSI  \n\nIn this section, we present the computation and communication complexities wrt the database and query sizes. $\\boldsymbol{c}$ holds a set of $T$ subsamples for a single query, and $\\mathcal{S}$ holds a database of $N$ records with associated labels, each with $T$ (subsample, share) pairs. Let $a,B,m$ be the number of partitions, size of each partition and size of SIMD batching vector, respectively.  \n\nCommunication complexity. FLPSI includes two interactive protocols: 2PC subsampling $\\left(C_{A E S},S_{A E S}\\right)$ and STLPSI $\\left(C_{S T L P S I},S_{S T L P S I}\\right)$ . Let $\\upbeta$ be the data transmission cost for a single $\\left(C_{A E S},S_{A E S}\\right)$ call, then the communication complexity for the former is $O(T\\upbeta)$ , which does not depend on the database size. Let $\\ell$ be the length of an item in $\\mathcal{M}\\mathcal{S}$ and SS, which is equal to domain of FHE scheme $\\mathbb{F}_{\\mathcal{P}}$ , where $\\ell=\\log\\mathcal{P}$ . Then, STLPSI has $\\begin{array}{r}{O(a\\ell+T\\ell)=O(T\\ell(\\frac{N}{m B}+1))}\\end{array}$ communication complexity. Since mB could be parameterized to be (almost) equal to $N$ (see Sect. 11.2), the total communication complexity is $O(T(\\ell+\\beta))$ (or $O(T\\ell)$ considering the dominant term) in practice. This is sublinear relative to the database, but linear relative to the number of subsamples.  \n\nComputation complexity. In the offline phase, $\\mathcal{S}$ needs to interpolate $m\\times a$ polynomials, each in the degree of $B=$ $\\frac{N T}{m a}$ . Given that the interpolation has a $O(B^{2})$ complexity, then the offline complexity is O( (NmTa) 2 ) [16]. In the online phase, $\\mathcal{S}$ homomorphically evaluates a $B$ -degree interpolation polynomial for all partitions, which has a $\\begin{array}{r}{O(\\frac{N T^{2}}{m^{2}})}\\end{array}$ complexity. Since $T\\ll m$ , we have $\\begin{array}{r}{O(\\frac{N T}{m})}\\end{array}$ FHE operations. Moreover, $c$ tries $\\binom{T}{t}$ combinations among plaintext results of each partition, which brings an additional $O(\\binom{T}{t}\\frac{m a}{T})$ share recovery cost through plaintext data. Note that we fix $t$ to a small value for all of the evaluated datasets, thus the share recovery cost does not become a bottleneck in our pipeline (e.g., only $0.95\\%$ of the query time, as reported in Fig. 9).  \n\n# E Parameter Selection Process  \n\nTuning all parameters together has its own challenges because this is a big search space to explore. Since $t$ and $T$ values (especially t) is also critical for the complexity of our protocol, we set $t=2$ and search for the minimum possible $T$ value. To achieve this, we first tune the length of bio-bit vectors. Then, we brute force the $\\mathcal{N}_{s b}$ and $T$ by targeting to the minimum errors. We also consider the threshold $\\tau_{r b}$ for the ratio of reliable bits along with these parameters. Instead of brute forcing, we follow a more probabilistic approach to find its optimal value. That is, we have to guarantee that enough bits are retained at the end of the NR layer to pick $T$ distinct subsampling functions (each has $\\mathfrak{N}_{s b}$ ones). Hence, 1) the number of the remaining reliable bits $(\\mathfrak{N}_{r b})$ should be more than the number of subsampled bits in each subsampling function (Nrb > Nsb) and 2) \u0000NNrsbb\u0001 ≥ T inequality should to be guaranteed. Finally, we fix our parameters to the values presented in Fig. 6.  "}