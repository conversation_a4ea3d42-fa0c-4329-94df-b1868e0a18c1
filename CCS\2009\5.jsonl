{"text": "# Reactive Noninterference  \n\nAaron <PERSON> of Pennsylvania University of Pennsylvania University of Pennsylvania  \n\n<PERSON> University of Pennsylvania University of Pennsylvania  \n\n# ABSTRACT  \n\nMany programs operate reactively—patiently waiting for user input, running for a while producing output, and eventually returning to a state where they are ready to accept another input (or occasionally diverging). When a reactive program communicates with multiple parties, we would like to be sure that it can be given secret information by one without leaking it to others.  \n\nMotivated by web browsers and client-side web applications, we explore definitions of noninterference for reactive programs and identify two of special interest—one corresponding to termination-insensitive noninterference for a simple sequential language, the other to termination-sensitive noninterference. We focus on the former and develop a proof technique for showing that program behaviors are secure according to this definition. To demonstrate the viability of the approach, we define a simple reactive language with an information-flow type system and apply our proof technique to show that well-typed programs are secure.  \n\n# Categories and Subject Descriptors  \n\nF.1.2 [Modes of Computation]: Interactive and reactive computation  \n\n# General Terms  \n\nLanguages, Security, Theory  \n\n# Keywords  \n\nNoninterference, information flow, reactive programming, web browsers, web applications  \n\n# 1. INTRODUCTION  \n\nReactive programs, which repeatedly perform singlethreaded computations in response to events generated by external agents (GUI button clicks, commands issued at a terminal, receipt of network packets, timer events, etc.), are ubiquitous. When a single reactive program may interact with multiple agents, questions of security and privacy immediately arise.  \n\nWeb browsers and the client-side web applications they run are an obvious case in point, because they interact with both a local user and mutually untrusting, remote agents (e.g., web servers). Web client security has attracted significant interest [18, 15, 10, 11]. However, we still lack the theoretical tools to answer the question “What does it mean for a browser, running a web application, to be secure?”  \n\nA web client may receive data and programs from many different servers, and each program may attempt to read any browser data and communicate with any remote server. Most browsers adhere to the “same-origin policy” [20], which is intended to enforce isolation among programs and data from different servers. However, the same-origin policy is a policy in name only: in fact, it is a set of rather complex and subtle rules, with no high-level statement of the security property they are intended jointly to enforce. Moreover, it has several practical problems: First, it prevents the client from calling useful third-party web services. Second, it prevents client-side data integration or collaborative behavior, even when the data being handled is guaranteed never to leave the user’s machine. Third, its rules involving subdomain relationships are complex and ambiguous [11]. And finally, because of its rigid restrictions, the same-origin policy is not applied to browser extensions or plug-ins.  \n\nOur goal in this paper is to offer a more principled approach to the sort of attack scenario that is addressed by the same-origin policy and to lay a foundation for more flexible enforcement mechanisms. In this scenario, the attacker is positioned at a remote host and can only communicate with the user’s client over HTTP. However, the attacker may have written some or all of the event handlers running on the client. The goal of the attacker is to retrieve some private piece of information that the user would not knowingly authorize the attacker to have.1  \n\nWhen compared with models of execution that have been previously studied in the information flow literature, reactive programs possess a novel combination of features: interactivity with buffered, asynchronous communication; incremental output; the requirement that the system respond in some way to arbitrary inputs; and the possibility of nontermination in handlers. Web client programs illustrate all of these features. They have handlers that wait idly for user input or responses to HTTP requests. After an event has occurred, the appropriate event handler runs from start to completion, possibly sending one or more messages to remote hosts or to the user. (To streamline the model, we can consider all updates to the browser display as “messages” to the user.) Outgoing messages are sent as execution proceeds, so a script need not terminate to produce visible output. When the browser is idle, any event can be processed (this feature has been called input-totality [14, 4, 22]), although it will typically result in a no-op if no handler exists. However, when the browser is running a script, additional events cannot be immediately processed because there is no notion of preemptive multitasking. Instead, additional events must be buffered and processed when and if the current script terminates.  \n\nOur aim is to find a natural definition of information-flow security for this execution model and to design a languagebased enforcement technique for reactive programming languages that execute under this model. Before coming to the details of our development, though, we briefly summarize its connections to the most closely related prior work.  \n\nGoguen and Meseguer [5, 6] gave one of the first formal accounts of noninterference, and they did so in a setting of machines that accept inputs and produce outputs. Their “MLS property” [6]—informally, “the low-visible outputs of a system remain unchanged after dropping the high-level inputs”—offers an attractive template for the sort of succinct, high-level specification we are looking for here. However, their model does not apply to nonterminating behaviors (they rely on a total function that immediately moves the system to a new state after each input); adapting the same intuition to our model involves an entirely new mathematical development.  \n\nBy contrast, some information-flow research [4, 22] has addressed execution models of concurrent systems with input and output that are somewhat more general than our reactive model. In stating the security properties for these systems, the observable behaviors of a system are usually characterized by a set of traces, each trace being a finite prefix of some, possibly infinite, sequence of transitions that might occur. This is a convenient tool for handling nondeterministic behavior, and it naturally encompasses nonterminating behavior as well. However, sets of traces are too abstract to capture some distinctions that we would like to make: in the standard trace representation, a system with the sole behavior of repeatedly outputting a single message forever is represented by the same set of traces as a system that repeatedly outputs that message until it nondeterministically decides to halt. We address this issue by using streams rather than sets of traces to represent infinite behaviors, leading to a notion of security that is fundamentally not expressible as a “security property” in the sense of Zakinthinos and Lee [22].  \n\nBesides articulating a natural information-flow property for reactive systems, we want to be able to enforce it using a security type system. There is a large body of research on such type systems (see Sabelfeld and Myers [19] for an overview). In particular, Volpano, Smith, and Irvine [21] develop a basic information-flow type system for sequential while-programs and prove that it guarantees a highlevel noninterference property. Notably, their noninterference property is termination-insensitive, which means that a program may diverge on some high inputs and terminate on others. Since this allows the possibility of a covert termination channel in a program, it is technically less secure than a termination-sensitive property, but it is more practical for language-based enforcement because ruling out these termination channels either requires the elimination of too many useful, secure programs, or else requires the use of static termination checking. Thus, a question of particular interest to us is whether there exists a termination-insensitive definition of security for reactive systems.  \n\nWhile most work on language-based security has ignored the issue of incremental inputs and outputs, it has been addressed by some recent work on “interactive programs” [16, 9, 2]. In contrast with our execution model, the execution model of these interactive programs is based on synchronous communication that is not input-total: the languages have explicit input operations that block, waiting for designated principals to respond. In stating its security properties, this line of work must tackle some of the same issues that we do; for instance, Hunt and Sands [9] make use of infinite streams. However, all of their definitions are terminationsensitive, and in order to pursue a termination-insensitive notion of noninterference, we have found it necessary to set up a more general framework for defining information security in the presence of inputs and outputs.  \n\nWe offer the following contributions. First, we define an abstract model of execution that is applicable to web client behavior (and any form of reactive computation that does not rely on preemptive multitasking). Second, by appealing to a stream-based semantics of these abstract systems, we give a generic definition of security for our execution model that is high-level in the manner of Goguen and Meseguer’s MLS property; then we show that this generic definition has specific instantiations corresponding to both terminationsensitive and to termination-insensitive definitions of noninterference. Third, we offer an “unwinding lemma” [6] for our termination-insensitive version of security, which gives rise to a lower-level, transition-based definition of security. Finally, we use this unwinding lemma to demonstrate how a security type system can be used to enforce security in a simple language with a reactive semantics.  \n\n# 2. REACTIVE COMPUTATION  \n\nWe use a constrained labeled transition system with input and output actions to capture our execution model. Although it bears similarity to labeled transition systems in the information-flow literature [14, 7, 4], the constraints are notably different.  \n\n2.1 Definition: A reactive system is a tuple (ConsumerState, ProducerState , Input, Output, →)  \n\nwhere $\\longrightarrow$ is a labeled transition system whose states are State = ConsumerState $\\cup$ ProducerState and whose labels are $A c t=I n p u t\\cup O u t p u t$ , subject to the following constraints:  \n\n• for all $C\\in$ ConsumerState, if $C{\\stackrel{a}{\\to}}Q$ , then $a\\in I n p u t$ and $Q\\in$ ProducerState , • for all P ∈ ProducerState , if $P{\\stackrel{u}{\\to}}Q$ , then $a\\in O u t p u t$ , • for all $C\\in$ ConsumerState and $i\\in I n p u t$ , there exists a $P\\in$ ProducerState such that $C{\\stackrel{i}{\\to}}P$ , and  \n\nIn words, a reactive system is one that takes the next available input, produces one or more outputs in response, and repeats the process.  \n\nOf course, a reactive system is inert unless it exists in an environment that can supply and receive messages. We may view an environment as comprising multiple agents communicating with the system over different channels by assuming that all inputs $i$ and outputs $o$ are tagged with some channel name $c$ . In this scenario, the environment performs the service of multiplexing multiple streams of messages into a single, buffered stream. In modeling a web application, channel names for inputs would be used to represent both the addresses of remote servers (e.g., domain names) and unique references to the input controls available to the user of the web client; channel names for outputs would be used to model server addresses and references to updateable browser display components.  \n\nIt is useful to make a few more observations about how our formal definition relates to the real systems we are trying to model. First, the definition implies that the system can always make some kind of progress unless it is blocking on input, but we note that this does not mean that it must always return to an input-accepting state: it can get into a loop producing outputs forever and never try to consume another input. Second, the definition requires every small step to produce an output. This is a technical device that does have any practical implications in our particular setting because, when we talk about security, we will assume that different outputs (and inputs) may be invisible to different observers, so we can easily model the act of a machine taking a silent, internal step using a system transition whose requisite output is invisible to all observers. This assumption conveniently allows us to assume, in our theoretical development, that all programs that diverge are continuously producing output, instead of having to make a special case for programs that diverge silently. We also force the system to go to a producer state after every input. Similarly, this does no harm in a setting where the system can simply produce a single invisible output in response to an input; however, it is a technical device that greatly simplifies our proofs because it entails that an infinite stream of inputs will always generate an infinite stream of outputs.  \n\nNow that we have a machine-like notion of reactive systems, we need a higher-level representation of their behavior to achieve a high-level definition of security. As mentioned earlier, we choose a stream-based interpretation instead of a trace-based interpretation. Formally, we define a stream as the coinductive2 interpretation of the grammar  \n\n$$\nS:=[]\\mid s::S\n$$  \n\nwhere $s$ ranges over stream elements. That is, a stream is a finite or infinite list of elements. We use metavariables $I$ and $O$ to range over streams of inputs $_i$ and outputs $o$ , respectively. Now we can view the behavior of a reactive system in a state $Q$ as a relation between input streams and output streams.  \n\n2.2 Definition: Coinductively define $Q(I)\\Rightarrow O$ ( $Q$ translates the input stream $I$ to the output stream $O$ ) with the following rules:  \n\n$$\n\\begin{array}{c}{{\\overline{{C([])\\Rightarrow[]}}}}\\ {{\\underline{{C\\stackrel{i}{\\to}}}P\\qquadP(I)\\Rightarrow O\\qquad\\underline{{P\\stackrel{o}{\\to}}}Q\\qquadQ(I)\\Rightarrow O}}\\ {{C(i:I)\\Rightarrow O}}\\end{array}\n$$  \n\nWe observe that this definition associates at least one output stream with every input stream, given the constraints on our transition systems.  \n\nIn order to illustrate how a reactive system might be programmed, we now introduce the syntax of the simple language RIMP—a reactive version of the IMP language of basic while-programs. The full semantics are given in Section 5; here we rely on an intuitive explanation of RIMP’s operational model. Input messages in RIMP are natural numbers tagged with their channels, where we let $_n$ range over the set of natural numbers, and $c h$ range over a set of channels. Outputs are either a natural number sent over a channel or a “tick” (which will be the default output on an internal step).  \n\n$$\n\\begin{array}{r c l l r}{{{\\cal I}n p u t}}&{{\\ni}}&{{i}}&{{::=}}&{{c h(n)}}\\ {{{\\cal O}u t p u t}}&{{\\ni}}&{{o}}&{{::=}}&{{c h(n)\\mid.}}\\end{array}\n$$  \n\nThe syntax of programs, handlers, commands, and expressions is defined as follows:  \n\n$$\n\\begin{array}{r l}{p}&{:=\\mathrm{~\\ensuremath~{~\\cdot~}~}|\\mathrm{~\\ensuremath~{~\\cdot~}~}p}\\ {h}&{::=\\mathrm{~\\ensuremath~{~c~h}(\\ensuremath~{\\boldsymbol~{~\\cdot~}})~}\\{c}&{:=\\mathrm{~\\ensuremath~{~\\cdot~}~}\\mathrm{~\\ensuremath~{~\\cdot~}~}c\\mathrm{~\\ensuremath~{~\\cdot~}~}|\\mathrm{~\\boldmath~{~output~}~}c h(e)\\mathrm{~\\ensuremath~{~\\cdot~}~}r:=e}\\ &{\\mathrm{~\\ensuremath~{~\\cdot~}~}|\\mathrm{~\\boldmath~{~i~f~}~}e\\mathrm{~then~}c\\mathrm{~e~lse~\\ensuremath~{~\\cdot~}~}|\\mathrm{~\\boldmath~{~while~}~}e~\\{c\\}}\\ {e}&{::=\\mathrm{~\\ensuremath~{~\\boldsymbol~{~\\cdot~}}~}|\\mathrm{~\\boldmath~{~\\cdot~}}|\\mathrm{~\\boldmath~{~\\it~e~}~}|\\mathrm{~\\boldmath~{~\\cdot~}~}e}\\ {\\odot\\mathrm{~\\ensuremath~{~\\cdot~}}:=\\mathrm{~\\ensuremath~{~\\cdot~}~}|\\mathrm{~\\boldmath~{~\\it~{~\\cdot~}}~}|=\\mathrm{~\\ensuremath~{~\\cdot}~}|\\mathrm{~\\boldmath~{~\\zeta~}~}<}\\end{array}\n$$  \n\nA program is a collection of event handlers, each of which accepts a message (a natural number) on some channel and runs a simple imperative program in response. The handler code may examine and modify shared global state, send messages, branch, and loop. When the handler for an input terminates, the RIMP program returns to a state in which it can handle another input. Handlers persist after handling events. Note that, since handlers share a global state, processing one input may affect the behavior of handlers in the future. The global state, called the store, is a mapping from variables $r$ to natural numbers. We assume that every variable in the global state is initialized to 0 at the start. In the machine that runs RIMP programs, a consumer state consists of the program text and the shared global state, and a producer state additionally includes the command that is currently being executed. If a producer state takes a step that does not otherwise generate an output message, we assume the label on that transition is $\\bullet$ .  \n\nOf course, RIMP is a long way from a full-featured web scripting language. Our goal with RIMP is to model only the event-handling mechanism of web application programming. Moreover, for the sake of simplicity, there is no mechanism for dynamically adding or removing handlers, which is characteristic of web programming; however, we believe our work on RIMP in this paper can be extended to account for this scenario once first-class functions and dynamic allocation are added to the language (which have been studied before in the context of information-flow type systems [17]). We leave that for future work. Finally, it is worth noting that the integration of secure web scripts into web documents also requires careful consideration, which is another topic we must defer to our future work.  \n\n# 3. SECURITY OF REACTIVE SYSTEMS  \n\nAs described earlier, reactive systems may send messages to and receive messages from multiple agents, which we will call principals. We assume there is a pre-order of security labels $({\\mathcal{L}},\\leq)$ and that all principals have a label corresponding to a level of authorization. We also assume that messages interchanged with the system have a label indicating their level of confidentiality. We intend to derive this level from the channel that carries the message, and this can be done at the point where the message streams are multiplexed. We assume that principals at a level $\\iota$ may only view messages at or below the level $\\iota$ . This is reasonable if we assume that observers would be positioned at the endpoints of HTTP connections and view the elements of $\\mathcal{L}$ as based on domain names.  \n\nIt is important to remember that, in the particular setting of web applications, it is the web browser user’s personal secrets and the user’s shared secrets with other principals that are being protected. (We are in no way addressing the issue of protecting a web server’s secrets from web clients.) As noted before, there must be some means to associate channels with user interface components. Since these channels determine the assigned security level of the data, it is necessary to have a user interface design that allows users of web applications to view the precise security level of any interface component.3 We assume that any user input control with a channel labeled by $\\mid$ (a maximal element of $\\mathcal{L}$ ) can be used to handle information that should never leave the user’s computer.  \n\nNow we can state an informal definition of information security: if a principal at one level cannot draw a distinction between two streams of inputs given to a reactive system starting in a particular state, then the same observer must not be able to draw a distinction between the resulting streams of outputs. This is a natural generalization of standard definitions of noninterference for imperative and functional languages [21, 17], and corresponds closely to Goguen and Meseguer’s MLS property [6]. We can state this definition formally in the following way:  \n\n3.1 Definition: A state $Q$ is secure if, for all $l$ , $I\\approx_{l}I^{\\prime}$ implies $O\\approx_{l}O^{\\prime}$ whenever $Q(I)\\Rightarrow O$ and $Q(I^{\\prime})\\Rightarrow O^{\\prime}$ .4  \n\nThe notation $S\\approx_{l}S^{\\prime}$ is meant to stand for a similarity relation on streams that is parametrized by a label $l$ —in other words, the inability of an observer at level $l$ to draw a distinction between $S$ and $S^{\\prime}$ . Defining this relation precisely is where things become interesting: it turns out that there are many natural notions of similarity between streams relative to an observer who cannot see all of the elements, leading us to multiple notions of security. Moreover, there are multiple ways to define each of these notions of similarity, and it is often difficult to guess which definitions are precisely equivalent. In the remainder of this section, we present a definition for four, increasingly-refined notions of similarity, and consider the technical implications for the corresponding definitions of security.  \n\nPreliminaries. To discuss these notions of security precisely, we need a few auxiliary definitions. To determine whether a stream element $s$ is visible to an observer at level $l$ , we use the predicate $v i s i b l e_{l}(s)$ . We assume that the set of security labels $\\mathcal{L}$ has a top element, $\\intercal$ , with $v i s i b l e\\tau\\left(s\\right)$ for all $s$ . In examples, we assume there are labels $\\intercal$ and $\\perp$ and channels $c h_{\\top}$ and $c h_{\\perp}$ , such that messages on channel $c h\\tau$ are invisible to an observer at level $\\perp$ .  \n\nWe also need some auxiliary definitions about streams. We write ${\\it f i n(S)}$ when $S$ is finite and $i n f(S)$ when $S$ is infinite. Next, we need a relation that associates a stream with its next $\\iota$ -visible element (if such an element exists) and with the remainder of the stream thereafter.  \n\n3.2 Definition: Inductively define $S~\\triangleright_{l}~s::S^{\\prime}$ ( $S$ l-reveals $s$ followed by $S^{\\prime}$ ) with the following rules:  \n\n<html><body><table><tr><td>visiblei(s)</td><td>visiblet(s) S::s 14 S</td></tr><tr><td>S :: S D1 s :: S</td><td>S : S DI s' :: S'</td></tr></table></body></html>  \n\nThis predicate is inductively defined because we only want it to hold true if one can find an $\\iota$ -visible element in a finite prefix of the potentially infinite stream. On the other hand, we would also like to define a predicate asserting that a stream contains no more l-visible elements.  \n\n3.3 Definition: Coinductively define $s i l e n t_{l}(S)$ with the following rules:  \n\n<html><body><table><tr><td></td><td>visiblet(s) silent(S) 一</td></tr><tr><td>silent()</td><td>silenti(s : S)</td></tr></table></body></html>  \n\nThis definition is coinductive because it is asserting a fact about all of the elements of a potentially infinite stream.  \n\nNonconflicting Security. The first two versions of similarity that we present are each defined by taking the negation of a definition of stream distinctness. The coarsest version of similarity, nonconflicting similarity, just requires that the observer cannot find two distinct stream elements in corresponding positions in the streams. Since a conflict must be evident from some finite prefixes of two streams, an inductive definition of this notion of distinctness is appropriate.  \n\n3.4 Definition: Inductively define conflictin $g_{l}(S,S^{\\prime})$ with the following rules:  \n\n$$\n\\begin{array}{r l}{\\underbrace{S\\textsf{P}_{l}s::S_{1}}_{c o n f i c t i n g_{l}(S,S^{\\prime})}\\quad}&{{s\\neq s^{\\prime}}}\\ {\\underbrace{S\\textsf{P}_{l}s:S_{1}}_{c o n f i c t i n g_{l}(S,S^{\\prime})}}&{{c o n f i c t i n g_{l}(S_{1},S_{1}^{\\prime})}}\\ {\\underbrace{S\\textsf{P}_{l}s:S_{1}}_{c o n f i c t i n g_{l}(S,S^{\\prime})}}&{{c o n f i c t i n g_{l}(S_{1},S_{1}^{\\prime})}}\\end{array}\n$$  \n\n3.5 Definition: Define $S~{\\approx}_{l}^{N C}$ $S^{\\prime}$ ( $S$ is NC-similar to $S^{\\prime}$ at $l$ ) to mean $\\neg c o n f i c t i n g_{l}(S,S^{\\prime})$ . Define $N C$ -security as Definition 3.1, instantiated with NC-similarity.  \n\nThere are other ways of defining NC-similarity. It turns out that $S$ is NC-similar to $S^{\\prime}$ at $l$ if the sequence of visible elements of one stream is a prefix of the visible elements of the other, which may be a more intuitive way to think about this relation. Nonconflicting similarity is reflexive and symmetric, but not transitive—we have $\\mathbb{I}\\approx_{l}^{N C}$ $S$ for any $\\iota$ and $S$ .  \n\n3.6 Example: The following program is not NC-secure:  \n\n$$\nc h\\tau(x)\\{\\mathsf{o u t p u t}}c h_{\\bot}(x)\\}\n$$  \n\nThis event handler has an explicit flow, and it is deemed insecure because the streams $\\left[c h-(0)\\right]$ and $\\left[c h-(1)\\right]$ are NC-similar at $\\perp$ but the corresponding output streams, $[c h_{\\perp}(0),\\bullet]$ and $[c h_{\\perp}(1),\\bullet]$ , are not NC-similar at $\\perp$ .  \n\n3.7 Example: The following program is not NC-secure:  \n\nThe second event handler has an implicit flow. It is deemed insecure because the input streams $[c h\\tau(0),c h_{\\bot}(0)]$ and $[c h\\tau(1),c h_{\\bot}(0)]$ are NC-similar at $\\perp$ but the corresponding output streams, $[\\bullet,\\bullet,\\bullet,c h_{\\bot}(0),\\bullet]$ and $[\\bullet,\\bullet,\\bullet,c h_{\\perp}(1),\\bullet]$ , are not NC-similar at $\\perp$ .  \n\nIt may not be immediately clear which $\\bullet$ outputs go with which inputs in the previous example, and the reader may wonder at this point whether our formalization of security has an inherent weakness because it handles the input and output streams separately rather than as one interleaved stream. In fact, this is a weakness of NC-similarity (but it will be resolved by stricter notions of similarity).  \n\n3.8 Example: The following program is NC-secure:  \n\nThis example is almost the same as the previous example. However, this one will map the input streams $[c h\\tau(0),c h_{\\bot}(0)]$ and $[c h\\tau(1),c h_{\\bot}(0)]$ to the output streams $[\\bullet,\\bullet,\\bullet,c h_{\\perp}(0),\\bullet]$ and $[\\bullet,\\bullet,\\bullet,c h_{\\bot}(0),\\bullet,c h_{\\bot}(0),\\bullet]$ , which are NC-similar at $\\perp$ . We can see that the program is NC-secure, in general, because the only outputs it can produce are $\\bullet$ and $c h_{\\perp}(0)$ , and any two streams of these elements are NCsimilar at $\\perp$ . In order to strengthen our notion of security to deal with the synchronization behavior of inputs and outputs, we need a more refined notion of similarity—one that coincides with the obvious definition on finite streams (i.e., dropping invisible items and comparing what remains for equality) when both streams are finite.  \n\nIndistinguishable Security. We modify the previous definition by adding two inference rules that effectively grant an observer the power to distinguish finite silent streams from streams that still have observable elements. We call this indistinguishable similarity.  \n\n3.9 Definition: Define distinguishable $\\phantom{}_{l}(S,S^{\\prime})$ inductively with the following rules:  \n\n$$\n\\frac{S\\textsf{\\textsf{P}}_{l}s::S_{1}s_{i}}{d i s t i n g u i s h a b l e_{l}(S^{\\prime})}\\quad\\mathrm{\\it~fin}(S^{\\prime})\n$$  \n\n$$\n\\begin{array}{r l r}&{}&{\\frac{s i l e n t_{l}(S)~f i n(S)~~S^{\\prime}~\\textmd{p}_{l}~s:~S_{1}^{\\prime}}{d i s t i n g u i s h a b l e_{l}(S,S^{\\prime})}}\\ &{}&{\\underbrace{S~\\textmd{p}_{l}~s:~S_{1}~S~^{\\prime}~\\textmd{p}_{l}~s^{\\prime}:~S_{1}^{\\prime}~}_{d i s t i n g u i s h a b l e_{l}(S,S^{\\prime})}~s\\neq s^{\\prime}}\\ &{}&{~S~\\textmd{p}_{l}~s:~S_{1}~S~^{\\prime}~\\textmd{p}_{l}~s:~S_{1}^{\\prime}}\\ &{}&{~\\frac{d i s t i n g u i s h a b l e_{l}(S_{1},S_{1}^{\\prime})}{d i s t i n g u i s h a b l e_{l}(S,S^{\\prime})}}\\end{array}\n$$  \n\n3.10 Definition: Define $S\\approx_{l}^{I D}S^{\\prime}$ ( $S$ is ID-similar to $S^{\\prime}$ at $l$ ) to mean $\\neg$ distinguishable $\\phantom{}_{l}(S,S^{\\prime})$ . Define $I D$ -security as Definition 3.1, instantiated with ID-similarity.  \n\nNote that we defined distinguishable ${\\boldsymbol{\\cdot}}_{l}(S,S^{\\prime})$ exactly as one would inductively define distinctness of finite streams, so its behavior on finite streams is the obvious one that simply filters out invisible elements and tests the remaining lists for equality. It immediately renders Example 3.8 insecure because, in general, if the high inputs differ, the output streams will not be equal after dropping the $\\bullet$ outputs. Although IDsimilarity gives an equivalence relation on finite streams, it is not transitive, in general, because of its subtle behavior on infinite streams. Observe that, if $i n f(S)$ and $s i l e n t_{l}(S)$ , then $S\\approx_{l}^{I D}S^{\\prime}$ for all $l$ and $S^{\\prime}$ . This observation leads us to our next example.  \n\n3.11 Example: The following program is ID-secure.  \n\ninput $c h\\tau(x)\\{r:=x\\}$ input $c h_{\\bot}(x)\\mathrm{~}\\left\\{\\begin{array}{r l}\\end{array}\\right.$ { if $r=0$ then output $c h_{\\perp}(0)$ else while 1 do skip }  \n\nThe second event handler creates a termination channel. Observe that the input streams $[c h\\tau(0),c h_{\\bot}(0)]$ and $[c h\\tau(1),c h_{\\bot}(0)]$ are ID-similar at $\\perp$ and the corresponding output streams $[\\bullet,\\bullet,\\bullet,c h_{\\perp}(0),\\bullet]$ and $[\\bullet,\\bullet,\\bullet,\\bullet,\\ldots]$ are, in fact, also ID-similar at $\\perp$ . Thus, this is a terminationinsensitive definition of security.  \n\nStandard definitions of noninterference [21, 17] usually imply some sort of functional dependency between the inputs and outputs of a program. The same is true here (and this fact is convenient for proving subsequent properties of our system).  \n\n3.12 Lemma: If a state $Q$ is ID-secure, then for all $I$ , $Q(I)\\Rightarrow O$ and $Q(I)\\Rightarrow O^{\\prime}$ implies $O=O^{\\prime}$ .  \n\nTo be precise, this does not mean a reactive system must be deterministic in order to be ID-secure: state transitions can be nondeterministic as long as they do not affect the output behavior.  \n\nIt is straightforward to demonstrate a relationship between ID-similarity and NC-similarity.  \n\n3.13 Lemma: $S\\approx_{l}^{I D}$ $S^{\\prime}$ implies $S\\approx_{l}^{N C}S^{\\prime}$ .  \n\nMore interesting is the fact that ID-security is stronger than NC-security. (This is not as straightforward to show because ID-similarity appears contravariantly in the definition of security.)  \n\n3.14 Lemma: If a transducer in a state $Q$ is ID-secure, then it is NC-secure.  \n\nFrom a practical standpoint, we don’t see any setting where NC-security is preferable to ID-security. We will see later that ID-security for RIMP programs can be guaranteed with a simple and flexible type system, and it is not clear how one would weaken the type system to include programs that are NC-secure but not ID-secure.  \n\nCoproductive Security. ID-security is terminationinsensitive because it does not give the observer the power to distinguish non-silent output streams from silent but infinite ones. We can ensure that such streams are always considered distinct with a more direct, coinductive definition of similarity, called coproductive similarity, which can be viewed as a weak bisimulation between the two streams, in which invisible elements correspond to internal $\\tau$ actions.  \n\n3.15 Definition: Coinductively define $S\\approx_{l}^{C P}S^{\\prime}$ ( $S$ is CPsimilar to $S^{\\prime}$ at $l$ ) with the following rules:  \n\n![](/tmp/output/5_20250326165042/images/62fc3b756f934ebf0f1f2869fd85ed76337d19ea952148ac3ec9a94798aaa5de.jpg)  \n\nDefine $\\mathit{C P}$ -security as Definition 3.1, instantiated with CPsimilarity.  \n\nUnlike the earlier definitions of similarity, this one is an equivalence relation. It is easy to check that Example 3.11 is not CP-secure, using the same input and output pairs mentioned above. Although we use a coinductive definition here, it should be possible to draw a very close correspondence between this definition and the ones used recently for “interactive programs” [16, 9, 2].  \n\nThe inductive definitions of NC-similarity and IDsimilarity resemble one another, so it was easy to prove Lemma 3.13; on the other hand, proving the following lemma requires a bit more work.  \n\n3.16 Lemma: $S\\approx_{l}^{C P}S^{\\prime}$ implies $S\\approx_{l}^{I D}S^{\\prime}$  \n\nWhat is the relationship between CP-security and IDsecurity, though? Again, since CP-similarity appears both co- and contravariantly in the definition of CP-security, their relationship is not at all obvious. The proof of the following lemma rests on several auxiliary definitions and lemmas, and additionally makes use of the bisimulation-based technique we introduce in Section 4.  \n\n3.17 Lemma: If a state $Q$ is CP-secure, then it is IDsecure.  \n\nCoproductive-Coterminating Security. CP-security is quite strong, but it is possible to go a step further by defining similarity in such a way that finite and infinite silent streams can be distinguished (coproductive-coterminating similarity).  \n\n3.18 Definition: Coinductively define $S_{\\mathrm{~}}\\approx_{l}^{C P C T}$ $S^{\\prime}$ ( $S$ is CPCT-similar to $S^{\\prime}$ at $l$ ) with the following rules:  \n\n$$\n\\begin{array}{r l r}{s i l e n t_{l}(S)\\quad}&{{}f n(S)}&{\\qquads i l e n t_{l}(S)\\quad}&{{}i n f(S)}\\ {\\frac{s i l e n t_{l}(S^{\\prime})\\quad}{S\\approx_{l}^{C P C T}S^{\\prime}}\\quad}&{{}\\quad}&{\\frac{s i l e n t_{l}(S^{\\prime})\\quad}{S\\approx_{l}^{C P C T}S^{\\prime}}}\\end{array}\n$$  \n\nHere is an example of a program that is secure by every other definition thus far but is not CPCT-secure.  \n\n3.19 Example: The following program is not CPCTsecure:  \n\nThis is the entire program. Low inputs are consumed but produce no low-visible output because there is no handler for them. (If this were not the case, then this program would fail to be CP-secure.)  \n\nThe definitions of CP-similarity and CPCT-similarity aren’t too different; so the following results shouldn’t be too surprising, although the latter one is still not trivial.  \n\n3.20 Lemma: $S\\approx_{l}^{C P C T}$ $S^{\\prime}$ implies $S\\approx_{l}^{C P}S^{\\prime}$ .  \n\n3.21 Lemma: If a transducer in a state $Q$ is CPCT-secure, then it is CP-secure.  \n\nCPCT-security guarantees that a reactive system can never make a choice between entering a input-accepting state or silently diverging based on a high input. However, this additional guarantee over CP-security is unimportant in practice because an attacker does not have the power to observe the results of such a choice in a CP-secure system. Consider a CP-secure machine that will silently diverge upon receiving a high input of 0 but will immediately return to a consumer state upon receiving a nonzero high input. A low observer who wishes to determine if the first high input was nonzero can only send a message to the machine and wait for a response (in our attack model, there is no other way to probe the system). A response would not be given to the low observer if the high input were 0; thus, CP-security guarantees that, even if the machine eventually consumes the low input, no response will be given to the low observer after (or even before) any high input. Since there is no possibility for getting feedback, there is no way for the low observer to determine if the system accepted the low input or whether the input is sitting in a buffer while the machine runs forever. Thus, CP-security is weaker than CPCT-security only on paper.  \n\nSummary. We have presented four definitions of security based on four definitions of similarity. Of these, two appear to be of practical interest: ID-security and CP-security. Enforcing CP-security through language-based techniques involves difficult trade-offs. For instance, O’Neill, Clarkson, and Chong [16] choose to disallow looping over high-level data, a very severe restriction. Instead, we choose to focus on termination-insensitive ID-security at this point. Although the type system we’ll use to enforce this looks quite standard, we first need to break down the definition of IDsecurity from a property on the input/output behavior of a system to a property on the states of a reactive system.  \n\n# 4. PROVING ID-SECURITY  \n\nWe now present a generic technique for proving the IDsecurity of a state in a reactive system. This is an “unwinding lemma” in the sense of Goguen and Meseguer [6]: it is a logically sufficient condition on the states of a transition system to ensure a high-level property of the system’s input/output behavior. One can alternatively view it as a bisimulation technique, given that it involves a binary relation that facilitates the coinductive proof of the unwinding lemma.  \n\n4.1 Definition: An ID-bisimulation on a reactive system is a label-indexed family of binary relations on states (written $\\sim_{l}$ ) with the following properties:  \n\n$(a)$ if $Q\\sim_{l}Q^{\\prime}$ , then $Q^{\\prime}\\sim_{l}Q$ ;   \n$(b)$ if $C\\sim_{l}C^{\\prime}$ and $C{\\stackrel{i}{\\to}}P$ and ${\\cal C}^{\\prime}\\stackrel{i}{\\rightarrow}P^{\\prime}$ , then $P\\sim_{l}P^{\\prime}$ ;   \n$(c)$ if $C\\sim_{l}C^{\\prime}$ and $\\neg v i s i b l e_{l}(i)$ and $C{\\stackrel{i}{\\to}}P$ , then $P\\sim_{l}C^{\\prime}$ ;   \n$(d)$ if $P\\sim_{l}C$ and $P{\\stackrel{o}{\\to}}Q$ , then $\\lnot v i s i b l e_{l}(o)$ and $Q\\sim\\iota C$ ;   \n$(e)$ if $P\\sim_{l}P^{\\prime}$ , then either • $P{\\stackrel{o}{\\to}}Q$ and $P^{\\prime}\\stackrel{o^{\\prime}}{\\longrightarrow}Q^{\\prime}$ implies $o=o^{\\prime}$ and $Q\\sim_{l}Q^{\\prime}$ , or else • $P{\\stackrel{o}{\\to}}Q$ implies ¬ visiblel(o) and $Q\\sim_{l}P^{\\prime}$ , or else P ′ o′ Q′ implies visiblel(o′) and P l Q′.  \n\nWe will see below that, if $Q\\sim_{l}Q$ for all $l$ , then $Q$ is IDsecure. We do not use a standard form of bisimulation (as is done in [4]) because we need a technique that gives rise to a termination-insensitive security property. Note that, in the first of the three cases under item $(e)$ , if one side can make a step with an output $o$ , then all steps taken by the other side must produce the same output $o$ . On the other hand, the other two cases under item (e) permit one side to take a silent step without being matched by the other side, which allows one side to get infinitely far ahead of the other when this definition is used coinductively.  \n\nBefore we can prove that this definition gives us the property we want, we need to introduce one more definition of similarity between streams.  \n\n4.2 Definition: Coinductively define $S\\approx_{l}^{V S}S^{\\prime}$ ( $S$ is visibly $\\iota$ -similar to $S^{\\prime}$ ) with the following rules:  \n\n$$\n\\begin{array}{r l}&{\\qquad\\overline{{\\prod\\approx_{l}^{V S}\\prod}}}\\ &{\\qquad\\overline{{\\quad\\qquad\\qquad\\quad}}\\frac{v i s i b l e_{l}(s)}{s:S\\approx_{l}^{V S}s::S^{\\prime}}}\\ &{\\qquad\\overline{{\\quad\\qquad\\quad}}\\frac{v i s i b l e_{l}(s)}{s:S\\approx_{l}^{V S}S^{\\prime}}\\quad\\frac{\\neg v i s i b l e_{l}(s)}{S\\approx_{l}^{V S}s::S^{\\prime}}}\\end{array}\n$$  \n\nObserve that this is a natural relation to define between two streams with invisible elements. It is easy to write down because it does not depend on auxiliary definitions such as $\\iota$ -reveals. Does this relation give rise to yet another notion of similarity and security? No, in fact, it coincides exactly with ID-similarity.  \n\n# 4.3 Lemma: $S\\approx_{l}^{V S}S^{\\prime}$ iff $S\\approx_{l}^{I D}S^{\\prime}$ .  \n\nVisible similarity is an important technical tool in our development since it gives us a coinduction principle that can be used to prove the following key lemma.  \n\n4.4 Lemma: Suppose that $Q\\sim_{l}Q^{\\prime}$ , and that $Q(I)\\Rightarrow O$ and $Q^{\\prime}(I^{\\prime})\\Rightarrow O^{\\prime}$ . Then $I\\approx_{l}^{V S}~I^{\\prime}$ implies $O\\approx_{l}^{V s}O^{\\prime}$ .  \n\nThe previous two lemmas lead us directly to our goal.  \n\n4.5 Theorem: If $Q\\sim_{l}Q$ for all $l$ , then $Q$ is ID-secure.  \n\n# 5. RIMP  \n\nRather than using our technique from Section 4 to prove programs secure one at a time, we would like to demonstrate that we can use a type system to show that all of the well-typed programs in a language are secure, in line with the previous work on language-based security [19]. To this end, we complete our technical development with a formal presentation of the RIMP language, along with a static type system that will ensure that well-typed programs are secure. We will prove this result by defining a relation on program states and showing that it is an ID-bisimulation for which well-typed programs are related to themselves.  \n\nOperational Semantics. We first define consumer and producer states of the RIMP reactive system. A consumer state, $C$ , is a store paired with a program. A producer state, $P$ , also includes the currently executing command and is tagged by the channel that triggered the execution. Stores, $\\mu$ , map global variables to the natural numbers they contain.  \n\n$$\n\\begin{array}{r c l}{{C}}&{{::=}}&{{(\\mu,p)}}\\ {{P}}&{{::=}}&{{(\\mu,p,c)^{c h}}}\\end{array}\n$$  \n\nThe transition between states in the RIMP reactive system is defined by the following four judgments of the operational semantics, whose definitions appear below.  \n\n1. $\\mu\\vdash e\\Downarrow n$ , a big step evaluation of closed expressions to numeric values, using the store to look up variables. (This definition is an entirely straightforward one, in which we use 0 for the boolean value false and nonzero numbers for true. See Appendix B for the formal definition.)   \n2. $(\\mu,c){\\overset{o}{\\longrightarrow}}(\\mu^{\\prime},c^{\\prime})$ , a small step execution of a closed command paired with a store, where each step produces an output.   \n3. $(p)(i)\\Downarrow c$ , the response to an input event, producing the command that will execute next.   \n4. $Q\\stackrel{u}{\\rightarrow}Q^{\\prime}$ , the actual transitions of the reactive system.  \n\nThe bulk of computation occurs when the commands in a handler are executed. Each step of computation produces an output, $o$ , although many of those outputs will be the trivial output $\\bullet$ , which is visible only to the highest-security observer. The rules below are standard except for the final rule, which produces output.  \n\n5.1 Definition: Inductively define $(\\mu,c)\\stackrel{o}{\\to}(\\mu^{\\prime},c^{\\prime})$ with the following rules:  \n\n$$\n\\begin{array}{r l}{\\overline{{(\\mu,(\\mathtt{s k i p};~c))\\overset{\\bullet}{\\to}(\\mu,c)}}}&{\\overline{{(\\mu,(c_{1};~c_{2}))\\overset{\\circ}{\\to}(\\mu^{\\prime},c_{1}^{\\prime})}}}\\ &{\\overline{{(\\mu,(\\mathtt{s k i p};~\\mathfrak{c}))\\overset{\\bullet}{\\to}(\\mu^{\\prime},(c_{1}^{\\prime};~c_{2}))}}}\\ &{\\overline{{(\\mu,(r:=e))\\overset{\\bullet}{\\to}(\\mu[r\\mapsto n],\\mathtt{s k i p})}}}\\end{array}\n$$  \n\n$$\n\\begin{array}{r l}&{\\frac{\\mu-\\epsilon\\ni e\\bar{\\nu}\\cdot n\\quad n\\quad n\\neq0}{\\left(\\mu,(\\mathbf{i}\\cdot\\epsilon\\quad\\mathrm{enen~close}_{2})\\right)^{\\bullet}\\left(\\mu,c_{\\mathrm{i}}\\right)}}\\ &{\\frac{\\mu\\cdot\\epsilon\\mathrm{~dif~}\\alpha}{\\left(\\mu,(\\mathbf{i}\\cdot\\epsilon\\quad\\mathrm{enen~cles}_{1})\\right)^{\\bullet}\\left(\\mu,c_{\\mathrm{c}}\\right)}\\right)\\cdot\\left(\\mu,c_{\\mathrm{2}}\\right)}\\ &{\\frac{\\mu\\cdot\\epsilon\\cdot\\phi\\cdot\\left(\\mu\\cdot\\mathbf{n}\\right)}{\\left(\\mu,(\\mathbf{u}\\cdot\\mathbf{u})\\mathrm{ine~}\\epsilon\\cdot\\epsilon^{\\prime}\\right)\\rangle)^{\\bullet}\\left(\\mu,(c_{\\mathrm{v}}\\cdot\\mathbf{u}\\mathrm{ine~}\\epsilon\\cdot\\epsilon^{\\prime})\\right)}}\\ &{\\frac{\\mu\\cdot\\epsilon\\mathrm{~dif~}\\alpha}{\\left(\\mu,(\\mathbf{u}\\cdot\\mathbf{u})\\mathrm{ine~}\\epsilon\\cdot\\epsilon^{\\prime}\\right)\\rangle)^{\\bullet}\\left(\\mu,\\mathbf{s}\\mathrm{kip}\\right)}}\\ &{\\frac{\\mu\\cdot\\epsilon\\cdot\\phi}{\\left(\\mu,(\\mathbf{u}\\cdot\\mathbf{u})\\mathrm{ine~}\\epsilon\\cdot\\epsilon^{\\prime}\\right)\\rangle^{\\bullet}\\left(\\mu,\\mathbf{s}\\mathrm{kip}\\right)}}\\ &{\\frac{\\mu\\cdot\\epsilon\\cdot\\phi\\cdot\\left(\\mu\\cdot\\mathbf{n}\\right)}{\\left(\\mu,(\\mathbf{u}\\cdot\\mathbf{u})\\mathrm{entery}\\right)\\mathrm{~he\\cdot\\mathbf{s}\\cdot\\mathbf{u}\\mathrm{ip}~}}}\\end{array}\n$$  \n\nNext, we need a definition that pairs an input with a program and builds the code that will be executed in response to that event. This will require a substitution of the message data for the parameter $_x$ in the body of the event handler. We assume a standard definition of substituting a value $_n$ for $x$ in an expression $e$ (written $e\\{n/x\\}$ ), extended to commands in the obvious way.  \n\n5.2 Definition: Inductively define $(p)(i)\\Downarrow c$ with the following rules:  \n\n$$\n\\begin{array}{r l r}&{}&{\\overline{{(c h(x)\\{c\\};~p)(c h(n))\\Downarrow c\\{n/x\\}}}~}\\ &{}&{\\underline{{(p)(c h^{\\prime}(n))\\Downarrow c~c h\\ne c h^{\\prime}}}~}\\ &{}&{\\underline{{(c h(x)\\{c\\};~p)(c h^{\\prime}(n))\\Downarrow c}}~}&{\\overline{{(\\cdot)(i)\\Downarrow\\mathtt{s k i p}}}~}\\end{array}\n$$  \n\nFinally, we give the labeled transition system corresponding to RIMP’s semantics. This system either transitions a consumer state to a producer state by looking up the appropriate handler, steps a producer state to a new producer state (if there is computation remaining), or steps a producer state to a consumer state (if the handler has finished execution).  \n\n5.3 Definition: Define $Q\\stackrel{a}{\\rightarrow}Q^{\\prime}$ (where $a::=i\\mid o.$ ) with the following rules:  \n\n$$\n\\begin{array}{c}{{{\\displaystyle\\frac{\\left(p\\right)\\left(c h\\left(n\\right)\\right)\\downarrow c}{\\left(\\mu,p\\right)\\stackrel{c h\\left(n\\right)}{\\longrightarrow}\\left(\\mu,p,c\\right)^{c h}}}}}\\ {{{\\mathrm{}\\frac{\\left(\\mu,c\\right)\\stackrel{o}{\\longrightarrow}\\left(\\mu^{\\prime},c^{\\prime}\\right)}{\\left(\\mu,p,c\\right)^{c h}\\stackrel{o}{\\longrightarrow}\\left(\\mu^{\\prime},p,c^{\\prime}\\right)^{c h}}}}}\\end{array}\\quad{\\begin{array}{c}{{{\\mathrm{}\\longleftrightarrow\\left(-k\\right)}}}\\ {{{\\mathrm{}\\left(\\mu,p,{\\bf s k i p}\\right)^{c h}\\stackrel{\\bullet}{\\longrightarrow}\\left(\\mu,p\\right)}}}\\end{array}}\n$$  \n\nWe can easily show that these rules define a reactive system, which is really just a matter of confirming that the RIMP execution will never halt if inputs are available.  \n\nTyping of RIMP Programs. Now we give a static type system to the RIMP language, whose purpose is to identify a subset of programs that are secure.  \n\nWe assume there is a function $l b l$ that associates a label with every channel and program variable, and we define $v i s i b l e_{l}(c h(n))$ to mean that $l b l(c h)\\leq l$ , for both inputs and outputs. Define $v i s i b l e_{l}(\\bullet)$ to hold iff $l=\\top$ .  \n\nExpressions are typed with a single label, which can be interpreted as an upper bound on the secrecy level of the components of the expression. The typing judgment for expressions is parametrized by a mapping $\\Gamma$ from parameters to labels. (Even though we use only one formal parameter $x$ in this language, we write it this way for consistency of notation with standard typing judgments in more expressive languages.)  \n\n5.4 Definition: Inductively define $\\Gamma\\vdash e:l$ with the following rules:  \n\n$$\n\\begin{array}{r l}&{\\frac{\\Gamma(x)\\leq l}{\\Gamma\\vdash x:l}\\qquad\\frac{l b l(r)\\leq l}{\\Gamma\\vdash n:l}\\qquad\\frac{l b l(r)\\leq l}{\\Gamma\\vdash r:l}}\\ &{\\qquad\\quad\\Gamma\\vdash e_{1}:l_{1}\\qquad\\Gamma\\vdash e_{2}:l_{2}\\qquadl_{1},l_{2}\\leq l}\\ &{\\qquad\\quad\\Gamma\\vdash e_{1}\\odot e_{2}:l}\\end{array}\n$$  \n\nCommands are also typed with a single label, which can be interpreted as a lower bound on the secrecy of the effects that could occur during the execution of the command. Traditionally, this label is called the label of the “program counter,” so we use $p c$ to range over it. Again, we need a typing environment $\\Gamma$ for the parameters that might be present in commands.  \n\n5.5 Definition: Inductively define $\\Gamma\\vdash c:p c$ with the following rules:  \n\n<html><body><table><tr><td>Hc1:pc1 HC2:pC2</td><td>F skip:pc pc ≤ pC1,pC2</td></tr><tr><td></td><td>rF(c1; c2): pc</td></tr><tr><td>Te:l</td><td>l ≤ lbl(ch) pc ≤ lbl(ch)</td></tr><tr><td>e:l</td><td>F output ch(e):pc (4)191 ≥ 1 (4)191 ≥ od</td></tr><tr><td></td><td>T-(r:=e): pc</td></tr><tr><td>T-e:l l ≤ pC1,pC2</td><td>TFC1:pC1 Hc2:pC2 pc ≤ pC1, pC2</td></tr><tr><td></td><td>THif ethen C1 else C2:pc</td></tr><tr><td></td><td>d1 pc ≤ pC1</td></tr><tr><td>Te:l Fc:pc1</td><td></td></tr><tr><td></td><td>while e{c}:pc</td></tr></table></body></html>  \n\nThe typing judgment for programs simply requires that each handler be well typed at the level of its channel, under the assumption that the message received is secret at the level of the channel.  \n\n5.6 Definition: Inductively define $\\vdash p$ with the following rules:  \n\n$$\n\\begin{array}{r l r}{\\_}&{{}\\quad}&{\\underline{{x:l b l(c h)\\vdash c:l b l(c h)}}\\qquad\\vdash p}\\ {\\vdash}&{{}\\qquad}&{\\vdash c h(x)\\{c\\};p}\\end{array}\n$$  \n\nFinally, we may define a typing judgment for producer and consumer states. Note that typing programs does not depend on the store. The channel that triggered a producer state also constrains the type of the command in that state.  \n\n5.7 Definition: Define the judgment ${\\vdash}Q$ with the following rules:  \n\n$$\n\\frac{\\vdash p}{\\vdash(\\mu,p)}\\qquad\\qquad\\frac{\\vdash p\\qquad\\vdash c:l b l(c h)}{\\vdash(\\mu,p,c)^{c h}}\n$$  \n\nThese definitions have the standard type preservation property.  \n\n# 5.8 Lemma: $\\operatorname{If}\\vdash Q$ and $Q\\stackrel{a}{\\rightarrow}Q^{\\prime}$ , then ${\\vdash}Q^{\\prime}$  \n\nThe standard progress theorem for well-typed terms is actually trivial here because by definition every term can make progress in a reactive system.  \n\nBisimulation on RIMP Programs. We now turn to defining a label-indexed family of binary relations on program states and showing that it is a ID-bisimulation. This relation is built from relations on stores, commands, and programs.  \n\nFirst, two stores are related at label $\\iota$ if the contents visible to $\\iota$ are identical. This relation is an equivalence relation.  \n\n5.9 Definition: Define two stores $\\mu$ and $\\mu^{\\prime}$ to be related at $l$ (written $\\mu\\sim_{l}\\mu^{\\prime}$ ) if, for all $r$ for which $l b l(r)\\le l$ , we have $\\mu(r)=\\mu^{\\prime}(r)$ .  \n\nNext, to define when two commands are related, we must first define a predicate $h i g h_{L}(c)$ stating that the effects of a command are visible only within a certain upward-closed set $L$ . In the following, we define the downward closure of a set of labels $L$ (written $L^{\\star}$ ) as $\\{l|\\exists l^{\\prime}\\in L.l\\subseteq l^{\\prime}\\}$ . Similarly, the upward closure of a set of labels $L$ (written $L^{\\pm})$ is $\\{l|\\exists l^{\\prime}\\in L.l^{\\prime}\\leq l\\}$ . (We write $l^{\\star}$ and $l^{\\pmb{\\Delta}}$ for ${\\{l\\}}^{\\star}$ and $\\{l\\}^{\\mathbf{a}}$ .) $\\overline{{L}}$ is the complement of $L$ .  \n\n5.10 Definition: Inductively define $h i g h_{L}(c)$ with the following rules:  \n\n$$\n\\begin{array}{r l}&{\\frac{\\mathrm{i}\\mathrm{H}\\mathrm{{end}\\_c l{-c l o s e d}}}{\\mathrm{i}g h_{L}({\\mathrm{otsig}})}\\qquad\\frac{h i g h_{L_{1}}(c_{1})\\quad~h i g h_{L_{2}}(c_{2})}{h i g h_{L_{1}\\mathrm{{fot}},{\\mathrm{fot}},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},\\mathrm{for},}\\ &{\\frac{h i g h_{L}({\\mathrm{otsig}}\\mathrm{{for}})\\quad\\mathrm{~for}~\\mathrm{{for}~\\mathrm{{exigh}}~}\\mathrm{{for}~}\\mathrm{{for}~}}{h i g h_{L}(c\\mathrm{{for}}\\mathrm{{for}})}}\\ &{\\frac{h i g h_{L}(c\\mathrm{{for}})}{h i g h_{L}({\\mathrm{ot}}\\mathrm{{for}}\\mathrm{{for}}\\mathrm{{for}}\\mathrm{{for}})}}\\ &{\\frac{h i g h_{L}(c\\mathrm{{for}})}{h i g h_{L}({\\mathrm{end}}\\mathrm{{for}}\\mathrm{{for}}\\mathrm{{for}})}}\\end{array}\n$$  \n\nNow we can define when two commands are related at a label. Intuitively, the commands must be identical, except for subcommands whose effects are invisible to an observer at level $\\it l$ .  \n\n5.11 Definition: Inductively define $c\\sim_{l}c^{\\prime}$ as follows:  \n\n$$\n\\mathrm{~\\frac{~c_{1}\\sim_{l}c_{1}^{\\prime}~}{~(c_{1};~c_{2})\\sim_{l}~(c_{1}^{\\prime};~c_{2}^{\\prime})}~}\\qquad}\\end{array}\\qquad\\begin{array}{l l}{{{\\frac{c_{1}\\sim_{l}c_{1}^{\\prime}~}{~(c_{1};~c_{2})\\sim_{l}~(c_{1}^{\\prime};~c_{2}^{\\prime})}}}}&{{}}\\end{array}\n$$  \n\n$$\n\\frac{\\vdash e:l^{\\prime}}{\\mathrm{output}c h(e)\\sim_{l}\\mathrm{output}c h(e)}\n$$  \n\n$$\n\\frac{\\vdash e:l^{\\prime}\\qquadl^{\\prime}\\leq l b l(r)\\leq l}{(r:=e)\\sim_{l}(r:=e)}\n$$  \n\n$$\n\\begin{array}{c c}{{\\vdash e:l^{\\prime}}}&{{l^{\\prime}\\leq l}}\\ {{c_{1}\\sim_{l}c_{1}^{\\prime}}}&{{c_{2}\\sim_{l}c_{2}^{\\prime}}}\\end{array}\n$$  \n\n$$\n\\begin{array}{r l r}&{\\frac{\\vdash e:l^{\\prime}\\qquadl^{\\prime}\\le l\\qquadc\\sim_{l}c^{\\prime}}{\\mathrm{while~}e\\{c\\}\\sim_{l}\\mathrm{~while~}e\\{c\\}}}&\\ &{\\frac{\\qquadh i g h_{L}(c)\\qquadh i g h_{L}(c^{\\prime})\\qquadl\\notin{\\cal L}}{c\\sim_{l}c^{\\prime}}}&\\end{array}\n$$  \n\n(This relation is symmetric and transitive; however, it is not reflexive for untypeable commands. For example, consider $c=$ output $c h(r)$ where $l b l(r)\\Zint l b l(c h)$ .)  \n\nNext we define when two programs are related. As for commands, this is a partial equivalence relation.  \n\n5.12 Definition: Two programs $p$ and $p^{\\prime}$ are related at $l$ (written $p\\sim_{l}p^{\\prime}$ ) if  \n\n• for all ch for which $l b l(c h)\\leq l$ , if $(p)(c h(n))\\downarrow c$ and $(p^{\\prime})(c h(n))\\downarrow c^{\\prime}$ , then $c\\sim_{l}c^{\\prime}$ , and   \n• for all $c h$ for which $l b l(c h)\\leq l$ , if $(p)(c h(n))\\downarrow c$ , then $h i g h\\overline{{{\\imath\\cdot}}}(c)$ , and   \n• for all $c h$ for which $l b l(c h)\\notin l$ , if $(p^{\\prime})(c h(n))\\downarrow c$ , then $h i g h\\overline{{{\\imath\\cdot}}}(c)$ .  \n\nFinally, we define when two program states are related. A consumer state is related to a producer state only when the outputs of the command in the producer state are invisible and the stores and programs are related. This relation is also a partial equivalence relation.  \n\n5.13 Definition: Two states $Q$ and $Q^{\\prime}$ are related at $\\it l$ (written $Q\\sim_{l}Q^{\\prime}$ ) with the following inductive definition:  \n\n$$\n\\begin{array}{c}{{{\\frac{\\mu\\sim_{l}\\mu^{\\prime}}{(\\mu,p)\\sim_{l}(\\mu^{\\prime},p^{\\prime})}}}}\\ {{{}}}\\ {{{\\frac{\\mu\\sim_{l}\\mu^{\\prime}}{(\\mu,p)\\sim_{l}(\\mu^{\\prime},p^{\\prime},c)^{c h}}}}}\\ {{{}}}\\ {{{\\frac{\\mu\\sim_{l}\\mu^{\\prime}}{(\\mu,p)\\sim_{l}(\\mu^{\\prime},p^{\\prime},c)^{c h}}}}}\\ {{{}}}\\ {{{\\frac{\\mu\\sim_{l}\\mu^{\\prime}{}}{(\\mu,p,c)^{c h}}}}}\\ {{{}}}\\ {{{\\frac{\\mu\\sim_{l}\\mu^{\\prime}{}}{(\\mu,p,c)^{c h}}}}}\\end{array}\\nonumber\n$$  \n\nSecurity of RIMP Programs. Now that we have defined a label-indexed family of relations on program states, we need to show that it is an ID-bisimulation.  \n\nA key lemma is that high commands step to high commands and only produce invisible outputs.  \n\n5.14 Lemma: If $h i g h_{L}(c)$ and $(\\mu,c)\\quad\\xrightarrow{o}\\quad(\\mu^{\\prime},c^{\\prime})$ , then $h i g h_{L}(c^{\\prime})$ and, for all $l\\notin L$ , w $\\mathrm{;~have}\\rightarrow v i s i b l e_{l}(o)$ and $\\mu\\sim_{l}\\mu^{\\prime}$ .  \n\nWe use this lemma to verify the conditions of Definition 4.1 to show that $\\sim_{l}$ is an ID-bisimulation. Since we carefully constructed our binary relation, we can also show that programs are related to themselves at every label $\\it l$ if they are well typed.  \n\n5.15 Lemma: ${\\mathrm{If}}\\vdash p$ , then $p\\sim_{l}p$ for all $\\iota$ .  \n\nCombining the previous lemma with Theorem 4.5 gives us the security result we claimed. This result guarantees us that any well-typed program will be secure when it is initialized with any store.  \n\n5.16 Theorem: If $\\vdash p$ , then $(\\mu,p)$ is an ID-secure transducer.  \n\n# 6. ADDITIONAL RELATED WORK  \n\nThe introduction has already drawn comparisons with the lines of work most directly relevant to ours. Here, we survey some more distantly related work that may also be of interest.  \n\nMcCullough’s “restrictiveness” property [14] is a security policy for labeled transition systems with input and output. Rather than defining a “high-level policy” on traces or streams, he defines restrictiveness with a set of constraints on transitions. It is therefore comparable with Goguen and Meseguer’s “unwound policy” [6] or our ID-bisimulation. McCullough’s restrictiveness property requires input totality in a strict sense (without an allowance for input buffering) and is termination-sensitive.  \n\nThe work of O’Neill, Clarkson, and Chong [16] (hereafter “OCC”) builds upon Halpern and O’Neill’s Multiagent Systems Framework [8]. They define noninterference in terms of user strategies, which are functions that map every history of $\\iota$ -visible events to the next action for each principal at level $l$ . This framework allows their security definitions to consider the possibility of a high user revealing information to a low user indirectly via choice of strategy. This seems to be of little practical value in a setting where high users have more direct means to interact with low users than through the system in question. For instance, in the setting of the web client, if a banking application wants to reveal the user’s account number to a third party, this can be done trivially on the server’s side of the application rather than through the code running in the user’s browser. Moreover, Clark and Hunt [2] demonstrate that the choice of strategy does not permit covert communication in deterministic programs that are interactive in the sense of OCC. Although our execution model is different, the same fact may well hold true in our setting. (Much of the focus in OCC is actually on dealing with probabilistic behaviors; since RIMP is deterministic, this aspect is orthogonal to our aims.)  \n\nAs mentioned earlier, Hunt and Sands [9] define security of their interactive programs in terms of infinite input streams, but don’t use streams for describing their outputs. On the other hand, Askarov, Hunt, Sabelfeld, and Sands [1] explicitly consider potentially infinite sequences of outputs in their assessment of the weaknesses of termination-insensitive security, but do not deal with any kind of input. Matos, et. al. [13] give a type system and a proof of information-flow security for a notion of “reactive programs” rather different from ours. Their programs run (deterministically) to completion without consuming any intermediate input or producing any intermediate output.  \n\n# 7. CONCLUSIONS AND FUTURE WORK  \n\nWe have proposed a formal definition of information-flow security for programs driven by event handling and have shown that language-based enforcement is a viable means to guarantee this property. It is now natural to ask how well this maps onto the real world of web programming.  \n\nThe first issue is whether our definitions of security correctly capture the sort of web browsing confidentiality policies we desire in practice. Of course, such definitions cannot guarantee confidentiality in an absolute sense, since there are covert channels, such as timing channels, that are outside of our model. Assessing these channels and taking steps to mitigate their effects is an important part of any realworld security implementation. At the same time, like other definitions of “pure noninterference,” our definitions are too restrictive, in that they rule out programs that must release (parts of) secret information to properly function: consider a mashup that must reveal a private street address to Google in the course of locating it with a Google Maps component. Finding appropriate mechanisms for declassification is an important direction for future work.  \n\nAnother question is whether our fundamental model of interaction is flexible enough to account for real-world web behavior. Real network messages may be structured and may include different substructures with different security levels. For the purpose of a noninterference analysis, one could easily model such a message as a sequence of messages at different levels. However, a naive labeling of web page structures and a strict adherence to reactive noninterference can lead to some surprising results. For instance, if an entire HTTP response containing a web page is given a non-public security label, then it would not be secure for the browser to load any images on that page from servers with incomparable security labels: there would be no direct flow, but a system input (the HTTP response in this case) at one security level must only cause system outputs at the same level or higher. One easy workaround for this scenario is to give the initial HTTP response a public label and to use a private label only for the body of the message.  \n\nThe connection between our model and the user interface is also very important, and the design space is complex. Users need to be able to understand the security interpretation that the browser assigns to each event they generate; otherwise they have no way of understanding the model’s guarantees about the confidentiality of their input and browsing actions. In particular, users need to understand the precise form of the “pseudo-messages” corresponding to their actions. The message content corresponding to the action of entering text in a text box is reasonably clear. However, the action of clicking on an HTML link is much more subtle: if it is interpreted as a message from the user to the browser that contains the entire URL of the link destination, then reactive noninterference actually puts the burden on the user for verifying that the URL does not contain any encoding of any piece of secret data. Obviously, the user cannot do this without assistance from the browser, but it must be noted that a reactive noninterference policy says nothing about the correctness of that assistance. In addition to the contents of these user-generated messages, their security levels must also be determined. This could be controlled with a global “secrecy mode” setting for the whole browser, a per-window secrecy mode, a per-DOM-element secrecy mode, a per-action dialog box, or some combination of these. Moreover, in principle, it is possible that some of these modes might cause a single user action, such as a button press, to be viewed as a sequence of messages with different security levels (this would be useful for the same reason that it might be useful to view an HTTP response as a sequence of messages, as described above). A complex security interface will be hard to understand; an overly simple one may not provide enough flexibility to support web pages that interact with multiple remote sites or may not provide as much confidentiality as the user would like. There seem to be fundamental tradeoffs between flexibility, complexity, and security in this design space.  \n\nSince our model rules out preemptive multitasking, one may wonder whether it can account for timer events, which are common in web programming. The information security of timer events can be understood by modeling them as AJAX requests to a remote server that sends back a response after a fixed amount of time. Of course, concerns about covert timing channels must still be handled separately, and timing channels that exploit timer events may have a much higher bandwidth than covert channels based solely upon the timing of real network messages.  \n\nAn entirely different question is whether language-based security is the best mechanism for enforcing our noninterference properties in the setting of web browsers. Although its event handling follows the same basic model as JavaScript, RIMP is a long way from a web scripting language. First, one would want to add some of the key features of JavaScript, such as first class functions, the ability to dynamically add and remove handlers, and eval. Second, one would need to design a security-aware version of the DOM interface for this language to use. Finally, one would have to implement a method for type-checking and running secure scripts in a manner that is reasonably backwards-compatible with existing web pages and scripts. All of these are important topics for future research.  \n\n# Acknowledgments  \n\nDamien Pous made a big contribution to the early discussions that eventually led to this paper. We gratefully acknowledge support from the National Science Foundation under grant number 0715936, Manifest Security.  \n\n# 8. REFERENCES  \n\n[1] A. Askarov, S. Hunt, A. Sabelfeld, and D. Sands. Termination-insensitive noninterference leaks more than just a bit. In In Proceedings of the 13th European Symposium on Research in Computer Security, pages 333–348, Malaga, Spain, Oct. 2008.   \n[2] D. Clark and S. Hunt. Non-interference for deterministic interactive programs. In Formal Aspects of Security and Trust (FAST) ’08, 2008.   \n[3] Coq Development Team. The Coq Proof Assistant Reference Manual v8.1. http://coq.inria.fr/.   \n[4] R. Focardi and R. Gorrieri. A classification of security properties for process algebras. Journal of Computer Security, 3(1):5–33, 1995.   \n[5] J. A. Goguen and J. Meseguer. Security policies and security models. In Proc. IEEE Symposium on Security and Privacy, pages 11–20. IEEE Computer Society Press, Apr. 1982.   \n[6] J. A. Goguen and J. Meseguer. Unwinding and inference control. In In Proceedings of the IEEE Symposium on Security and Privacy, 1984.   \n[7] I. Gray, J.W. Probabilistic interference. pages 170–179, May 1990.   \n[8] J. Y. Halpern and K. R. O’Neill. Secrecy in multiagent systems. ACM Transactions on Information and Systems Security, 12(1):1–47, 2008.   \n[9] S. Hunt and D. Sands. Just forget it—the semantics and enforcement of information erasure. In In Proceedings of the 17th European Symposium on Programming (ESOP’08). Springer-Verlag (LNCS), 2008.   \n[10] C. Jackson, A. Bortz, D. Boneh, and J. C. Mitchell. Protecting browser state from web privacy attacks. In WWW ’06: Proceedings of the 15th international conference on World Wide Web, pages 737–744, New York, NY, USA, 2006. ACM.   \n[11] C. Jackson and H. J. Wang. Subspace: Secure cross-domain communication for web mashups. In WWW ’07: Proceedings of the 16th international conference on World Wide Web, 2007.   \n[12] B. Jacobs and J. Rutten. A tutorial on (co)algebras and (co)induction. EATCS Bulletin, 62:62–222, 1997.   \n[13] A. A. Matos, G. Boudol, and I. Castellani. Typing noninterference for reactive programs. In In Proceeding of the Workshop on Foundations of Computer Security, 2004.   \n[14] D. McCullough. Noninterference and the composability of security properties. In Proc. IEEE Symposium on Security and Privacy, pages 177–186. IEEE Computer Society Press, May 1988.   \n[15] M. S. Miller, M. Samuel, B. Laurie, I. Awad, and M. Stay. Caja: Safe active content in sanitized JavaScript. A Google research project., Jan. 2008.   \n[16] K. R. O’Neill, M. R. Clarkson, and S. Chong. Information-flow security for interactive programs. In In Proceedings of the 19th IEEE Workshop on Computer Security Foundations, pages 190–201, Washington, DC, USA, 2006. IEEE.   \n[17] F. Pottier and V. Simonet. Information flow inference for ML. ACM Transactions on Programming Languages and Systems, 25(1):117–158, 2003.   \n[18] C. Reis, S. D. Gribble, and H. M. Levy. Architectural principles for safe web programs. Presented at the Sixth Workshop on Hot Topics in Networks (HotNets-VI), Nov. 2007.   \n[19] A. Sabelfeld and A. C. Myers. Language-based information-flow security. IEEE Journal on Selected Areas in Communications, 21(1):5–19, 2003.   \n[20] Same origin policy for JavaScript. http://www.mozilla.org/projects/security/components/ same-origin.html.   \n[21] D. Volpano, G. Smith, and C. Irvine. A sound type system for secure flow analysis. Journal of Computer Security, 4(2-3):167–187, 1996.   \n[22] A. Zakinthinos and E. S. Lee. A general theory of security properties. In In Proceedings of the IEEE Symposium on Security and Privacy, pages 94–102, Washington, DC, USA, 1997. IEEE.  \n\n# APPENDIX  \n\n# A. COINDUCTIVE DEFINITIONS  \n\nWe have used the Coq proof assistant [3] to guide our intuition about coinduction and to check many of our definitions and proofs. Following Coq’s type-theoretic notion of coinduction, we take coinductive definitions as a primitive notion. We view our inference rules, both inductive and coinductive, as definitions of logical propositions, although they have an obvious translation to a set-theoretic definition of mathematical relations.  \n\nA coinductive definition can be understood as taking the greatest fixed-point interpretation of a grammar or a set of inference rules. In the case of a grammar, a coinductive definition describes the set of all finite or infinite objects that can be built with repeated applications of the term constructors (instead of just the finite objects). In the case of a proposition defined by a set of inference rules, a coinductive definition means that we allow the proposition to be proved with a finite or infinite derivation. This is often (and only) necessary when defining predicates on infinite data. Note that it is also perfectly reasonable to use an inductively defined proposition over coinductively defined data, which will mean that the truth of the proposition can only depend on a finite portion of the potentially infinite data. Inductively defined propositions give rise to a principle of induction, which can be used to prove a statement in which such a proposition appears as a hypothesis. On the other hand, coinductive definitions give rise a principle of coinduction, which can be used to prove a statement in which such a proposition appears as a conclusion.  \n\nFor further background on inductive and coinductive reasoning, see the tutorial by Jacobs and Rutten [12].  \n\n# B. EVALUATION OF RIMP EXPRESSIONS  \n\nB.1 Definition: Inductively define $\\mu\\vdash e\\Downarrow n$ with the following rules:  \n\n![](/tmp/output/5_20250326165042/images/d1175c70ae3a726d2ba6bf4d1e34a6ec96a0023897cfc76e6da0c77e1f74468a.jpg)  "}