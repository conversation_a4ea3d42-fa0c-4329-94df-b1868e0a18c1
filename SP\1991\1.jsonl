{"text": "# Safety Analysis For The Extended Schematic Protection Model  \n\nP.E<PERSON>  \n\nR.S. Sandhu  \n\nCenter for Secure Information Systems and Department of Information and Software Systems Engineering George Mason University, Fairfax, VA 22030-4444  \n\n# Abstract  \n\nAccess control models provide a formalism and framework for specifying control over access to information and other resources in multi-user computer systems. Useful access control models must balance expressive power with the decidability and complexity of safety analysis, i.e. the determination of whether or not a given subject can ever acquire access to a given object. The access matrix model of <PERSON>, <PERSON><PERSON>, and <PERSON> (HRU) has very broad expressive power. Unfortunately, HRU also has extremely weak safety properties; safety analysis is undecidable for most policies of practical interest. In this paper we show the remarkable result that an alternate formulation of HRU gives us strong safety properties. This alternate formulation is called the Extended Schematic Protection Model (ESPM). ESPM is derived from the Schematic Protection Model (SPM) by extending the creation operation to allow multiple parents for a child, as opposed to the conventional create operation of SPM which has a single parent for a child. It has earlier been shown that ESPM is equivalent in expressive power to HRU. Here we analyze the safety properties of ESPM. We show that, despite its equivalence to HRU, ESPM retains tractable safety analysis for a large class of protection schemes that are of practical interest.  \n\n# 1. Introduction, Background And Motivation  \n\nThe need for access controls arises in any computer system that provides for controlled sharing of information and other resources among multiple users. Access control models (or protection models) provide a formalism and framework for specifying, analyzing and implementing security policies in multi-user systems. These models are typically defined in terms of the well-known abstractions of subjects, objects and access rights with which we assume the reader is familiar.  \n\nIn this section we first give a brief review of access control models emphasizing the fact that the conventional black and white distinction between mandatory and discretionary access controls is inadequate. We then argue that models based on propagation of access rights actually transcend this distinction. This leads us to a discussion of the central topic of this paper, viz. the safety problem of determining whether or not a given subject can ever acquire access to a given object. Following this we give an outline of the principal contribution of this paper, which is the formal demonstration that very general expressive power and strong safety properties are simultaneously achieved by the Extended Schematic Protection Model (ESPM). This result is established and elaborated in the main body of the paper.  \n\n# 1.1. Access Control Models  \n\nThe first access control models to be proposed [12, 17] were completelydiscretionaryin thatthe creator of an object was vested with absolute freedom regarding who may or may not access the object. The vulnerability of such completely discretionary access controls (DAC) to Trojan Horse attacks is well known [11, for instance]. This vulnerability led to development of the so-called mandatory access controls (MAC) of the Bell and LaPadula model (BLP) [4]. Since then the MAC/DAC distinction has served as a basic principle for computer security. For instance it has been been embodied in the TCSEC [10] (popularly known as the Orange Book).  \n\nIt recent times it has become increasingly clear that useful access control models must go beyond the traditional MAC/DAC distinction. Indeed opinion on this matter has changed so rapidly that what would have been considered heresy a few years ago is now being accepted without controversy. There are several major lines of argument that have together resulted in bringing about this rapid conversion of opinion. We enumerate these below.  \n\n(1) Arguments based on secrecy policies. As might be expected these arguments have come from the military sector [13, 24]. In the main they consist of the demonstration that there are document handling policies in the military-such as ORCON (originator control) and NOFORN (no foreign)--- which cannot be readily expressed in BLP and are indeed not quite MAC or DAC in the conventional sense. A more abstract description of this need was given earlier by Millen [29]。   \n(2) Arguments based on integrity policies. The black and white MAC/DAC distinction of BLP was carried over to integrity by Biba [5]. An early attempt by Lipner [20] to apply Biba's model showed the need for additional controls on program execution. Boebert and Kain [7] pointed out limitations of Biba's MAC and proposed the type enforcement controls of LOCK. Attempts by Lee [19] and Schockley [37] to implement the Clark and Wilson “\"commercial'\" integrity policy [8] within the framework of Biba demonstrated that either additional “\"mandatory' controls must be enforced or the policy must be emasculated by requiring certain aspects of it to be statically specified. Clark and Wilson [9] have described a notion of mandatory controls which is derived from their model. Collectively the papers cited here make a compeling case that the BLP and Biba notions of MAC are simply too limited for integrity policies.   \n(3) Arguments based on a more general notion of MAC. Sandhu [34, 36] has given alternate definitions of \"'mandatory' which show that the conventional BLP notion of MAC is but one special case of the general notion of access controls based on properties of subjects and objects rather than their identities. In the military nondisclosure context these properties turn out to be best expressed as partially ordered labels. In other contexts these properties are more naturally obtained in other ways. For instance the data type of an object determines what operations can be executed on that object. In [36], Sandhu argues that the real issue we need to focus on is whether or not the properties on which our \"'mandatory' access controls are based are static or dynamic. He also argues that attempts to define a notion of \"'mandatory'' controls as something which lie between label-based mandatory and discretionary controls should be dropped in favor of definitions such as his which treat label-based mandatory controls as a special  \n\ncase of general \"'mandatory'' controls. Abrams et al [1, 18] have also made similar arguments in their concept of a generalized  framework for accesscontrol.  \n\n(4) Arguments based on foundational inadequacies of BLP. The foundational inadequacies of the BLP notion of mandatory controls were demonstrated in a series of papers by McLean [25, 26, 27, 28]. The thrust of McLean's argument is that if labels are considered to be changeable then the state-invariant  properties  of  BLP  can  be preserved by changing labels which in effect automatically   downgrades   information   on demand. He shows that the Basic Security Theorem of BLP can be proved for such an obviously insecure system. The main lesson from McLean's work for our purpose here is that we must attend to the dynamic aspects of access control.  \n\nWe now draw attention to a line of research in access control models based on the idea of controlling the propagation of access rights. The basic concept is that the access-matrix is used not only to control access of subjects to objects but also to control the transport of access rights from one subject to another. The seminal work was that of Harrison, Ruzzo and Ullman [14] who described an access control model, commonly called HRU, in which complex policies for transport of access rights could be easily stated. The BLP model is known to be a particularly trivial case of HRU [30] as is the completely discretionary access matrix of [12, 17]. Therefore HRU does indeed transcend the traditional MAC/DAC distinction and could readily handle many of the concerms enumerated above. The problem with HRU however is that it has weak safety properties which we explain next. Before doing so we wish to emphasize and reiterate that our contribution in this paper is the demonstration of a model, viz. ESPM, which is formally equivalent in expressive power to HRU and yet has strong safety properties.  \n\n# 1.2. The Safety Problem  \n\nAs noted above, access control models not only specify the control of access rights to resources but also specify the propagation of such rights. The safety problem for access control models is the determination of whether or not a given subject can ever acquire access to a given resource.  Thus protection models must satisfy two conflicting requirements:  \n\n(1) The need for expressive power suffcient to conveniently describe security policies of practical interest.  \n\n# 1.2.1. Weak Safety Properties Of HRU  \n\nThe most general access control model, the access matrix model of Harrison, Ruzzo, and Ullman [14] (HRU), has broad expressive power; unfortunately, it also has weak safety properties. Harrison and Ruzzo [15] proved the following demarcation for the decidability of safety analysis in HRU:  \n\n(1) Safety  is  undecidable   for  bi-conditional schemes; i.e., the condition part of every command has at most two terms.   \n(2) Safetyis decidable  for  mono-conditional schemes; i.e., the condition part of every command has at most one term.t  \n\nMono-conditional systems are very restrictive and can accommodate only the simplest security policies. At the same time, models such as take-grant [16], which require bi-conditional commands, do have efficient safety analysis. Thus the demarcation of decidable safety in HRU is too pessimistic.  \n\nA restriction on expressive power that can have substantial benefits for safety analysis is that of monotonicity; monotonic models do not allow the deletion of access privileges. It must be noted that a strictly monotonic model is too restrictive to be of much practical use, since the ability to delete access privileges is an important requirement. We are really interested in models which can be reduced to monotonic models for purpose of safety analysis. In particular, we can ignore deletion of an access privilegeP whenever the deletion canitself be undone by regranting P. This is by far the most common form of revocation and it is indeed fortunate that monotonicmodelscan accommodatesuchdeletion.  \n\nSince monotonic models do not permit the deletion of access privileges, backtracking in analysis can be avoided. However, monotonicity by itself is insuffcient for tractable safety analysis. The monotonic version of the access matrix model of Harrison, Ruzzo, and Ullman [14]  (HRU) retains broad expressive power; unfortunately, despite its monotonicity, it also retains the weak demarcation of safety cited above. Safety analysis remains undecidable even for monotonic bi-conditional  \n\n# 1.2.2. Strong Safety Properties Of SPM  \n\nIn response to the relatively weak safety properties of HRU, a variety of models+ with more desirable safety properties have been proposed [6, 16, 21, 22, 23]. However, a substantial gap in expressive power exists between these models and HRU. Sandhu's Schematic Protection Model (SPM) [31, 32] was developed to fill this gap in expressive power while sustaining effcient safety analysis. The various models referenced above are all subsumed by SPM. SPM has remarkably strong safety properties and has been shown to represent a wide variety ofcases ofpracticalinterest.  \n\nDespite SPM's expressive power, atempts to demonstrate the equivalence of SPM to HRU have so far failed. There is now strong reason to believe that SPM is in fact less expressive than HRU [2, 3], although formal demonstration of this fact remains an important open question.  \n\nAccordingly, SPM has been extended. The single parent creation operation in SPM has been redefined to allow multiple parents for a child. With the joint creation operation, the new model, called ESPM, has been shown to be precisely equivalent to monotonic HRU [3].  \n\nThe question naturally arises as how safety analysis is affected by the increase in expressive power provided by the joint creation operation. In this paper, we present a safety analysis algorithm for a restricted subclass of ESPM schemes, namely those schemes in which the create structure is acyclic with the possible exception of attenuating loops (particular kinds of cycles of length one). The algorithm given here is based upon the SPM algorithm presented in [31]. However, the machinery of [31] is insuffcient for ESPM schemes. Here we demonstrate that the inclusion of a joint creation operation in the ESPM model still permits tractable safety analysis for many cases of practical interest. We also offer guidelines for how to employ the joint creation operation so as to minimize the computational effort of the safety analysis.  \n\n# 1.3. Outline of the Paper  \n\nThe organization of the rest of this paper is as follows. In section 2 we describe ESPM, an extension of SPM that has been shown equivalent in expressive power to monotonic HRU [2, 3]. In section 3, we present the our main result, which is a safety analysis algorithm for ESPM schemes with acyclic attenuating loops. The computational complexity of analyzing such schemes is given, and it is shown that the complexity is feasible for cases of practical interest. In section 4, we compare the safety results for ESPM and HRU. Section 5 concludes the paper.  \n\n# 2. ESPM  \n\nIn this section we define ESPM (Extended Schematic Protection Model). The description of ESPM here is of necessity terse and formal. Motivation of the various components of the model is given in [3, 31].  \n\nESPM is derived from SPM (Schematic Protection Model) by extending the creation operation to allow multiple parents for a child, as opposed to the conventional create operation of SPM which has a single parent for a child. This is the only difference between SPM and ESPM. For convenience, here we have chosen to define ESPM directly rather than first defining SPM and then defining ESPM as an extension.  \n\nESPM is based on the key principle of protection types, henceforth abbreviated as types. ESPM subjects and objects are strongly typed, i.e., the type of an entity (subject or object) is determined when the entity is created and does not change thereafter. Types are an abstraction of the intuitive notion of properties that are security relevant. An ESPM scheme is to a large extent, but not exclusively, defined in terms of types. The dynamic privileges in ESPM are tickets of the form Y/r where Y identifies some unique entity and $\\pmb{r}$ is a right. The notion of type is extended to tickets by defining type $(Y/\\gamma)$ to be the ordered pair type $({\\pmb{\\mathrm{m}}}/{\\pmb{r}}.$ That is the type of a ticket is determined by the type of entity it addresses and the right symbol it carries.  \n\nESPM has only two operations for changing the protection state, viz., create and copy.f These operations are authorized by rules which comprise the scheme defined by specifying the following (finite) components.  \n\n(1) Disjoint sets of subject types TS and object types $\\pmb{T O}$ . Let $T=T S\\cup T O$   \n(2) A set of rights ${\\pmb R}$ The set of ticket types is thereby $\\pmb{T}\\times\\pmb{R}$   \n(3) A can-create function: $c c\\colon T S{\\times}T S{\\times}\\cdots{\\times}T S\\rightarrow2^{T}$   \n(4) Create rules of the form: $\\begin{array}{r l}&{c r_{p_{i}}(u_{1},u_{2},...,u_{N},\\nu)=}\\ &{\\qquadc/R_{1}^{i}\\cup p_{i}/R_{2}^{i}\\mathrm{for}i=1..N}\\ &{}\\ &{c r_{c}(u_{1},u_{2},...,u_{N},\\nu)=}\\ &{\\qquadc/R_{3}\\cup p_{1}/R_{4}^{1}\\cup p_{2}/R_{4}^{2}\\cup\\dots\\cup p_{N}/R_{4}^{N}.}\\end{array}$ where $\\pmb{p_{i}}$ is the ith parent and $\\pmb{c}$ is the child.   \n(5) Acollectionof linkpredicates $\\langle l i n k_{i}\\rangle$   \n(6) A flter function $f_{i}:T S{\\times}T S\\to2^{T\\times R}$ for each predicate link;.  \n\nAn ESPM scheme is itself static and does not change. We now explain how the scheme controls and regulates the propagation and creation of access rights.  \n\n# The Create Operation  \n\nCreation is authorized exclusively by types. Subjects of type $u_{1},u_{2},...,u_{N}$ can (jointly) create entities of type v if and only if $\\nu\\in c c(u_{1},u_{2},...,u_{N})$ $\\pmb{N}$ may take on any positive value, although for any given scheme this value is of course bounded. The case of $N{=}1$ corresponds to the conventional creation operation in SPM. The case of $N{>}1$ makes ESPM different from SPM by authorizing multiple parents to jointly and cooperatively create a child subject or object. Note that, if type constraints are met, we allow a subject to participate as more than one parent in a joint create operation.  \n\nTickets introduced as the side effect of creation are specified by create-rules.  In the create rules c is the name of the jointly created entity and $\\pmb{p_{i}}$ is the name of the ith parent. The sets $R_{1}^{i},R_{2}^{i},R_{3}$ , and ${\\pmb R}_{4}^{i}$ ,for $i=1.\\pmb{\\mathscr{N}}$ are subsets of R. When subjects $U_{1},U_{2},\\cdots U_{N}$ of type $u_{1},u_{2},\\cdots u_{N}$ create entity $\\pmb{V}$ of type $\\pmb{\\nu}$ the parent $U_{i}$ gets the tickets $V/R_{1}^{i}$ and $\\pmb{U}_{i}/\\pmb{R}_{2}^{i}$ as specified by $c r_{p_{i}}$ . The child $\\pmb{V}$ similarly gets the tickets $V/R_{3}$ and $U_{i}/R_{4}^{i}$ for each parent $U_{i}$ as specified by $c r_{\\pmb{c}}$ . As an example, consider the single parent creation case in which ${\\hat{\\pmb{\\mathscr{n}}}}l e\\in $ $\\pmb{c c}(\\pmb{u s e r})$ authorizes users to creates fles; $c r_{p}(u s e r,\\hat{\\pi}l e)$ $=c/\\w$ and $c r_{c}(u s e r,f l e)=\\emptyset$ gives the creator r and $\\pmb{w}$ tickets for the created file. Note that the superscript i is used to specify a (potentially) different set for each of the $\\pmb{N}$ parents. Also note that the parents are not allowed to directly exchange tickets with other parents as a result of creation; the copy operation is required to do this.  \n\n# The Copy Operation  \n\nA copy of a ticket can be transferred from one subject to another leaving the original ticket intact. Permission to copy a ticket Y/r depends in part on possession of the ESPM copy flag, c, for that ticket, denoted Y/rc. Possession of Y/rc implies possession of $\\pmb{Y}/\\pmb{r}$ but not vice versa. It is possible to copy $\\pmb{Y}/\\pmb{r}$ only, or to copy Ylrc, in which case the ticket may be further copied. Let dom $(U)$ signify the set of tickets possessed by $\\pmb{U}$ Three independent pieces of authorization are required to copy Y/r from $\\pmb{U}$ to V.  \n\n(1) $Y/r c\\in d o m(U)$ i.e., $\\pmb{U}$ must possess Y/rc for copying eitherY Irc or $\\pmb{Y}/\\pmb{r}$   \n(2) There is a link from $\\pmb{U}$ to $\\pmb{V}.$ Links are established by tickets for $\\pmb{U}$ and $\\pmb{V}$ in the domains of $\\pmb{U}$ and V. The predicate $l i n k_{i}(U,V)$ is defined as a conjunction or disjunction, but not negation, of one or more of the following terms for any $r\\in R$ $U/r\\in d o m(U)$ U lr e dom(V), $V/r\\in d o m(U)$ ， $V/r\\in d o m(V)$ . and true . Some examples from the literature are given below [21, 23, 32, respectively]: $l i n k_{t_{\\ell}}(U,V)\\equiv V/g\\in d o m(U)\\times U/t\\in d o m(V)$ $l i n k_{t}(U,V)\\equiv U/t\\in$ dom(V) $l i n k_{s r}(U,V)\\equiv V/s\\in d o m(U)\\land U/r\\in d o m(V)$ $l i n k_{*}(U,V)\\equiv$ true   \n(3) The final condition is defined by the filter functions $f_{i}$ , one per predicate $l i n k_{i}$ .The value of $f_{i}(u,v)$ specifies types of tickets that may be copied from subjects of type $\\pmb{\\lfloor\\lfloor\\underline{{u}}\\rfloor}$ to subjects of type $\\pmb{\\nu}$ over $l i n k_{i}$ .Also $f_{i}$ determines whether or not the copied ticket can have the copy flag. Example values are $T{\\times}R,T O{\\times}R$ and $\\pmb{\\varnothing}$ respectively authorizing all tickets, object tickets and no tickets to be copied via a link;.  \n\nIn short $\\pmb{Y}/r$ can be copied from $\\pmb{U}$ to $\\pmb{V}$ if and only if there exists some $l i n k_{i}$ such that:  \n\n$$\nY/r c\\in d o m(U)\\wedge l i n k_{i}(U,V)\\wedge y/r\\in f_{i}(u,\\nu)\n$$  \n\nwhere the types of $\\pmb{U}$ ,V and $\\pmb{Y}$ are respectively ${\\pmb u},{\\pmb v}$ and y. To copy Y/rc from $\\pmb{U}$ to $\\pmb{V},$ it must also be the case that $y/r c\\in f_{i}(u,\\nu)$  \n\n# 3. Safety Analysis Of ESPM  \n\nThe fact that safety analysis is undecidable for arbitrary ESPM schemes is immediate from either of the following two observations:  \n\n(1) ESPM is equivalent in expressive power to HRU [2, 3], and safety is undecidable for HRU [14, 15].   \n(2) ESPM is a generalization of SPM, and safety is known to be undecidable for arbitrary SPM schemes [35].  \n\nHowever, relatively minor restrictions (from a practical point of view) on ESPM schemes permit decidable and indeed tractable safety analysis. As in SPM [31], the tractability of safety analysis for ESPM given here turns out to be determined by the creation operation.  \n\nThe safety analysis of ESPM is modeled on Sandhu's treatment of SPM in[31].There are significant differences in the details. In this section we give an outline of how the results of [31] can be modified to apply to ESPM. Section 3.1 reviews the general strategy of unfolding and  the canonical and maximal states employed in [31]. Section 3.2 defines the analog of SPM's acyclic attenuating create operations in ESPM. This is a straightforward generalization. Section 3.3 defines the analog of SPM's surrogate function in ESPM which we call the ID function. This requires some care because of the multi-parent creation in ESPM. Section 3.4 defines the construction of a canonical state in ESPM by unfolding. Unlike in SPM, we have to be very careful regarding the sequence of creates to ensure that the unfolding process will terminate for acyclic attenuating creates.Section 3.5 sketches a proof of correctness for this construction.Section 3.6 discusses the complexity of safety analysis,including some observations on how the complexity can be kept manageable in practice.  \n\n# 3.1. Safety Analysis from the Maximal State  \n\nInhissafetyanalysisforSPM,Sandhufirstshows that we can assume without loss of generality that all create operations occur before any copy operations [31, lemma 12]. This property also clearly applies to ESPM, and will in fact be true for many monotonic models.f This fact motivates the following strategy for safety analysis.  \n\n(1) Starting with the given initial state, first create as many subjects as are necessary to account for the worst-casebehavior of thesystemwithrespectto propagation of access rights.Call this the canonical state of thesystem.  \n\n(2) Given the canonical state, perform all copy operations until the state does not change any further. Call this the maximal state of the system.  \n\n(3) A specific safety question such as, “Can subject X obtain right r for object Y?'\" is then answered by looking at the maximal state and seeing whether or not X actually possesses the right 1 for Y in this worst-case state.  \n\nThe second step in this procedure is guaranteed to terminate because the canonical state has a finite number of subjects, objects and rights and therefore the copy operations will eventually be unable to propagate any new privileges. The problem lies in the first step where we need some criteria to determine when all the necessary create operations have occurred. In other words we need to be able to recognize a canonical state. The undecidability result of [35] shows that it is impossible in general to recognize a canonical state. However, there are reasonable restrictions on the can-create function of SPM which make construction of the canonical state trivial [31]. In particular if can-create has no cycles or only has the so-called attenuating cycles of Iength one the canonical state can be constructed by an operation called unfolding.   These restrictions on  can-create are eminently reasonable as evidenced by the fact that no practical policy to date has required non-attenuating cycles in can-create [31, 32].  \n\n# 3.2. Restrictions on Joint Create Operations  \n\nIn the safety analysis given for SPM in [31], the create function is restricted to be acyclic except for certain cycles of length one, or loops. In other words, the cc function partially orders the types. We define the create graph to be the directed graph with types as nodes and edges defined by cc. The loops that are allowed are for create operations with attenuating create rules. Attenuating create rules specify that for those tickets acquired as a result of a create operation, the tickets acquired by the child are a subset of those acquired by the parent. The idea is that the parent can then simulate any possible action of the child, and thus the creation of the child may be ignored.  \n\nFor the analysis of the joint create operation in ESPM we similarly restrict the multiple parent create function to be acyclic except for attenuating create loops. Here the create graph is a hypergraph; each directed edge originates at a set of nodes. The restriction that at least one parent in a multiple-parent, loop create operation must be able to simulate the child effectively means that, in general, child tickets may not be distributed to either the child or its parents, and that no parent tickets may be distributed to the child. Thus for creation operations in whichthetype $\\pmb{\\nu}$ of the child is the same as the type $\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lfloor}\\pmb{\\lambda}\\ \\ {\\mathclose\\b{\\lfloor}}\\end{array}}\\ \\pmb{\\lambda}$ of one or more parents, we have the general rule:  \n\n$$\n\\begin{array}{r l}&{c r_{p_{i}}(u_{1},u_{2},...,u_{N},v)=p_{i}/R_{2}^{i}~\\mathrm{for}~i=1..N}\\ &{c r_{c}(u_{1},u_{2},...,u_{N},v)=\\emptyset.}\\end{array}\n$$  \n\nNote that a parent can only use an attenuating loop create operation to increase the set of tickets that it holds for itself.  \n\nHowever, the general rule given above is too pessimistic in two important situations. First, for single parent creation, these rules are relaxed, as was done in [31], so that the parent's tickets need only be a superset of the child's tickets. Second, for multi-parent creation, one of the parents with type matching the child may be treated in a similar manner; the child may receive tickets for itself and that one parent if that parent receives a superset of these tickets.f If multiple parents in an attenuating loop creation were allowed to receive tickets for the child, no single parent would be able to simulate the actions of the child. For this reason, attenuating create rules do not allow such a situation.  \n\n# 3.3. The ID Function  \n\nTo realize the strategy of section 3.1 we need to provide a sufficiently rich set of canonical entities and show how to map actual ESPM entities onto canonical entities. In [31], a special function, called the surrogate function, is introduced to provide correspondence between canonical SPM entities and entities in arbitrary SPM histories. The surrogate function turns out to be inadequate for ESPM schemes because, except for individual entities in the initial state, the surrogate function is based strictly on the notion of type. For joint create, we need to capture the notion of grouping entities together. We therefore define a function, which we call the $\\pmb{I D}$ function, which assigns a name or identification to every ESPM entity.  \n\nThe ID function is recursively defined below. Consider an entity V of type ${\\pmb v}_{\\bullet}$ f $\\pmb{v}$ is not in the initial state, it is assumed to have parent(s) $U_{1}\\dots\\pmb{U_{N}}$ of types ${\\pmb u}_{1}...{\\pmb u}_{N}$ There are three cases to consider:  \n\nIf V is in the initial state then $I D(V)=V.$ The ID of an entity in the initial state is simply the name  \n\n# of that entity.  \n\n(2) If ${\\pmb{v}}\\ne{\\pmb{u}}_{i}$ forall $i=1.\\mathcal{N}$ then $I D(V)=C_{v}(I D(U_{1}),\\dots,I D(U_{N}))$ . The ID of an entity that is strictly below all of its parents in the create/joint-create graph is simply a grouping of the $\\pmb{I D}^{*}\\pmb{S}$ of the child's parents. The group is tagged with the child's type. Note that since there are a finite number of types, we may effectivelyregard the $c_{\\ast}$ as constants.  \n\n(3) If $v=u_{i}$ for some $i=1.\\ensuremath{N}$ then $I D(V)=I D(U_{i})$ for the first i for which $\\pmb{\\nu}=\\pmb{u}_{i}$ . Since any child produced by an attenuating create rule can be simulated by at least one of the parents, the ID of an entity that has been created with an attenuating loop create rule is the $\\pmb{I D}$ of aparent of the matching type. Since there may be multiple parents with the same type as the child, we simply define the $\\pmb{I D}$ function to map to the first such parent.  \n\nWe define one canonical entity for each element in the range of the ID function.Thus questions about the correspondence between ESPM entities and canonical entitiesreducetoquestions about the $\\pmb{I D}$ function.Our first task is to show that there are a finite number of canonical entities for acyclic attenuating schemes, i.e. that therangeof $\\pmb{I D}$ is finite:  \n\nLemma 1 Given any acyclic attenuating ESPM scheme, the range of the ID function is a finite set.  \n\nProof Consider each of the three cases. Clearly, case 1 presents no difficulty since the case represents the base case of the recursion and there are a finite number of entities in theinitial state. In a depth-first evaluation, Case 2 cannot be applied more often than there are types in the ESPM scheme due to the acyclic structure of the create graph. Since the tree structure introduced by case 2 has a bounded number of children at each node and a finite depth, the number of entries in the tree must be finite. Finally, case 3 does not alter the value of the ID function, so it may be invoked an arbitrary number of times without affecting thefunction'svalue.Since cases 1 and2 are the only rules that canbe used to generate distinct names, and since each case can only be applied a finite number of times, the range of the ID function is finite.  \n\n# 3.4. The Unfolding Aigorithm  \n\nOur goal is to unfold an initial state and have the result be the complete collection of canonical entities, one entity for each element in the range of the ID function. As a first step, we define cc ', to be the acyclic part of the function cc. Now, if we simply apply arbitrary rules from $\\acute{c c}$ first to entities in an initial state and then to entities in each resulting state, it is not at all clear whether the unfolding process ever terminates. However, there is an order for applying the creation rules in cc ' such that the unfolding process is guaranteed to terminate. The order depends upon the type of entity produced by a create operation, but not upon the parent type(s).  \n\nTo develop the correct ordering, we reformulate the can-create function cc' as a relation. For each tuple of types $(u_{1},u_{2},...,u_{N})$ in the domain of $\\acute{c c},$ we replace the  mapping  cC $\\left(u_{1},u_{2},...,u_{N}\\right)=S$ where $\\boldsymbol{S}=\\{s_{1},s_{2},...,s_{M}\\}$ is a subset of $\\pmb{T},$ with the relation, $\\{((u_{1},u_{2},...,u_{N}),s_{1}),$ $((u_{1},u_{2},...,u_{N}),s_{2}),$ . $((u_{1},u_{2},...,u_{N}),s_{M})\\}$ . Note that in each ordered pair in the relation, the abscissa, or first element, is the tuple of parent types and the ordinate, or second element, is a single child type. We refer to an ordered pair in this relation as a create-tuple.  \n\nNow we (partially) order the create-tuples based on the ordinate $\\mathbf{\\Delta}_{i e}$ the child type). That is, for every pair of types $\\pmb{S}$ and t, if $\\pmb{S}$ precedes 1 in the create graph, then every create-tuple with $\\pmb{S}$ as an ordinate must precede every create-tuple with t as an ordinate. We may then make the key observation, which is guaranteed by the acyclic structure of cc',that in the ordered list of create-tuples, every create-tuple that employs type t as a parent follows all of the create-tuples that produce type 1 as a child. The observation allows us to consider a create-tuple once during the unfolding process and be sure that the create-tuple will not be subsequently “reenabled' with a new set of parents as a result of some later creation operation.  \n\nWe build the canonical state as follows: First, we put the entities from the initial state into the canonical state. Next, we proceed down the ordered list of create-tuples and apply each create-tuple once to each possible tuple of parent entities in the canonical state. The resulting entity from each application of a create-tuple is placed in the canonical state. This process is illustrated with an example in fig. 1. In fig. 1, entities are represented by subscripted upper case letters; the type of a given entity is the same letter in lower case.  \n\n# Lemma 2  The unfolding operation terminates.  \n\nProof That the unfolding procedure terminates can be seen by noting that no application of a particular create-tuple can result in either that create-tuple, or any other create-tuple  \n\nTypes: $\\bar{T}=\\{x,y,z\\}$   \nCreate Rules: $\\begin{array}{l}{c c(x,y)=\\{y,z\\}}\\ {c c(x)=\\{x,y\\}}\\end{array}$   \nAcyclicPortion Of CreateRules: $\\begin{array}{l}{{c c^{\\prime}(x,y)=z}}\\ {{c c^{\\prime}(x)=y}}\\end{array}$   \nOrdered Create Tuples from cc “ $\\begin{array}{l}{(\\langle x\\rangle,y)}\\ {((x,y),z)}\\end{array}$   \nInitial State $\\{\\pmb{X}_{1},\\pmb{X}_{2},\\pmb{Y}_{1}\\}$  \n\nCanonical State and $\\pmb{I D}$ function values:  \n\n$\\begin{array}{r l}{X_{1}}&{{}I D(X_{1})=X_{1}}\\ {X_{2}}&{{}I D(X_{2})=X_{2}}\\ {Y_{1}}&{{}I D(Y_{1})=Y_{1}}\\end{array}$   \n$\\pmb{Y_{2}}$ $\\begin{array}{r l}&{^{I D}(\\textbf{\\emph{1}}/\\textbf{\\emph{1}})=C_{1}}\\ &{^{I D}(Y_{2})=C_{9}(I D(X_{1}))=C_{9}(X_{1})}\\ &{^{I D}(Y_{3})=C_{9}(I D(X_{2}))=C_{9}(X_{2})}\\ &{^{I D}(Z_{1})=C_{*}(I D(X_{1}),I D(Y_{1}))=C_{*}(X_{1},Y_{1})}\\ &{^{I D}(Z_{2})=C_{*}(I D(X_{1}),I D(Y_{2}))=C_{*}(X_{1},C_{9}(X_{1}))}\\ &{^{I D}(Z_{3})=C_{*}(I D(X_{1}),I D(Y_{3}))=C_{*}(X_{1},C_{9}(X_{2}))}\\ &{^{I D}(Z_{4})=C_{*}(I D(X_{2}),I D(Y_{1}))=C_{*}(X_{2},Y_{1})}\\ &{^{I D}(Z_{5})=C_{*}(I D(X_{2}),I D(Y_{2}))=C_{*}(X_{2},C_{9}(X_{1}))}\\ &{^{I D}(Z_{6})=C_{*}(I D(X_{2}),I D(Y_{3}))=C_{*}(X_{2},C_{9}(X_{2}))}\\end{array}$   \n$\\pmb{Y_{3}}$   \n$z_{1}$   \n$\\mathbf{Z_{2}}$   \n$\\mathbf{z_{3}}$   \nZ4   \nZs   \nZ6  \n\nFig. 1. Example of Unfolding An Initial State.  \n\nentities are unique and the fact that each create-tuple is being applied only once to each unique tuple of parents.  \n\nconsidered before it, being applicable to a previously  unconsidered  tuple  of entities. Thus for each create-tuple, there are a fixed number of applications possible. Since each create-tuple is considered only once, the procedure terminates.  \n\nFinally, we consider the attenuating loop portion of cc. We allow a single application of each attenuating rule to each possible entity or set of entities in the unfolded state. The resulting entities are not placed into the canonical state, since the IDs for these entities are already present. The efect is to supply each canonical parent with all tickets that can be acquired as a result of creation.  \n\nLemma 3 The ID function assumes a unique value for each entity in the unfolded state.  \n\nTo complete our analysis we need to show that  \n\n# 3.5. Proof of Correctness  \n\nProof The proof proceeds by induction. Clearly the property holds in the initial state. Assume that the property holds at some arbitrary point in the unfolding and consider the application of the current create-tuple. The $\\pmb{I D}$ function for the new entity must be acquired by applying case 2 of the definition of the $\\pmb{I D}$ function. That the resulting $\\pmb{I D}$ is unique follows from the fact that the IDs of all existing canonical  \n\n(1) From the safety perspective, an arbitrary sequence of acyclic attenuating ESPM create and copy operations can be subsumed by copy operations only on the canonical state.   \n(2) Safety questions in the canonical state can be answered in polynomial time.  \n\nBoth of these question are dealt with for SPM in [31]; the first question is theorem 17, and the second is discussed in the text immediately following corollary 18. We have carefully arranged our analysis here such that if the surrogate function from [31] is replaced with the ID function, the analysis for the two questions listed above can be taken directly from [31]. A complete formal proof of these results is much too long for the scope of this paper and may be found in [2].  \n\n# 3.6. Complexity of Safety Analysis  \n\nWe now present the safety algorithm's cost in computational complexity and offer guidelines on how to minimize costs in practical applications. As is the case for SPM, the time required to construct the canonical state for ESPM is exponential in the number of types if the cc relation is dense. This may not be a serious obstacle if the scheme employs relatively few types.  \n\nOn the other hand, the required time is also multiply exponential in the number of parents of the joint creation operator. The reason is that each application of an $N\\cdot$ parent create operation that produces a specific child type, v, results in on the order of $I^{N}$ new entities being added to the canonical state, where I is the number of entities in the canonical state before any entities of type v are produced. For each case in which the child of a joint create can participate as a parent in another joint create (excluding attenuating loops) this expansion is repeated.  \n\nLet $\\pmb{x}$ be the number of create-tuples derived from cc', $N_{i}$ ， $\\mathbf{\\Phi}_{i=1.x}$ be the number of parents in the creation operation corresponding to the ith create-tuple, and I be the size of the initial state. Note that $\\pmb{N_{i}}$ may well equal 1, which corresponds to using the SPM create operation. The application of the first create-tuple results in a canonical state whose size, $I_{1}$ ,is on the order of $I_{1}=O(I+I^{N_{1}})$ . The application of the second createtuple results in $\\begin{array}{r}{I_{2}=O(\\dot{I_{1}}^{-}+I_{1}^{N_{2}})}\\end{array}$ . This process continues up  to the， complete  canonical  state $\\pmb{I_{x}}$ $I_{x}^{'}=O(I_{x-1}+I_{x-1}^{N_{x}})$  \n\nClearly, the joint create operation needs to be used with great care to keep the analysis feasible. Some simple rules are:  \n\n(1) Use single parent creation where possible.   \n(2) Do not use a value for $\\pmb{N}$ that is any larger than necessary, ie keep the number of parents in joint creates as small as possible.   \n(3) Minimize the opportunities for descendants of a joint creation operation to participate in further creation operations.   \n(4) Avoid dense cc functions.  \n\nThese appear to be reasonable guidelines which can be easily achieved in practice.  \n\n# 4. Expressive Power Of ESPM  \n\nIn this section we discuss results regarding the expressive power of ESPM from both the formal and pragmatic aspects.  \n\n# 4.1. Formal Equivalence Of ESPM And HRU  \n\nWith respect to the formal expressive power of ESPM, it has been shown that ESPM is precisely equivalent in expressive power to the monotonic case of HRU [2, 3]. Since monotonic HRU is the most general monotonic protection model to date, the theoretical expressive power of ESPM is thereby the most general known thus far. Equivalence of ESPM and HRU is established by simulating monotonic HRU in ESPM and vice versa. The simulation of HRU in ESPM is by far the more difficult part of this proof. Since the complete details are lengthy [2], we briefly sketch below just the key role played by the joint creation operation.  \n\nAn ESPM simulation of monotonic HRU is required to account for all of the various operations involved in executing an arbitrary HRU command. It turns out that there is no particular difficulty in simulating most of these operations, even though the details of the simulations are quite intricate. Simulating the evaluation of an  \n\nHRU conditional, the creation of a new HRU entity, and the entering of a right into a cell in the access matrix all turn out to be fairly straight forward, provided that one has access to exactly those HRU entities that are participating in the particular invocation of the HRU command.  \n\nIt is the grouping of HRU entities into the parameter lists for HRU commands that appears to lie beyond the expressive power of SPM. On a particular HRU command invocation, it is necessary to consider a specific subset of HRU entities, and to exclude from consideration all other HRU entities. On a subsequent HRU command invocation, it is necessary to consider a different subset of HRU entities, and so on. The single parent creation operation of SPM is not suited this task, nor, apparently, is any other SPM feature. However, the joint creation operation of ESPM is ideal. In the simulation in [2], the joint creation operation is used with each (existing) entity in the HRU parameter list participating as a parent. The child entity of this creation is given a ticket to identify each parent and its position in the parameter list. These tickets enable the child to oversee the simulation of the various parts of an HRU command.  \n\nSeveral points warrant notice. First, something like a joint creation capability seems to be necessary to achieve the expressive power of monotonic HRU. SPM by itself appears to be less expressive than HRU. Second, since the create structure used in the construction is cyclic (i.e., entities of type $\\pmb{p}$ can indirectly create othertype $\\pmb{p}$ entities), the safety of the scheme is outside the cases known to be decidable [31, 35]; this characteristic is consistent with the weak safety properties of HRU. Finally, the construction is not the most natural way to implement policies. Due to the general nature of the construction, even simple policies, such as Take/Grant, are transformed into lengthy schemes. For instance,   a   straightforward   implementation   of Take/Grant in SPM requires only two link predicates [31]. However, defining Take/Grant in HRU, and then applying the construction outlined in [2] results in over twenty link predicates.  \n\n# 4.2. Pragmatic Expressive Power Of ESPM  \n\nNot only does ESPM have the theoretical expressive power discussed above but it also has a natural expression of many practical policies which have been published in the literature. A great deal of evidence for this assertion comes from the known expressive power of SPM in this regard. It has been shown [32] that SPM subsumes several well-known protection models, including BLP [4] and take/grant [16, 21] as special cases. Moreover SPM subsumes these models within its decidable cases for safety analysis. Therefore SPM subsumes these models not only in terms of its expressive power but also in terms of safety analysis. This is in sharp contrast to HRU, which does subsume these models but outside its known decidable classes for safety. The joint creation capability of ESPM in addition provides significant pragmatic benefit in solving a variety of well-known security problems such as mutual suspicion, protected subsystems, confinement and separation of duties [3].  These problems have very natural and intuitive solutions based on joint creation.  \n\n# 5. Conclusion  \n\nIn this paper, we have enumerated a compelling list of arguments as to why the MAC/DAC framework for defining security policies is inadequate. The arguments are based on the requirements for secrecy and integrity policies, the lack of generality of the MAC framework, and the foundational weaknesses of the Bell LaPadula model. More general access control models can address these arguments and include MAC and DAC as special cases.  \n\nWe have argued that models based on propagation of access rights already transcend the MAC/DAC distinction. The challenge with access control models is to provide adequate  expressive power  without sacrificing safety analysis. To date, models with broad expressive power, eg HRU, exhibit weak safety properties, and models  with  desirable  safety  properties  exhibit  less expressive power than HRU. The most expressive model todate with strong safety properties is Sandhu's Schematic Protection Model (SPM). However, its relative power with respect to monotonic HRU remains an open question. We now conjecture that SPM is actually less expressive than HRU.  \n\nWe have demonstrated that by extending SPM with a joint creation operation, the resulting model, called ESPM, simultaneously enjoys the expressive power of monotonic HRU and yet retains effcient safety analysis for protection schemes of practical interest. We have presented a safety analysis algorithm for ESPM schemes with acyclic attenuating loops in the create structure, and we have given the computational complexity of the safety algorithm. ESPM is therefore in effect an alternate formulation of HRU with strong safety properties.  \n\n# References  \n\n[1]  Abrams, M., Eggers, K., LaPadula, L., Olson I1., \"A Generalized Framework for Access Control: An Informal Description', Proceedings Thirteenth National   Computer  Security Conference,  \n\nWashington, DC, October, 1990.   \n[2]  Ammann, P.E. and Sandhu, R.S., \"The Extended Schematic Protection Model', Technical Report, George Mason University, 1990.   \n[3]   Ammann, PE. and Sandhu, R.S., “Extending the Creation Operation in the Schematic Protection Model', Proceedings Sixth Annual Compuer Security Application Conference, Tucson, AZ (1990).   \n[4] Bell, D.E. and LaPadula, L.J., “Secure Computer Systems: Unified Exposition and Multics Interpretation\", Mitre Technical Report MTR-2997, Bedford, MA (1975).   \n[5]  Biba, KJ., \"Integrity Considerations for Secure Computer Systems'', Mitre Technical Report MTR3153, Bedford, MA (1977).   \n[6]  Bishop, M. and Snyder, L., \"\"The Transfer of Information and Authority in a Protection System'', 7th ACM Symposium on Operating Systems Principles, 45-54 (1979).   \n[7]  Boebert, W.E. and Kain, R.Y., \"\"A Practical Alternative to Hierarchical Integrity Policies\", Proceed. ings Eighth National Computer Security Conference, 18-27 (1985).   \n[8]  Clark, D.D. and Wilson, D.R., \"A Comparison of Commercial and Military Computer Security Policies', Proceedings 1987 IEEE Symposium on Security and Privacy, Oakland, CA, May, 1987.   \n[9] Clark, D.D. and Wilson, D.R., \"Evolution of a Model for Computer Integrity', Report of the Invi. tational Workshop on Data Integrity, NIST Special Publication 500-168, (1989).   \n[10] Department of Defense National Computer Security Center, Department of Defense Trusted Computer Systems Evaluation Criteria, DoD 5200.28-STD, (1985).   \n[11] Downs, D.D., Rub, J.R., Kung, K.C. and Jordan, C.s., \"Issues in Discretionary Access Control'. Proceedings 1985 IEEE Symposium on Security and Privacy, Oakland, CA, May, 1985.   \n[12] Graham, G.S. and Denning, PJ.,\"Protection - Principles and Practice'', AFIPS Spring Joint Computer Conference, 40:417-429 (1972).   \n[13] Graubart, R.D., \"On the Need for a Third Form of Access Control\", Proceedings Twelfth National Computer Security Conference', Baltimore, MD, October, 1989.   \n[14] Harrison, M.H., Ruzzo, WL. and Ullman, J.D., \"Protection  in Operating Systems', CACM, 19(8):461-471 (1976).   \n[15] Harrison, M.H. and Ruzzo, W.L., \"Monotonic Protection Systems', In Foundations of Secure Computations, DeMillo, R.A., Dobkin, D.P., Jones, A.K. and Lipton, R.J. (Editors), Academic Press (1978).   \n[16] Jones, A.K., Lipton, R.J. and Snyder, L., \"A Linear Time Algorithm for Deciding Security', 17th IEEE Symposium on the Foundations of Computer Science, 337-366 (1976).   \n[17] Lampson, B.W., \"Protection\", 5th Princeton Symposium on Information Science and Systems, 437-443 (1971). Reprinted in ACM Operating Systems Review, 8(1):18-24 (1974).   \n[18] LaPadula, L.J., “Formal Modeling in a Generalized Framework for Access Control', Compuer Security Foundations Workshop, 100-109 (1990).   \n[19] Lee, T.M.P, \"Using Mandatory Integrity to Enforce \"Commercial'’ Security\", Proceedings 1988 IEEE Symposium on Security and Privacy, Oakland, CA, May, 1988.   \n[20] Lipner, S.B., \"Non-discretionary Controls for Commercial Applications\", Proceedings 1982 IEEE Symposium on Security and Privacy, Oakland, CA, April, 1982.   \n[21] Lipton, RJ. and Snyder, L., \"A Linear Time Algorithm for Deciding Subject Security', JACM, 24(3):455-464 (1977).   \n[22] Lipton, R.J. and Budd, T.A., \"On Classes of Protection Systems', In Foundations of Secure Compuations, DeMillo, R.A., Dobkin, D.P., Jones, A.K. and Lipton, RJ. (Editors), Academic Press (1978).   \n[23] Lockman, A. and Minsky, N., “\"Unidirectional Transport of Rights and Take-Grant Control', IEEE Transactions on Software Engineering, SE8(6):597-604 (1982).   \n[24] McCollum, C.J., Messing, J.R. and Notargiacomo, L., \"Beyond the Pale of MAC and DAC - Defining New Forms of Access Control', Proceedings 1990 IEEE Symposium on Research in Security and Privacy, Oakland, CA, May, 1990.   \n[25] McLean, J., “A Comment on the “Basic Security Theorem' of Bell and LaPadula\", Information Processing Letters, 20(2):67-70 (1985).   \n[26] McLean, J., “\"Reasoning About Security Models\", Proceedings 1987 IEEE Symposium on Security and Privacy, Oakland, CA, May, 1987.   \n[27] McLean, J., \"\"The Algebra of Security', Proceedings 1988 IEEE Symposium on Security and Privacy, Oakland, CA, May, 1988.   \n[28] McLean, J., \"\"Specifying and Modeling Computer Security', IEEE Computer, 23(1):9-16 (1990).   \n[29] Millen, J.K., \"A1 Policy Modeling\"', Proceedings Seventh National Computer Security Conference, 137-145 (1984).   \n[30] Pittelli, P., “\"The Bell-LaPadula Computer Security Model Represented as a  Special  Case of the Harrison-Ruzzo-Ullman  Model',  Proceedings Tenth National Computer Security Conference, Baltimore, MD, October, 1987.   \n[31] Sandhu, R.S.,“The Schematic Protection Model: Its Definition and Analysis for Acyclic Attenuating Schemes\"', JACM, 35(2):404-432 (1988).   \n[32] Sandhu, R.s., \"Expressive Power of the Schematic Protection Model', Computer Security Foundations Workshop, 188-193 (1988).   \n[33] Sandhu, R.S., “\"The Demand Operation in the Schematic Protection Model', Information Processing Letters, 32(4):213-219 (1989).   \n[34] Sandhu, R.s., “\"Terminology, Criteria and System Architectures for Data Integrity', Report of the Invitational Workshop on Data Integrity, NIST Special Publication 500-168, (1989).   \n[35] Sandhu, R.S., “\"Undecidability of the Safety Problem for the Schematic Protection Model with Cyclic Creates', JCSS, to appear.   \n[36] Sandhu, R.S., “Mandatory Controls for Database Integrity', in Database Security, Ill: Status and Prospects, Spooner, D.L. and Landwehr, C., editors, Elsevier Science Publishers B.V. (North Holland), (1990).   \n[37] Shockley, W.R., “\"Implementing the Clark/Wilson Integrity Policy Using Current Technology', Proceedings Eleventh National Computer Security Conference, Baltimore, MD, October, 1988.  "}