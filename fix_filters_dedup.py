import pandas as pd

# 读取filters.csv
df = pd.read_csv('filters.csv')

# 按query{zh}、query{en}、answer去重，保留第一条
df_dedup = df.drop_duplicates(subset=['query{zh}', 'query{en}', 'answer'], keep='first').reset_index(drop=True)

# 重新生成no序号
df_dedup['no'] = range(1, len(df_dedup) + 1)

# 调整列顺序
df_dedup = df_dedup[['no', 'query{zh}', 'query{en}', 'answer']]

# 保存去重后的文件
df_dedup.to_csv('filters.csv', index=False, encoding='utf-8-sig')
print('filters.csv 已去重') 