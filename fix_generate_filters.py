import pandas as pd
import re

# 读取原始csv
df = pd.read_csv('papa_search_agent.csv')

filters = []
no = 1

for idx, row in df.iterrows():
    # 字段提取
    keywords_all = str(row['原始关键词（raw_keywords_zh）']).strip()
    keywords_en_all = str(row['原始查询词（query_keywords_en）']).strip()
    question_zh = str(row['查询问题（query_zh）']).strip()
    question_en = str(row['查询问题（query_en）']).strip()
    semantic_zh = str(row['语义提取(zh)']).strip()
    semantic_en = str(row['语义提取(en)']).strip()
    authors_all = str(row['作者']).strip()
    conf_year = str(row['顶会名称/年份']).strip()
    paper_name = str(row['论文名称']).strip()

    # 拆分顶会和年份
    conf, year = '', ''
    if conf_year and '/' in conf_year:
        parts = conf_year.split('/')
        conf = parts[0]
        year = parts[1] if len(parts) > 1 else ''
    elif conf_year:
        conf = conf_year

    # 关键字拆分（常见分隔符：, ; 、，空格）
    keywords_list = [k.strip() for k in re.split(r'[;,，、\s]+', keywords_all) if k.strip()]
    keywords_en_list = [k.strip() for k in re.split(r'[;,，、\s]+', keywords_en_all) if k.strip()]
    # 保证中英文关键字一一对应，否则用空字符串补齐
    max_kw_len = max(len(keywords_list), len(keywords_en_list))
    while len(keywords_list) < max_kw_len:
        keywords_list.append('')
    while len(keywords_en_list) < max_kw_len:
        keywords_en_list.append('')

    # 作者拆分（常见分隔符：, ; 、，和、与、空格）
    authors_list = [a.strip() for a in re.split(r'[;,，、和与\s]+', authors_all) if a.strip()]

    # 1. 帮我查询【年份】【关键字】方向的所有论文
    for k_idx in range(max_kw_len):
        keyword_zh = keywords_list[k_idx]
        keyword_en = keywords_en_list[k_idx]
        if year and keyword_zh:
            query_zh = f"请列出{year}年关于{keyword_zh}方向的所有学术论文。"
            query_en = f"Please list all academic papers in the field of {keyword_en} published in {year}."
            filters.append([no, query_zh, query_en, paper_name])
            no += 1

    # 2. 帮我查询【作者】，【关键字】方向的论文
    for author in authors_list:
        for k_idx in range(max_kw_len):
            keyword_zh = keywords_list[k_idx]
            keyword_en = keywords_en_list[k_idx]
            if author and keyword_zh:
                query_zh = f"请检索作者{author}在{keyword_zh}领域发表的学术论文。"
                query_en = f"Retrieve academic papers authored by {author} in the field of {keyword_en}."
                filters.append([no, query_zh, query_en, paper_name])
                no += 1

    # 3. 【顶会名称】中涉及到【关键字】的论文有哪些
    for k_idx in range(max_kw_len):
        keyword_zh = keywords_list[k_idx]
        keyword_en = keywords_en_list[k_idx]
        if conf and keyword_zh:
            query_zh = f"在{conf}会议中，涉及{keyword_zh}主题的论文有哪些？"
            query_en = f"Which papers presented at {conf} are related to the topic of {keyword_en}?"
            filters.append([no, query_zh, query_en, paper_name])
            no += 1

    # 4. 【顶会名称】中【问题】解决方案（专业优化）
    if conf and question_zh:
        query_zh = f"在{conf}会议中，针对\"{question_zh}\"这一科学问题，学者们提出了哪些解决方案？"
        query_en = f'What solutions have been proposed by researchers for the scientific problem of \"{question_en}\" at the {conf} conference?'
        filters.append([no, query_zh, query_en, paper_name])
        no += 1

    # 5. 【顶会名称/年份】中【关键字】【问题】（专业优化）
    for k_idx in range(max_kw_len):
        keyword_zh = keywords_list[k_idx]
        keyword_en = keywords_en_list[k_idx]
        if conf and year and keyword_zh and question_zh:
            query_zh = f"请列举{conf}{year}年在{keyword_zh}领域，针对\"{question_zh}\"问题所发表的学术论文。"
            query_en = f'Please list the academic papers published in {conf} {year} in the field of {keyword_en} that address the issue of \"{question_en}\".'
            filters.append([no, query_zh, query_en, paper_name])
            no += 1

# 输出到filters.csv
filters_df = pd.DataFrame(filters, columns=['no', 'query{zh}', 'query{en}', 'answer'])
filters_df.to_csv('filters.csv', index=False, encoding='utf-8-sig')
print('filters.csv 已生成') 