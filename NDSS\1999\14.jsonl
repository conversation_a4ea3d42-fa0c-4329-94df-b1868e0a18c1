{"text": "# Distributed Policy Management for Java 2  \n\n![](/tmp/output/14_20250327005345/images/fb3bacd0d02a6ac9fbc061d389d0ff25d8aa686929636ad0c51ff0194e41c7e7.jpg)  \n\nISOC NDSS'99 4-5 February 1999. San Diego  \n\n<PERSON>  \n\n# Overview  \n\nIntroduction   \nJava 2 security model   \nAuthorization certificates, SPKI   \nUsing SPKI certificates to improve   \nJava 2 security policy management   \nImplementation   \nConclusions  \n\n# Introduction  \n\n1中  \n\nWe are considering a very large, distributed Java environment  \n\n- Computers - Cellular phones - PDAS  \n\nThe users want to run software from many different sources without compromising security  \n\n# The Problem  \n\nHow to manage the security policy?   \n- In a scalable way?   \n- With minimum dependency of external security mechanisms?   \n- In a way transparent to the applications?  \n\n# ProtectionDomains  \n\n![](/tmp/output/14_20250327005345/images/39539fc504a4aad9181ae207f04f4309ebf02ccf2e9c48fb3e726113a63a8436.jpg)  \n\nHelsinki University of Technology  \n\n# Java 2 Access Control  \n\nWhen the class tries to access a   \nprotected resource, the   \nAccessController checks the   \npermissions in the class' protection   \ndomain  \n\n山L  \n\n- The class cannot add permissions to its protection domain   \n- The class cannot change its protection domain  \n\n# The Current Solution has Limitations  \n\nAccess rights are defined in local   \nconfiguration files   \n- Changing the policy requires editing the files   \n- The files can get very complex   \nAccess rights are practically static   \nHow can the administrator know what   \naccess rights a certain class needs?  \n\n# Authorization Certificates  \n\nIdentity certificates bind a name to a key   \n- Usually ACLs are then used to define what the name is allowed to do   \nAuthorization certificates bind access   \nrights directly to a key   \n- Close to the concept of capability   \n- Can provide anonymity  \n\n# SPKI Certificates  \n\nH  \n\nSimple Public Key Infrastructure   \nBeing published as Experimental RFC   \nSPKI certificates are signed five-tuples   \n-Issuer   \n- Subject   \n- Delegation   \n- Tag (i.e. authorization)   \n-Validity  \n\n# Certificate Loops  \n\nWhen authorization is delegated, the certificates form chains When used, the chain is closed into a loop:  \n\nP5 中   \nSH 1 10  \n\n![](/tmp/output/14_20250327005345/images/6cd90fc38cdd20dec7c136f2d649ac2788be193772c0e02cfa4c4144648526d3.jpg)  \n\n# SPKI Certificates for Java  \n\nIssuer, subject, delegation, validity etc.   \nexpressed according to the SPKl specs   \nTag definition is more focused:   \ntags express Java permission objects (tag (java-permission (type java.io.FilePermission) (target /tmp/myfile) (action read)))   \n- Tags may also express a set or \"any\" permissions  \n\n# Authorizing Java Classes  \n\nS  \n\n![](/tmp/output/14_20250327005345/images/47d52b566439829ec33b5378b41b356844857111dcfbe1ac1b8c27b105875119.jpg)  \n\n# Prototype  \n\nPublic interfaces for SPKl certificates A Provider that implements the SPKI certificate functionality   \nA Policy that uses dynamic protection domains and SPKI certificates to grant permissions   \nA simple certificate repository   \n- Is being replaced with DNS  \n\nHelsinki University of Technology  \n\n# Distributed ProtectionDomains  \n\n中H9  \n\nIf the protection domains could have   \ntemporary keys, they could delegate   \ntheir permissions to other domains   \n- The JVM must provide the keys   \n- The JVM must help bind the temporary key to the object   \nFor example, a client could authorize an   \nagent on a server to perform tasks on   \nits behalf  \n\nHelsinki University of Technology  \n\nJonna Partanen  \n\n# Conclusions  \n\nSPKl certificates can be used to make   \nJava security policy management   \n- Secure   \n- Distributed   \n- Scalable   \n- Dynamic  "}