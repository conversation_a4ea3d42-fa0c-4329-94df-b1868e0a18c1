{"text": "# Who is <PERSON> Bob? Adversarial Attacks on Speaker Recognition Systems  \n\nGuangke <PERSON>†‡, <PERSON>¶§∥, Lingling $\\mathrm{Fan^{\\S}}$ , Xiaoning $\\mathrm{Du^{\\S}}$ , <PERSON><PERSON>, <PERSON>‡‡\u0002 and <PERSON>§ ∗ShanghaiTech University, †Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences ‡University of Chinese Academy of Sciences, §Nanyang Technological University ¶College of Intelligence and Computing, Tianjin University ‡‡Shanghai Engineering Research Center of Intelligent Vision and Imaging, $\\|_{\\mathbf{C}0}$ -first Author  \n\nAbstract—Speaker recognition (SR) is widely used in our daily life as a biometric authentication or identification mechanism. The popularity of SR brings in serious security concerns, as demonstrated by recent adversarial attacks. However, the impacts of such threats in the practical black-box setting are still open, since current attacks consider the white-box setting only.  \n\nIn this paper, we conduct the first comprehensive and systematic study of the adversarial attacks on SR systems (SRSs) to understand their security weakness in the practical blackbox setting. For this purpose, we propose an adversarial attack, named FAKEBOB, to craft adversarial samples. Specifically, we formulate the adversarial sample generation as an optimization problem, incorporated with the confidence of adversarial samples and maximal distortion to balance between the strength and imperceptibility of adversarial voices. One key contribution is to propose a novel algorithm to estimate the score threshold, a feature in SRSs, and use it in the optimization problem to solve the optimization problem. We demonstrate that FAKEBOB achieves $99\\%$ targeted attack success rate on both open-source and commercial systems. We further demonstrate that FAKEBOB is also effective on both open-source and commercial systems when playing over the air in the physical world. Moreover, we have conducted a human study which reveals that it is hard for human to differentiate the speakers of the original and adversarial voices. Last but not least, we show that four promising defense methods for adversarial attack from the speech recognition domain become ineffective on SRSs against FAKEBOB, which calls for more effective defense methods. We highlight that our study peeks into the security implications of adversarial attacks on SRSs, and realistically fosters to improve the security robustness of SRSs.  \n\n# I. INTRODUCTION  \n\nSpeaker recognition [1] is an automated technique to identify a person from utterances which contain audio characteristics of the speaker. Speaker recognition systems (SRSs) are ubiquitous in our daily life, ranging from biometric authentication [2], forensic tests [3], to personalized service on smart devices [4]. Machine learning techniques are the mainstream method for implementing SRSs [5], however, they are vulnerable to adversarial attacks (e.g., [6], [7], [8]). Hence, it is vital to understand the security implications of SRSs under adversarial attacks.  \n\nThough the success of adversarial attack on image recognition systems has been ported to the speech recognition systems in both the white-box setting (e.g., [9], [10]) and black-box setting (e.g., [11], [12]), relatively little research has been done on SRSs. Essentially, the speech signal of an utterance consists of two major parts: the underlying text and the characteristics of the speaker. To improve the performance, speech recognition will minimize speaker-dependent variations to determine the underlying text or command, whereas speaker recognition will treat the phonetic variations as extraneous noise to determine the source of the speech signal. Thus, adversarial attacks tailored to speech recognition systems may become ineffective on SRSs.  \n\nAn adversarial attack on SRSs aims at crafting a sample from a voice uttered by some source speaker, so that it is misclassified as one of the enrolled speakers (untargeted attack) or a target speaker (targeted attack) by the system under attack, but still correctly recognized as the source speaker by ordinary users. Though current adversarial attacks on SRSs [13], [14] are promising, they suffer from the following three limitations: (1) They are limited to the white-box setting by assuming the adversary has access to the information of the target SRS. Attacks in a more realistic black-box setting are still open. (2) They only consider either the close-set identification task [13] that always classifies an arbitrary voice as one of the enrolled speakers [15], or the speaker verification task [14] that checks if an input voice is uttered by the unique enrolled speaker or not [16]. Attacks on the open-set identification task [17], which strictly subsumes both close-set identification and speaker verification, are still open. (3) They do not consider overthe-air attacks, hence it is unclear whether their attacks are still effective when playing over the air in the physical world. Therefore, in this work, we investigate the adversarial attack on all the three tasks of SRSs in the practical black-box setting, in an attempt to understand the security weakness of SRSs under adversarial attack in practice.  \n\nIn this work, we focus on the black-box setting, which assumes that the adversary can obtain at most the decision result and scores of the enrolled speakers for each input voice. Hence attacks in the black-box setting is more practical yet more challenging than the existing white-box attacks [13], [14]. We emphasize that the scoring and decision-making mechanisms of SRSs are different among recognition tasks [18]. Particularly, we consider 40 attack scenarios (as demonstrated in Fig. 2) in total differing in attack types (targeted vs. untargeted), attack channels (API vs. over the air), genders of source and target speakers, and SR tasks (cf. §II-B). We demonstrate our attack on 16 representative attack scenarios.  \n\nTo launch such a practical attack, two technical challenges need to be addressed: (C1) crafting adversarial samples as less imperceptible as possible in the black-box setting, and (C2) making the attack practical, namely, adversarial samples are effective on an unknown SRS, even when playing over the air in the physical world. In this paper, we propose a practical black-box attack, named FAKEBOB, which is able to overcome these challenges.  \n\nSpecifically, we formulate the adversarial sample generation as an optimization problem. The optimization objective is parameterized by a confidence parameter and the maximal distortion of noise amplitude in $L_{\\infty}$ norm to balance between the strength and imperceptibility of adversarial voices, instead of using noise model [10], [19], [20], due to its device- and background-dependency. We also incorporate the score threshold, a key feature in SRSs, into the optimization problem. To solve the optimization problem, we leverage an efficient gradient estimation algorithm, i.e., the natural evolution strategy (NES) [21]. However, even with the estimated gradients, none of the existing gradient-based white-box methods (e.g., [22], [23], [10], [24]) can be directly used to attack SRSs. This is due to the score threshold mechanism, where an attack fails if the predicated score is less than the threshold. To this end, we propose a novel algorithm to estimate the threshold, based on which we leverage the Basic Iterative Method (BIM) [23] with estimated gradients to solve the optimization problem.  \n\nWe evaluate FAKEBOB for its attacking capabilities, on 3 SRSs (i.e., ivector-PLDA [25], GMM-UBM [16] and xvectorPLDA [26]) in the popular open-source platform Kaldi [27] in the research community and 2 commercial systems (i.e., Talentedsoft [28] and Microsoft Azure [29]) which are proprietary without any publicly available information about the internal design and implementations, hence completely blackbox. We evaluate FAKEBOB using 16 representative attack scenarios (out of 40) based on the following five aspects: (1) effectiveness/efficiency, (2) transferability, (3) practicability, (4) imperceptibility, and (5) robustness.  \n\nThe results show that FAKEBOB achieves $99\\%$ targeted attack success rate (ASR) on all the tasks of ivector-PLDA, GMM-UBM and xvector-PLDA systems, and $100\\%$ ASR on the commercial system Talentedsoft within 2,500 queries on average (cf. $\\S\\mathbf{V}{\\cdot}\\mathbf{B}_{\\cdot}$ ). To demonstrate the transferability, we conduct a comprehensive evaluation of transferability attack on ivector-PLDA, GMM-UBM and xvector-PLDA systems under cross-architecture, cross-dataset, and cross-parameter circumstances and the commercial system Microsoft Azure. FAKEBOB is able to achieve $34\\%-68\\%$ transferability (attack success) rate except for the speaker verification of Microsoft Azure. The transferability rate could be increased by crafting high-confidence adversarial samples at the cost of increasing distortion. To further demonstrate the practicability and imperceptibility, we launch an over-the-air attack in the physical world and also conduct a human study on the Amazon Mechanical Turk platform [30]. The results indicate that FAKEBOB is effective when playing over the air in the physical world against both the open-source systems and the open-set identification task of Microsoft Azure (cf. $\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{\\mathrm{{8}}}\\mathrm{\\hbar\\Omega_{{8}}}}}}}}}}}}}}}$ and it is hard for humans to differentiate the speakers of the original and adversarial voices (cf. $\\mathrm{\\SV}{\\cdot}\\mathrm{E},$ ).  \n\nFinally, we study four defense methods that are reported promising in speech recognition domain: audio squeezing [10], [31], local smoothing [31], quantization [31] and temporal dependency-based detection [31], due to lacking of domainspecific defense solutions for adversarial attack on SRSs. The results demonstrate that these defense methods have limited effects on FAKEBOB, indicating that FAKEBOB is a practical and powerful adversarial attack on SRSs.  \n\nOur study reveals that the security weakness of SRSs under black-box adversarial attacks. This weakness could lead to lots of serious security implications. For instance, the adversary could launch an adversarial attack (e.g., FAKEBOB) to bypass biometric authentication on the financial transaction [2], [32] and smart devices [4], as well as high-security intelligent voice control systems [33] so that follow-up voice command attacks can be launched, e.g., CommanderSong [10] and hidden voice commands [34]. For the voice-enabled cars using Dragon Drive [33], the attacker could bypass its voice biometrics using FAKEBOB so that command attacks can be launched to control cars. Even for commercial systems, it is a significant threat under such a practical black-box adversarial attack, which calls for more robust SRSs. To shed further light, we discuss the potential mitigation and further attacks to understand the arm race in this topic. In summary, our main contributions are:  \n\n• To our knowledge, this is the first study of targeted adversarial attacks on SRSs in the black-box setting. Our attack is launched by not only using gradient estimation based methods, but also incorporating the score threshold into the adversarial sample generation. The proposed algorithm to estimate the score threshold is unique in SRSs.   \n• Our black-box attack addresses not only the speaker recognition tasks considered by existing white-box attacks but also the more general task, open-set identification, which has not been considered by previous adversarial attacks.   \n• Our attack is demonstrated to be effective on the popular open-source systems and commercial system Talentedsoft, transferable and practical on the popular open-source systems and the open-set identification task of Microsoft Azure even when playing over the air in the physical world.   \n• Our attack is robust against four potential defense methods which are reported very promising in speech recognition domain. Our study reveals the security implications of the adversarial attack on SRSs, which calls for more robust SRSs and more effective domain-specific defense methods. For more information of FAKEBOB, please refer to our   \nwebsite [35] which includes voice samples and source code.  \n\n# II. BACKGROUND  \n\nIn this section, we introduce the preliminaries of speaker recognition systems (SRSs) and the threat model.  \n\nA. Speaker Recognition System (SRS)  \n\nSpeaker recognition is an automated technique that allows machines to recognize a person’s identity based on his/her utterances using the characteristics of the speaker. It has been studied actively for four decades [18], and currently supported by a number of open-source platforms (e.g., Kaldi and MSR Identity [36]) and commercial solutions (e.g., Microsoft Azure, Amazon Alexa [37], Google home [38], Talentedsoft, and SpeechPro VoiceKey [39]). In addition, NIST actively organizes the Speaker Recognition Evaluation [40] since 1996.  \n\n![](/tmp/output/18_20250326021609/images/b66ae4f7e27b8bde877c653414524a20dee7f67c6629f7bc57d96398f37e18ec.jpg)  \n\nOverview of SRSs. Fig. 1 shows an overview of a typical SRS, which includes five key modules: Feature Extraction, Universal Background Model (UBM) Construction, Speaker Model Construction, Scoring Module and Decision Module. The top part is an offline phase, while the lower two parts are an online phase composed of speaker enrollment and recognition phases.  \n\nIn the offline phase, a UBM is trained using the acoustic feature vectors extracted from the background voices (i.e., voice training dataset) by the feature extraction module. The UBM, intending to create a model of the average features of everyone in the dataset, is widely used in the state-of-the-art SRSs to enhance the robustness and improve efficiency [1]. In the speaker enrollment phase, a speaker model is built using the UBM and feature vectors of enrolling speaker’s voices for each speaker. During the speaker recognition phase, given an input voice $x$ , the scores $S(x)$ of all the enrolled speakers are computed using the speaker models, which will be emitted along with the decision $D(x)$ as the recognition result.  \n\nThe feature extraction module converts a raw speech signal into acoustic feature vectors carrying characteristics of the signal. Various acoustic feature extraction algorithms have been proposed such as Mel-Frequency Cepstral Coefficients (MFCC) [41], Spectral Subband Centroid (SSC) [42] and Perceptual Linear Predictive (PLP) [43]. Among them, MFCC is the most popular one in practice [1], [18].  \n\nSpeaker recognition tasks. There are three common recognition tasks of SRSs: open-set identification (OSI) [17], close-set identification (CSI) [15] and speaker verification (SV) [16].  \n\nAn OSI system allows multiple speakers to be enrolled during the enrollment phase, forming a speaker group $G$ . For an arbitrary input voice $x$ , the system determines whether $x$ is uttered by one of the enrolled speakers or none of them, according to the scores of all the enrolled speakers and a preset (score) threshold $\\theta$ . Formally, suppose the speaker group $G$ has $n$ speakers $\\{1,2,\\cdots,n\\}$ , the decision module outputs $D(x)$ :  \n\n$$\nD(x)=\\left\\{\\begin{array}{l l}{\\underset{i\\in G}{\\operatorname{argmax}}~[S(x)]_{i},}&{\\mathrm{if}\\underset{i\\in G}{\\operatorname*{max}}~[S(x)]_{i}\\geq\\theta;}\\ {\\underset{\\mathbf{reject}}{\\operatorname*{argmax}},}&{\\mathrm{otherwise}.}\\end{array}\\right.\n$$  \n\nwhere $[S(x)]_{i}$ for $i\\in G$ denotes the score of the voice $x$ that is uttered by the speaker $i$ . Intuitively, the system classifies the input voice $x$ as the speaker $i$ if and only if the score $[S(x)]_{i}$ of the speaker $i$ is the largest one among all the enrolled speakers, and not less than the threshold $\\theta$ . If the largest score is less than $\\theta$ , the system directly rejects the voice, namely, it is not uttered by any of the enrolled speakers.  \n\nCSI and SV systems accomplish similar tasks as the OSI system, but with some special settings. A CSI system never rejects any input voices, i.e., an input will always be classified as one of the enrolled speakers. Whereas an SV system can have exactly one enrolled speaker and checks if an input voice is uttered by the enrolled speaker, i.e., either accept or reject.  \n\nText-Dependency. SRSs can be either text-dependent, where cooperative speakers are required to utter one of pre-defined sentences, or text-independent, where the speakers are allowed to speak anything. The former achieves high accuracy on short utterances, but always requires a large amount utterances repeating the same sentence, thus it is only used in the SV task. The latter may require longer utterances to achieve high accuracy, but practically it is more versatile and can be used in all tasks (cf. [18]). Therefore, in this work, we mainly demonstrate our attack on text-independent SRSs.  \n\nSRS implementations. ivector-PLDA [25], [44] is a mainstream method for implementing SRSs in both academia [27], [45], [46] and industries [47], [48]. It achieves the state-ofthe-art performance for all the speaker recognition tasks [49], [50]. Another one is GMM-UBM based methods, which train a Gaussian mixture model (GMM) [16], [51] as UBM. Basically, GMM-UBM tends to provide comparative (or higher) accuracy on short utterances [52].  \n\nRecently, deep neural network (DNN) becomes used in speech [53] and speaker recognition (e.g., xvector-PLDA [26]), where speech recognition aims at determining the underlying text or command of the speech signal. However, the major breakthroughs made by DNN-based methods reside in speech recognition; for speaker recognition, ivector based methods still exhibit the state-of-the-art performance [5]. Moreover, DNN-based methods usually rely on a much larger amount of training data, which could greatly increase the computational complexity compared with ivector and GMM based methods [54], thus are not suitable for off-line enrollment on clientside devices. We denote by ivector, GMM, and xvector the ivector-PLDA, GMM-UBM, and xvector-PLDA, respectively.  \n\nB. Threat Model  \n\nWe assume that the adversary intends to craft an adversarial sample from a voice uttered by some source speaker, so that it is classified as one of the enrolled speakers (untargeted attack) or the target speaker (targeted attack) by the SRS under attack, but is still recognized as the source speaker by ordinary users.  \n\nTo deliberately attack the authentication of a target victim, we can compose adversarial voices, which mimic the voiceprint of the victim from the perspective of the SRSs. Reasonably, the adversary can unlock the smartphones [55], log into applications [56], and conduct illegal financial transactions [2]. Under untargeted attack, we can manipulate voices to mimic the voiceprint of any one of enrolled speakers. For example, we can bypass the voice-based access control such as iFLYTEK [57], where multiple speakers are enrolled. After bypassing the authentication, follow-up hidden voice command attacks (e.g., [10], [34]) can be launched, e.g., on smart car with Dragon Drive [33]. These attack scenarios are practically feasible, for example, when the victim is not within the hearable distance of the adversarial voice, or the attack voice does not raise the alertness of the victim due to the presence of other voice sources, either human or loudspeakers.  \n\nFig. 2: Attack scenarios, where $^*$ means that targeted and untargeted are the same on the SV task, as an SV system only has one enrolled speaker.   \n\n\n<html><body><table><tr><td>Attacktype</td><td>Gender</td><td>Attackchannel</td><td>1 TargetSRS</td><td></td><td>Output</td></tr><tr><td>Untargeted</td><td>Intra-gender</td><td>API</td><td>X</td><td>SV*</td><td>Decision+Scores</td></tr><tr><td>Targeted</td><td>Inter-gender</td><td>X</td><td>Over-the-air</td><td>CSI OSI</td><td>Decision-only</td></tr></table></body></html>  \n\nThis paper focuses on the practical black-box setting where the adversary has access only to the recognition result (decision result and scores) of a target SRS for each test input, but not the internal configurations or training/enrollment voices. This black-box setting is feasible in practice, e.g., the commercial systems Talentedsoft [28], iFLYTEK, SinoVoice [58] and SpeakIn [59]. If the scores are not accessible (e.g., OSI task in the commercial system Microsoft Azure), we can leverage transferability attacks. We assume the adversary has some voices of the target speakers to build a surrogate model, while these voices are not necessary the enrollment voices. This is also feasible in practice as one can possibly record speeches of target speakers. To our knowledge, the targeted black-box setting renders all previous adversarial attacks impractical on SRSs. Indeed, all the adversarial attacks on SRSs are whitebox [13], [14] except for the concurrent work [60], which performs only untargeted attacks.  \n\nSpecifically, in our attack model, we consider five parameters: attack type (targeted vs. untargeted attack), genders of speakers (inter-gender vs. intra-gender), attack channel (API vs. over-the-air), speaker recognition task (OSI vs. CSI vs. SV) and output of the target SRS (decision and scores vs. decisiononly) as shown in Fig. 2. Intra-gender (resp. inter-gender) means that the genders of the source and target speakers are the same (resp. different). API attack assumes that the target SRS (e.g., Talentedsoft) provides an API interface to query, while over-the-air means that attacks should be played over the air in the physical world. Decision-only attack means that the target SRS (e.g., Microsoft Azure) only outputs decision result (i.e., the adversary can obtain the decision result $D(x)$ ), but not the scores of the enrolled speakers. Therefore, targeted, inter-gender, over-the-air and decision-only attacks are the most practical yet the most challenging ones. In summary, by counting all the possible combinations of the parameters in Fig. 2, there are $48={\\mathrm{~2~}}\\times{\\mathrm{~2~}}\\times{\\mathrm{~2~}}\\times{\\mathrm{~3~}}\\times{\\mathrm{~2~}}$ attack scenarios. Since targeted and untargeted attacks are the same on the  \n\nSV task, there are $40=48\\mathrm{~-~}2\\times2\\times2$ attack scenarios. However, demonstrating all the 40 attack scenarios requires huge engineering efforts, we design our experiments to cover 16 representative attack scenarios (cf. Appendix B).  \n\n# III. METHODOLOGY  \n\nIn this section, we start with the motivations, then explain the design philosophy of our attack in black-box setting and the possible defenses, finally present an overview of our attack.  \n\n# A. Motivation  \n\nThe research in this work is motivated by the following questions: (Q1) How to launch an adversarial attack against all the tasks of SRSs in the practical black-box setting? (Q2) Is it feasible to craft robust adversarial voices that are transferable to an unknown SRS under cross-architecture, cross-dataset and cross-parameter circumstances, and commercial systems, even when played over the air in the physical world? (Q3) Is it possible to craft human-imperceptible adversarial voices that are difficult, or even impossible, to be noticed by ordinary users? (Q4) If such an attack exists, can it be defended?  \n\n# B. Design Philosophy  \n\nTo address Q1, we investigate existing methods for blackbox attacks on image/speech recognition systems, i.e., surrogate model [61], gradient estimation [62], [21] and genetic algorithm [63], [64]. Surrogate model methods are proved to be outperformed by gradient estimation methods [62], hence are excluded. For the other two methods: it is known that natural evolution strategy (NES) based gradient estimation [21] requires much fewer queries than finite difference gradient estimation [62], and particle swarm optimization (PSO) is proved to be more computationally efficient than other genetic algorithms [63], [65]. To this end, we conduct a comparison experiment on an OSI system using NES as a black-box gradient estimation technique and PSO as a genetic algorithm. The result shows that the NES-based gradient estimation method obviously outperforms the PSO-based one (cf. Appendix A). Therefore, we exploit the NES-based gradient estimation.  \n\nHowever, even with the estimated gradients, none of the existing gradient based white-box methods (e.g., [22], [23], [66], [67], [10], [20], [19], [24]) can be directly used to attack SRSs. This is due to the threshold $\\theta$ which is used in the OSI and SV tasks, but not in image/speech recognition. As a result, these methods will fail to mislead SRSs when the resulted score is less than $\\theta$ . To solve this challenge, we incorporate the threshold $\\theta$ into our adversarial sample generation and propose a novel algorithm to estimate $\\theta$ in the black-box setting.  \n\nTheoretically, the adversarial samples crafted in the above way are effective if directly fed as input to the target SRS via exposed API. However, to launch a practical attack as in Q2, adversarial samples should be played over the air in the physical world to interact with a SRS that may differ from the SRS on which adversarial samples are crafted. To address Q2, we increase the strength of adversarial samples and the range of noise amplitude, instead of using noise model [10], [19], [20], due to its device- and background-dependency. We have demonstrated that our approach is effective in transferability attack even when playing over the air in the physical world.  \n\n![](/tmp/output/18_20250326021609/images/d22309c6f6a0401086c49b38053c852a68ac0752133545e5fed3cbfa3856826c.jpg)  \nFig. 3: Overview of our attack: FAKEBOB  \n\nTo address Q3, we should consider two aspects of the human-imperceptibility. First, the adversarial samples should sound natural when listened by ordinary users. Second, and more importantly, they should sound as uttered by the same speaker of the original one. As a first step towards addressing Q3, we add a constraint onto the perturbations using $L_{\\infty}$ norm, which restricts the maximal distortion at each sample point of the audio signal. We also conduct a real human study to illustrate the imperceptibility of our adversarial samples.  \n\nTo address Q4, we should launch attacks on SRSs with defense methods. However, to our knowledge, no defense solution exists for adversarial attacks on SRSs. Therefore, we use four defense solutions for adversarial attacks on speech recognition systems: audio squeezing [10], [31], local smoothing [31], quantization [31] and temporal dependency detection [31], to defend against our attack.  \n\n# C. Overview of Our Attack: FAKEBOB  \n\nAccording to our design philosophy, in this section, we present an overview (shown in Fig. 3) of our attack, named FAKEBOB, addressing two technical challenges (C1) and (C2) mentioned in $\\S\\mathbf{I}.$ . To address C1, we formulate adversarial sample generation as an optimization problem (cf. $\\mathrm{\\SIV{-}A,}$ ), for which specific loss functions are defined for different attack types (i.e., targeted and untargeted) and tasks (i.e., OSI, CSI and SV) of SRSs (cf. $\\mathrm{\\SIV{-}B}$ , $\\mathrm{\\SIV{-C}}$ and $\\mathrm{\\hbar\\Omega}_{\\mathrm{\\left\\{N-D\\right\\}}}$ ). To solve the optimization problem, we propose an approach by leveraging a novel algorithm to estimate the threshold, NES to estimate gradient and the BIM method with the estimated gradients. C2 is addressed by incorporating the maximal distortion $'L_{\\infty}$ norm) of noise amplitude and strength of adversarial samples into the optimization problem (cf. §IV-A, $\\mathrm{\\SIV{-}B}$ , $\\mathrm{\\SIV{-C}}$ and $\\mathrm{\\SIV{-}D)}$ .  \n\n# IV. OUR ATTACK: FAKEBOB  \n\nIn this section, we elaborate on the techniques behind FAKEBOB, including the problem formulation and attacks on OSI, CSI, and SV systems.  \n\n# A. Problem Formulation  \n\nGiven an original voice, $x$ , uttered by some source speaker, the adversary aims at crafting an adversarial voice $\\acute{x}=x+\\delta$ by finding a perturbation $\\delta$ such that (1) $\\acute{x}$ is a valid voice [68], (2) $\\delta$ is as human-imperceptible as possible, and (3) the SRS under attack classifies the voice $\\acute{x}$ as one of the enrolled speakers or the target speaker. To guarantee that the adversarial voice $\\acute{x}$ is a valid voice, which relies upon the audio file format (e.g., WAV,  \n\n![](/tmp/output/18_20250326021609/images/17aa4de21517ab65dd69161cae0dda42bc5875f8678b7d2ad61d8c3e093b0ebe.jpg)  \nFig. 4: Attack on OSI systems  \n\nMP3 and AAC), our attack FAKEBOB first normalizes the amplitude value $x(i)$ of a voice $x$ at each sample point $i$ into the range $[-1,1]$ , then crafts the perturbation $\\delta$ to make sure $-1\\leq\\dot{x}(i)=x(i)+\\delta(i)\\leq1$ , and finally transforms $\\acute{x}$ back to the audio file format which will be fed to the target SRS. Hereafter, we assume that the range of amplitude values is $[-1,1]$ . To be as human-imperceptible as possible, our attack FAKEBOB adapts $L_{\\infty}$ norm to measure the similarity between the original and adversarial voices and ensures that the $L_{\\infty}$ distance $\\lVert\\Dot{\\boldsymbol{x}},\\boldsymbol{x}\\rVert_{\\infty}\\mathrm{:=}\\operatorname*{max}_{i}\\{|\\Dot{\\boldsymbol{x}}(i)-\\boldsymbol{x}(i)|\\}$ is less than the given maximal amplitude threshold $\\epsilon$ of the perturbation, where $i$ denotes sample point of the audio waveform. To successfully fool the target SRS, we formalize the problem of finding an adversarial voice $\\acute{x}$ for a voice $x$ as the following constrained minimization problem:  \n\n$$\n\\begin{array}{r l}&{\\operatorname{argmin}_{\\delta}f(x+\\delta)}\\ &{\\operatorname{suchthat}\\|x+\\delta,x\\|_{\\infty}<\\epsilon\\mathrm{~and~}x+\\delta\\in[-1,1]^{n}}\\end{array}\n$$  \n\nwhere $f$ is a loss function. When $f$ is minimized, $x+\\delta$ is recognized as the target speaker (targeted attack) or one of enrolled speakers (untargeted attack). Our formulation is designed to be fast for minimizing the loss function rather than minimizing the perturbation $\\delta$ , as done in [22], [23]. Some studies, e.g., [24], [7], formulate the problem to minimize both the loss function and perturbation. It remains to define the loss function and algorithm to solve the optimization problem. In the rest of this section, we mainly address them on the OSI system, then adapt the solution to the CSI and SV systems.  \n\n# B. Attack on OSI Systems  \n\nAs shown in Fig. 4, to attack an OSI system, we want to craft an adversarial voice $\\acute{x}$ starting from a voice $x$ uttered by some source speaker (i.e., $D(x)={\\mathrm{reject}}$ such that the voice $\\acute{x}$ is classified as the target speaker $t\\in G=\\{1,\\cdot\\cdot\\cdot,n\\}$ by the SRS, i.e., $D(\\acute{x})=t$ . We first present the loss function $f$ and then show how to solve the minimization problem.  \n\nLoss function $f$ . To launch a successful targeted attack on an OSI system, the following two conditions need to be satisfied simultaneously: the score $[S(x)]_{t}$ of the target speaker $t$ should be (1) the maximal one among all the enrolled speakers, and (2) not less than the preset threshold $\\theta$ . Therefore, the loss function $f$ for the target speaker $t$ is defined as follows:  \n\n$$\nf(x)=\\operatorname*{max}\\left\\{(\\operatorname*{max}\\{\\theta,\\operatorname*{max}_{i\\in G\\setminus\\{t\\}}[S(x)]_{i}\\}-[S(x)]_{t}),-\\kappa\\right\\}\n$$  \n\nwhere the parameter $\\kappa$ , inspired by [24], intends to control the strength of adversarial voices: the larger the $\\kappa$ is, the more confidently the adversarial voice is recognized as the target speaker $t$ by the SRS. This has been validated in $\\mathrm{\\SV}{-}C$ .  \n\nOur loss function is similar to the one defined in [24], but we also incorporate an additional threshold $\\theta$ . Considering $\\kappa=0$ , when $(\\mathrm{max}\\{\\theta,\\mathrm{max}_{i\\in G\\backslash\\{t\\}}[S(x)]_{i}\\}-[S(x)]_{t})$ is minimized, the score $[S(x)]_{t}$ of the target speaker $t$ will be maximized until it exceeds the threshold $\\theta$ and the scores of all other enrolled speakers. Hence, the system recognizes the voice $x$ as the speaker $t$ . When $\\kappa>0$ , instead of looking for a voice that just barely changes the recognition result of $x$ to the speaker $t$ , we want that the score $[S(x)]_{t}$ of the speaker $t$ is much larger than any other enrolled speakers and the threshold $\\theta$ .  \n\nTo launch an untargeted attack, the loss function $f$ can be revised as follows:  \n\n$$\nf(x)=\\operatorname*{max}\\{(\\theta-\\operatorname*{max}_{i\\in G}[S(x)]_{i}),-\\kappa\\}.\n$$  \n\nIntuitively, we want to find a perturbation $\\delta$ such that the largest score of $x$ is at least $\\kappa$ greater than the threshold $\\theta$ .  \n\nSolving the optimization problem. To solve the optimization problem in Eq. (1), we use NES as a gradient estimation technique and employ the BIM method with the estimated gradients to craft adversarial examples. Specifically, the BIM method begins by setting $\\acute{x}_{0}=x$ and then on the $i^{t h}$ iteration,  \n\n$$\n\\begin{array}{r}{\\mathcal{\\dot{x}}_{i}=\\mathrm{clip}_{x,\\epsilon}\\{\\dot{x}_{i-1}-\\eta\\cdot\\mathrm{sign}(\\nabla_{x}f(\\dot{x}_{i-1}))\\}}\\end{array}\n$$  \n\nwhere $\\eta$ is a hyper-parameter indicating the learning rate, and the function $\\mathsf{c l i p}_{x,\\epsilon}(\\dot{x})$ , inspired by [23], performs persample clipping of the voice $\\acute{x}$ , so the result will be in $L_{\\infty}$ $\\epsilon$ -neighbourhood of the source voice $x$ and will be a valid voice after being transformed back into the audio file format. Formally, $\\mathsf{c l i p}_{x,\\epsilon}(\\dot{x})=\\operatorname*{max}\\{\\operatorname*{min}\\{\\dot{x},1,x+\\epsilon\\},-1,x-\\epsilon\\}$ .  \n\nWe compute the gradient $\\nabla_{x}f(\\acute{x}_{i-1})$ by leveraging NES, which only depends on the recognition result. In detail, on the $i^{t h}$ iteration, we first create $m$ (must be even) Gaussian noises $(u_{1},...,u_{m})$ and add them onto $\\acute{x}_{i-1}$ , leading to $m$ new voices $\\hat{x}_{i-1}^{1},...,\\acute{x}_{i-1}^{m}$ , where $\\boldsymbol{\\dot{x}}_{i-1}^{j}=\\boldsymbol{\\dot{x}}_{i-1}+\\boldsymbol{\\sigma}\\times\\boldsymbol{u}_{j}$ and $\\sigma$ is the search variance of NES. Note that uj = −um+1−j for j = 1, ..., m2 . Then, we compute the loss values $f(\\acute{x}_{i-1}^{1}),...,f(\\acute{x}_{i-1}^{m})$ by querying the target system $\\mathrm{\\textmum}$ queries). Next, the gradient $\\nabla_{x}f(\\dot{x}_{i-1})$ is approximated by computing  \n\n$$\n\\begin{array}{r}{\\frac{1}{m\\times\\sigma}\\sum_{j=1}^{m}f(\\acute{x}_{i-1}^{j})\\times u_{j}.}\\end{array}\n$$  \n\nIn our experiments, $m=50$ and $\\sigma=1e-3$ . Finally, we compute $\\mathrm{sign}(\\nabla_{x}f(\\dot{x}_{i-1}))$ , a vector over the domain $\\{-1,0,1\\}$ , by applying element-wise sign mathematical operation to the gradient vector $\\textstyle{\\frac{1}{m\\times\\sigma}}\\sum_{j=1}^{m}f{\\overline{{({\\dot{x}}_{i-1}^{j})}}}\\times u_{j}$ .  \n\nHowever, the BIM method with the estimated gradients alone is not sufficient to construct adversarial samples in the black-box setting, due to the fact that the adversary has no access to the threshold $\\theta$ used in the loss function $f$ . To solve this problem, we present a novel algorithm for estimating $\\theta$ .  \n\nEstimating the threshold $\\theta$ . To estimate the threshold $\\theta$ , the main technical challenge is that the estimated threshold $\\acute{\\theta}$ should be no less than $\\theta$ in order to launch a successful attack, but should not exceed $\\theta$ too much, otherwise, the attack cost might become too expensive. Therefore, the goal is to compute a small $\\acute{\\theta}$ such that ${\\dot{\\theta}}\\geq\\theta$ . To achieve this goal, we propose a novel approach as shown in Algorithm 1. Given an OSI system with the scoring $S$ and decision $D$ modules, and an arbitrary voice $x$ such that $D(x)={\\tt r e j e c t}$ , i.e., $x$ is uttered by an imposter, Algorithm 1 outputs $\\acute{\\theta}$ such that ${\\dot{\\theta}}\\geq\\theta$ .  \n\n<html><body><table><tr><td>Input:</td><td>The target OSI system with scoringS and decisionDmodules Anarbitraryvoicecsuch thatD(c）=reject</td></tr><tr><td></td><td>Output:Estimated threshold0</td></tr><tr><td>2:△←1l;</td><td>1:0←maxi∈G[S(c)]; θ</td></tr><tr><td>3:←；</td><td></td></tr><tr><td>4:while True do</td><td></td></tr><tr><td>5:</td><td>←+△;</td></tr><tr><td>6:</td><td>f' ← 入x.max{θ -maxi∈G[S(x)],-k};</td></tr><tr><td>7:</td><td>whileTruedo</td></tr><tr><td>8:</td><td>←clipx,e{-n·sign(Vxf'(c))}; ≥ craft sample using f' >maxi∈G[S()]≥0</td></tr><tr><td>9:</td><td>if D()≠reject then;</td></tr><tr><td>10:</td><td>returnmaxieG[S()];</td></tr><tr><td>11:</td><td></td></tr><tr><td></td><td>ifmaxi∈G[S()]≥θ thenbreak;</td></tr></table></body></html>  \n\nIn detail, Algorithm 1 first computes the maximal score $\\acute{\\theta}=$ $\\mathrm{max}_{i\\in G}[S(x)]_{i}$ of the voice $x$ by querying the system (line 1). Since $D(x)={\\mathfrak{r e j e c t}}$ , we can know $\\ensuremath{\\vec{\\theta}}<\\theta$ . At Line 2, we initialize the search step $\\begin{array}{r}{\\Delta=|\\frac{\\theta}{10}|}\\end{array}$ , which will be used to estimate the desired threshold $\\acute{\\theta}$ . $\\Big\\vert\\frac{\\theta}{10}\\Big\\vert$ is chosen as a tradeoff between the precision of $\\acute{\\theta}$ and efficiency of the algorithm. The outer-while loop (Lines 4-11) iteratively computes a new candidate 0 by adding $\\Delta$ onto it (Line 5) and computes the function $f^{\\prime}=\\lambda x.\\operatorname*{max}\\{\\not\\theta-\\operatorname*{max}_{i\\in G}[S(x)]_{i},-\\kappa\\}$ (Line 6). $f^{\\prime}$ indeed is the loss function for untargeted attack in Eq. (3), in which $\\theta$ is replaced by the candidate $\\acute{\\theta}$ . The function $f^{\\prime}$ will be used to craft samples in the inner-while loop (Lines 7-11). For each candidate $\\hat{\\boldsymbol{\\theta}}$ , the inner-while loop (Lines 7-11) iteratively computes samples $\\acute{x}$ by querying the target system until the target system recognizes $\\acute{x}$ as some enrolled speaker (Line 9) or the maximal score of $\\acute{x}$ is no less than $\\acute{\\theta}$ (Line 11). If $\\acute{x}$ is recognized as some enrolled speaker (Line 9), then Algorithm 1 terminates and returns the maximal score of $\\acute{x}$ (Line 10), as $\\mathrm{max}_{i\\in G}[S(\\acute{x})]_{i}\\geq\\theta$ is the desired threshold. If the maximal score of $\\acute{x}$ is no less than 0 (Line 11), we restart the outer-while loop.  \n\nOne may notice that Algorithm 1 will not terminate when $D(\\acute{x})$ is always equal to reject. In our experiments, this never happens (cf. $\\S\\mathbf{V}_{\\cdot}$ ). Furthermore, it estimates a very close value to the actual threshold. Remark that the actual threshold $\\theta$ , obtained from the open-source SRS, is used to evaluate the performance of Algorithm 1 only.  \n\n# C. Attack on CSI Systems  \n\nA CSI system always classifies an input voice as one of the enrolled speakers. Therefore, we can adapt the attack on the OSI systems by ignoring the threshold $\\theta$ . Specifically, the loss function for targeted attack on CSI systems with the target speaker $t\\in G$ is defined as:  \n\nTABLE I: Dataset for experiments   \n\n\n<html><body><table><tr><td>Datasets</td><td>#Speaker</td><td>Details</td></tr><tr><td>Train-1 Set</td><td>7,273</td><td>PartofVoxCeleb1[69]andwholeVoxCeleb2 [70]used for training ivector and GMM</td></tr><tr><td>Train-2 Set</td><td>2,411</td><td>Part of LibriSpeech[71] used for training system C in transferability</td></tr><tr><td>Test Speaker Set</td><td>5</td><td>speakersfromLibriSpeech 3female and 2male,5voices per speaker, voicesrangefrom3to4seconds</td></tr><tr><td>Imposter Speaker Set</td><td>4</td><td>Another4speakersfromLibriSpeech 2female and 2male,5voices per speaker, voicesrangefrom2 to14seconds</td></tr></table></body></html>  \n\n$$\nf(x)=\\mathrm{{max}}\\left\\{(\\mathrm{{max}}_{i\\in G\\backslash\\{t\\}}[S(x)]_{i}-[S(x)]_{t}),-\\kappa\\right\\}\n$$  \n\nIntuitively, we want to find some small perturbation $\\delta$ such that the score of the speaker $t$ is the largest one among all the enrolled speakers, and $[S(x)]_{t}$ is at least $\\kappa$ greater than the second-largest score.  \n\nSimilarly, the loss function for untargeted attack on CSI systems is defined as:  \n\n$$\n\\begin{array}{r}{f(x)=\\operatorname*{max}\\{([S(x)]_{m}-\\operatorname*{max}_{i\\in G\\backslash\\{m\\}}[S(x)]_{i}),-\\kappa\\}}\\end{array}\n$$  \n\nwhere $m$ denotes the true speaker of the original voice. Intuitively, we want to find some small perturbation $\\delta$ such that the largest score among other enrolled speakers is at least $\\kappa$ greater than the score of the speaker $m$ .  \n\n# D. Attack on SV Systems  \n\nAn SV system has exactly one enrolled speaker and checks if an input voice is uttered by the enrolled speaker or not. Thus, we can adapt the attack on OSI systems by assuming the speaker group $G$ is a singleton set. Specifically, the loss function for attacking SV systems is defined as:  \n\n$$\nf(x)=\\operatorname*{max}\\{\\theta-S(x),-\\kappa\\}\n$$  \n\nIntuitively, we want to find a small perturbation $\\delta$ such that the score of $x$ being recognized as the enrolled speaker is at least $\\kappa$ greater than the threshold $\\theta$ . We remark that the threshold estimation algorithm for SV systems should be revised by replacing the loss function $f^{\\prime}$ at Line 6 in Algorithm 1 with the following function: $f^{\\prime}=\\lambda x$ . $\\operatorname*{max}\\{\\dot{\\theta}-S(x),-\\kappa\\}$ .  \n\n# V. ATTACK EVALUATION  \n\nWe evaluate FAKEBOB for its attacking capabilities based on the following five aspects: effectiveness/efficiency, transferability, practicability, imperceptibility, and robustness.  \n\n# A. Dataset and Experiment Design  \n\nDataset. We mainly use three widely used datasets: VoxCeleb1, VoxCeleb2, and LibriSpeech (cf. Table I). To demonstrate our attack, we target the ivector and GMM systems from the popular open-source platform Kaldi, having 7,631 stars and 3,418 forks on Github [27]. The UBM model is trained using the Train-1 Set as the background voices. The OSI and CSI are enrolled by 5 speakers from the Test Speaker Set, forming a speaker group. The SV is enrolled by 5 speakers from the Test Speaker Set, resulting in 5 ivector and 5 GMM systems.  \n\nTABLE II: Metrics used in this work   \n\n\n<html><body><table><tr><td>Metric</td><td>Description</td></tr><tr><td>Attack successrate (ASR)</td><td>Proportionofadversarialvoicesthat arerecognizedas thetargetspeaker</td></tr><tr><td>Untargeteds success rate (UTR)forCSI</td><td>Proportionofadversarial samples that arenotrecognized as the source speaker</td></tr><tr><td>Untargeteds success rate (UTR)forOSI</td><td>Proportionofadversarialsamplesthat arenotrejectedbythetargetsystem</td></tr></table></body></html>  \n\nWe conducted the experiments on a server with Ubuntu 16.04 and Intel Xeon CPU E5-2697 v2 2.70GHz with 377G RAM (10 cores). We set $\\kappa=0$ , max iteration $_{\\cdot=1,000}$ , max/min learning rate $\\eta$ is 1e-3/1e-6, search variance $\\sigma$ in NES is 1e-3, and samples per draw $m$ in NES is 50, unless explicitly stated. Evaluation metrics. To evaluate our attack, we use the metrics shown in Table II. Signal-noise ratio (SNR) is widely used to quantify the level of signal power to noise power, so we use it to measure the distortion of the adversarial voices [10]. We use the equation, $\\mathrm{SNR}(\\mathrm{dB}){=}10\\log_{10}\\left(P_{x}/P_{\\delta}\\right)$ , to obtain SNR, where $P_{x}$ is the signal power of the original voice $x$ and $P_{\\delta}$ is the power of the perturbation $\\delta$ . Larger SNR value indicates a (relatively) smaller perturbation. To evaluate efficiency, we use two metrics: number of iterations and time. (Note that the number of queries is the number of iterations multiplied by samples per draw $m$ in NES and $m=50$ in this work.)  \n\nExperiment design. We design five experiments. (1) We evaluate the effectiveness and efficiency on both open-source systems (i.e., ivector, GMM, and xvector) and the commercial system Talentedsoft. We also evaluate FAKEBOB under intragender and inter-gender scenarios, as inter-gender attacks are usually more difficult. (2) We evaluate the transferability by attacking the open-source systems with different architecture, training dataset, and parameters, as well as the commercial system Microsoft Azure. (3) We further evaluate the practicability by playing the adversarial voices over the air in the physical world. (4) For human-imperceptibility, we conduct a real human study through Amazon Mechanical Turk platform (MTurk) [30], a crowdsourcing marketplace for human intelligence. (5) We finally evaluate defense methods, local smoothing, quantization, audio squeezing, temporal dependency-based detection, to defend against FAKEBOB.  \n\nRecall that we demonstrate our attack on 16 representative attack scenarios out of 40 (cf. $\\S\\Pi{\\bf-B},$ ). In particular, we mainly consider targeted attack which is much more powerful and challenging than untargeted attack [9]. Our experiments suffice to understand the other four parameters of the attack model, i.e., inter-gender vs. intra-gender, API vs. over-the-air, OSI vs. CSI vs. SV, decision and scores vs. decision-only.  \n\nThe OSI task can be seen as a combination of the CSI and SV tasks (cf. $\\mathrm{\\SII},$ ). Thus, we sometimes only report and analyze the results on the OSI task due to space limitation, which is much more challenging and representative than the other two. The missing results can be found in Appendix.  \n\nB. Effectiveness and Efficiency  \n\nTarget model training. To evaluate the effectiveness and efficiency, we train ivector and GMM systems for the OSI,  \n\nTABLE III: Six trained SRSs   \n\n\n<html><body><table><tr><td>Task</td><td>Metrics</td><td>ivector</td><td>GMM</td></tr><tr><td>CSI</td><td>Accuracy</td><td>99.6%</td><td>99.3%</td></tr><tr><td rowspan=\"2\">SV</td><td>FRR</td><td>1.0%</td><td>5.0%</td></tr><tr><td>FAR</td><td>11.0%</td><td>10.4%</td></tr><tr><td rowspan=\"3\">OSI</td><td>FRR</td><td>1.0%</td><td>4.2%</td></tr><tr><td>FAR</td><td>7.9%</td><td>11.2%</td></tr><tr><td>OSIER</td><td>0.2%</td><td>2.8%</td></tr></table></body></html>  \n\nTABLE IV: Results of threshold estimation   \n\n\n<html><body><table><tr><td colspan=\"3\">ivector</td><td colspan=\"3\">GMM</td></tr><tr><td></td><td></td><td>Time (s)</td><td></td><td></td><td>Time (s)</td></tr><tr><td>1.45</td><td>1.47</td><td>628</td><td>0.091</td><td>0.0936</td><td>157</td></tr><tr><td>1.57</td><td>1.60</td><td>671</td><td>0.094</td><td>0.0957</td><td>260</td></tr><tr><td>1.62</td><td>1.64</td><td>686</td><td>0.106</td><td>0.1072</td><td>269</td></tr><tr><td>1.73</td><td>1.75</td><td>750</td><td>0.113</td><td>0.1141</td><td>289</td></tr><tr><td>1.84</td><td>1.87</td><td>804</td><td>0.119</td><td>0.1193</td><td>314</td></tr></table></body></html>  \n\nCSI and SV tasks. The performance of these systems is shown in Table III, where accuracy is as usual, False Acceptance Rate (FAR) is the proportion of voices that are uttered by imposters but accepted by the system [18], False Rejection Rate (FRR) is the proportion of voices that are uttered by an enrolled speaker but rejected by the system [18], Open-set Identification Error Rate (OSIER) is the rate of voices that cannot be correctly classified [17]. Notice that the threshold $\\theta$ is 1.45 for ivector and 0.091 for GMM, so that the FAR is close to $10\\%$ . Although the parameter $\\theta$ in SV and OSI tasks can be tuned using Equal Error Rate, i.e., FAR is equal to FRR, we found that the results for SV and OSI tasks do not vary too much (cf. Table XVII in Appendix).  \n\nSetting. The parameter $\\epsilon$ is one of the most critical parameters of our attack. To fine-tune $\\epsilon$ , we study ASR, efficiency and distortion by varying $\\epsilon$ from 0.05, 0.01, 0.005, 0.004, 0.003, 0.002, to 0.001, on ivector and GMM for the CSI task. The results are given in Appendix C. With decreasing of $\\epsilon$ , both the attack cost and SNR increase, while ASR decreases. As a trade-off between ASR, efficiency, and distortion, we set $\\epsilon=0.002$ in this experiment.  \n\nThe target speakers are the speakers from the Test Speaker Set (cf. Table I), the source speakers are the speakers, from the Test Speaker Set for CSI, and from the Imposter Speaker Set (cf. Table I) for SV and OSI. Ideally, we will craft 100 adversarial samples using FAKEBOB for each task, where 40 adversarial samples are intra-gender and 60 inter-gender for CSI, and 50 intra-gender and 50 inter-gender for SV and OSI. Note that to diversify experiments, the source speakers of CSI and SV/OSI are designated to be different.  \n\nResults. The results are shown in Table V. Since the OSI task is more challenging and representative than the other two, we only analyze the results of the OSI task here. We can observe that FAKEBOB achieves $99.0\\%$ ASR for both ivector and GMM. In terms of SNR, the average SNR value is 31.5 (dB) for ivector and 31.4 (dB) for GMM, indicating that the perturbation is less than $0.071\\%$ and $0.072\\%$ . Furthermore, the average numbers of iterations and execution time are 86 and 38.0 minutes on ivector. The average numbers of iterations and execution time are 38 and 3.8 minutes on GMM, much smaller than that of ivector. Due to space limitation, results of attacking xvector are given in Appendix D where we observe similar results. These results demonstrate the effectiveness and efficiency of FAKEBOB.  \n\nWe can also observe that inter-gender attack is much more difficult (more iterations and execution time) than intra-gender attack due to the difference between sounds of male and female. Moreover, ASR of inter-gender attack is also lower than that of intra-gender attack. The result unveils that once the gender of the target speaker is known by attackers, it is much easier to launch an intra-gender attack.  \n\n![](/tmp/output/18_20250326021609/images/7942fd4af91fbdf46d90d6f2c571ee6dd66176eaf16c80173c00aff1fad43b51.jpg)  \nFig. 5: Transferability rate vs. κ  \n\nFor evaluation of the threshold estimation algorithm, we report the estimated threshold $\\acute{\\theta}$ in Table IV by setting 5 different thresholds. The estimation error is less than 0.03 for ivector and less than 0.003 for GMM. This shows that our algorithm is able to effectively estimate the threshold in less than 13.4 minutes. Note that our attack is black-box, and the actual thresholds are accessed only for evaluation.  \n\nAttacking the commercial system Talentedsoft [28]. We also evaluate the effectiveness and efficiency of FAKEBOB on Talentedsoft, developed by the constitutor of the voiceprint recognition industry standard of the Ministry of Public Security (China). We query this online platform via the HTTP post (seen as the exposed API). Since Talentedsoft targets Chinese Mandarin, to fairly test Talentedsoft, we use the Chinese Mandarin voice database aishell-1 [72]. Both FAR and FRR of Talentedsoft are $0.15\\%$ , tested using 20 speakers and 7,176 voices in total which are randomly chosen from aishell-1.  \n\nWe enroll 5 randomly chosen speakers from aishell-1 as targeted speakers, resulting in 5 SV systems. Each of them is attacked using another 20 randomly chosen speakers and one randomly chosen voice per speaker. Our attack achieves $100\\%$ ASR within 50 iterations (i.e., 2,500 queries) on average. Remark that FAKEBOB is an iterative-based method. We can always set some time slot between iterations or queries so that such amount of queries do not cause heavy traffic burden to the server, hence our attack is feasible. This demonstrates the effectiveness and efficiency of FAKEBOB on commercial systems that are completely black-box.  \n\n# C. Transferability  \n\nTransferability [7] is the property that some adversarial samples produced to mislead a model (called source system) can mislead other models (called target system) even if their architectures, training datasets, or parameters differ.  \n\nSetting. To evaluate the transferability, we regard the previously built GMM (A) and ivector (B) as source systems and build another 8 target systems (denoted by C,. . . ,J respectively). C,. . . ,I are ivector systems differing in key parameter and training dataset, and J is the xvector system. For details and performance of these systems, refer to Tables XIV and XV in Appendix. We denote by $X\\rightarrow Y$ the transferability attack where $X$ is the source system and $Y$ is the target system. The distribution of the transferability attacks is shown in Fig. 6 in terms of architecture, training dataset, and key parameters. We can see that some attacks belong to multiple scenarios. We set $\\epsilon=0.05$ and (1) $\\kappa=0.2$ (GMM) and $\\kappa=10$ (ivector) for the CSI task, (2) $\\kappa=3$ (GMM) and $\\kappa=4$ (ivector) for the SV task, (3) $\\kappa=3$ (GMM) and $\\kappa=5$ (ivector) for the OSI task. Remark that $\\kappa$ differs from architectures and tasks due to their different scoring mechanisms. We fine-tuned the parameter $\\kappa$ for ASR under the max iteration bound 1,000.  \n\nTABLE V: Experimental results of FAKEBOB when $\\epsilon=0.002$ , where #Iter refers to #Iteration.   \n\n\n<html><body><table><tr><td></td><td colspan=\"8\">System</td><td colspan=\"8\">System (Intra-gender attack)</td><td colspan=\"10\">System (Inter-gender attack)</td></tr><tr><td>Task</td><td colspan=\"2\"></td><td colspan=\"2\">ivector</td><td colspan=\"2\"></td><td colspan=\"2\">GMM</td><td colspan=\"2\"></td><td colspan=\"2\">ivector</td><td colspan=\"2\"></td><td colspan=\"2\">GMM</td><td></td><td colspan=\"2\"></td><td>ivector</td><td colspan=\"2\"></td><td colspan=\"2\">GMM</td><td colspan=\"2\"></td></tr><tr><td></td><td>#Iter</td><td>(s)</td><td>TimeSNRASR (dB)(%)</td><td></td><td>#Iter</td><td>Time SNRASR (s)</td><td>(dB)(%)</td><td></td><td>#Iter</td><td>(s)</td><td>(dB)(%)</td><td>TimeSNRASR</td><td>#Iter</td><td></td><td>TineSNRASR</td><td></td><td></td><td>#Iter</td><td>Time SNRASR</td><td></td><td></td><td></td><td>#Iter</td><td></td><td></td><td>Time SNR ASR</td></tr><tr><td>CSI</td><td>124</td><td>2845 30.2 99.0</td><td></td><td></td><td>40</td><td>218</td><td>29.399.0</td><td></td><td>92</td><td>2115</td><td>29.3100.0</td><td></td><td>25</td><td>s 126</td><td></td><td>(dB)(%) 28.8 100.0146</td><td></td><td></td><td>(s) 334030.8</td><td></td><td>(dB)(%)</td><td></td><td></td><td>(s) 278</td><td>(dB)(%)</td><td>29.62 98.0</td></tr><tr><td>SV</td><td>84</td><td>2014 31.6 99.0</td><td></td><td></td><td>39</td><td>241</td><td>31.499.0</td><td></td><td>31</td><td>751</td><td>31.7</td><td>98.03</td><td>30</td><td>185</td><td>31.7</td><td></td><td>100.0</td><td>135</td><td>3252 31.6 100.0</td><td></td><td>98.0</td><td>50</td><td></td><td>298</td><td>31.2</td><td>98.0</td></tr><tr><td>ISO</td><td>86</td><td>227731.5 99.0</td><td></td><td></td><td>38</td><td>226</td><td>31.499.0</td><td></td><td>32</td><td>833</td><td></td><td>31.398.0</td><td>31</td><td></td><td></td><td>17831.5100.0</td><td></td><td>1403692 31.6 100.0</td><td></td><td></td><td></td><td>48 45</td><td>274</td><td></td><td>31.2</td><td>98.0</td></tr></table></body></html>\n\nsuccessful transferability attack.  \n\n![](/tmp/output/18_20250326021609/images/592e8ae41abcaeaf13b4849ee6739557c3ce0dd92c3f78443ffb9cc9102529d3.jpg)  \nFig. 6: Distribution of transferability attacks  \n\nResults. The results of attacking OSI systems are shown in Table VI. All the attacks (except for $B\\rightarrow A$ ) achieve $34\\%$ - $68\\%$ ASR and $40\\%-100\\%$ UTR. For $B\\rightarrow D$ , $B\\rightarrow E$ , $B\\rightarrow F$ , $B\\rightarrow G$ , and $B\\rightarrow H$ (all are ivector, but differ in one key parameter), FAKEBOB achieves $100\\%$ ASR and UTR, indicating that cross architecture reduces transferability rate. From $A\\rightarrow B$ and $A\\rightarrow C$ (where $A$ is GMM, $B$ and $C$ are ivector but differ in training data), cross dataset also reduces transferability rate. The transferability rate of $B\\rightarrow A$ is the lowest one and less than that of $A\\rightarrow B$ , indicating that transferring from the architecture ivector $(B)$ to GMM $(A)$ is more difficult. Compared with $A\\rightarrow C$ (both cross dataset and architecture), $B\\rightarrow C$ (cross dataset) achieves nearly $20\\%$ more ASR and UTR. This reveals that the larger the difference between the source and target systems is, the more difficult the transferability attack is. Due to space limitation, the results of attacking the CSI and SV systems are shown in Tables XVI and XVIII in Appendix. We can observe similar results. The average SNR is similar to the one given in Table VII.  \n\nTo understand how the value of $\\kappa$ influences the transferability rate, we conduct $B\\rightarrow F$ attack (OSI task) by fixing $\\epsilon=0.05$ and varying $\\kappa$ from 0.5 to 5.0 with step 0.5. In this experiment, the number of iterations is unlimited. The results are shown in Fig. 5. Both ASR and UTR increase quickly with $\\kappa$ , and reach $100\\%$ when $\\kappa=4.5$ . This demonstrates that increasing the value of $\\kappa$ increases the probability of a  \n\nAttacking the commercial system Microsoft Azure [29]. Microsoft Azure is a cloud service platform with the second largest market share in the world. It supports both the SV and OSI tasks via HTTP REST API. Unlike Talentedsoft, Azure’s API only returns the decision (i.e., the predicted speaker) along with 3 confidence levels (i.e., low, normal and high) instead of scores, so we attack this platform via transferability. We enroll 5 speakers from the Test Speaker Set to build an OSI system on Azure (called OSI-Azure for simplicity). Its FAR is $0\\%$ tested by the Imposter Speaker Set. For each target speaker, we randomly select 10 source speakers and 2 voices per source speaker from LibriSpeech, which are rejected by OSI-Azure. We set $\\epsilon=0.05$ and craft 100 adversarial voices on the GMM system, as it produces high tranferability rate in the above experiment. The ASR, UTR and SNR are $26.0\\%$ , $41.0\\%$ and $6.8~\\mathrm{dB}$ , respectively. They become $34.0\\%$ , $57.0\\%$ and $2.2~\\mathrm{dB}$ when we increase $\\epsilon$ from 0.05 to 0.1.  \n\nWe also demonstrate FAKEBOB on the SV task of Azure (SV-Azure) which is text-dependent with 10 supported texts. We recruited and asked 2 speakers to read each text 10 times, resulting in 200 voices. For each pair of speaker and text, we randomly select 3 enrollment voices for both GMM and SVAzure, and the FARs of them are $0\\%$ . We attack SV-Azure using 200 adversarial samples crafted from GMM $\\epsilon=0.05$ , $\\kappa=3$ ). However, SV-Azure reports “error, too noisy” instead of “accept” or “reject” for 190 adversarial voices. Among the other 10 voices, one voice is accepted, leading to $10\\%$ ASR. To our knowledge, this is the first time that SV-Azure is successfully attacked. As Azure is proprietary without any publicly available information, it is very difficult to know the reason why SV-Azure outputs “error, too noisy”. After comparing the SNR of the 190 voices with the other 10 voices (8.8 dB vs. 11.5 dB), we suspect that it checks each input and outputs “error, too noisy” without model classification if the noise of the input is too large. This check makes SV-Azure more challenging to attack, but we infer it may also reject normal voices when the background is noisy in practice.  \n\n# D. Practicability for Over-the-Air Attack  \n\nTo simulate over-the-air attack in the physical world, we first craft adversarial samples by directly interacting with API of the system (i.e., over the line), then play and record these adversarial voices via loudspeakers and microphones, and finally send recorded voices to the system via API to check their effectiveness. Our experiments are conducted in an indoor room (length, width, and height are 10, 4, 3.5 meters).  \n\nTABLE VI: Transferability rate $(\\%)$ for OSI task, where S and T denote source and target systems respectively.   \n\n\n<html><body><table><tr><td>T</td><td colspan=\"2\">A</td><td colspan=\"2\"></td><td colspan=\"2\"></td><td colspan=\"2\">C</td><td colspan=\"2\">D</td><td colspan=\"2\">E</td><td colspan=\"2\">F</td><td colspan=\"2\">G</td><td colspan=\"2\">H</td><td colspan=\"2\"></td><td colspan=\"2\"></td></tr><tr><td>S</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td></td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ATR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td></td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td></tr><tr><td>A</td><td>一</td><td></td><td>62.0</td><td>64.0</td><td></td><td>48.0</td><td>48.0</td><td>55.2</td><td>56.9</td><td>68.0</td><td>68.0</td><td>64.0</td><td>64.0</td><td></td><td>52.0</td><td>54.0</td><td>68.0</td><td>68.0</td><td>38.0</td><td>40.0</td><td>34.0</td><td>42.0</td></tr><tr><td>B</td><td>5.0</td><td>5.0</td><td>一</td><td>一</td><td></td><td>67.5</td><td>67.5</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td></td><td>100.0</td><td>72.5</td><td>75.0</td><td>40.0</td><td>41.7</td></tr></table></body></html>  \n\nTABLE VII: Results of different systems   \n\n\n<html><body><table><tr><td colspan=\"2\">System</td><td>SNR</td><td colspan=\"2\">Result (%)</td></tr><tr><td rowspan=\"3\">ivector</td><td></td><td>(dB)</td><td>Normalvoices</td><td>Adversarialvoices</td></tr><tr><td>CSI</td><td>6.6</td><td>Accuracy:100</td><td>ASR:80,UTR:80</td></tr><tr><td>SV</td><td>9.8</td><td>FAR:O,FRR:0</td><td>ASR:76</td></tr><tr><td rowspan=\"3\">GMM</td><td>ISO</td><td>7.8</td><td>FAR:4,FRR:0,OSIER:0</td><td>ASR:100,UTR:100</td></tr><tr><td>CSI</td><td>6.1</td><td>Accuracy:85</td><td>ASR:90,UTR:100</td></tr><tr><td>SV</td><td>7.9</td><td>FAR:0,FRR:62</td><td>ASR:100</td></tr><tr><td>Azure</td><td>ISO ISO</td><td>8.2 6.8</td><td>FAR:0,FRR:65,OSIER:0 FAR:5,FRR:2,OSIER:0</td><td>ASR:100,UTR:100 ASR:70,UTR:70</td></tr></table></body></html>  \n\nTo thoroughly evaluate FAKEBOB, the over-the-air attacks vary in systems, devices (loudspeakers and microphones), distance between loudspeakers and microphones, and acoustic environments. In total, it covers 26 scenarios. The overview of different settings is shown in Table XIX in Appendix. We consider all tasks of ivector and GMM, and the OSI-Azure only. We use the same parameters as in Section V-C, as over-the-air attack is more practical yet more challenging due to the noise introduced from both air channel and electronic devices which probably disrupts the perturbations of adversarial samples. For OSI-Azure, we use the adversarial voices crafted on GMM in Section V-C that are successfully transferred to OSI-Azure.  \n\nResults of different systems. We use portable speaker (JBL clip3 [73]) as the loudspeaker, iPhone 6 Plus (iOS) as the microphone with 1 meter distance between them. We attack all tasks of ivector and GMM, and the OSI-Azure in a relatively quiet environment. The results are shown in Table VII. We can observe that the FRR of GMM SV (resp. OSI) is $62\\%$ (resp. $65\\%$ ), revealing that GMM is less robust than ivector for normal voices. FAKEBOB achieves (1) for the CSI task, $90\\%$ ASR (i.e., the system classifies the adversarial voice as the target speaker) and $100\\%$ UTR (i.e., the system does not classify the adversarial voice as the source speaker) on the GMM, and achieves $80\\%$ ASR and $80\\%$ UTR on the ivector; (2) for the SV task, at least $76\\%$ ASR; (3) for the OSI task, $100\\%$ ASR on both the GMM and ivector; (4) achieves $70\\%$ ASR on the commercial system OSI-Azure.  \n\nIn terms of SNR, the average SNR is no less than $6.1~\\mathrm{dB}$ , and the average SNR is up to $9.8~\\mathrm{dB}$ on the ivector for the SV task, indicating that the power of the signal is 9.5 times greater than that of the noise. Moreover, the SNR is much better than the over-the-air attack in CommanderSong [10].  \n\nResults of different devices. For loudspeakers, we use 3 common devices: laptop (DELL), portable speaker (JBL clip3) and broadcast equipment (Shinco [74]). For microphones, we use built-in microphones of 2 mobile phones: OPPO (Android) and iPhone 6 Plus (iOS). We evaluate FAKEBOB against the OSI task of ivector with 1 meter distance in a relatively quiet environment. The results are shown in Table VIII.  \n\nTABLE VIII: Results of different devices $(\\%)$ , where L and M denote loudspeakers and microphones respectively.   \n\n\n<html><body><table><tr><td rowspan=\"3\">M L</td><td colspan=\"5\">iPhone 6 Plus (iOS)</td><td colspan=\"5\">OPPO (Android)</td></tr><tr><td colspan=\"3\">Normalvoices</td><td colspan=\"2\">Adv.voices</td><td colspan=\"3\">Normalvoices</td><td colspan=\"2\">Adv.voices</td></tr><tr><td>FAR</td><td>FRR</td><td>OSIER</td><td>ASR</td><td>UTR</td><td>FAR</td><td>FRR</td><td>OSIER</td><td>ASR</td><td>UTR</td></tr><tr><td>DELL</td><td>10</td><td>0</td><td>0</td><td>100</td><td>100</td><td>13</td><td>6</td><td>0</td><td>78</td><td>80</td></tr><tr><td>JBLclip3</td><td>4</td><td>0</td><td>0</td><td>100</td><td>100</td><td>6</td><td>0</td><td>0</td><td>80</td><td>80</td></tr><tr><td>Shinco</td><td>8</td><td>5</td><td>0</td><td>89</td><td>91</td><td>14</td><td>0</td><td>0</td><td>75</td><td>75</td></tr></table></body></html>  \n\nWe can observe that for any pair of loudspeaker and microphone, FAKEBOB can achieve at least $75\\%$ ASR and UTR. When JBL clip3 or DELL is the loudspeaker and iPhone 6 Plus is the microphones, FAKEBOB is able to achieve $100\\%$ ASR. When the loudspeaker is fixed, the ASR and UTR of attacks using IPhone 6 Plus are higher (at least $14\\%$ and $16\\%$ more) than that of using OPPO. Possible reason is that the sound quality of iPhone 6 Plus is better than that of OPPO phone. These results demonstrate the effectiveness of FAKEBOB on various devices.  \n\nResults of different distances. To understand the impact of the distance between loudspeakers and microphones, we vary distance from 0.25, 0.5, 1, 2, 4 to 8 meters. We attack the OSI task of ivector in a relatively quiet environment by using JBL clip3 as the loudspeaker and iPhone 6 Plus as the microphone.  \n\nThe results are shown in Table IX. We can observe that FAKEBOB can achieve $100\\%$ ASR and UTR when the distance is no more than 1 meter. When the distance is increased to 2 meters (resp. 4 meters), ASR and UTR drop to $70\\%$ (resp. $40\\%$ and $50\\%$ ). Although ASR and UTR drop to $10\\%$ when the distance is 8 meters, FRR also increases to $32\\%$ . This shows the effectiveness of FAKEBOB under different distances.  \n\nResults of different acoustic environments. We attack the OSI task of ivector using JBL clip3 and iPhone 6 Plus with 1 meter distance. To simulate different acoustic environments, we play different types of noises in the background using Shinco broadcast equipment. Specifically, we select 5 types of noises from Google AudioSet [75]: white noise, bus noise, restaurant noise, music noise, and absolute music noise. White noise is widespread in nature, while bus, restaurant, (absolute) music noises are representative of several daily life scenarios where FAKEBOB may be launched. For white noise, we vary its volume from $45~\\mathrm{dB}$ to $75~\\mathrm{dB}$ , while the volumes of other noises are $60~\\mathrm{dB}$ . Both adversarial and normal voices are played at $65~\\mathrm{dB}$ on average. The results are shown in Table X.  \n\nWe can observe that FAKEBOB achieves at least $48\\%$ ASR and UTR when the volume of background noises is no more than $60~\\mathrm{dB}$ no matter the type of the noises. Although both ASR and UTR decrease with increasing the volume of white noises, the FRR also increases quickly. This demonstrates the effectiveness of FAKEBOB in different acoustic environments.  \n\nTABLE IX: Results of different distances $(\\%)$ )   \n\n\n<html><body><table><tr><td colspan=\"2\">Distance (meter)</td><td>0.25</td><td>0.5</td><td>1</td><td>2</td><td>4</td><td>8</td></tr><tr><td rowspan=\"3\">Normal Voices</td><td>FAR</td><td>4</td><td>3</td><td>4</td><td>6</td><td>0</td><td>0</td></tr><tr><td>FRR</td><td>0</td><td>0</td><td>0</td><td>5</td><td>10</td><td>32</td></tr><tr><td>OSIER</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td rowspan=\"2\">Adversarial Voices</td><td>ASR</td><td>100</td><td>100</td><td>100</td><td>70</td><td>40</td><td>10</td></tr><tr><td>UTR</td><td>100</td><td>100</td><td>100</td><td>70</td><td>50</td><td>10</td></tr></table></body></html>  \n\n# E. Human-Imperceptibility via Human Study  \n\nTo demonstrate the imperceptibility of adversarial samples, we conduct a human study on MTurk [30]. The survey is approved by the Institutional Review Board (IRB) of our institutes.  \n\nSetup of human study. We recruit participants from MTurk and ask them to choose one of the two tasks and finish the corresponding questionnaire. We neither reveal the purpose of our study to the participants, nor record personal information of participants such as first language, age and region. The Amazon MTurk has designed Acceptable Use Policy for permitted and prohibited uses of MTurk, which prohibits bots or scripts or other automated answering tools to complete Human Intelligence Tasks [76]. Thus, we argue that the number of participants can reasonably guarantee the diversity of participants. The two tasks are described as follows.  \n\n• Task 1: Clean or Noisy. This task asks participants to tell whether the playing voice is clean or noisy. Specifically, we randomly select 12 original voices and 15 adversarial voices crafted from other original voices, among which 12 adversarial voices are randomly selected from the voices which become non-adversarial (called ineffective) when playing over the air with $\\epsilon=0.002$ and low confidence, and the other 3 are randomly selected from the voices which remain adversarial (called effective) when playing over the air with $\\epsilon~=~0.1$ and high confidence. We ask users to choose whether a voice has any background noise (The three options are clean, noisy, and not sure).  \n\n• Task 2: Identify the Speaker. This task asks participants to tell whether the voices in a pair are uttered by the same speaker. Specifically, we randomly select 3 speakers (2 male and 1 female), and randomly choose 1 normal voice per speaker (called reference voice). Then for each speaker, we randomly select 3 normal voices, 3 distinct adversarial voices that are crafted from other normal voices of the same speaker, and 3 normal voices from other speakers. In summary, we build 27 pairs of voices: 9 pairs are normal pairs (one reference voice and one normal voice from the same speaker), 9 pairs are other pairs (one reference voice and one normal voice from another speaker) and 9 pairs are adversarial pairs (one reference voice and one adversarial voice from the same speaker). Among 9 adversarial pairs, 6 pairs contain effective adversarial samples when playing over the air, and 3 pairs do not. We ask the participants to tell whether the voices in each pair are uttered by the same speaker (The three options are same, different, and not sure).  \n\nTABLE X: Results of different acoustic environments $(\\%)$ )   \n\n\n<html><body><table><tr><td colspan=\"2\">Environment</td><td>Quiet</td><td>White (45 dB)(</td><td>White (50 dB)(</td><td>White</td><td>White (60 dB)(65 dB)</td><td>White (75 dB)(60 dB)(</td><td>Bus</td><td>Rest. (60 dB)(60 dB)</td><td>Music</td><td>Abs.Music (60 dB)</td></tr><tr><td rowspan=\"3\">Normal voices</td><td>FAR</td><td>4</td><td>0</td><td>6</td><td>0</td><td>0</td><td>10</td><td>0</td><td>0</td><td>0</td><td>4</td></tr><tr><td>FRR</td><td>0</td><td>5</td><td>12</td><td>30</td><td>40</td><td>97</td><td>25</td><td>20</td><td>10</td><td>10</td></tr><tr><td>OSIER</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>10</td><td>0</td></tr><tr><td rowspan=\"2\">Adv. voices</td><td>ASR</td><td>100</td><td>75</td><td>70</td><td>57</td><td>20</td><td>2</td><td>50</td><td>50</td><td>66</td><td>48</td></tr><tr><td>UTR</td><td>100</td><td>75</td><td>70</td><td>60</td><td>20</td><td>2</td><td>50</td><td>50</td><td>67</td><td>48</td></tr></table></body></html>  \n\nTo ensure the quality of our questionnaire and validity of our results, we filter out the questionnaires that are randomly chosen by participants. In particular, we set three simple questions in each task. For task 1, we insert three silent voices as a concentration test. For task 2, we insert three pairs of voices, where each pair contains one male voice and one female voice as a concentration test. Only when all of them are correctly answered, we regard it as a valid questionnaire, otherwise, we exclude it.  \n\nResults of human study. We finally received 135 questionnaires for task 1 and 172 questionnaires for task 2, where 27 and 11 questionnaires are filtered out as they failed to pass our concentration tests. Therefore, there are 108 valid questionnaires for task 1 and 161 valid questionnaires for task 2. The results of the human study are shown in Fig. 7.  \n\nFor task 1, as shown in Fig. 7(a), $10.7\\%$ of participants heard noise on normal voices, while $20.2\\%$ and $84.8\\%$ of participants heard noise on ineffective and effective adversarial voices (when played over-the-air) respectively. We can see that $78.8\\%$ of participants still believe that ineffective voices are clean. For effective voices, we found that $84.8\\%$ is comparable to the recent white-box adversarial attack (i.e., $83\\%$ ) that tailors to craft imperceptible voices against speech recognition systems [20]. (We are not aware of any other adversarial attacks against SRSs that have done such human study.)  \n\nFor task 2 which is more interesting (in Fig. 7(b)), $86.5\\%$ of participants believe that voices in each other pair are uttered by different speakers, indicating the quality of collected questionnaires. For the adversarial pairs, $54.6\\%$ of participants believe that voices in each pair are uttered by the same speaker, very close to the baseline $53.7\\%$ of normal pairs, indicating that humans cannot differentiate the speakers of the normal and adversarial voices. The prior work [14] conducted an ABX testing on adversarial samples crafted by white-box attacks against SV systems. The ABX test first provides to users two voices $A$ and $B$ , each being either the original (reconstructed) voice or an adversarial voice; then provides the third voice $X$ which was randomly chosen from $\\{A,B\\}$ ; finally asks the users to decide if $X$ is $A$ or $B$ . The ABX testing of [14] shows that $54\\%$ of participants correctly classified the adversarial voices, which is very close to ours. For the adversarial pairs which contain ineffective adversarial voices, $64.9\\%$ of participants believed that the two voices are from the same speakers, much greater than the baseline $53.7\\%$ , thus more imperceptible. For the adversarial pairs which contain effective adversarial voices, $54.0\\%$ of participants can definitely differentiate the speaker, not too larger than the baseline $42.2\\%$ of normal pairs.  \n\n![](/tmp/output/18_20250326021609/images/fc30da70f4ff6a11d3b0a96e9124c46e76ec8572629c555bcab632f7f17905ab.jpg)  \nFig. 7: Results of human study, where air (resp. non air) denotes voices that are effective (resp. ineffective) for overthe-air attack  \n\nThe results unveil that the adversarial voices crafted by FAKEBOB can make systems misbehave (i.e., making a decision that the adversarial voice is uttered by the target speaker), while most of ineffective adversarial samples are classified clean and cannot be differentiated by ordinary users, and the results of effective ones are comparable to existing related works. Hence, our attack is reasonably human-imperceptible.  \n\n# F. Robustness of FAKEBOB against Defense Methods  \n\nAs mentioned in Section III-B, we study four defense methods: local smoothing, quantization, audio squeezing and temporal dependency detection. We evaluate on the OSI task of the GMM system unless explicitly stated using 100 seed voices. The FRR, FAR, ASR and UTR of the system without defense is $4.2\\%$ , $11.2\\%$ , $99\\%$ and $99\\%$ , respectively. We consider two settings: (S1) crafting adversarial voices on the system without defense and attacking the system with defense, and (S2) directly attacking the system with defense. S1 follows from CommanderSong [10]. An effective defense method should be able to mitigate the perturbation or detect the adversarial voices in S1. Thus, we will use the UTR metric. In S2, an effective defense method should increase the overhead of the attack and decrease the attack success rate, thus we will use the ASR metric. We set $\\epsilon=0.002$ , a very weak attacker capacity. Increasing $\\epsilon$ will make FAKEBOB more powerful.  \n\nWe found that the local smoothing can increase attack cost, but is ineffective in terms of ASR, audio squeezing is ineffective in terms of both attack cost and ASR, while the other two are not suitable for defending our attack. Due to space limitation, details are given in Appendix E.  \n\n# VI. DISCUSSION OF THE POSSIBLE ARM RACE  \n\nThis section discusses the potential mitigation of our attacks and possible advanced attacks.  \n\nMitigation of FAKEBOB. We have demonstrated that four defense methods have limited effects on FAKEBOB although some of them are reported promising in the speech recognition domain. This reveals that more effective defense methods are needed to mitigate FAKEBOB. We discuss several possible defense methods as follows.  \n\nVarious liveness detection methods have been proposed to detect spoofing attacks on SRSs. Such methods detect attacks by exploiting the different physical characteristics of the voices generated by the human speech production system (i.e., lungs, vocal cords, and vocal tract) and electronic loudspeaker. For instance, Shiota et al. [77] use pop noise caused by human breath, VoiceLive [78] leverages time-difference-of-arrival of voices to the receiver, and VoiceGesture [79] leverages the unique articulatory gesture of the user. Adversarial voices also need to be played via loudspeakers, hence liveness detection could be possibly used to detect them. An alternative detection method is to train a detector using adversarial voices and normal voices. Though promising in image recognition domain [80], it has a very high false-positive rate and does not improve the robustness when the adversary is aware of this defense [81]. Another scheme to mitigate adversarial images is input transformation such as image bit-depth reduction and JPEG compression [82]. We could mitigate adversarial voices by leveraging input transformations such as bit-depth reduction and MP3 compression. However, Athalye et al. [83] have demonstrated that input transformation on images can be easily circumvented by strong attacks such as Backward Pass Differentiable Approximation. We conjecture that bitdepth reduction and MP3 compression may become ineffective for high-confidence adversarial voices.  \n\nFinally, one could also improve the security of SRSs by using a text-dependent system and requiring users to read dynamically and randomly generated sentences. By doing so, the adversary has to attack both the speaker recognition and the speech recognition, hence incurring attack costs. If the set of phrases to be uttered is relatively small, we could also attack the system by iteratively querying the target system using the voice corresponding to the generated phrase. While our attack will fail when the set of phrases to be uttered is very large or even infinite. However, this also brings the challenge for the recognition system, as the training data may not be able to cover all the possible normal phrases and voices.  \n\nWe will study the above methods [77], [78], [79], [82], [83], [84], [85], [86] for adversarial attacks in future. We next discuss possible methods on improving adversarial attacks.  \n\nPossible advanced attacks. For a system that outputs the decision result and scores, FAKEBOB can directly craft adversarial voices via interacting with it. However, for a system that only outputs the decision result, we have to attack it by leveraging transferability. When the gap between source and target systems is larger, the transferability rate is limited. One possible solution to improve FAKEBOB is to leverage the boundary attack, which is proposed to attack decision-only image recognition systems by Brendel et al. [87].  \n\nOur human study shows that our attack is reasonably human-imperceptible. However, many of effective adversarial voices are still noisier than original voices (human study task 1), and some of effective adversarial voices can be differentiated from different speakers by ordinary users (human study task 2), there still has space for improving imperceptibility in future. One possible solution is to build a psychoacoustic model and limit the maximal difference between the spectrum of the original and adversarial voices to the masking threshold (hearing threshold) of human perception [88], [20].  \n\n# VII. RELATED WORK  \n\nThe security issues of intelligent voice systems have been studied in the literature. In this section, we discuss the most related work on attacks over the intelligent voice systems, and compare them with FAKEBOB.  \n\nAdversarial voice attacks. Gong et al. [13] and Kreuk et al. [14] respectively proposed adversarial voice attacks on SRSs in the white-box setting, by leveraging the Fast Gradient Sign Method (FGSM) [22]. The attack in [13] addresses DNN-based gender recognition, emotion recognition and CSI systems, while the attack in [14] addresses a DNN-based SV system. Compared to them: (1) Our attack FAKEBOB is black-box and more practical. (2) FAKEBOB addresses not only the SV and CSI, but also the more general OSI task. (3) We demonstrate our attack on ivector, GMM and DNNbased systems in the popular open-source platform Kaldi. (4) FAKEBOB is effective on the commercial systems, even when playing over the air, which was not considered in [13], [14].  \n\nIn a concurrent work, Abdullah et al. [60] proposed a poisoning attack on speaker and speech recognition systems, that is demonstrated on the OSI-Azure. There are three key differences: (1) Their attack crafts an adversarial voice from a voice uttered by an enrolled speaker $A$ such that the adversarial voice is neither rejected nor recognized as the speaker $A$ . Thus, their attack neither can choose a specific source speaker nor a specific target speaker to be recognized by the system, consequently, they cannot launch targeted attack or attacks against the SV task. Whereas our attack goes beyond their attack. (2) They craft adversarial voice by decomposing and reconstructing an input voice, hence, achieved a limited untargeted success rate and cannot be adapted to launch more interesting and powerful targeted attacks. (3) We evaluate overthe-air attacks in the physical world, but they did not.  \n\nWe cannot compare the performance (i.e., effectiveness and efficiency) of our attack with the three related works above [13], [14], [60] because all of them are not available. We are the first considering the threshold $\\theta$ in adversarial attack. Adversarial attacks on speech recognition systems also have been studied [11], [9], [89]. Carlini et al. [9] attacked DeepSpeech [90] by crafting adversarial voices in the whitebox setting, but failed to attack when playing over the air. In the black-box setting, Rohan et al. [11] combined a genetic algorithm with finite difference gradient estimation to craft adversarial voices for DeepSpeech, but achieved a limited success rate with strict length restriction over the voices. Alzantot et al. [89] presented the first black-box adversarial attack on a CNN-based speech command classification model by exploiting a genetic algorithm. However, due to the difference between speaker recognition and speech recognition, these works are orthogonal to our work and cannot be applied to ivector and GMM based SRSs.  \n\nOther types of voice attacks. Other types of voice attacks include hidden voice attack (both against speech and speaker recognition) and spoofing attack (against speaker recognition).  \n\nHidden voice attack aims to embed some information (e.g., command) into an audio carrier (e.g., music) such that the desired information is recognized by the target system without catching victims’ attention. Abdullah et al. [91] proposed such an attack on speaker and speech recognition systems. There are two key differences: (1) Based on characteristics of signal processing and psychoacoustics, their attack perturbed a sample uttered by an enrolled speaker such that it is still correctly classified as the enrolled speaker by the target system but becomes incomprehensible to human listening. While our attack perturbed a sample uttered by an arbitrary speaker such that it is misclassified as a target speaker (targeted attack) or another enrolled speaker (untargeted attack) but the perturbation is imperceptible to human listening. This means their attack addresses a different attack scenario compared with ours. (2) They did not demonstrate over-the-air attack on SRSs and their tool is not available, hence it is unclear how effective it is on SRSs. DolphinAttack [92], CommanderSong [10] and the work done by Carlini et al. [34] proposed hidden voice attacks on SRSs. Carlini et al. launched both black-box (i.e., inverse MFCC) and white-box (i.e., gradient decent) attacks on GMM based speech recognition systems. DolphinAttack exploited vulnerabilities of microphones and employed the ultrasound as the carrier of commands to craft inaudible voices. However, it can be easily defended by filtering out the ultrasound from voices. CommanderSong launched whitebox attacks by exploiting a gradient descent method to embed commands into music songs.  \n\nAnother attack type on SRSs is spoofing attack [93] such as mimic [94], replay [95], [96], recorder attack [97], [96], voice synthesis [98], and voice conversion [99], [100], [101], [96] attacks. Different from adversarial attack [14], [102], spoofing attack aims at obtaining a voice such that it is correctly classified as the target speaker by the system, and also sound like the target speaker listened by ordinary users. When anyone familiar with the victim (including the victim) cannot hear the attack voice, both spoofing and adversarial attacks can be launched. However, if someone familiar with the victim (including the victim) can hear the attack voice, he/she may detect the spoofing attack. Whereas, adversarial attack could be launched in this setting as discussed in Section II-B.  \n\n# VIII. CONCLUSION  \n\nIn this paper, we conducted the first comprehensive and systematic study of adversarial attack on SRSs in a practical black-box setting, by proposing a novel practical adversarial attack FAKEBOB. FAKEBOB was thoroughly evaluated in 16 attack scenarios. FAKEBOB can achieve $99\\%$ targeted attack success rate on both open-source and the commercial systems. We also demonstrated the transferability of FAKEBOB on Microsoft Azure. When played over the air in the physical world, FAKEBOB is also effective. Our findings reveal the security implications of FAKEBOB for SRSs, calling for more robust defense methods to better secure SRSs against such practical adversarial attacks.  \n\n# ACKNOWLEDGMENTS  \n\nThis research was partially supported by National Natural Science Foundation of China (NSFC) grants (No. 61532019 and No. 61761136011), National Research Foundation (NRF) Singapore, Prime Ministers Office under its National Cybersecurity R&D Program (Award No. NRF2014NCR-NCR001- 30 and No. NRF2018NCR-NCR005-0001), National Research Foundation (NRF) Singapore, National Satellite of Excellence in Trustworthy Software Systems under its Cybersecurity R&D Program (Award No. NRF2018NCR-NSOE003-0001), and National Research Foundation Investigatorship Singapore (Award No. NRF-NRFI06-2020-0001).  \n\n# REFERENCES  \n\n[1] T. Kinnunen and H. Li, “An overview of text-independent speaker recognition: From features to supervectors,” Speech Commun., 2010.   \n[2] TD Bank voiceprint. https://www.tdbank.com/bank/tdvoiceprint.html.   \n[3] S. Nand, “Forensic and automatic speaker recognition system,” IJCEE, 2018.   \n[4] H. Ren, Y. Song, S. Yang, and F. Situ, “Secure smart home: A voiceprint and internet based authentication system for remote accessing,” in ICCSE, 2016.   \n[5] D. Ribas and E. Vincent, “An improved uncertainty propagation method for robust i-vector based speaker recognition,” in ICASSP, 2019.   \n[6] B. Biggio, I. Corona, D. Maiorca, B. Nelson, N. Srndic, P. Laskov, G. Giacinto, and F. Roli, “Evasion attacks against machine learning at test time,” in ECML/PKDD, 2013.   \n[7] C. Szegedy, W. Zaremba, I. Sutskever, J. Bruna, D. Erhan, I. J. Goodfellow, and R. Fergus, “Intriguing properties of neural networks,” in ICLR, 2014.   \n[8] Y. Lei, S. Chen, L. Fan, F. Song, and Y. Liu, “Advanced evasion attacks and mitigations on practical ml-based phishing website classifiers,” arXiv preprint arXiv:2004.06954, 2020.   \n[9] N. Carlini and D. Wagner, “Audio adversarial examples: Targeted attacks on speech-to-text,” in IEEE S&P Workshops, 2018.   \n[10] X. Yuan, Y. Chen, Y. Zhao, Y. Long, X. Liu, K. Chen, S. Zhang, H. Huang, X. Wang, and C. A. Gunter, “Commandersong: A systematic approach for practical adversarial voice recognition,” in USENIX Security, 2018.   \n[11] R. Taori, A. Kamsetty, B. Chu, and N. Vemuri, “Targeted adversarial examples for black box audio systems,” in IEEE S&P Workshops, 2019.   \n[12] S. Khare, R. Aralikatte, and S. Mani, “Adversarial black-box attacks for automatic speech recognition systems using multi-objective genetic optimization,” CoRR, vol. abs/1811.01312, 2018.   \n[13] Y. Gong and C. Poellabauer, “Crafting adversarial examples for speech paralinguistics applications,” in DYNAMICS, 2018.   \n[14] F. Kreuk, Y. Adi, M. Cisse, and J. Keshet, “Fooling end-to-end speaker verification with adversarial examples,” in ICASSP, 2018.   \n[15] T. Liu and S. Guan, “Factor analysis method for text-independent speaker identification,” JSW, 2014.   \n[16] D. A. Reynolds, T. F. Quatieri, and R. B. Dunn, “Speaker verification using adapted gaussian mixture models,” Digit. Signal Process., 2000.   \n[17] J. Fortuna, P. Sivakumaran, A. Ariyaeeinia, and A. Malegaonkar, “Open-set speaker identification using adapted gaussian mixture models,” in INTERSPEECH, 2005.   \n[18] H. Beigi, Fundamentals of Speaker Recognition. Springer, 12 2011.   \n[19] H. Yakura and J. Sakuma, “Robust audio adversarial example for a physical attack,” in IJCAI, 2019.   \n[20] Y. Qin, N. Carlini, G. W. Cottrell, I. J. Goodfellow, and C. Raffel, “Imperceptible, robust, and targeted adversarial examples for automatic speech recognition,” in ICML, 2019.   \n[21] A. Ilyas, L. Engstrom, A. Athalye, and J. Lin, “Black-box adversarial attacks with limited queries and information,” in ICML, 2018.   \n[22] I. J. Goodfellow, J. Shlens, and C. Szegedy, “Explaining and harnessing adversarial examples,” in ICLR, 2015.   \n[23] A. Kurakin, I. J. Goodfellow, and S. Bengio, “Adversarial examples in the physical world,” in ICLR, 2017.   \n[24] N. Carlini and D. Wagner, “Towards evaluating the robustness of neural networks,” in IEEE S&P, 2017.   \n[25] N. Dehak, P. J. Kenny, R. Dehak, P. Dumouchel, and P. Ouellet, “Frontend factor analysis for speaker verification,” IEEE Trans. on Audio, Speech, and Language Processing, 2010.   \n[26] D. Snyder, D. Garcia-Romero, G. Sell, A. McCree, D. Povey, and S. Khudanpur, “Speaker recognition for multi-speaker conversations using x-vectors,” in IEEE ICASSP, 2019.   \n[27] Kaldi. https://github.com/kaldi-asr/kaldi.   \n[28] Talentedsoft. http://www.talentedsoft.com.   \n[29] Microsoft Azure, https://azure.microsoft.com.   \n[30] Amazon Mechanical Turk Platform. https://www.mturk.com.   \n[31] Z. Yang, B. Li, P. Chen, and D. Song, “Characterizing audio adversarial examples using temporal dependency,” in ICLR, 2019.   \n[32] Citi uses voice prints to authenticate customers quickly and effortlessly. https://www.forbes.com/sites/tomgroenfeldt/2016/06/27/citiuses-voice-prints-to-authenticate-customers-quickly-andeffortlessly/#7b01dea1109c.   \n[33] The voice-enabled car of the future. https://tractica.omdia.com/userinterface-technologies/the-voice-enabled-car-of-the-future.   \n[34] N. Carlini, P. Mishra, T. Vaidya, Y. Zhang, M. Sherr, C. Shields, D. Wagner, and W. Zhou, “Hidden voice commands,” in USENIX Security, 2016.   \n[35] Fakebob. https://sites.google.com/view/fakebob.   \n[36] MSR Identity. https://www.microsoft.com/enus/download/details.aspx?id=52279.   \n[37] Amazon Alexa. https://developer.amazon.com/en-US/alexa.   \n[38] Google Home. https://store.google.com/product/google˙home.   \n[39] Speechpro. https://speechpro-usa.com.   \n[40] NIST. National institute of standards and technology speaker recognition evaluation. https://www.nist.gov/itl/iad/mig/speaker-recognition.   \n[41] L. Muda, M. Begam, and I. Elamvazuthi, “Voice recognition algorithms using mel frequency cepstral coefficient (MFCC) and dynamic time warping (dtw) techniques,” Journal of Computing, 2010.   \n[42] N. P. H. Thian, C. Sanderson, and S. Bengio, “Spectral subband centroids as complementary features for speaker authentication,” in ICB, 2004.   \n[43] H. Hermansky, “Perceptual linear predictive (PLP) analysis of speech,” The Journal of the Acoustical Society of America, vol. 87, no. 4, 1990.   \n[44] M. K. Nandwana, L. Ferrer, M. McLaren, D. Castan, and A. Lawson, “Analysis of critical metadata factors for the calibration of speaker recognition systems,” in INTERSPEECH, 2019.   \n[45] P. S. Nidadavolu, V. Iglesias, J. Villalba, and N. Dehak, “Investigation on neural bandwidth extension of telephone speech for improved speaker recognition,” in ICASSP, 2019.   \n[46] K. A. Lee, Q. Wang, and T. Koshinaka, “The CORAL+ algorithm for unsupervised domain adaptation of PLDA,” in ICASSP, 2019.   \n[47] Tencent VPR. https://cloud.tencent.com/product/vpr.   \n[48] Fosafer VPR. http://caijing.chinadaily.com.cn/chanye/2018- 06/06/content˙36337667.htm.   \n[49] G. Heigold, I. Moreno, S. Bengio, and N. Shazeer, “End-to-end textdependent speaker verification,” in ICASSP, 2016, pp. 5115–5119.   \n[50] S. Sremath Tirumala and S. R. Shahamiri, “A review on deep learning approaches in speaker identification,” in ICSPS, 2016.   \n[51] D. A. Reynolds and R. C. Rose, “Robust text-independent speaker identification using gaussian mixture speaker models,” IEEE Trans. Speech and Audio Processing, vol. 3, no. 1, pp. 72–83, 1995.   \n[52] V. Vestman, D. Gowda, M. Sahidullah, P. Alku, and T. Kinnunen, “Speaker recognition from whispered speech: A tutorial survey and an application of time-varying linear prediction,” Speech Commun., 2018.   \n[53] D. Amodei, S. Ananthanarayanan, R. Anubhai, J. Bai, E. Battenberg, C. Case, J. Casper, B. Catanzaro, Q. Cheng, G. Chen et al., “Deep speech 2: End-to-end speech recognition in english and mandarin,” in International conference on machine learning, 2016, pp. 173–182.   \n[54] C. Li, X. Ma, B. Jiang, X. Li, X. Zhang, X. Liu, Y. Cao, A. Kannan, and Z. Zhu, “Deep speaker: an end-to-end neural speaker embedding system,” CoRR, vol. abs/1705.02304, 2017.   \n[55] “Android app which enables unlock of mobile phone via voice print,” http://app.mi.com/details?id=com.jie.lockscreen.   \n[56] “Social software wechat adds voiceprint lock login function,” https://kf.qq.com/touch/wxappfaq/1208117b2mai141125YZjAra.html.   \n[57] VPR of iFLYTEK. https://www.xfyun.cn/services/isv.   \n[58] Sinovoice voice print recognition. http://doc.aicloud.com/sdk5.2.8.   \n[59] Speakin vpr. http://www.speakin.mobi/devPlatform.html.   \n[60] H. Abdullah, M. S. Rahman, W. Garcia, L. Blue, K. Warren, A. S. Yadav, T. Shrimpton, and P. Traynor, “Hear ”no evil”, see ”kenansville”: Efficient and transferable black-box attacks on speech recognition and voice identification systems,” CoRR, vol. abs/1910.05262, 2019.   \n[61] N. Papernot, P. McDaniel, I. Goodfellow, S. Jha, Z. B. Celik, and A. Swami, “Practical black-box attacks against machine learning,” in AsiaCCS, 2017, pp. 506–519.   \n[62] P.-Y. Chen, H. Zhang, Y. Sharma, J. Yi, and C.-J. Hsieh, “Zoo: Zeroth order optimization based black-box attacks to deep neural networks without training substitute models,” in AISec, 2017, pp. 15–26.   \n[63] M. Sharif, S. Bhagavatula, L. Bauer, and M. K. Reiter, “Accessorize to a crime: Real and stealthy attacks on state-of-the-art face recognition,” in ACM CCS, 2016, pp. 1528–1540.   \n[64] M. Alzantot, Y. Sharma, S. Chakraborty, H. Zhang, C. Hsieh, and M. B. Srivastava, “Genattack: practical black-box attacks with gradient-free optimization,” in GECCO, 2019, pp. 1111–1119.   \n[65] L. M. Rios and N. V. Sahinidis, “Derivative-free optimization: a review of algorithms and comparison of software implementations,” Journal of Global Optimization, vol. 56, no. 3, pp. 1247–1293, 2013.   \n[66] Y. Dong, F. Liao, T. Pang, H. Su, J. Zhu, X. Hu, and J. Li, “Boosting adversarial attacks with momentum,” in CVPR, 2018, pp. 9185–9193.   \n[67] A. Madry, A. Makelov, L. Schmidt, D. Tsipras, and A. Vladu, “Towards deep learning models resistant to adversarial attacks,” in ICLR, 2018.   \n[68] Y. Duan, Z. Zhao, L. Bu, and F. Song, “Things you may not know about adversarial example: A black-box adversarial image attack,” CoRR, vol. abs/1905.07672, 2019.   \n[69] A. Nagrani, J. S. Chung, and A. Zisserman, “Voxceleb: a large-scale speaker identification dataset,” in INTERSPEECH, 2017.   \n[70] J. S. Chung, A. Nagrani, and A. Zisserman, “Voxceleb2: Deep speaker recognition,” in INTERSPEECH, 2018.   \n[71] V. Panayotov, G. Chen, D. Povey, and S. Khudanpur, “Librispeech: An   \n[72] H. Bu, J. Du, X. Na, B. Wu, and H. Zheng, “Aishell-1: An open- asr corpus based on public domain audio books,” in ICASSP, 2015. source mandarin speech corpus and a speech recognition baseline,” in 2017 20th Conference of the Oriental Chapter of the International Coordinating Committee on Speech Databases and Speech I/O Systems and Assessment (O-COCOSDA), Nov 2017, pp. 1–5.   \n[73] JBL clip3 portable speaker. https://www.jbl.com/bluetoothspeakers/JBL+CLIP+3.html.   \n[74] Shinco broadcast equipment. https://item.jd.com/5009202.html.   \n[75] J. F. Gemmeke, D. P. W. Ellis, D. Freedman, A. Jansen, W. Lawrence, R. C. Moore, M. Plakal, and M. Ritter, “Audio set: An ontology and human-labeled dataset for audio events,” in ICASSP, 2017.   \n[76] Amazon mechanical turk acceptable use policy. https://www.mturk.com/acceptable-use-policy.   \n[77] S. Shiota, F. Villavicencio, J. Yamagishi, N. Ono, I. Echizen, and T. Matsui, “Voice liveness detection algorithms based on pop noise caused by human breath for automatic speaker verification,” in INTERSPEECH, 2015.   \n[78] L. Zhang, S. Tan, J. Yang, and Y. Chen, “VoiceLive: A phoneme localization based liveness detection for voice authentication on smartphones,” in ACM CCS, 2016.   \n[79] L. Zhang, S. Tan, and J. Yang, “Hearing your voice is not enough: An articulatory gesture based liveness detection for voice authentication,” in ACM CCS, 2017.   \n[80] Z. Gong, W. Wang, and W.-S. Ku, “Adversarial and clean data are not twins,” arXiv preprint arXiv:1704.04960, 2017.   \n[81] N. Carlini and D. Wagner, “Adversarial examples are not easily detected: Bypassing ten detection methods,” in AISec, 2017.   \n[82] C. Guo, M. Rana, M. Cisse, and L. Van Der Maaten, “Countering adversarial images using input transformations,” arXiv preprint arXiv:1711.00117, 2017.   \n[83] A. Athalye, N. Carlini, and D. Wagner, “Obfuscated gradients give a false sense of security: Circumventing defenses to adversarial examples,” arXiv preprint arXiv:1802.00420, 2018.   \n[84] X. Du, X. Xie, Y. Li, L. Ma, Y. Liu, and J. Zhao, “Deepstellar: Model-based quantitative analysis of stateful deep learning systems,” in ESEC/FSE, 2019.   \n[85] X. Zhang, X. Xie, L. Ma, X. Du, Q. Hu, Y. Liu, J. Zhao, and M. Sun, “Towards characterizing adversarial defects of deep learning software from the lens of uncertainty,” in ICSE, 2020.   \n[86] Y. Liu, L. Ma, and J. Zhao, “Secure deep learning engineering: A road   \n[87] tWo.w aBrrdesn qduela,l itJ.y  aRsasuubraern,c ea nodf  iMnt.e llBiegtehngt es, y“stDeemcsi,s”i oinn- bIaCsFedE Ma,d v2e0r1s9a.rial attacks: Reliable attacks against black-box machine learning models,” arXiv preprint arXiv:1712.04248, 2017. [88] L. Schonherr, K. Kohls, S. Zeiler, T. Holz, and D. Kolossa, “Adversarial attacks against automatic speech recognition systems via psychoacoustic hiding,” in NDSS, 2019.   \n[89] M. Alzantot, B. Balaji, and M. B. Srivastava, “Did you hear that? adversarial examples against automatic speech recognition,” CoRR, vol. abs/1801.00554, 2018.   \n[90] A. Y. Hannun, C. Case, J. Casper, B. Catanzaro, G. Diamos, E. Elsen, R. Prenger, S. Satheesh, S. Sengupta, A. Coates, and A. Y. Ng, “Deep speech: Scaling up end-to-end speech recognition,” CoRR, vol. abs/1412.5567, 2014. [91] H. Abdullah, W. Garcia, C. Peeters, P. Traynor, K. R. B. Butler, and J. Wilson, “Practical hidden voice attacks against speech and speaker recognition systems,” in NDSS, 2019.   \n[92] G. Zhang, C. Yan, X. Ji, T. Zhang, T. Zhang, and W. Xu, “Dolphinattack: Inaudible voice commands,” in ACM CCS, 2017.   \n[93] Z. Wu, N. Evans, T. Kinnunen, J. Yamagishi, F. Alegre, and H. Li, “Spoofing and countermeasures for speaker verification: A survey,” Speech Commun., 2015.   \n[94] R. G. Hautamaki, T. Kinnunen, V. Hautamaki, T. Leino, and A.- M. Laukkanen, “I-vectors meet imitators: on vulnerability of speaker verification systems against voice mimicry.” in INTERSPEECH, 2013.   \n[95] Z. Wu, S. Gao, E. S. Cling, and H. Li, “A study on replay attack and anti-spoofing for text-dependent speaker verification,” in APSIPA, 2014.   \n[96] M. Shirvanian, S. Vo, and N. Saxena, “Quantifying the breakability of voice assistants,” in PerCom, 2019.   \n[97] M. Shirvanian and N. Saxena, “Wiretapping via mimicry: Short voice imitation mitm attacks on crypto phones,” in ACM CCS, 2014.   \n[98] P. L. De Leon, M. Pucher, J. Yamagishi, I. Hernaez, and I. Saratxaga, “Evaluation of speaker verification security and detection of hmmbased synthetic speech,” IEEE/ACM Trans. Audio, Speech & Language Processing, 2012.   \n[99] Z. Wu and H. Li, “Voice conversion and spoofing attack on speaker verification systems,” in APSIPA, 2013.   \n[100] D. Mukhopadhyay, M. Shirvanian, and N. Saxena, “All your voices are belong to us: Stealing voices to fool humans and machines,” in ESORICS, 2015.   \n[101] M. Shirvanian, N. Saxena, and D. Mukhopadhyay, “Short voice imitation man-in-the-middle attacks on crypto phones: Defeating humans and machines,” Journal of Computer Security, 2018.   \n[102] S. Chen, M. Xue, L. Fan, S. Hao, L. Xu, H. Zhu, and B. Li, “Automated poisoning attacks and defenses in malware detection systems: An adversarial machine learning approach,” Computers & Security, 2018.   \n[103] R. Eberhart and J. Kennedy, “A new optimizer using particle swarm theory,” in MHS, 1995.   \n[104] J. Sohn, N. Kim, and W. Sung, “A statistical model-based voice activity detection,” IEEE Signal Processing Letters, 1999.   \n[105] A TensorFlow implementation of Baidu’s DeepSpeech architecture. https://github.com/mozilla/DeepSpeech.  \n\n# APPENDIX  \n\n# A. Comparison of our FAKEBOB and PSO-based Method  \n\nW compare our attack FAKEBOB over a PSO-based method. We reduce the finding of an adversarial sample as an optimization problem (cf. $\\mathrm{\\SIV{-}A}$ ), then solve the optimization problem via the PSO algorithm. PSO solves the optimization problem by imitating the behaviour of a swarm of birds [103]. Each particle is a candidate solution, and in each iteration, the particle updates itself by the weighted linear combination of three parts, i.e., inertia, local best solution and global best solution. The related weights are initial inertia factor $w_{i n i t}$ , final inertia factor $w_{e n d}$ , acceleration constant $c_{1}$ and $c_{2}$ .  \n\nWe implement a PSO-based attack following the algorithm of Sharif et al. [63] which is used to fool face recognition systems. After fine-tuning the above hyper-parameters, we conduct the experiment using the PSO-based method with 50 particles for a maximum of 35 epochs, and we set the iteration  \n\nTABLE XI: Our attack FAKEBOB vs. the PSO-based method, where $[S(x_{0})]_{t}$ denotes the initial score of input voice of the speaker $t$ , and $^*$ denotes that only one adversarial attack succeeds.  \n\n<html><body><table><tr><td rowspan=\"2\"></td><td colspan=\"2\">-∞∞<[S(o)]t<∞∞</td><td colspan=\"2\">[S(co)]t≤-0.5</td><td></td><td>-0.5<[S(co)]t≤0</td><td>0<[S(co)]t≤0.5</td><td></td><td>0.5<[S(co)]t≤1</td><td></td><td>1<[S(co)]t≤1.5</td><td></td></tr><tr><td>FAKEBOB</td><td>PSO</td><td>FAKEBOB</td><td>PSO</td><td>FAKEBOB</td><td>PSO*</td><td>FAKEBOB</td><td>PSO</td><td>FAKEBOB</td><td>PSO</td><td>FAKEBOB</td><td>PSO</td></tr><tr><td>#Iteration</td><td>86</td><td>136</td><td>187</td><td>二</td><td>84</td><td>72</td><td>61</td><td>147</td><td>17</td><td>297</td><td>4</td><td>24</td></tr><tr><td>Time (s)</td><td>2277</td><td>2524</td><td>4409</td><td>二</td><td>1947</td><td>1311</td><td>1384</td><td>2715</td><td>357</td><td>5517</td><td>77</td><td>449</td></tr><tr><td>SNR (dB)</td><td>31.5</td><td>31.9</td><td>31.4</td><td>二</td><td>30.5</td><td>22.8</td><td>31.5</td><td>31.6</td><td>32.4</td><td>32.3</td><td>31.8</td><td>32.2</td></tr><tr><td>ASR(%)</td><td>99.0</td><td>33.0</td><td>96.3</td><td>0.0</td><td>100.0</td><td>5.3</td><td>100.0</td><td>17.6</td><td>100.0</td><td>60.0</td><td>94.1</td><td>100.0</td></tr></table></body></html>  \n\nTABLE XII: Experimental results of FAKEBOB on xvector system   \n\n\n<html><body><table><tr><td rowspan=\"3\">Task</td><td colspan=\"8\">All</td><td colspan=\"4\">Intra-genderattack</td><td colspan=\"4\">Inter-gender attack</td></tr><tr><td colspan=\"4\">TargetedAttack</td><td></td><td colspan=\"3\">UntargetedAttack</td><td colspan=\"4\">TargetedAttack</td><td colspan=\"4\">Targeted Attack</td></tr><tr><td>#Iter</td><td>Time</td><td>SNR</td><td>ASR</td><td></td><td></td><td>Time SNR</td><td>ASR</td><td>#Iter</td><td>Time</td><td>SNR</td><td>ASR</td><td>#Iter</td><td>Time</td><td>SNR</td><td>ASR</td></tr><tr><td></td><td></td><td>(s)</td><td>(dB)</td><td>(%)</td><td>#Iter</td><td>(s)</td><td>(dB)</td><td>(%)</td><td></td><td>(s)</td><td>(dB)</td><td>(%)</td><td></td><td>(s)</td><td>(dB)</td><td>(%)</td></tr><tr><td>CSI</td><td>117</td><td>575</td><td>30.1</td><td>100.0</td><td>73</td><td>499</td><td>29.6</td><td>100.0</td><td>89</td><td>444</td><td>29.3</td><td>100</td><td>135</td><td>662</td><td>30.7</td><td>100.0</td></tr><tr><td>SV</td><td>92</td><td>702</td><td>31.8</td><td>100.0</td><td>二</td><td>二</td><td></td><td></td><td>44</td><td>340</td><td>31.9</td><td>100.0</td><td>136</td><td>1035</td><td>31.7</td><td>100.0</td></tr><tr><td>OSI</td><td>95</td><td>995</td><td>32.0</td><td>100.0</td><td>26</td><td>171</td><td>31.5</td><td>100.0</td><td>51</td><td>601</td><td>32.0</td><td>100.0</td><td>138</td><td>1380</td><td>32.0</td><td>100.0</td></tr></table></body></html>  \n\nlimitation of each epoch to 30, $w_{i n i t}$ to 0.9, $w_{e n d}$ to 0.1, $c_{1}$ to 1.4961 and $c_{2}$ to 1.4961. The experiment is conducted on the ivector system for the OSI task.  \n\nThe results are shown in Table XI. For comparison purposes, we also report the results of our attack FAKEBOB in Table XI. Overall, the PSO-based method achieves $33\\%$ targeted attack success rate (ASR), only one-third of FAKEBOB, indicating that FAKEBOB is much more effective than the PSO-based method. Specifically, the PSO-based method is less effective for input voices whose initial scores are low.  \n\n• When $[S(x_{0})]_{t}~\\leq~-0.5$ , the PSO-based method fails to launch attack for all the voices. • When $-0.5<[S(x_{0})]_{t}\\leq0$ and $0<[S(x_{0})]_{t}\\leq0.5$ , the ASR is very low, i.e., $5.3\\%$ and $17.6\\%$ , respectively.  \n\nWhereas our attack FAKEBOB is more effective no matter the initial scores of input voices.  \n\nIn terms of efficiency, FAKEBOB takes less number of iterations and execution time than the PSO-based method, except for the case $-0.5<[S(x_{0})]_{t}\\leq0$ on which the PSO-based method is able to launch a successful attack for one voice only. Specifically, the higher the initial score of the input voice is, the more efficient of our attack FAKEBOB is compared to the PSO-based method. For instance, when $0.5<[S(x_{0})]_{t}\\leq1$ , the number of iterations (resp. execution time) of the PSO-based method is 17 times (resp. 15 times) larger than the one of FAKEBOB.  \n\nIn summary, the experimental results demonstrate that our attack FAKEBOB is much more effective and efficient than the PSO-based method.  \n\n# B. 16 Attack Scenarios  \n\nAll of following combinations are evaluated in this work, where D.&S. denotes decision and scores.  \n\n$$\n\\left\\{\\begin{array}{l l}{\\begin{array}{r l}{\\left(\\begin{array}{c}{\\mathrm{untrageted}}\\ {\\mathrm{untargeted}}\\end{array}\\right)\\times\\left(\\begin{array}{c}{\\mathrm{intra-gender}}\\ {\\mathrm{inter-gender}}\\end{array}\\right)\\times\\mathrm{API}\\times\\left(\\begin{array}{c}{\\mathrm{OSI}}\\ {\\mathrm{CSI}}\\end{array}\\right)\\times\\mathrm{D}\\mathcal{B}\\mathcal{S}.}\\ {\\begin{array}{r l}{+}&{{}}\\ {\\mathrm{targeted}\\times\\left(\\begin{array}{c}{\\mathrm{OSI}}\\ {\\mathrm{SV}}\\end{array}\\right)\\times\\mathrm{API}\\times\\mathrm{decision-only}}\\ {\\qquad\\mathrm{targeted}\\times\\left(\\begin{array}{c}{\\mathrm{OSI}}\\ {\\mathrm{CSI}}\\end{array}\\right)}\\end{array}}\\ {\\begin{array}{r l}{\\mathrm{targeted}\\times\\left(\\begin{array}{c}{\\mathrm{OSI}}\\ {\\mathrm{CSI}}\\end{array}\\right)\\times\\mathrm{over-the-air}\\times\\mathrm{D}\\mathcal{A}\\mathcal{S}.}\\ {\\qquad\\mathrm{targeted}\\times\\mathrm{OSI}\\times\\mathrm{aver-the-air}\\times\\mathrm{decision-only}}\\end{array}}\\end{array}\\right.\n$$  \n\n# C. Results of Tuning the Parameter ϵ  \n\nTable XIII shows the results of tuning the parameter $\\epsilon$ on both ivector and GMM systems for the CSI task. To choose a suitable $\\epsilon$ , we need to trade off the imperceptibility and the attack cost. Smaller $\\epsilon$ contributes to less perturbation (i.e, higher SNR), but also give rise to the attack cost (i.e, more iterations and execution time and lower success rate). We found that 0.002 is a more suitable value of $\\epsilon$ for two reasons: (1) compared with other $\\epsilon$ values, the average SNR of adversarial voices when $\\epsilon=0.002$ is higher, indicating that $\\epsilon=0.002$ introduces less perturbation, while the success rate of 0.002 is merely $1\\%$ lower than that of other $\\epsilon$ values. (2) $\\epsilon=0.001$ introduce less perturbation than $\\epsilon=0.002$ , but the success rate of $\\epsilon=0.001$ drops to $41\\%$ for ivector and $87\\%$ for GMM, $58\\%$ and $12\\%$ lower than that of $\\epsilon=0.002$ . Moreover, the attack cost increases more sharply when decreasing $\\epsilon$ from 0.002 to 0.001 compared with decreasing $\\epsilon$ from 0.003 to 0.002. That is, the number of iterations and execution time of $\\epsilon=0.002$ are 1.6 times and 1.4 times than that of $\\epsilon=0.003$ , while the number of iterations and execution time of $\\epsilon=0.001$ are 2.2 times and 2.4 times than that of $\\epsilon=0.002$ .  \n\nTABLE XIII: Results of tuning $\\epsilon$ on the CSI task   \n\n\n<html><body><table><tr><td rowspan=\"2\">E</td><td colspan=\"4\">ivector</td><td colspan=\"4\">GMM</td></tr><tr><td>#Iter</td><td>Time (s)</td><td>SNR (dB)</td><td>ASR (%)</td><td>#Iter</td><td>Time (s)</td><td>SNR (dB)</td><td>ASR (%)</td></tr><tr><td>0.05</td><td>18</td><td>422</td><td>12.0</td><td>100</td><td>18</td><td>91</td><td>16.7</td><td>100</td></tr><tr><td>0.01</td><td>23</td><td>549</td><td>16.2</td><td>100</td><td>16</td><td>81</td><td>19.1</td><td>100</td></tr><tr><td>0.005</td><td>44</td><td>1099</td><td>21.8</td><td>100</td><td>19</td><td>102</td><td>22.3</td><td>100</td></tr><tr><td>0.004</td><td>56</td><td>1423</td><td>23.8</td><td>100</td><td>21</td><td>104</td><td>24.0</td><td>100</td></tr><tr><td>0.003</td><td>76</td><td>2059</td><td>26.3</td><td>100</td><td>27</td><td>124</td><td>26.1</td><td>100</td></tr><tr><td>0.002</td><td>124</td><td>2845</td><td>30.2</td><td>99</td><td>40</td><td>218</td><td>29.3</td><td>99</td></tr><tr><td>0.001</td><td>276</td><td>6738</td><td>36.4</td><td>41</td><td>106</td><td>551</td><td>35.7</td><td>87</td></tr></table></body></html>  \n\n# D. Experiment results of FAKEBOB on xvector system  \n\nWe demonstrate the effectiveness and efficiency of FAKEBOB against a state-of-the-art DNN-based SRS [26], called xvector system, in which xvector is extracted from DNN  \n\nTABLE XIV: Details of source and target systems for transferability attacks, where DF denotes Dimension of feature, FL/FS denotes Frame length/Frame step, $\\sharp\\mathbf{GC}$ denotes the number of gaussian components, DV denotes Dimension of ivector (xvector), and xvector is a DNN-based SRS from [26].  \n\nTABLE XV: The performance of the target systems C,...,J   \n\n\n<html><body><table><tr><td>SystemID</td><td>A</td><td>B</td><td>C</td><td>D</td><td>E</td><td>F</td><td>G</td><td>H</td><td></td><td>J</td></tr><tr><td>Architecture</td><td>GMM</td><td>ivector</td><td>ivector</td><td>ivector</td><td>ivector</td><td>ivector</td><td>ivector</td><td>ivector</td><td>ivector</td><td>xvector</td></tr><tr><td>Training set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-2Set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-1Set</td><td>Train-1Set</td></tr><tr><td>Feature</td><td>MFCC</td><td>MFCC</td><td>MFCC</td><td>PLP</td><td>MFCC</td><td>MFCC</td><td>MFCC</td><td>MFCC</td><td>PLP</td><td>MFCC</td></tr><tr><td>DF</td><td>24×3</td><td>24×3</td><td>24×3</td><td>24×3</td><td>13x3</td><td>24×3</td><td>24×3</td><td>24×3</td><td>13×3</td><td>30</td></tr><tr><td>FL/FS (ms)</td><td>25/10</td><td>25/10</td><td>25/10</td><td>25/10</td><td>25/10</td><td>50/10</td><td>25/10</td><td>25/10</td><td>50/10</td><td>25/10</td></tr><tr><td>#GC</td><td>2048</td><td>2048</td><td>2048</td><td>2048</td><td>2048</td><td>2048</td><td>1024</td><td>2048</td><td>1024</td><td>一</td></tr><tr><td>DV</td><td>一</td><td>400</td><td>400</td><td>400</td><td>400</td><td>400</td><td>400</td><td>600</td><td>600</td><td>512</td></tr></table></body></html>  \n\nTABLE XVI: Results of transferability attack for CSI task $(\\%)$ , where S denotes source system and T denotes target system.   \n\n\n<html><body><table><tr><td colspan=\"2\">System</td><td rowspan=\"2\">C</td><td rowspan=\"2\">D</td><td rowspan=\"2\"></td><td rowspan=\"2\">F</td><td rowspan=\"2\">G</td><td rowspan=\"2\">H</td><td rowspan=\"2\"></td><td rowspan=\"2\">J</td></tr><tr><td>Task</td><td></td></tr><tr><td rowspan=\"2\">CSI</td><td>Accuracy</td><td>99.8%</td><td>99.4%</td><td>99.2%</td><td>99.8%</td><td>99.6%</td><td>99.8%</td><td>99.2%</td><td>99.2%</td></tr><tr><td>FAR</td><td>10.0%</td><td>9.8%</td><td>9.4%</td><td>10.0%</td><td>11.2%</td><td>9.8%</td><td>10.4%</td><td>10.2%</td></tr><tr><td rowspan=\"2\">SV</td><td>FRR</td><td>1.2%</td><td>0.6%</td><td>1.6%</td><td>1.2%</td><td>0.8%</td><td>1.0%</td><td>2.2%</td><td>0.8%</td></tr><tr><td>FAR</td><td>9.1%</td><td>8.8%</td><td>10.9%</td><td>9.2%</td><td>8.5%</td><td>8.1%</td><td>11.0%</td><td>7.7%</td></tr><tr><td rowspan=\"2\">OSI</td><td>FRR</td><td>1.4%</td><td>0.6%</td><td>1.6%</td><td>1.4%</td><td>1.2%</td><td>0.8%</td><td>2.2%</td><td>0.8%</td></tr><tr><td>OSIER</td><td>0.0%</td><td>0.2%</td><td>0.2%</td><td>0.0%</td><td>0.2%</td><td>0.0%</td><td>0.4%</td><td>0.2%</td></tr></table></body></html>  \n\nTABLE XVIII: Results of transferability attack for SV task $(\\%)$ , where S: source system and T: target system.   \n\n\n<html><body><table><tr><td></td><td>T</td><td>A</td><td></td><td>B</td><td></td><td>C</td><td></td><td>D</td><td></td><td>E</td><td></td><td>F</td><td></td><td>G</td><td></td><td>H</td><td></td><td></td><td></td><td></td><td>1</td></tr><tr><td>S</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td></td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ATR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td><td>ASR</td><td>UTR</td></tr><tr><td>A</td><td>一</td><td>一</td><td>76.9</td><td>76.9</td><td></td><td>89.7</td><td>89.7</td><td>64.1</td><td>71.8</td><td>87.2</td><td>89.7</td><td>84.6</td><td>84.6</td><td>76.9</td><td>87.2</td><td>76.9</td><td>84.6</td><td>48.7</td><td>69.2</td><td>28.2</td><td>38.5</td></tr><tr><td></td><td>30.7</td><td>88.0</td><td>一</td><td>一</td><td></td><td>93.3</td><td>96.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>88.0</td><td>89.3</td><td>100.0</td><td>100.0</td><td>73.3</td><td>80.0</td><td>25.3</td><td>38.7</td></tr></table></body></html>  \n\n<html><body><table><tr><td>T</td><td>A</td><td>B</td><td>C</td><td>D</td><td>E</td><td>F</td><td>G</td><td>H</td><td></td><td>J</td></tr><tr><td>S</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td><td>ASR</td></tr><tr><td>A</td><td>一</td><td>57.9</td><td>49.1</td><td>54.4</td><td>64.9</td><td>61.4</td><td>52.6</td><td>66.7</td><td>36.8</td><td>33.3</td></tr><tr><td>B</td><td>5.0</td><td></td><td>67.5</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>80.0</td><td>38.3</td></tr></table></body></html>  \n\nTABLE XVII: Results of FAKEBOB when $\\theta$ is tuned based on Equal Error Rate. The Equal Error Rate and corresponding threshold $\\theta$ for ivector (resp. GMM) are $2.2\\%$ and 1.75 (resp. $5.8\\%$ and 0.103), and $\\epsilon=0.002$ .  \n\n<html><body><table><tr><td rowspan=\"2\">Task</td><td colspan=\"4\">ivector</td><td colspan=\"4\">GMM</td></tr><tr><td>#Iter</td><td>Time (s)</td><td>SNR (dB)</td><td>ASR (%)</td><td>#Iter</td><td>Time (s)</td><td>SNR (dB)</td><td>ASR (%)</td></tr><tr><td>SV</td><td>120</td><td>2297</td><td>31.7</td><td>99.0</td><td>46</td><td>273</td><td>31.4</td><td>99.0</td></tr><tr><td>OSI</td><td>125</td><td>2786</td><td>32.1</td><td>99.0</td><td>54</td><td>334</td><td>31.9</td><td>99.0</td></tr></table></body></html>  \n\nnetworks. We use the pre-trained xvector model from SITW recipe of Kaldi and construct OSI, CSI and SV systems. We use the same settings as in Section V-B. The baseline performance of the resulting systems is shown in Column J of Table XV. Moreover, we also conduct untargeted attacks against these systems. The results are shown in Table XII. Our attack is able to achieve $100\\%$ ASR, indicating FAKEBOB is also effective and efficient against DNN-based SRSs.  \n\nE. Robustness of FAKEBOB against Defense Methods  \n\nLocal smoothing. It mitigates attacks by applying the mean, median or gaussian filter to the waveform of a voice. Based on the results in [31], we use the median filter. A median filter with kernel size $k$ (must be odd) replaces each audio element $x_{k}$ by the median of $k$ values $[x_{k-\\frac{k-1}{2}},...,x_{k},...,$ , $x_{k+\\frac{k-1}{2}}]$ . In S1, we vary $k$ from 1 to 19 with step 2. The results are shown in Fig. 8a. We can see that the defense is ineffective against high-confidence (hc) adversarial voices.  \n\nFor low-confidence (hc) adversarial voices, though the UTR drops from $99\\%$ to nearly $0\\%$ , the minimal FRR of normal voices increases to $35\\%$ , significantly larger than the baseline $4.2\\%$ . We also tested median with $k=3$ on ivector. The FRR of normal voices only increases by $7\\%$ . It seems that ivector is more robust than GMM. In S2, we fix $k{=}7$ as [31] did. The results are shown in Fig. 9a. Although the median filter increases the attack cost slightly, FAKEBOB can quickly achieve $90\\%$ ASR using $250\\mathrm{max}$ iteration bound, where the baseline is 90. To solve other few voices $(9\\%)$ , the max iteration bound should be 15,000. Though ivector is more robust than GMM, the similar result is observed (cf. Fig. 9b). We conclude that the local smoothing (at least median filter) can increase attack cost, but is ineffective in terms of ASR.  \n\n![](/tmp/output/18_20250326021609/images/d160f8a6889da21699170584bc8f77689b5f8f71baaa309f2ab152a33b7b1b50.jpg)  \nFig. 8: Results of median filter and audio squeezing in S1, where UTR-lc denotes UTR of low-confidence adversarial voices $(\\kappa{=}0)$ , and UTR-hc denotes UTR of high-confidence adversarial voices $0<\\kappa<5$ ).  \n\nAudio squeezing. It down-samples voices and applies signal recovery to disrupt perturbations. In S1, we vary $\\tau$ (the ratio between new and original sampling frequency) from 0.1 to  \n\nTABLE XIX: Settings of the over-the-air attacks, where $x$ meter $(y d B)$ means when the microphone is kept x meters away from the loudspeaker, the average volume of voices reaches y dB, and white noise $(z d B)$ means the acoustic environment is degraded with a white-noise generator playing at z dB.   \n\n\n<html><body><table><tr><td></td><td>System</td><td>Loudspeaker</td><td>Microphone</td><td>Distance</td><td>AcousticEnvironment</td></tr><tr><td>Different Systems</td><td>GMMOSI/CSI/SV ivectorOSI/CSI/SV Azure OSI</td><td>JBL clip3 portable speaker</td><td>IPhone 6 Plus (iOS)</td><td>1 meter (65 dB)</td><td>relatively quiet</td></tr><tr><td>Different Devices</td><td>ivector OSI</td><td>DELLlaptop JBLclip3portablespeaker Shincobrocastequipment</td><td>IPhone 6 Plus (iOS) OPPO (Android)</td><td>1 meter (65 dB) 0.25meter (70 dB)</td><td>relatively quiet</td></tr><tr><td>Different Distances</td><td>ivector OSI</td><td>JBL clip3 portable speaker</td><td>IPhone 6 Plus (iOS)</td><td>0.5 meter (68 dB) 1 meter (65 dB) 2 meters (62 dB) 4 meters (60 dB) 8 meters (55 dB)</td><td>relatively quiet</td></tr><tr><td>Different Acoustic Environments</td><td>ivector OSI</td><td>JBL clip3 portable speaker</td><td>IPhone 6Plus (iOS)</td><td>1 meter (65 dB)</td><td>white n0ise (45/50/60/65/75dB) bus noise (60 dB) restaurant noise (60 dB) music noise (60 dB) absolute musicnoise (60 dB)</td></tr></table></body></html>  \n\n![](/tmp/output/18_20250326021609/images/40bc92943af2313933f546d9ae23ffa63dea7212070d47fdfa14e858327a1bad.jpg)  \nFig. 9: Attack cost of median filter and audio squzzeing  \n\n1.0, the same as [10]. The results are shown in Fig. 8b. We can observe that when $\\tau~=~0.9$ , (1) the FRR of normal voices is $6\\%$ , close to the baseline $4.2\\%$ , (2) the UTR of the low-confidence adversarial voices is $17\\%$ , smaller than the baseline $99\\%$ , (3) however, the UTR of the high-confidence adversarial voices is the same as the baseline. In S2, we fix ${\\tau=}0.5$ as [31] did. The results are shown in Fig. 9a and Fig. 9b. Unexpectedly, the defense decreases the overhead of attack and increases ASR. For instance, FAKEBOB achieves $100\\%$ ASR using $200\\mathrm{max}$ iteration bound on the system with defense, while can only achieve $99\\%$ ASR even using $16,000\\mathrm{max}$ iteration bound on the unsecured system. It is possibly because audio squeezing $\\mathrm{\\Delta}\\tau=0.5\\$ ) sacrifices the performance of SRSs.  \n\nWe conclude that the audio squeezing is ineffective against FAKEBOB in terms of both attack cost and ASR.  \n\nQuantization. It rounds the amplitude of each sample point of a voice to the nearest integer multiple of factor $q$ to mitigate the perturbation. In S1, we vary $q$ from 128, 256, 512 to 1024 as [31] did. However, the system did not output any result on adversarial and normal voices. An in-depth analysis reveals that all the frames of voices are regarded as unvoiced frame by the Voice Activity Detection (VAD) [104] component. This demonstrates that quantization is not suitable for defending against FAKEBOB. Due to this, we do not consider S2.  \n\nTemporal dependency Detection. For a given voice $v$ , suppose a speech-to-text system produces text $t(v)$ . Given a parameter $0\\leq k\\leq1$ , let $v_{k}$ (resp. $t_{k}$ ) denote the $k$ percent prefix of the voice $v$ (resp. text $t$ ). The temporal dependency detection uses the distance between the texts $t(v)_{k}$ and $t(v_{k})$ to determine whether $v$ is an adversarial voice, as the distance of adversarial voices is greater than that of normal voices. We use this method to check adversarial voices crafted by FAKEBOB using $k{=}\\frac{4}{5}$ and the Character Error Rate distance metric, the best one in [31]. We do not test different values of $k$ as the result will not vary too much as mentioned in [31]. We use Baidu’s DeepSpeech model as the speech-to-text system, which is implemented by Mozilla on Github [105] with more than $13\\mathrm{k\\Omega}$ stars.  \n\n![](/tmp/output/18_20250326021609/images/5897db2b1223584bdd97c1075052e58889042a67867a1a04110500bde76121d5.jpg)  \nFig. 10: ROC curves of Temporal Dependence Detection  \n\nFig. 10 shows the ROC curves of this method distinguishing low-confidence and high-confidence adversarial samples. It obtains $50\\%$ true positive rate at about $50\\%$ false positive rate. The AUC values are $46.7\\%$ and $50.5\\%$ , close to random guess, indicating it fails to detect adversarial samples. This is because FAKEBOB does not alter the transcription of the voices, thus the temporal dependency is preserved. Due to this, we do not consider S2.  "}