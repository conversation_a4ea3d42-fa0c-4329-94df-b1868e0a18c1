{"text": "# The Hardware Environment  \n\nR.M. <PERSON>ham  \n\nSecurity protocols are as they are in part because of the hardware environment in which they are expected to function.  Expectations were set nearly twenty years ago, when communications were very unreliable and slow, when reliable sources of time were most unusual, when encryption was extremely slow, when memories and disks were small and slow.  In consequence protocol designers went to great lengths to minimise the number of messages sent and their size, particularly the size of the encrypted part.  It was considered very undesirable to rely on time other than very locally for ordinal purposes, and systems were expected as far as possible to be stateless.  \n\nLife is different today. It is reasonable to encrypt more material and to maintain registers of past activity to protect against replays.  It is reasonable to exchange more messages.  It is possible to avoid being clever, and this is always a good thing to do.  The notorious prevalence of errors in security protocols derived to a considerable extent from the designers being clever and not being as explicit as one would wish in their messages, relying instead on complicated inference.  Simplicity is the sign of truth, and simplicity is not to be equated with smallness and compactness.  This will be a familiar point to anyone old enough to have been an APL programmer.  \n\nWe have not yet seen all the consequences of a relative lack of resource constraints.  For example I am unaware of any practical application of encryption by hashing and effective decryption by table lookup and indirection, which is attractive when there is a bounded population of messages.  The attraction comes from not having a key and in consequence not being able to lose it.  \n\nThe foregoing points are with us today, but I include them in a piece about the future because we have not yet seen all the consequences of changes that have already happened.  What of changes to come?  <PERSON>wain famously said that it was always very difficult to predict the future, especially before it had happened. Nevertheless one who agrees to speak in a panel like this one has to be prepared to have a go.  \n\nMany of our attitudes today derive from computers with many uses and many users.  Going all the way back to early time-sharing systems we systems people regarded the users, and any code they wrote, as the mortal enemies of us and of each other.  We were like the police force in a violent slum.  Two distinct developments are changing all this. One is that computers are becoming more and more single-purpose single-user objects.  PCs on desks are single-user, of course, but it goes much further than that.  Servers are becoming single service and, most importantly, computers are portable, mobile, ubiquitous, and single-purpose.  \n\nA single-purpose machine doesn’t have internal security because there isn’t anything for such security to do.  If a portable machine has more than one purpose, as for example it’s both my watch and my calendar, it at any rate has a fixed budget of program in it, in all probability with a common source.  A single-service server is not something the operating system needs to be protected from, and it is not completely obvious that the system needs to help with segregating users from each other.  \n\nThe other development is the rise and rise of communications based distribution.  Networks are conductors of information but insulators of behaviour. Many years ago when making a distributed operating system out of single-purpose machines we realised that the insulating qualities of the network allowed us to let first-year students play with quite intimate system components. Securing integrity and identity of communication will become almost the only measures needed.  \n\nIn consequence the research community will concentrate more on higher level issues of access management, verification, and audit.  "}