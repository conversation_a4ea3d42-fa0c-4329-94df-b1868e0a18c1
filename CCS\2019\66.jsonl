{"text": "# DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU  \n\n<PERSON><PERSON> Cheng <NAME_EMAIL>  \n\nXiaoyu Ji∗   \nZhejiang University   \n<EMAIL>  \n\n<PERSON><PERSON><PERSON> Zhang <PERSON> University <EMAIL>  \n\n<PERSON><PERSON> Xu <NAME_EMAIL>  \n\n<PERSON>-Chao Chen University of Texas <NAME_EMAIL>  \n\n# ABSTRACT  \n\nWith the widespread use of smart devices, device authentication has received much attention. One popular method for device authentication is to utilize internally-measured device fingerprints, such as device ID, software or hardware-based characteristics. In this paper, we propose DeMiCPU, a stimulation-response-based device fingerprinting technique that relies on externally-measured information, i.e., magnetic induction (MI) signals emitted from the CPU module that consists of the CPU chip and its affiliated power supply circuits. The key insight of DeMiCPU is that hardware discrepancies essentially exist among CPU modules and thus the corresponding MI signals make promising device fingerprints, which are difficult to be modified or mimicked. We design a stimulation and a discrepancy extraction scheme and evaluate them with 90 mobile devices, including 70 laptops (among which 30 are of totally identical CPU and operating system) and 20 smartphones. The results show that DeMiCPU can achieve $99.1\\%$ precision and recall on average, and $98.6\\%$ precision and recall for the 30 identical devices, with a fingerprinting time of $0.6s$ . In addition, the performance can be further improved to $99.9\\%$ with multi-round fingerprinting.  \n\n# CCS CONCEPTS  \n\n• Security and privacy $\\longrightarrow$ Security services.  \n\n# KEYWORDS  \n\nDevice Fingerprinting; Electromagnetic Radiation; CPU; Smart Devices.  \n\n# ACM Reference Format:  \n\nYushi Cheng, Xiaoyu Ji, Juchuan Zhang, Wenyuan Xu, and Yi-Chao Chen. 2019. DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU. In 2019 ACM SIGSAC Conference on Computer and Communications Security (CCS ’19), November 11–15, 2019, London, United Kingdom. ACM, New York, NY, USA, 14 pages. https://doi.org/10.1145/3319535.3339810  \n\n![](/tmp/output/66_20250326142658/images/dbfa477f1d5ab2ea7860b711c808542a1047f1fd1f78bfab837acfeebbea16a3.jpg)  \nFigure 1: Based on CPU fingerprints, DeMiCPU provides the ability to fingerprint devices for software and applications.  \n\n# 1 INTRODUCTION  \n\nMobile devices have emerged as the most popular platforms to assist daily activities and exchange information over the Internet. According to Gartner [16], there are more than 11 billion phones, tablets and laptops by the end of 2018. Along with the rapid growth is the rising demand of device authentication: it is useful for applications to recognize whether they are executing on the same device as the previously registered one, e.g., during payments, to ensure the safety of personal privacy or cyber assets.  \n\nOne of the strategies for device authentication is device fingerprinting. Existing device fingerprinting solutions are mainly based on internal device information (e.g., IMEI (device ID), serial numbers of laptops), or built out of software or hardware characteristics. Software-based fingerprints utilize wireless traffic patterns [33], browser properties [46], and etc., while hardware-based fingerprints utilize hardware characteristics such as clock skews [26, 34], accelerometers [13], gyroscopes [2], microphones [11], cameras [14, 29], and Bluetooth implementation [1].  \n\nIn this paper, we propose to fingerprint devices exploiting the featured electromagnetic interference (EMI) signals radiated by CPU modules on devices, which we call CPU fingerprints. The advantage of such a CPU fingerprint is that it can be measured externally rather than internally by the operating system (OS), which could be a useful feature for applications on external devices to authenticate the devices. In addition, a CPU module is indispensable for almost all mobile or smart devices, and thus the CPU fingerprint is likely to be more universal compared with aforementioned built-in sensor based approaches.  \n\nBased on it, we design DeMiCPU, a device fingerprinting scheme consisting of a trusted DeMiCPU server, a stimulation program on the target device, and a trusted stand-alone DeMiCPU capturing module with a built-in magnetic sensor (in short DeMiCPU sensor), as shown in Fig. 1, and it works as follows. Once an application requests for device fingerprinting, DeMiCPU starts the stimulation program, and the DeMiCPU sensor measures and packages the measurements with protection and uploads the packaged measurements to the DeMiCPU server for fingerprint matching. An attacker may try to impersonate a target device by emulating the EMI radiated by its CPU module, but it is almost impossible to produce an EMI pattern close enough to that of the target device, as analyzed in Sec. 7.  \n\nDeMiCPU is promising yet challenging. First, EMI spans a wide spectrum, including high frequency that may produce data at the rate of Gbps. Such computation and communication costs are unacceptable, especially for the DeMiCPU sensor. Second, all electronic components inside a device emit EMI and their operation status affects the level of EMI. It is difficult, if ever possible, to control the status of each component across various attempts of measurement. Besides, it is unclear whether the EMI radiated from the same device at various time instants or locations is consistent and the ones from different devices are distinct. Last but not least, the EMI radiation may contain a large amount of noise and how to extract fingerprints efficiently out of the noisy EMI radiation is nontrivial. This paper addresses aforementioned challenges and validates the feasibility of CPU fingerprint.  \n\nWhich frequency to measure and how to measure? After careful analysis and experimental validation, we choose low-frequency magnetic induction (MI) signals $<100~k H z\\$ ). EMI generated by electronic components includes both electromagnetic radiation (EMR) in the far field ( $>$ two wavelengths) and magnetic induction (MI) in the near field ( $\\mathrm{\\check{\\Psi}}<$ a wavelength). Since EMR is the main cause that affects interoperability of devices, it is suppressed for electromagnetic compatibility [18]. Yet MI signals dominate the near field and do not propagate as far as EMR. Being less a concern of interference, MI signals are not intentionally suppressed and serve as an excellent candidate for extracting hardware fingerprints.  \n\nHow to induce consistent MI? It is almost impossible to control the status of each component, and thus we focus on controlling the one that emits the majority of MI signals, i.e., the CPU module that consists of the CPU chip and its affiliated power supply circuits. In this way, MI signals contributed by other components on the motherboard can be neglected. CPU fingerprints are made possible because even for devices of the same model, CPU modules are discrepant due to hardware diversities introduced during the manufacturing process. However, various applications may lead to various MI signals of the CPU module (as our experiments confirmed). To ensure that the CPU load and operation status are similar across measurements, we analyze the cause and influencing factors of the emitted MI signals and design a set of instructions to generate an identical $100\\%$ utilization stimulation to the CPU module.  \n\n![](/tmp/output/66_20250326142658/images/a1c227df747c337f5e7dfc1d79cef840a6f038bcd360ea6ff8dfd24f91309107.jpg)  \nFigure 2: An illustration of a simplified CPU module. A DC/DC converter is connected to the CPU chip for voltage conversion. The inductor in the DC/DC converter can produce strong MI signals when large currents flow through it.  \n\nHow to extract fingerprints despite of noise? To distinguish the subtle discrepancies of CPU modules when the measurement of MI signals could be noisy, we remove the effects of the geomagnetic field and environmental noise in the pre-processing phase before extracting a set of 15 carefully-selected features, which serves as the fingerprint of the device. To further ensure high accuracy, reliability and usability in DeMiCPU, we compare 10 common classifiers to elect the appropriate classification algorithm. In summary, our contribution includes the following:  \n\n• We propose to fingerprint mobile devices by monitoring the MI signals emitted from the CPU module. To the best of our knowledge, this is the first work to attempt device fingerprinting based on the fingerprints of CPU modules. • We design an efficient MI-based fingerprinting scheme consisting of identical stimulation generation, effective feature extraction and valid fingerprint matching, which can identify devices reliably and accurately. • We validate DeMiCPU on 90 mobile devices, including 70 laptops and 20 smartphones. The results show that DeMiCPU can achieve $99.1\\%$ precision and recall on average, and $98.6\\%$ precision and recall for 30 identical devices, with a fingerprinting time of $0.6s$ . Both precision and recall can be further improved to $99.9\\%$ with multi-round fingerprinting.  \n\n# 2 BACKGROUND  \n\n# 2.1 Magnetic Induction of Electronic Devices  \n\nAll electronic components emit electromagnetic interference (EMI) when currents flow. EMI emitted from electronic components (e.g., CPUs, fans, GPUs) includes two types: high-frequency electromagnetic radiation (EMR) signals and low-frequency magnetic induction (MI) signals. EMR refers to electromagnetic waves that are synchronized oscillations of electric and magnetic fields and propagate at the speed of light. High-frequency EMR waves are mainly at an order of $M H z$ or above, and are always effectively reduced or shielded [18] to eliminate interference with other electronic components or devices. By contrast, MI signals are non-radiative waves generated by currents and are typically not intentionally suppressed. In addition, MI signals have a relatively larger strength and a lower frequency than EMR, and thus can be measured by low-frequency magnetic sensors. Therefore, MI signals are good representatives of EMI emitted from a device.  \n\n# 2.2 The CPU Module  \n\nThe CPU module of a device refers to the CPU chip and its affiliated DC/DC converter. The computation-intensive nature of the CPU chip draws heavy currents from the DC/DC converter, which generate strong MI signals.  \n\nCPU. A CPU chip consists of hundreds of millions of CMOS (complementary metal oxide semiconductor) transistors arranged in a lattice form, which performs basic arithmetic, logical, control and input/output $\\mathrm{(I/O)}$ operations. The CPU current depends on the power consumption of the CMOS circuits, which has three components: static power dissipation, short-circuit power dissipation, and dynamic power dissipation, mathematically denoted as follows [38]:  \n\n$$\nP_{c m o s}=P_{s t a t i c}+P_{s h o r t-c i r c u i t}+P_{d y n a m i c}\n$$  \n\n$P_{s t a t i c}$ , a.k.a., leakage power dissipation, is a steady and constant energy cost caused by the leakage currents of transistors. $P_{s h o r t}.$ −circu arises when two transistors in a CMOS gate are on at the same time, which creates a short circuit from the voltage supply to the ground and thus consumes energy. $P_{d y n a m i c}$ is caused by the switching of CMOS gates. Energy consumption of a CPU mainly depends on the dynamic power dissipation of the CMOS lattice, which is roughly equal to the energy change in the output capacitance of CMOS transistors. Average power consumption of a multi-core CPU can be modeled as follows [39]:  \n\n$$\nP_{a v g}=\\sum_{i=1}^{N}{\\frac{C_{i}V(\\alpha)^{2}A F(\\alpha)}{2}}\n$$  \n\nwhere $N$ is the number of CPU cores. $C_{i},A,V$ and $F$ are influencing factors, with their meanings summarized in Tab. 1. $V$ and $F$ are further related to the CPU load $\\alpha$ due to the power-management technique DVFS (dynamic voltage and frequency scaling) [28] applied by modern devices. DVFS decreases the clock frequency and allows a corresponding reduction in the supply voltage for energy saving. For example, for a ThinkPad T440p laptop, $V$ and $F$ are $0.899~V$ and $3095.95~M H z$ when the CPU load is $100\\%$ , and they drop to $0.668V$ and $798.95\\:M H z$ when the CPU becomes idle $(2-3\\%$ load on average). As all the four factors are hardware related and CMOS circuits are various across CPUs, those factors are distinct from device to device (detailed in Sec. 2.3).  \n\nIn this section, we begin with the principle of magnetic signals, then elaborate how CPU modules can produce magnetic signals, and finally explain why magnetic signals from CPU modules are differentiated in nature.  \n\nDC/DC converter. Due to the difference of voltage levels between the CPU and the power supply system (either a battery or an external power source), a DC/DC converter is placed close to the CPU chip to convert a high voltage to a low one [10]. In Fig. 2, we show the key components of a DC/DC converter and its relationship with the CPU chip. In principle, the high-frequency switch in the DC/DC converter works in a duty-cycle mode to generate a lower voltage. Electronic components including the capacitors, inductors, and diodes are utilized to make the output voltage smooth and continuous. The regulated voltage and currents are then fed into the CPU chip to satisfy its computation requirements.  \n\n![](/tmp/output/66_20250326142658/images/f90c5d96d23750f981657a4906e8741755674be05b5af11fb690a1ddd0a93cf2.jpg)  \nFigure 3: Investigation of MI signals emitted from the T440p laptop. (a) The heatmap of measured MI signal strength. (b) Physical structure of the laptop.  \n\nIn short, CPU chips nowadays exploit a reduced voltage for energy efficiency, but incur heavy currents when performing computationintensive tasks. The heavy currents flowing through the CPU module generate strong MI signals, which are further amplified by the inductor inside the DC/DC converter, due to the effect of coils.  \n\n# 2.3 CPU Module Discrepancy  \n\nHardware discrepancies exist among devices, or more precisely, their CPU modules. For CPUs of various models, all the four factors $C_{i}$ , $V$ , $A$ and $F$ that affect the CPU power consumption, can be different due to the discrepancies in hardware structure and specification. Even for CPUs of the same model, e.g., Intel Core i5-3210M for ThinkPad T440p laptops, discrepancies exist due to the imperfections introduced during the manufacturing process. As shown in Tab. 1, manufacture techniques have influence upon three factors $C_{i}$ , $V$ , and $F$ , i.e., the transistor sizes, working voltages, and working frequencies of CPU chips can be distinct. Besides, the DC/DC converter of the CPU module further enlarges the differences. Therefore, MI signals from CPU modules of the same or various models are distinct due to the hardware discrepancies across devices.  \n\nIn summary, MI signals from CPU modules are different in nature and can serve as a candidate of device fingerprints. In addition, CPU load $\\alpha$ affects MI signals since it influences $V$ and $F$ . As a result, MI signals can be strengthened by increasing the CPU load. Thus, to maintain a stable observation of MI signals, the CPU load shall be accurately controlled.  \n\nTable 1: Impact factors of CPU power consumption.   \n\n\n<html><body><table><tr><td rowspan=\"2\">Paug</td><td colspan=\"2\">Factors</td><td rowspan=\"2\">Meaning</td></tr><tr><td>H</td><td>α</td></tr><tr><td>Ci</td><td></td><td></td><td>CMOS capacitance,related to thetransistorsizeandthewirelength</td></tr><tr><td>V</td><td>√</td><td>√</td><td>Supplyvoltageto CPU</td></tr><tr><td>A</td><td>√</td><td></td><td>Averageswitchingfrequencyoftransistors</td></tr><tr><td>F</td><td>√</td><td>√</td><td>Clockfrequency</td></tr></table></body></html>\n\nH: Hardware related. α: CPU load.  \n\n![](/tmp/output/66_20250326142658/images/dac54ab340479e2888fd022f42b26487b7877d003e1df17d6c59d01587f25930.jpg)  \n\n# 3 PRELIMINARY ANALYSIS  \n\nIn this section, we verify the feasibility of CPU fingerprints empirically. As shown in Fig. 10, we collect MI signals emitted from the CPU models with a magnetic-field sensor DRV425 [22] from Texas Instruments (TI), and conduct AD conversion with a data acquisition (DAQ) card U2541A [25] from Keysight at a sampling rate of $200~k H z$ . Each collection lasts for $1s$ $0.5s$ is shown to be sufficient to fingerprint a device in Sec. 6).  \n\n# 3.1 MI Signals from CPU Module  \n\nDoes the CPU module produce the strongest MI? To verify whether the CPU module emits the strongest MI signals among all components, we execute a while(1) loop (in $\\mathrm{C}{+}+\\mathrm{\\O}.$ ) to generate a CPU utilization of $100\\%$ , and measure the MI signal strength by placing the sensor on various spots (33 spots in total) of a Lenovo ThinkPad T440p laptop’s surface (device No. 31 in Tab. 3). We plot the heatmap of the MI signals measured across the laptop’s surface in Fig. 3(a), from which we can find that the strongest MI signals are observed at $^{\\circ}S^{\\prime}$ and $^{\\mathrm{{6}}}D^{\\mathrm{{5}}}$ keys. Dismantling the laptop reveals that two inductors of the DC/DC converter that powers the CPU chip are located right below these two keys, as shown in Fig. 3(b). This indicates that the CPU module, specifically the DC/DC converter, produces the strongest MI signals when the CPU is under a high load.  \n\nDoes the CPU load affect the MI Signals? To understand whether the variation of the CPU load affects MI signals emitted from the CPU module, we force the CPU to work in a duty-cycle mode at a frequency of $5H z$ , i.e., alternating between a $100\\%$ utilization and an idle mode at an interval of $100m s$ . Throughout the experiments, the sensor was placed above the CPU module, i.e., on $S$ and $D$ keys, to measure the emitted MI signals. The results shown in Fig. 4 confirm that the CPU load does affect the MI signals. Thus, it is important to create a consistent software stimulation to ensure the same CPU load such that the fingerprints generated from the CPU module are consistent for the same device.  \n\nDo other components affect the MI Signals? Modifying the status of other computer components may lead to variation of the MI signals. However, MI signals generated by others attenuate rapidly with distance due to the near field effect. We observe no noticeable difference between the MI signals collected right above the CPU module when the fan was turned on and off. As a result, DeMiCPU does not control other components during device fingerprinting.  \n\n![](/tmp/output/66_20250326142658/images/51cba5b1df8c046f407aa6639525c9e14f96bab04d3cf5a49f4d1345aae10342.jpg)  \nFigure 8: Histograms of MI signals from 5 laptops. Even for the two laptops of the same model, i.e., T440p-1 and T440p-2, the MI signals show discrepancies.  \n\n# 3.2 Evidence of CPU Fingerprint  \n\nTo explore the existence of CPU fingerprint, we conduct an experiment with 5 laptops, which are two Lenovo ThinkPad T440p (T440p-1 and T440p-2, for short), Dell XPS 13, Lenovo R720, and Dell XPS 14. Detailed specifications of these laptops (Device No. 31, No. 32, No. 61, No. 49 and No. 62) are summarized in Tab. 3, among which two laptops (T440p-1 and T440p-2) are from the same model and installed with the same operating system and the rest are of different models.  \n\nWe execute the while(1) program to keep the CPU at a $100\\%$ utilization and measure MI signals above the CPU module of each laptop. We then perform Fast Fourier Transform (FFT) on the collected MI signals and plot their one-dimensional histograms in Fig. 8, with a logarithmic bin size of $10^{0.1}$ . The histogram represents the frequency distribution of the MI signals, from which we can observe distinct “patterns” for the 5 laptops in the frequency range from $20H z$ to $10k H z$ . Especially, laptops of different models show more discrepancies compared with those of the same model. Nevertheless, the two T440p laptops remain distinguishable even only with one histogram feature.  \n\nThe above findings shed light on the existence of CPU fingerprints. However, to make the fingerprint robust and accurate, especially for devices from the same model, more features in both time and frequency domains should be investigated to enhance the fingerprint.  \n\n# 3.3 What Contributes to CPU Fingerprint?  \n\nTo understand whether the fingerprint is created by the CPU chip, the DC/DC converter, or the combination of both, we exchange the CPUs of the two T440p laptops and obtain two “new” laptops (T440p-1 with CPU from T440p-2 and T440p-2 with CPU from T440p-1). Similar to previous experiments, the CPU utilization is set to $100\\%$ during collection and MI signals are measured above the CPU module before and after swapping the CPUs. The results in Fig. 5 show that MI signals for four configurations are all different, which indicates that the fingerprint originates from the combination of the CPU chip and its affiliated DC/DC converter, i.e., the CPU module.  \n\n# 3.4 Temporal and Spatial Consistency  \n\nThe MI signal from a device should be consistent across time and space to serve as a robust fingerprint. To investigate the temporal consistency, we collect $30~\\mathrm{MI}$ signals from the T440p-1 laptop at 5 time instants across two days, i.e., the first three instants are within one day (morning, afternoon and evening) and the other two are in the next day (morning and evening). The T440p-1 laptop is set to $100\\%$ utilization and one-second MI signals are collected each time. The results depicted in Fig. 6 indicate that MI signals remain consistent regardless of time.  \n\nTo investigate the spatial consistency, we collect 30 MI signals from the T440p-1 laptop at 3 locations (one in a lab, two at home; and the two places are about 3 kilometers apart). Note that we do not intentionally avoid or remove metal and magnetic materials around the collecting device during experiments. As a result, due to the impact of the earth’s magnetic field and ambient noise (especially in the lab, with numerous electronic devices surrounding), the initial magnetic magnitude of the sensor is geo-spatial dependent. However, the strength of the earth’s magnetic field and ambient noise is relatively static at a specific spot and thus mainly contributes to the constant part of the collected MI signals. As a result, the FFT operation shall have eliminated the impact of the earth’s magnetic field as well as the ambient noise. The results in Fig. 7 also validate that the frequency-domain MI signals remain consistent regardless of locations.  \n\nAll these experiments provide strong evidence that CPU modules can produce strong MI signals that maintain good distinguishability and consistency, and the MI signals from CPU modules serve as promising device fingerprints.  \n\n# 4 THREAT MODEL  \n\nIn this paper, we have the following assumptions.  \n\nImpersonation. Although it is feasible for attackers to launch a Denial-of-Service (DoS) attack by emitting EMI or even placing a strong magnet close to the DeMiCPU sensor, the goal of the attackers is to impersonate a legitimate device. Thus, we focus on replay or mimic attacks.  \n\n![](/tmp/output/66_20250326142658/images/120b981339e1d79fa61d41c7722682115770d06eecab5625be9d96d849e04829.jpg)  \nFigure 9: Structure of the MI signal, including a 0.1 s preamble and a $0.5s$ fingerprinting sample.  \n\nAcquisition of Similar Device. We assume the adversary can obtain similar devices as the target one, e.g., a device of the same model, to imitate the target device and have full control of them.  \n\nSecure Communication. We assume that the communication between the DeMiCPU sensor and the DeMiCPU server and between the server and the software (application) is secure. For instance, DeMiCPU can package the MI measurements or matching results with encryption, by the well-known secure communication protocols [3, 31, 32]. As a result, the attacker cannot create forged measurements or modify the measurements/matching results.  \n\n# 5 DESIGN  \n\nIn this section, we describe the 3 sub-modules of the overall DeMiCPU system: (1) Fingerprint generation; (2) Fingerprint extraction; (3) Fingerprint matching.  \n\n# 5.1 DeMiCPU Fingerprint Generation  \n\nTo obtain MI measurements that produce consistent fingerprints, it is important to solve the following two challenges.  \n\n• How to stimulate the CPU such that it generates the MI signal that can produce a consistent device fingerprint? • How to collect and identify the MI signal segment that maps to the one under stimulation even if an attacker may disturb the communication between the stimulation program and the trusted capturing sensor?  \n\nTo address these two challenges, we design the stimulation program such that it produces the MI signal trace in Fig. 9, which is composed of a preamble and a fingerprinting signal that are both generated by controlling the CPU load in a proactive way. As thus, DeMiCPU only needs to transmit a signal as short as $0.6s$ for fingerprinting.  \n\n5.1.1 Preamble. To identify the MI signal segment that is under stimulation, a preamble is used for the trusted capturing sensor to detect the start of the fingerprinting signal. DeMiCPU stimulates the device such that a unique MI pattern is generated as a preamble, thereby allowing the sensor to identify it with cross-correlation. We realize the preamble by manipulating the CPU load and generate a sequence of [1,0,1,0] (“1” for full-utilization mode and $^{\\mathrm{{\\scriptsize~6}}}0^{\\mathrm{{\\scriptsize~3}}}$ for idle mode) as shown in Fig. 9, which lasts for $100m s$ in total.  \n\n5.1.2 Stimulating CPU. The strength of the MI signals emitted from the CPU module depends on the current, which is related to the CPU load. In order to obtain stable MI signals to produce CPU fingerprints, we stimulate the CPU by controlling its utilization ratio. Without loss of generality, the total CPU utilization is the sum of CPU utilization from all running processes, including both system and user processes, which can be modeled as follows:  \n\n$$\nC P U\\_u t i l=S y s\\_p r o c e s s e s+U s e r\\_p r o c e s s e s\n$$  \n\nUtilization Ratio. One intuitive question is what utilization ratio to use, $100\\%$ , $50\\%$ , or other values? In fact, it is difficult to precisely control the utilization since 1) it is hard to accurately restrict system and user processes to a certain level, and 2) the CPU scheduling policy further worsens the problem. For instance, $50\\%$ CPU utilization means that the CPU works in 5 clock cycles and is idle in the remaining 5. Without inspecting and modifying the scheduling algorithm, it is almost impossible to ensure that the CPU behaves the same in all clock cycles.  \n\nTo address it, we choose to keep the CPU running in the fullutilization $\\left(100\\%\\right)$ mode to obtain an identical output. Another benefit of such an implementation is that higher CPU utilization generates stronger MI signals, which helps to lighten the impact of ambient noise. We achieve the full-utilization mode by invoking CPU-consuming instructions, such as while(1) in our implementation. As thus, system processes, DeMiCPU stimulation process, and other user processes together compose the $100\\%$ utilization.  \n\nDeMiCPU Priority. During fingerprinting, however, other user processes, i.e., background applications, are not likely to be the same, which may render the stimulation nonidentical. To eliminate the influence of other user processes, we assign a superior priority to the DeMiCPU stimulation program, which is higher than the base one of other user processes yet lower than that of the system processes since they only account for $1-2\\%$ CPU utilization on average.  \n\nMainstream operating systems such as Windows, Linux, and Mac OS X, are all able to support such an implementation. For instance, Windows implements a priority-driven, preemptive scheduling system, where the highest priority runnable threads are executed first. Each thread, which is the smallest unit of program execution flow, has a base priority as a function of its process priority class and relative thread priority. Normally, user applications and services start with a base priority level 8, i.e., both process and thread priorities are normal [37]. Thus, we shall at least assign the DeMiCPU stimulation program with a priority level higher than that.  \n\nIn particular, we examine the highest priority of the user threads, which is usually a priority level 8 as mentioned before. Then, we assign a higher priority to the DeMiCPU thread, e.g., a normal process priority but an above normal thread priority, i.e., a priority level 9, to eliminate the impact of other user processes. In addition, since modern CPU chips support multi-core and multi-thread, we bind a stimulation thread to each available logical processor core, including the virtual ones created by Hyper-Threading [30]. As thus, the CPU utilization under stimulation is as follows:  \n\n$$\nC P U_{-}u t i l\\_s t i m u=S y s\\_p r o c e s s e s+\\mathsf{D e M i C P U}=100\\%\n$$  \n\nFeedback. In general, such a design is able to generate an identical stimulation. However, in a rare case, a thread with a higher priority may be launched during fingerprinting, making the stimulation different than planned. To further guarantee the validity of the DeMiCPU stimulation, we introduce a feedback mechanism, i.e., examining system logs after stimulation to confirm that DeMiCPU exclusively uses the CPU during fingerprinting. If not, DeMiCPU abandons the current measurements and triggers a second collection. Moreover, the CPU frequency may drop due to a high CPU temperature or low battery. Thus, the feedback mechanism examines the CPU working frequency before and during stimulation. If a previous or midway frequency drop is detected, DeMiCPU abandons the current measurements and defers its collection till the CPU recovers from the low frequency mode, as revealed in Algorithm 1. In this way, we minimize the influence of software environment and output stable fingerprinting signals as shown in Fig. 9.  \n\n<html><body><table><tr><td colspan=\"2\">Algorithm 1: DeMiCPU Stimulation</td></tr><tr><td colspan=\"2\">1 CPU_Frequency← GET_CURRENT_CPU_FREQUENCYO</td></tr><tr><td></td><td>2 if CPU_Frequency > threshold_1 then</td></tr><tr><td>3</td><td>C_priority ←GET_CURRENT_H1GHEST_PRIORITYO</td></tr><tr><td>4</td><td>//get the highest priority level of running user threads</td></tr><tr><td>5</td><td>DeMiCPU_priority ←GEN_PRIoRITy(C_priority)</td></tr><tr><td>9</td><td>cpunum ← GET_CPU_CoRE_NuMO)</td></tr><tr><td>7</td><td>// get the number of CPU logical processors</td></tr><tr><td>8</td><td>for i E range(1,cpunum) do</td></tr><tr><td>6</td><td>hThread(i)←CreateThreadO</td></tr><tr><td>10</td><td>// create the ith DeMicPU stimulating thread</td></tr><tr><td>11</td><td>SetThreadPriority(hThread(i), DeMiCPU_priority)</td></tr><tr><td></td><td>// set the ith DeMiCPU stimulating thread with the generated DeMiCPU priority level</td></tr><tr><td>12</td><td>C_Thread ← GetCurrentThread (</td></tr><tr><td>13</td><td>C_Mask = 0x0001 * 2i-1</td></tr><tr><td>14</td><td>SetThreadAffinityMask (C_Thread,C_Mask)</td></tr><tr><td>15</td><td>/ bind the ith DeMiCPU stimulating thread to the ith</td></tr><tr><td></td><td>CPU logical processor</td></tr><tr><td>16</td><td>preamble_genO</td></tr><tr><td>17</td><td>fingerprinting_signal_genO</td></tr><tr><td>18</td><td>Stim_UtilGET_UrIL_FEEDBACKO)</td></tr><tr><td>19</td><td>Stim_Freq ←GET_FREQ_FEEDBACKO)</td></tr><tr><td>20</td><td>if Stim_Util<threshold_2then</td></tr><tr><td>21</td><td>DeMiCPU Stimulation</td></tr><tr><td>22</td><td>if Stim_Freq< threshold_1 then</td></tr><tr><td>23</td><td>sleep(5)</td></tr><tr><td>24</td><td>DeMiCPU Stimulation</td></tr><tr><td colspan=\"2\">25 5else</td></tr><tr><td>26</td><td>sleep(5)</td></tr><tr><td>27</td><td>DeMiCPU Stimulation</td></tr></table></body></html>  \n\n# 5.2 DeMiCPU Fingerprint Extraction  \n\n5.2.1 Pre-processing. Preliminary analysis confirms the temporal and spatial consistency of the MI signals in the frequency domain. However, the time-domain MI signal is geo-spatial dependent due to the impact of the earth’s magnetic field and ambient noise. As the strength of the earth’s magnetic field and ambient noise is relatively static at a specific spot, we assume it mainly contributes to the constant part of the collected MI signals. To eliminate its impact, we normalize the raw MI signal, i.e., the measured signal in Fig. 9, before extracting features.  \n\nDenote the measured signal as $B$ , we normalize $B$ to obtain the pre-processed MI signal $M$ for feature extraction as follows:  \n\n$$\nM=\\frac{B-m i n(B)}{m a x(B)-m i n(B)}\n$$  \n\nNote that although the above solution is designed for scenarios where ambient MI signals are relatively static, we argue it also works with time-varying magnetic signals such as power frequency interference from nearby electrical equipment, because that the time-varying MI signals from other devices quickly attenuate and thus have little influence.  \n\n5.2.2 Feature Selection. For each pre-processed signal $M$ , we extract 30 scalar features from both time and frequency domains. We exploit LibXtract [7], a lightweight feature extraction library for time series, which can output a number of statistical feature candidates. Besides the features offered by LibXtract, we investigate the physical meaning of the MI signal, and manually select features, e.g., Spectrum Kurtosis and Spectrum Smoothness, on which remarkable distinctions can be observed in Fig. 8.  \n\nTo further determine critical features, we rank them with the help of FEAST toolbox [6], which is a commonly used feature ranking tool in machine learning. From the results, we obtain the top 15 features in time and frequency domains and construct a feature set as: $\\mathbb{F}=$ {Spectrum Roll Off, Spectrum Kurtosis, Average Deviation, Spectrum Spread, Spectrum Smoothness, RMS Amplitude, Spectrum Standard Deviation, Spectrum Irregularity-K, Spectrum Skewness, Spectrum Flatness, Standard Deviation, Spectrum Irregularity-J, Mean, Skewness, Spectrum Mean}. The orders in the set indicate their ranking orders with Spectrum Roll Off giving the largest information gain.  \n\nFor a fingerprinting signal i from a device, hereafter we define the feature set $\\mathbb{F}_{i}$ as the fingerprint of the device.  \n\n# 5.3 DeMiCPU Fingerprint Matching  \n\nThe DeMiCPU cloud server utilizes supervised learning to classify each trace with the extracted feature set F. To select the appropriate classification algorithm, we compare 10 commonly-used classifiers and the detailed results can be found in Fig. 11(a). For the sake of high classification accuracy and robustness over a single classification algorithm, we employ an ensemble classification approach ExtraTrees [19], which fits a number of randomized decision trees on various sub-samples of the dataset and uses averaging to improve prediction accuracy and avoid over-fitting.  \n\nTraining. During the training process, for a specific device, $k$ traces from it are utilized as the positive class, and $k$ traces from each of the rest devices serve as the negative class to train a binary classifier. Therefore, for $j$ devices, $j$ binary classifiers are trained in total. In real-world deployment, we may need to extend the classification system when a new device comes and registers. Under that circumstance, the feature sets of the new device are extracted and trained to obtain a new binary classifier without the need of retraining the original $j$ classifiers. The new classifier is finally incorporated with the existing classifiers to constitute a new classification system.  \n\n![](/tmp/output/66_20250326142658/images/c7e3c1cee9877a55667dbbe9aa1d7de300082f9f12693fe5123f1c57b035f83e.jpg)  \nFigure 10: Experimental setup. The magnetic sensor is vertically placed on the surface of the target laptop for MI signal collection.  \n\nMatching. When matching, the server analyzes the fingerprint signal from the device to be identified and extracts its feature set F. Then, the server feeds it to the classifier of which class the device claims to be, to verify its identity.  \n\n# 6 EVALUATION  \n\nTo evaluate the performance of DeMiCPU, we have conducted experiments with 70 laptops and 20 phones across 30 days, among which 30 laptops are of the same model. The detailed information of each device is shown in Tab. 3 (in Appendix A.1). In summary, the performance of DeMiCPU is:  \n\n• DeMiCPU achieves $99.1\\%$ precision and recall for both laptops and phones, and more than $98.6\\%$ precision and recall for 30 identical devices with one-round fingerprinting, and the performance can be further improved to $99.9\\%$ with multiround fingerprinting.   \n• DeMiCPU can operate with little influence from operating systems, background applications, fan on/off states or CPU temperature.   \n• DeMiCPU supports low sampling rate which makes it a universal approach running on ubiquitous smart devices.  \n\n# 6.1 Experiment Setup  \n\nWith the experiment setup described in Tab. 3 and Fig. 10, we collect $100~\\mathrm{MI}$ traces for each of the 90 devices and each trace lasts for $0.5~\\mathrm{s}$ (excluding the preamble). The settings for the laptops and smartphones are as follows.  \n\nStimulation Program Setup. We implement the stimulation program in Algorithm 1 on five operating systems, i.e., Windows (in $\\mathrm{C}{+}{+}$ ), Linux (in $\\mathrm{C}{+}{+}$ ), Mac OS (in Java), Android (in Java), and iOS (in $\\mathrm{C}++$ ), to stimulate the CPU and generate a fingerprinting signal. The lightweight program is pre-installed on the experimental laptops/ smartphones.  \n\n![](/tmp/output/66_20250326142658/images/869d268f853569a8ec567031419d3e2668f9f0035e3146f208aaadc289045383.jpg)  \nFigure 11: Micro-benchmark evaluation results of DeMiCPU on 5 randomly-chosen devices.  \n\nData Collection Setup. We collect MI signals from the 90 devices using a magnetic-field sensor DRV425 [22] from TI. As shown in Fig. 10, the sensor is vertically placed on the surface of laptops or phones (test points are shown in Tab. 3 in detail) since MI signals emitted from the CPU modules are in the vertical direction. A data acquisition (DAQ) card U2541A [25] from Keysight is utilized for AD conversion with different sampling rates, e.g., $100H z$ , $200H z$ , $1k H z$ , and etc. A data processing laptop connects with the DAQ card through a USB, which locally stores and processes the collected data.  \n\n# 6.2 Performance Metrics  \n\nGiven an MI fingerprint from a device, DeMiCPU verifies whether it belongs to the device (classifier) that it claims to be. For each classifier $i$ , we define $T P_{i}$ as the true positives for classifier $i,$ , i.e., the number of fingerprints that are correctly accepted as i. Similarly, $F N_{i}$ and $F P_{i}$ refer to the number of fingerprints that are wrongly rejected, and wrongly accepted as $i$ , respectively. We define the standard classification metrics for each classifier $i$ as:  \n\n$$\nP r e c i s i o n(i)=\\frac{T P_{i}}{(T P_{i}+F P_{i})}\n$$  \n\n$$\nR e c a l l(i)=\\frac{T P_{i}}{(T P_{i}+F N_{i})}\n$$  \n\n$$\nF1-S c o r e(i)=\\frac{2\\times P r_{i}\\times R e_{i}}{(P r_{i}+R e_{i})}\n$$  \n\nThe final precision, recall and F1-Score for DeMiCPU are the average of the 90 classes.  \n\n# 6.3 Micro-benchmark Evaluation  \n\nIn this subsection, we evaluate the impact of classifier choices, operating systems, background applications, on/off states of fans, temperatures and displacements of test points. Five devices from Tab. 3 are randomly chosen for the micro-benchmark evaluation.  \n\n6.3.1 Classifier Choice. To select the appropriate classifier for DeMiCPU, we compare 10 commonly-used supervised learning algorithms. They are 1) Logistic Regression, 2) Gaussian Naive Bayes, 3) K-Nearest Neighbors, 4) Linear Discriminant Analysis, 5) Quadratic Discriminant Analysis, 6) Decision Tree, 7) Support Vector Machine, 8) ExtraTrees, 9) Random Forest, and 10) Gradient Boosting. We employ the 10-fold cross validation to evaluate the classifier performance, which can combine measures of fit and thus derive a more accurate estimation for model prediction performance.  \n\nWe randomly choose 30 traces from each device, feed them into the classifiers and record the corresponding accuracy. The results in Fig. 11(a) show that 6 out of 10 classifiers show an F1-score above 0.9, with the classifier 8) ExtraTrees, 9) Random Forest, and 10) Gradient Boosting being the best 3 classifiers. Thus, we can assume that the data possesses good property in terms of discrepancies, i.e., CPU fingerprints are able to discriminate devices. In the following experiments, we employ ExtraTrees since 1) it shows the best accuracy, and 2) it’s an ensemble classification approach which achieves better robustness over a single classification algorithm.  \n\n6.3.2 Operating Systems. A device may install different OSs during its lifetime. To investigate whether OSs affect DeMiCPU, we install 4 OSs which are 1) Window 7 Home Basic 7601, 2) Kali Linux 2.0, 3) Windows 8 Professional 9200, and 4) Windows 10 Enterprise 10240 on the experimental laptops, and conduct experiments under each OS to investigate the impact of OSs. We train the classifier with traces from one OS and test it under all the four OSs. The results in Fig. 11(b) indicate that with the DeMiCPU stimulation program, the same device can be successfully identified across different OSs with precision, recall and F1-Score of 1. It confirms that with elaborately designed stimulation, OS-associated processes only account for a tiny portion of the CPU utilization during fingerprinting, which is within the tolerance of DeMiCPU. Thus, we believe DeMiCPU fingerprint is independent on OSs.  \n\n6.3.3 Background Applications. DeMiCPU stimulation is designed to be undisturbed by other user processes. To evaluate its performance against background applications in practice, we conduct experiments on each device with several daily-used applications. They are 1) WeChat, 2) Microsoft Word, 3) Google Chrome, 4) YouTube, and 5) MATLAB, with statistically increasing CPU utilization when normally used. We train the classifier using traces with no background application, and test it using traces with one of the aforementioned background applications, respectively. The results shown in Fig. 11(c) confirm that, background applications barely have impact on the performance of DeMiCPU since it can preempt the CPU even if user applications run.  \n\n6.3.4 Displacement of Test Point. Due to that all electronic components inside a device emit MI signals, the measuring sensor may capture MI signals from other components when moved away from the CPU module. To investigate the impact of the test point displacement, we vary the position of the sensor as follows: Starting from the center of the original test point (depicted in terms of key positions in Tab. 3), we gradually move the sensor with a step of 1 mm in four directions: upwards, downwards, left and right. The classifier is trained at the original test point, and tested at each changed position. For each displacement, we average the precisions, recalls and F1-scores in four directions, and show the final results in Fig. 11(d). From the results, we can see that within an offset of 8 mm, DeMiCPU achieves a high accuracy $\\left(>99\\%\\right)$ . That is, a user can conduct DeMiCPU fingerprinting with a displacement tolerance of around a key size, which is approximately $10-15~m m$ wide.  \n\n![](/tmp/output/66_20250326142658/images/0150f95b0edb94d0ad9566b551109f4406f785aa6cdea1d5c84fe234ceaad581.jpg)  \nFigure 12: Overall performance of DeMiCPU with different training data sizes (10, 20, 30 and 40).  \n\n6.3.5 Fans. When fingerprinting a laptop, an electric fan aside the CPU module emits MI signals as well. To investigate the impact of fans, we collect 200 traces from each device with fan on and off, i.e., 100 traces each. We train the classifier with the fan-on traces and test it with the fan-off traces. The resulting F1-Score is 1, indicating that MI signals from the fan have little influence. We assume it is because that fans have much lower power (several watts) compared with CPUs (tens of watts), and the large distance (around $10~c m$ ) between fan and CPU makes the MI signal from a fan quickly attenuate.  \n\n6.3.6 Temperature. CPU temperature changes over time and load, and might be an influence factor for DeMiCPU. To investigate, we test DeMiCPU under different CPU temperatures. Note that in DeMiCPU stimulation, we introduce a CPU frequency check before stimulation since the CPU protection mechanism will decrease the CPU frequency when its temperature becomes too high, e.g., above $90^{\\mathrm{o}}\\mathrm{C}$ Thus, DeMiCPU normally works when the CPU temperature is not too high to cause a frequency drop and we first test DeMiCPU under this range. We train the classifier using traces collected when CPU temperature is $65~^{\\mathrm{o}}C_{:}$ , and test it under the cases of $43~^{\\mathrm{o}}C$ , $52^{\\mathrm{~o~}}\\mathrm{C}$ , $60~^{\\mathrm{o}}\\mathrm{C}$ , $68~^{\\mathrm{o}}C$ , and $78~^{\\mathrm{o}}C$ , respectively. The F1-Scores for the five cases are all 1. To further explore the performance of DeMiCPU under a high temperature, we manually turn off the CPU protection mechanism and test the system under $90^{\\mathrm o}\\mathrm{C}$ . The resulting F1-score is also 1, indicating that DeMiCPU works as well. Thus, we believe DeMiCPU is robust to CPU temperature changes.  \n\n![](/tmp/output/66_20250326142658/images/92d93f92749e7391a6a5611425b6712ec3dbd5e055286f36a44bbee992f53c56.jpg)  \nFigure 13: Confusion matrix of 30 identical laptops.  \n\n# 6.4 Overall Performance  \n\nIn the overall performance evaluation, 100 traces are collected from each device in Tab. 3, and the employed classifier is ExtraTrees with a tree number of 100.  \n\n6.4.1 Impact of Training Size. In the first set of experiments, we train the system with $x$ traces and test it with the rest $100-x$ traces (they are never used for training). $x$ is set to 10, 20, 30, and 40 (correspond to 5, 10 ,15, and 20 seconds) respectively, to evaluate the appropriate size of training data. We calculate the Precision(i) and Recall(i) for each device (class) $i$ , and plot their CDFs in Fig. 12(a) - 12(d) with different training data size $x$ . Even with 10 training traces (correspond to 5 seconds), $90\\%$ of the precisions and recalls are above $93.0\\%$ for all the laptops and smartphones. The average precision and recall are $98.3\\%$ and $98.2\\%$ for the 70 laptops, and $99.4\\%$ and $99.3\\%$ for the 20 smartphones. With the increasing of training data size, both precision and recall are improved. Given the training size 20, DeMiCPU is able to achieve an average precision and recall of $99.0\\%$ and $99.0\\%$ for the laptops, and $99.8\\%$ and $99.8\\%$ for the smartphones. Besides, when the training data size further increases, the performance of DeMiCPU approaches $100\\%$ . To strike the balance between usability and accuracy, we choose 20 traces for training, which only amount to $10~s$ . Training data size is then set to 20 in the rest of the evaluation.  \n\n![](/tmp/output/66_20250326142658/images/7fabbe2675649acfe593daf70a661ece60599e7ab142d251e0c4c40086eed34e.jpg)  \n\n6.4.2 Performance of Devices of Same Model. The reason why precision and recall behave better on smartphones is that there are more devices of the same model for laptops. As a result, false positives and false negatives may occur. To take a close look, we plot a confusion matrix for devices No. 1-30 (i.e., the 30 ThinkPad T430 laptops) in Fig. 13. These 30 laptops are of the same model and installed with the same operating system, and thus are more likely to be confused with each other. From the confusion matrix, we can observe that device No.23 and No.24, as well as device No.25 and No.26 contribute a relatively lower accuracy, with the worst precision of $91.6\\%$ . Nevertheless, DeMiCPU can still achieve an average precision of $98.7\\%$ , and an average recall of $98.6\\%$ for the 30 identical devices.  \n\n6.4.3 Impact of Sampling Rate. To investigate the sampling rate requirement of DeMiCPU, we test the system by setting the sampling rate to 100, 200, 1 k, $5k$ , $25k$ , $100~k$ , and $200~k H z$ respectively. 100 traces from each of the 90 devices are collected at each sampling rate for training and testing. The resulting precisions and recalls are shown in Fig. 14, from which we can observe that precisions and recalls of DeMiCPU do not change significantly with lower sampling rates. Especially, with a $1k H z$ sampling rate, DeMiCPU achieves a precision of $98.5\\%$ and a recall of $98.3\\%$ , which are nearly equivalent to the results under higher sampling rates. Even with a $100H z$ sampling rate, the precision and recall can be as high as $93.2\\%$ . This finding is encouraging since it indicates that DeMiCPU can even use ubiquitous smart devices with limited sampling rate capability for fingerprint collection. For instance, most smartphones nowadays are equipped with a built-in magnetometer that supports $100H z$ sampling rate. Low requirement of sampling rate makes DeMiCPU a more universal device fingerprinting mechanism.  \n\n6.4.4 Scalability of DeMiCPU. Although it’s difficult to evaluate the capability of DeMiCPU with a very large set of devices, we conduct several experiments in which we increase the number of tested devices gradually to get a sense of how DeMiCPU scales. With the same settings in the 90-device experiments, we change the total number of tested devices and repeat the experiments. First, we randomly choose and use 20 devices to obtain the precision and recall of DeMiCPU. Then, we increase the quantity of devices to 30, 50, 70 and 90, and recalculate the precisions and recalls. Tab. 2 shows how accuracy changes with the increasing number of devices, from which we can find that the performance of DeMiCPU does not change significantly as the number of devices increases. It provides encouraging signs that DeMiCPU is likely scalable to a large number of devices.  \n\nTable 2: Average precision, recall and F1-Score of DeMiCPU with different numbers of tested devices.   \n\n\n<html><body><table><tr><td>Numberofdevices</td><td>Precision</td><td>Recall</td><td>F1-Score</td></tr><tr><td>20</td><td>1.000</td><td>1.000</td><td>1.000</td></tr><tr><td>30</td><td>0.997</td><td>0.996</td><td>0.996</td></tr><tr><td>50</td><td>0.997</td><td>0.997</td><td>0.997</td></tr><tr><td>70</td><td>0.995</td><td>0.994</td><td>0.994</td></tr><tr><td>90</td><td>0.991</td><td>0.991</td><td>0.991</td></tr></table></body></html>  \n\n6.4.5 Impact of Alien Devices. In real-world deployment, it is likely that DeMiCPU needs to identify alien devices, i.e., devices that are not trained beforehand. To understand how DeMiCPU performs with alien devices, we conduct the following experiments. From the 90 devices, we randomly choose 85 devices for training and get the corresponding 85 binary classifiers. The rest 5 devices, which serve as aliens to the trained system (they are never used for training), are utilized to test the performance. The 5 devices take turns to input their traces to each of the 85 classifiers to see if they can be accepted. We repeat the experiment for 10 times to eliminate the random errors and plot the CDF of true negative rates in Fig. 15. The results reveal that DeMiCPU can successfully reject alien devices with a minimum probability of $98.2\\%$ and an average probability of $98.7\\%$ , which indicates its high reliability.  \n\n6.4.6 Multi-round Fingerprinting. In aforementioned evaluation, the threshold for each binary classifier is 0.5 by default. However, in practice, precision is likely to be prior to recall for the sake of high reliability and security, and recall can be further improved through multi-round fingerprinting.  \n\nTo investigate the appropriate threshold to achieve high precision and the minimum fingerprinting round to achieve high recall, we plot the precision-recall curve by varying the threshold for each classifier. As DeMiCPU is a system consisting of multiple binary classifiers, we employ the same threshold in each classifier and average their precisions and recalls as the final performance. The results shown in Fig. 16 reveal that, for both laptops and smartphones, the precision approaches $100\\%$ when the threshold increases. Specifically, for laptops, the recall is $97.0\\%$ when the precision is $99.9\\%$ with a threshold of 0.54, which can be further improved to $99.9\\%$ with two-round fingerprinting and $99.99\\%$ with three-round fingerprinting. Similarly, for smartphones, the recall is $98.3\\%$ when the precision is $99.9\\%$ with a threshold of 0.64, and the recall can approach $99.999\\%$ with only three-round fingerprinting. Therefore, with three-round fingerprinting, DeMiCPU can achieve a $99.9\\%$ precision and an over $99.99\\%$ recall on both laptops and smartphones.  \n\n![](/tmp/output/66_20250326142658/images/3bcb1b0932b86f5305fa24096b506be7b4402b59a3836b4fee7375ff87b322fc.jpg)  \nFigure 17: DIY replay attack equipment with a handcrafted induction coil. The recorded MI sample is emitted by the MSP430 in the form of discrete voltages, which are first converted into analog signals by a Digital-to-Analog Converter (DAC) and then converted into corresponding current signals by a Voltage-to-Current Converter (VCC).  \n\nTo summarize, our evaluation with 90 laptops and smartphones shows that smart devices can be identified leveraging the fingerprints of their CPU modules. While even a larger study is needed to confirm the scalability of our findings, to the best of our knowledge, this is the first work to attempt device fingerprinting based on fingerprints of CPU modules.  \n\n# 7 DISCUSSION  \n\nIn this section, we conduct the security analysis and discuss the limitations of DeMiCPU.  \n\n# 7.1 Security Analysis  \n\nSince the goal of the attackers is to impersonate a legitimate device, we discuss two attacks: replay attacks and mimicry attacks. To launch a replay attack, an adversary may have a brief physical access to the target device. She may record the MI signal of the target device and replay the recorded sample to fool the DeMiCPU sensor. For mimicry attacks, she may find a similar device to imitate the legitimate one.  \n\n7.1.1 Replay Attack. A replay attack consists of two steps: recording and reproducing. We study the feasibility of such attacks based on two sets of equipment: commercial off-the-shelf (COTS) devices and DIY sets with handcrafted coils.  \n\nCOTS device. The effectiveness of recording and emitting radiation signals is determined by the sensitivity of the sampling devices and the gain of the antennas. Much work has demonstrated the feasibility of replaying radio frequency (RF) signals at reasonable cost, e.g., utilizing a Universal Software Radio Peripheral (USRP) with a matching antenna to replay signals at 2.4 or 5 $G H z$ for Wi-Fi, $900M H z$ for GSM (Global System for Mobile Communications), $13.56~M H z$ for NFC (Near-field Communication), and etc. These RF bands are at least at the order of $M H z$ and a variety of off-theshelf matching antennas are available. In comparison, the effective frequency range of DeMiCPU is below $10k H z$ , whose matching antennas, i.e., VLF (very low frequency) antennas, are usually used for military communication with submarines and few commodity antennas are available. Moreover, VLF antennas are typically large, e.g., a dipole antenna for $10k H z$ can be longer than $7.5k m$ .  \n\nWithout matching antennas, we may refer to dedicated equipment to record MI samples. For instance, we found a N9038A MXE EMI receiver from Keysight that can analyze signals from 3 $;H z$ to 44 $G H z$ at the cost of $\\$90$ , 000 USD. However, we were unable to find equipment that can reproduce the recorded samples with abundant signals ranging from DC to $10~k H z$ since most RF generators on the market only support frequency higher than $9k H z$ .  \n\nDIY set with handcrafted coils. Unable to replay MI signals with COTS devices, we design our own replay equipment: We record the MI sample with the DRV425 magnetic sensor and replay the signal with a handcrafted induction coil driven by a MSP430F5529 LaunchPad [23], as shown in Fig. 17. We program the LaunchPad to output the recorded MI sample in a form of discrete voltages, which are then converted into analog signals by a Digital-to-Analog Converter (DAC). The analog voltage signals are further converted into corresponding current signals to drive the induction coil. A ferrite core is inserted into the coil to augment its permeability. A Constant Voltage Source (CVS) is utilized to power the VCC, and an oscilloscope is used to monitor the output voltage of the DAC.  \n\nTo quantify the MI signals measured by sensors, we refer to the Ampere’s circuital law [45], which models the magnetic flux generated by a charged coil as follows:  \n\n$$\n\\Phi_{B}=\\mu N I S c o s\\theta\n$$  \n\nwhere $\\mu$ is the magnetic permeability of the coil, $N$ is the number of turns, $I$ is the current flowing through the coil, $S$ is the area of the magnetic sensor’s sensing surface, and $\\theta$ is the angle between the magnetic field lines and the normal line (perpendicular) to $S$ . Therefore, although we elaborately reproduce the MI signal, the distance and angle between the coil and sensor affect the measurement. Given the dynamic nature of the produced magnetic field and the noise introduced during DA conversion, it is extremely difficult for the sensor to record MI signals that equal the recorded one.  \n\nTo validate, we randomly choose five samples from five devices, and obtain 10 replayed samples for each. Although we try our best to obtain a similar replayed signal, none of them matches with the enrolled fingerprints. We believe that is because the fingerprint discrepancy caused by the CPU hardware is subtle and the differences as well as noises introduced during the replay attack is likely to ruin such subtle characteristics. Thus, replay attacks targeted at DeMiCPU are challenging to perform even at a single point and the difficulty will increase dramatically with the increasing of testing sensors.  \n\n7.1.2 Mimicry Attack. The mimicry attack utilizes a similar device to imitate the target device by manipulating the software or configurations of the attack device. To impersonate the target device, the attack device has to precisely learn and mimic the fingerprint of the victim. However, the essential discrepancies of DeMiCPU fingerprints originate from the hardware of CPU modules. Manipulating software or configurations may alter the CPU fingerprint but the mapping between the configurations and the fingerprint is difficult to profile. As a result, the mimicry is likely to be unsupervised. In addition, according to our observations, the fingerprint discrepancy caused by the hardware of the CPU module is subtler compared with that caused by configurations. Thus, mimicry attack is not likely to make the attack device’s fingerprint exactly the same as the one of the target device.  \n\nIn summary, given the low frequency nature and the high precision of DeMiCPU, we believe it is difficult for adversaries to launch either a replay attack or a mimicry attack against DeMiCPU.  \n\n# 7.2 Limitation  \n\nAuthentication Point. DeMiCPU that relies on one sensor requires the test point within a $16~m m$ range, which may affect the usability. A significant displacement of the DeMiCPU sensor from the CPU module may lead to failure in identification. However, we envision it can be addressed by exploiting a sensor array, which shall effectively reduce the requirement of test points and enlarge the fingerprinting area.  \n\nLong-term Consistency. We conducted our experiments over 30 days. However, a smart device usually can be used for years and it may experience changes due to aging, which in turn may change the features gradually. For example, the number of available CMOS transistors in the CPU may decrease due to the hardware aging. Nevertheless, we assume that we can compensate the aging by postulating a fingerprint slow updating technique: We update the fingerprints in the database occasionally if the current fingerprint is still classified to the legitimate user yet a small constant offset is detected, such that slow changes can be compensated.  \n\nUser Process Suppress. DeMiCPU employs a higher priority for stimulation compared with other user processes. As a result, other user applications will be suppressed during fingerprinting. However, as DeMiCPU stimulation only lasts for $0.6~s$ , we argue it is relatively short and might be acceptable for most applications without affecting user experience.  \n\nFirmware-update Resistance. The firmware and CPU microcode of a smart device can be updated in accordance with requirements. During our experiments, the devices were kept natural and haven’t been updated intentionally. As the firmware and CPU microcode may affect the execution of CPU instructions, they may have impact on DeMiCPU fingerprinting. We remain it as the future work.  \n\n# 8 RELATED WORK  \n\nDevice Fingerprinting. Fingerprint is one of the most common biometrics in user identification [24, 35]. The same concept was extended to device identification by the US government in 1960s to identify and track unique mobile transmitters [27]. Since then, much effort has been devoted to identifying network devices by building a fingerprint out of their software or hardware. In terms of software-based fingerprint, the combination of chipsets, firmware and device drivers [15], timing interval of probe request frames [12], patterns of wireless traffic [33], and browser properties [46], can be used to identify devices. The downside of these methods is that fingerprints will change once device configuration or user behavior changes. Hardware-based approaches fingerprint a device through their physical components or properties. Clock skews [26, 34], radio frequency (RF) discrepancy at the waveform [20, 36, 41] or modulation [5] levels are well explored to identify wireless devices such as Wi-Fi routers. Mobile device fingerprinting utilizes the difference in hardware compositions [34, 40] or components such as accelerometers [13, 42], gyroscopes [2], microphones [11, 48], speakers [47], cameras [14, 29], Bluetooth implementation [1], or some of them in combination [4, 21]. The advantage of hardware-based device fingerprinting is that fingerprints are generated essentially from manufacture discrepancies, which can remain stable during the lifecycle of the device and are difficult to mimic.  \n\nEMI Leakage Based Side-channels. The use of EMI leakage as a side-channel has been widely investigated. This work [17] extracts the key of RSA software implementation on a Lenovo laptop using a near-field magnetic probe with a frequency around $100k H z$ . Vaucelle et al. [43] detect the existence of ambient electromagnetic fields using a magnetometer bracelet with a frequency of up to 50 $k H z$ . DOSE [9] detects the usage of electrical appliances by monitoring device EMI radiations with an expensive EMI measurement equipment. Magnifisense [44] recognizes the electrical appliance usage using a wrist-worn magnetic sensor and a set of data acquisition device, with a sampling rate of 16-bit resolution at 44.1 $k H z$ . ZOP [8] utilizes electromagnetic emanations generated by computing systems during program execution to track a program’s execution path and generate profiling information.  \n\nDeMiCPU is inspired by the aforementioned work and utilizes the natural discrepancies existing in CPU modules. Given the fact that a CPU module is indispensable for almost all mobile or smart devices, DeMiCPU makes a more universal method compared with aforementioned built-in sensor based approaches.  \n\n# 9 CONCLUSION AND FUTURE WORK  \n\nIn this paper, we propose DeMiCPU, an effective device fingerprinting approach utilizing the unique features of magnetic induction (MI) signals generated from CPU modules, as a result of hardware discrepancy. We evaluate DeMiCPU with 90 mobile devices, including 70 laptops and 20 smartphones. The results show that DeMiCPU can achieve $99.1\\%$ precision and recall on average and $98.6\\%$ precision and recall for 30 identical devices, with a fingerprinting time of $0.6s$ . Both precision and recall can be further improved to $99.9\\%$ with multi-round fingerprinting.  \n\nFuture directions include exploring a larger study to confirm the scalability of DeMiCPU.  \n\n# ACKNOWLEDGMENTS  \n\nWe thank all anonymous reviewers for their insightful comments on this paper. This work is supported by China NSFC Grant 61702451, ZJNSF Grant LGG19F020020, and the Fundamental Research Funds for the Central Universities 2019QNA4027.  \n\n# REFERENCES  \n\n[1] Aksu, H., Uluagac, A. S., and Bentley, E. Identification of wearable devices with bluetooth. IEEE Transactions on Sustainable Computing (2018). [2] Baldini, G., Steri, G., Dimc, F., Giuliani, R., and Kamnik, R. Experimental identification of smartphones using fingerprints of built-in micro-electro mechanical systems (mems). Sensors 16, 6 (2016), 818.   \n[3] Bellovin, S. M., and Merritt, M. Cryptographic protocol for secure communications, Aug. 31 1993. US Patent 5,241,599.   \n[4] Bojinov, H., Michalevsky, Y., Nakibly, G., and Boneh, D. Mobile device identification via sensor fingerprinting. arXiv preprint arXiv:1408.1416 (2014). [5] Brik, V., Banerjee, S., Gruteser, M., and Oh, S. Wireless device identification with radiometric signatures. In MobiCom (2008), ACM, pp. 116–127.   \n[6] Brown, G. FEAST, January 2017. https://github.com/Craigacp/FEAST. [7] Bullock, J. LibXtract, July 2014. http://jamiebullock.github.io/LibXtract/ documentation/. [8] Callan, R., Behrang, F., Zajic, A., Prvulovic, M., and Orso, A. Zero-overhead profiling via em emanations. In ISSTA (2016), ACM, pp. 401–412. [9] Chen, K.-Y., Gupta, S., Larson, E. C., and Patel, S. Dose: Detecting user-driven operating states of electronic devices from a single sensing point. In PerCom (2015), IEEE, pp. 46–54.   \n[10] Cleveland, T. L. Bi-directional power system for laptop computers. In APEC (2005), vol. 1, IEEE, pp. 199–203.   \n[11] Das, A., Borisov, N., and Caesar, M. Do you hear what i hear?: Fingerprinting smart devices through embedded acoustic components. In CCS (2014), ACM, pp. 441–452.   \n[12] Desmond, L. C. C., Yuan, C. C., Pheng, T. C., and Lee, R. S. Identifying unique devices through wireless fingerprinting. In WiSec (2008), ACM, pp. 46–55.   \n[13] Dey, S., Roy, N., Xu, W., Choudhury, R. R., and Nelakuditi, S. Accelprint: Imperfections of accelerometers make smartphones trackable. In NDSS (2014).   \n[14] Dirik, A. E., Sencar, H. T., and Memon, N. Digital single lens reflex camera identification from traces of sensor dust. IEEE Transactions on Information Forensics and Security 3, 3 (2008), 539–552.   \n[15] Franklin, J., McCoy, D., Tabriz, P., Neagoe, V., Randwyk, J. V., and Sicker, D. Passive data link layer 802.11 wireless device driver fingerprinting. In USENIX Security (2006), vol. 3, pp. 16–89.   \n[16] Gartner. Gartner Forecasts Flat Worldwide Device Shipments Until 2018, January 2017. http://www.gartner.com/newsroom/id/3560517.   \n[17] Genkin, D., Pachmanov, L., Pipman, I., and Tromer, E. Stealing keys from pcs using a radio: Cheap electromagnetic attacks on windowed exponentiation. In CHES (2015), Springer, pp. 207–228.   \n[18] Getz, R., and Moeckel, B. Understanding and eliminating emi in microcontroller applications. National Semiconductor (1996).   \n[19] Geurts, P., Ernst, D., and Wehenkel, L. Extremely randomized trees. Machine learning 63, 1 (2006), 3–42.   \n[20] Hall, J., Barbeau, M., and Kranakis, E. Radio frequency fingerprinting for intrusion detection in wireless networks. IEEE Transactions on Defendable and Secure Computing 12 (2005), 1–35.   \n[21] Hupperich, T., Hosseini, H., and Holz, T. Leveraging sensor fingerprinting for mobile device authentication. In Detection of Intrusions and Malware, and Vulnerability Assessment. Springer, 2016, pp. 377–396.   \n[22] Instrument, T. Integrated Fluxgate Magnetic Sensor IC for Open-Loop Applications, March 2016. https://www.ti.com/product/DRV425.   \n[23] Instruments, T. MSP430F5529 LaunchPad Development Kit, April 2017. http: //www.ti.com/lit/ug/slau533d/slau533d.pdf.   \n[24] Jain, A. K., Hong, L., Pankanti, S., and Bolle, R. An identity-authentication system using fingerprints. IEEE 85, 9 (1997), 1365–1388.   \n[25] Keysight. U2541A 250kSa/s USB Modular Simultaneous Data Acquisition, June 2017. https://tinyurl.com/yb5r768y.   \n[26] Kohno, T., Broido, A., and Claffy, K. C. Remote physical device fingerprinting. IEEE Transactions on Dependable and Secure Computing 2, 2 (2005), 93–108.   \n[27] Langley, L. E. Specific emitter identification (sei) and classical parameter fusion technology. In WESCON (1993), IEEE, pp. 377–381.   \n[28] Le Sueur, E., and Heiser, G. Dynamic voltage and frequency scaling: The laws of diminishing returns.   \n[29] Lukas, J., Fridrich, J., and Goljan, M. Digital camera identification from sensor pattern noise. IEEE Transactions on Information Forensics and Security 1, 2 (2006), 205–214.   \n[30] Marr, D., Binns, F., Hill, D., Hinton, G., Koufaty, D., et al. Hyper-threading technology in the netburst® microarchitecture. Hot Chips (2002).   \n[31] Mondri, R., and Bitan, S. Inspected secure communication protocol, Sept. 1 2009. US Patent 7,584,505.   \n[32] Nguyen, K. T., Laurent, M., and Oualha, N. Survey on secure communication protocols for the internet of things. Ad Hoc Networks 32 (2015), 17–31.   \n[33] Pang, J., Greenstein, B., Gummadi, R., Seshan, S., and Wetherall, D. 802.11 user fingerprinting. In MobiCom (2007), ACM, pp. 99–110.   \n[34] Radhakrishnan, S. V., Uluagac, A. S., and Beyah, R. Gtid: A technique for physical device and device type fingerprinting. IEEE Transactions on Dependable and Secure Computing 12, 5 (2015), 519–532.   \n[35] Ratha, N. K., Bolle, R. M., Pandit, V. D., and Vaish, V. Robust fingerprint authentication using local structural similarity. In WACV (2000), IEEE, pp. 29–34.   \n[36] Remley, K., Grosvenor, C. A., Johnk, R. T., Novotny, D. R., Hale, P. D., McKinley, M., Karygiannis, A., and Antonakakis, E. Electromagnetic signatures of wlan cards and network security. In ISSPIT (2005), IEEE, pp. 484–488.   \n[37] Solomon, D. A., Russinovich, M. E., and Ionescu, A. Windows internals. Microsoft Press, 2009.   \n[38] Suleiman, D., Ibrahim, M., and Hamarash, I. Dynamic voltage frequency scaling (dvfs) for microprocessors power and energy reduction. In ICEEE (2005).   \n[39] Travers, M. Cpu power consumption experiments and results analysis of intel i7-4820k.   \n[40] Uluagac, A. S., Radhakrishnan, S. V., Corbett, C., Baca, A., and Beyah, R. A passive technique for fingerprinting wireless devices with wired-side observations. In CNS (2013), IEEE, pp. 305–313.   \n[41] Ureten, O., and Serinken, N. Wireless security through rf fingerprinting. Canadian Journal of Electrical and Computer Engineering 32, 1 (2007), 27–33.   \n[42] Van Goethem, T., Scheepers, W., Preuveneers, D., and Joosen, W. Accelerometer-based device fingerprinting for multi-factor mobile authentication. In ESSoS (2016), Springer, pp. 106–121.   \n[43] Vaucelle, C., Ishii, H., and Paradiso, J. A. Cost-effective wearable sensor to detect emf. In CHI (2009), ACM, pp. 4309–4314.   \n[44] Wang, E. J., Lee, T.-J., Mariakakis, A., Goel, M., Gupta, S., and Patel, S. N. Magnifisense: Inferring device interaction using wrist-worn passive magnetoinductive sensors. In UbiComp (2015), ACM, pp. 15–26.   \n[45] Wikipedia. Ampère’s circuital law, May 2018. https://en.wikipedia.org/wiki/ Amp%C3%A8re%27s_circuital_law.   \n[46] Yen, T.-F., Xie, Y., Yu, F., Yu, R. P., and Abadi, M. Host fingerprinting and tracking on the web: Privacy and security implications. In NDSS (2012).   \n[47] Zhou, Z., Diao, W., Liu, X., and Zhang, K. Acoustic fingerprinting revisited: Generate stable device id stealthily with inaudible sound. In CCS (2014), ACM, pp. 429–440.   \n[48] Zou, L., He, Q., and Wu, J. Source cell phone verification from speech recordings using sparse representation. Digital Signal Processing 62 (2017), 125–136.  \n\n# A APPENDIX  \n\n# A.1 Experimental Device  \n\nTable 3: Experimental devices and their detailed specifications. A total of 90 devices are used, including 70 laptops and 20 smartphones. Among them, 1-30, 31-33, 50-51, 84-85 and 88-89 are of the same model and OS respectively.  \n\n∗BVK $=$ Beside Volume Key   \n\n\n<html><body><table><tr><td rowspan=\"2\">No.</td><td rowspan=\"2\">Manuf.</td><td rowspan=\"2\">Model</td><td rowspan=\"2\">OS</td><td colspan=\"4\">CPU Parameters</td></tr><tr><td>Model</td><td>Core Number</td><td>rThread Number</td><td>Test Point</td></tr><tr><td>1-30</td><td>Lenovo</td><td>ThinkPad T430</td><td>Win 7</td><td>i5-3320M</td><td>2</td><td>4</td><td>S</td></tr><tr><td>31-33</td><td>Lenovo</td><td>ThinkPad T440p</td><td>Win 7</td><td>i5-4210M</td><td>2</td><td>4</td><td>S</td></tr><tr><td>34</td><td>Lenovo</td><td>G480</td><td>Win 7</td><td>i5-3210M</td><td>2</td><td>4</td><td>R</td></tr><tr><td>35</td><td>Lenovo</td><td>G480</td><td>Win 10</td><td>i5-3210M</td><td>2</td><td>4</td><td>R</td></tr><tr><td>36</td><td>Lenovo</td><td>ThinkPad X201</td><td>Win 10</td><td>i5-540M</td><td>2</td><td>4</td><td>F6</td></tr><tr><td>37</td><td>Lenovo</td><td>ThinkPad T440</td><td>Debian</td><td>i7-4500U</td><td>2</td><td>4</td><td>N</td></tr><tr><td>38</td><td>Lenovo</td><td>ThinkPad W520</td><td>GNOME</td><td>i7-2760QM</td><td>4</td><td>8</td><td>E</td></tr><tr><td>39</td><td>Lenovo</td><td>ThinkPad Edge E431</td><td>Win 10</td><td>i7-3632QM</td><td>4</td><td>8</td><td>S</td></tr><tr><td>40</td><td>Lenovo</td><td>ThinkPad Edge E530</td><td>Win 10</td><td>i5-3210M</td><td>2</td><td>4</td><td>S</td></tr><tr><td>41</td><td>Lenovo</td><td>IdeaPad Y470</td><td>Win 7</td><td>i5-2450M</td><td>2</td><td>4</td><td>E</td></tr><tr><td>42</td><td>Lenovo</td><td>IdeaPad Y485</td><td>Win 7</td><td>A8-4500M</td><td>4</td><td>4</td><td>F5</td></tr><tr><td>43</td><td>Lenovo</td><td>Yoga2 13</td><td>Win 10</td><td>i5-4210U</td><td>2</td><td>4</td><td>F4</td></tr><tr><td>44</td><td>Lenovo</td><td>Yoga 710</td><td>Win 10</td><td>i5-6200U</td><td>2</td><td>4</td><td>0</td></tr><tr><td>45</td><td>Lenovo</td><td>U430P</td><td>Win 10</td><td>i5-4200U</td><td>2</td><td>4</td><td>F1</td></tr><tr><td>46</td><td>Lenovo</td><td>Erazer Z410</td><td>Win 10</td><td>i7-4702MQ</td><td>4</td><td>8</td><td>6</td></tr><tr><td>47</td><td>Lenovo</td><td>E47a</td><td>Win 7</td><td>i5-2520M</td><td>2</td><td>4</td><td>S</td></tr><tr><td>48</td><td>Lenovo</td><td>X200 7455</td><td>GNOME</td><td>Intel P8600</td><td>2</td><td>2</td><td>F</td></tr><tr><td>49</td><td>Lenovo</td><td>R720</td><td>Win 10</td><td>i5-7300HQ</td><td>4</td><td>4</td><td>7</td></tr><tr><td>50-51</td><td>Apple</td><td>MacBook Air A1466</td><td>OS x</td><td>i5-4260U</td><td>2</td><td>4</td><td>W&E</td></tr><tr><td>52</td><td>Apple</td><td>MacBook Pro A1707</td><td>OS x</td><td>i7-6920HQ</td><td>4</td><td>8</td><td>W&E</td></tr><tr><td>53</td><td>Apple</td><td>MacBook ProA1502</td><td>OS x</td><td>i5-4278U</td><td>2</td><td>4</td><td>C</td></tr><tr><td>54</td><td>Dell</td><td>Inspiron N4050</td><td>Win 7</td><td>i3-2350M</td><td>2</td><td>4</td><td>F</td></tr><tr><td>55</td><td>Dell</td><td>Inspiron N5110</td><td>Win 7</td><td>i5-2450M</td><td>2</td><td>4</td><td>F</td></tr><tr><td>56</td><td>Dell</td><td>Inspiron 14 7460</td><td>Win 10</td><td>i5-7200U</td><td>2</td><td>4</td><td>6</td></tr><tr><td>57</td><td>Dell</td><td>Inspiron15R5520</td><td>Win 10</td><td>i5-3210M</td><td>2</td><td>4</td><td>Fn</td></tr><tr><td>58</td><td>Dell</td><td>Inspiron 15 7559</td><td>Win 10</td><td>i5-6300HQ</td><td>4</td><td>4</td><td>F6</td></tr><tr><td>59</td><td>Dell</td><td>Latitude E4300</td><td>Win XP</td><td>Intel SP9400</td><td>2</td><td>2</td><td>F</td></tr><tr><td>60</td><td>Dell</td><td>Latitude E7440</td><td>Win 10</td><td>i5-4200U</td><td>2</td><td></td><td>E&R</td></tr><tr><td>61</td><td>Dell</td><td>XPS13</td><td>Win 10</td><td>i5-3317U</td><td>2</td><td>4</td><td>6</td></tr><tr><td>62</td><td>Dell</td><td>XPS14 L421X</td><td>Win 10</td><td>i7-3537U</td><td>2</td><td>4</td><td>4</td></tr><tr><td>63</td><td>Asus</td><td>Eee PC 1201HA</td><td>Win 7</td><td>Intel Z520</td><td>1</td><td>4</td><td>A</td></tr><tr><td>64</td><td>Asus</td><td>N46V</td><td>Win 8.1</td><td>i5-3210M</td><td>2</td><td>2</td><td>B&N</td></tr><tr><td>65</td><td>Asus</td><td>X450EI323VC-SL</td><td>Win 10</td><td>i5-3230M</td><td>2</td><td>4</td><td>F</td></tr><tr><td>66</td><td>Acer</td><td>V5-471G</td><td>Win 7</td><td>i5-3337U</td><td>2</td><td>4</td><td>D</td></tr><tr><td>67</td><td>HP</td><td>TPN-Q173</td><td>Win 10</td><td>i5-6300HQ</td><td>4</td><td>4</td><td>Backspace</td></tr><tr><td>68</td><td>MSI</td><td>MS16-H8</td><td>Win 10</td><td>i7-6700HQ</td><td>4</td><td>4 8</td><td>Scroll Lock</td></tr><tr><td>69</td><td>Sony</td><td>SVT131A11T</td><td>Win 7</td><td>i5-3317U</td><td>2</td><td></td><td>X</td></tr><tr><td>70</td><td>Sony</td><td>SVT131A11T</td><td>Win 10</td><td>i5-3317U</td><td>2</td><td>4 4</td><td>X</td></tr><tr><td>71</td><td>Mi</td><td>5</td><td>Android 6.0</td><td>Snapdragon 820</td><td>4</td><td>4</td><td>BVK*</td></tr><tr><td>72</td><td>Mi</td><td>5S</td><td>Android 6.0</td><td>Snapdragon 820</td><td>4</td><td>4</td><td>BVK*</td></tr><tr><td>73</td><td>Huawei</td><td>Honor 5X</td><td>Android 5.1</td><td>Snapdragon 616</td><td>8</td><td></td><td>BVK*</td></tr><tr><td>74</td><td>Huawei</td><td>Honor 8</td><td>Android 6.0</td><td>Kirin 950</td><td></td><td>8</td><td>BVK*</td></tr><tr><td>75</td><td>Huawei</td><td>Honor V8</td><td>Android 6.0</td><td>Kirin 950</td><td>8 8</td><td>8</td><td>BVK*</td></tr><tr><td>76</td><td>Huawei</td><td>P9</td><td>Android 6.0</td><td>Kirin 955</td><td></td><td>8</td><td>BVK*</td></tr><tr><td>77</td><td>LG</td><td>Nexus 5</td><td>Android 4.4</td><td>Snapdragon 800</td><td>8</td><td>8</td><td>BVK*</td></tr><tr><td>78</td><td>LG</td><td>Nexus 5X</td><td>Android 6.0</td><td>Snapdragon 808</td><td>4 4</td><td>4</td><td>BVK*</td></tr><tr><td>79</td><td>Vivo</td><td>X7</td><td>Android 5.1</td><td>Snapdragon 652</td><td>4</td><td>4 4</td><td>BVK*</td></tr><tr><td>80</td></table></body></html>\n\n•BPK $=$ Beside Power Key  "}