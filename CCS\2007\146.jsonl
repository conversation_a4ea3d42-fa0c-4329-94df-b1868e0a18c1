{"text": "# Security-by-Contract for Web Services  \n\nor How to Trade Credentials for Services∗  \n\n<PERSON>i Fabio Massacci Department of Information and Communication Technologies University of Trento, Italy surname $@$ dit.unitn.it  \n\n# ABSTRACT  \n\nThe classical approach to access control of Web Services is to present a number of credentials for the access to a service and possibly negotiate their disclosure using a suitable negotiation protocol and a policy to protect them.  \n\nIn practice a “Web Service” is not really a single service but rather a set of services that can be accessed only through a suitable conversation. Further, in real-life we are often willing to trade the disclosure of personal attributes (frequent flyer number, car plate or AAA membership etc.) in change of additional services and only in a particular order.  \n\nIn this paper we propose a novel negotiation framework where services, needed credentials, and behavioral constraints on the disclosure of privileges are bundled together and that clients and servers have a hierarchy of preferences among the different bundles.  \n\nWhile the protocol supports arbitrary negotiation strategies we sketch two concrete strategies (one for the client and one for the service provider) that make it possible to successfully complete a negotiation when dealing with a cooperative partner and to resist attacks by malicious agent to ”vacuum-clean” the preference policy of the honest participant.  \n\n# Categories and Subject Descriptors  \n\nK.6.5 [Management of Computing and Information Systems]: Security and Protection; H.3.5 [Information Storage and Retrieval]: On-line Information Services— Web-based services, Commercial services; I.2.11 [Artificial Intelligence]: Distributed Artificial Intelligence—Multiagent systems  \n\n# General Terms  \n\nAlgorithms, Security, Theory  \n\n# Keywords  \n\nSecurity-by-Contract, Automated Trust Negotiation, Web Services  \n\n# 1. INTRODUCTION  \n\nThe basic tenet of Service Oriented Computing is the possibility of building distributed applications on the Web by using Web Services (WS) as basic building blocks. If the set of related functionalities is significantly large and can only be accessed according a suitable work-flow then we more appropriately speak of WS Conversations or Business Processes for WS.  \n\nControlling access to such services has become a key issue because services are distributed, and might also be controlled by different entities. Indeed, one often speak of policy-based access control of services (e.g. [18, 6, 13] or the large number of papers appeared in the IEEE Policy Workshop series and the ACM SACMAT series). The intuition is that access to services and resources is automatically derived from policies that are deployed on the point of service. Policies might specify the various attributes (often called privileges) that the individual clients might need to show in order to access to each service.  \n\nIn order to identify the holders of the appropriate set of privileges across the web cryptographic credentials can be put in place thus creating what is called a Privilege Management Infrastructure or PMI [5] or, with a slightly misleading terminology, a trust management system [3, 19]. Credentials needed to access the system might be presented in push or pull mode or discovered interactively [11].  \n\nOne of the key issue, as pointed out initially by Yu, Winslett and Seamons [20], is the problem of gradually building trust between two unknown parties so that the client doesn’t empty its wallet on the counter and the server doesn’t list an encyclopedia of policies rules (sometimes highly sensitive, most likely highly irrelevant).  \n\nYet, Yu, Winslett, and Seamons [20], and many other works [16, 4, 12, 11] where the need of credentials is gradually disclosed to the client, take for granted that a client always starts the negotiation by requesting access to a resource.  \n\nInstead, as pointed out by Mecella et al. [14]:  \n\nWhile many in the literature treated Web services as a set of independent single operations, interacting with real world Web services involves generally a sequence of invocations of several of their operations, referred to as conversation. A simple example is a bookstore Web service; buying a book involves generally searching for the book, browsing the details and reviews about this book, adding the book to the shopping cart, checking out, paying, etc.  \n\nIt is therefore important to consider the access control and negotiation issues for the overall WS conversation. As noted by Koshutanski and Massacci [10] it might well be that the conversation takes different routes, therefore changing the set of needed credentials. Keeping up with the book example, if we decided to send the books as gift then we only need to specify the address of the credit card holder and the address of the gift recipient. Our address is not needed.  \n\nMecella et al. [14] have provided an access control model and a trust-negotiation scheme for WS where such conversational aspect is taken care of. While they take full care of the behavioral aspect of WS, their negotiation protocol still sticks to the progressive disclosure of credentials while keeping the set of requested services fixed.  \n\nWhat is missing is a typical feature of real-life negotiations: we are usually willing to trade off disclosure of our security attributes for (additional) services. Back to the book shopping example: we might not be willing to disclose our Frequent Flyer card for buying a book. But we might be willing to do so if the system tells us that travel books gets a $10\\%$ discount if a Frequent Flyer card is disclosed.  \n\n# 1.1 The Contribution of this Paper  \n\nIn this paper we propose a negotiation framework that considers not only the negotiation of credentials but also the corresponding negotiation of services and the behavioral constraints on the disclosures of credentials depending on the business process.  \n\nIn a nutshell we envisage that services and privileges are bundled together and that clients and servers have a hierarchy of preferences among the different bundles.  \n\nFrom the perspective of a client, say Alice, we assume that she has a ranked list of services and for each set of services in this rank she has a ranked list of security attributes or digital credentials that she is willing to disclose. For each set of attributes, she also has a behavioral model that dictates how she will disclose her credentials as soon as she uses the services. For example Alice might be willing to disclose her credit card in order to buy a book and her frequent flier number in order to have a discount. She might be willing to disclose her car plate number in order to buy technical manuals from the car manufacturer only available to holder of a specific brand of cars. A similar tree-like structure might be present at Bob’s server.  \n\nNotice that we do not assume that the ranked list is complete (i.e. in the lists there are all possible sets) because it will not be realistic.  \n\nWhile negotiation protocols in the agent community [15] can assume cooperative agents, such assumption is not acceptable in a security setting. So we have designed the framework to take into account both cooperative and malicious agents.  \n\nIn the next Section we introduce the overall framework which also defines the type of messages participants can exchange, the type of information messages will contain and finally we specify the general protocol flow (§3). In order to show that the flow is actually executable we define two possible strategies for the client and the service provider ( 4). Such strategies are at the same time cooperative (they do negotiate a mutually liked bundle of services and privileges if one exists) and robust (they resist to potentially malicious parties that would only like to discover the preferences of their opponents). We conclude the paper with a discussion on related works and highlighting the main contributions of our proposal (§5).  \n\n# 2. THE NEGOTIATION FRAMEWORK  \n\nThe basic idea behind our proposal is a combination of the idea of programming-by-contract originally introduced by B. Meyer for object oriented software and later applied to WS [9] and extended to security-by-contract as proposed by Dragoni et al. [7]. Similar ideas are also present in the definition of the WS security behavior by Mecella et al. [14]. In a nutshell a web service provider is offering a contract: I’ll grant you the services $s_{1}$ , $\\cdots\\cdot s_{n}$ , but in change I want you to show me that you have security attributes (or privileges) $p_{1}$ , $\\cdots\\cdot p_{n}$ . Further, I’ll ask you to show me your credentials according the following dynamic behavior $\\omega_{1}$ where e.g. possession of privilege $p_{i}$ is asked before service $s_{j}$ can be accessed.  \n\nOn the other side a client is making a counter-proposal: I want to use your services $s_{1}^{\\prime},\\ldots s_{n}^{\\prime}$ , and I’m only willing to give you evidence that I have security privileges $p_{1}^{\\prime}$ , $\\cdots p_{m}^{\\prime}$ . Further, I’m going to accept showing my credentials only according the following dynamic behavior $\\omega_{1}^{\\prime}$ where e.g. I’m willing to show possession of privilege $p_{i}$ but only is I’m asking service $s_{1}$ or $s_{2}$ .  \n\nIn our setting security attributes will usually be digitally signed credentials in X509 format [5] and requests for credentials can be provided by SAML tokens as proposed by Kohustanski and Massacci [11], but it is not necessarily the case. For example one can speculate that certain services are only available to mobile users and one resorts to techniques belonging to Mobile IPv6 security [2]. On the other side services could be described by WSLD or semantic web services in OWL.  \n\nFor the formal theory that we develop in the rest of the paper, we assume them to be atomic predicates as in Yu, Winslett, and Seamons [20] and Koshutanski and Massacci [11]. Further, instead of referring to them as ”security attributes or digital credentials to be disclosed” we will simply refer to them as privileges (from PMI).  \n\nDefinition 2.1. Let $\\mathcal{P}$ be a set of atomic proposition $p$ denoting security privileges and let $s$ be a set of atomic propositions s (disjoint from $\\mathcal{P}$ ) denoting services.  \n\nIf contract represents the security behavior of a WS the temptation would be to make such contractual claims arbitrarily complex. Since we argue that contract should be matched and negotiated by the WS on-the-fly a complex procedure is likely to defy the spirit of our proposal. So we suggest to follow the ideas behind a number of papers [17, 14, 7] and represent the security behavior as an automaton.  \n\nDefinition 2.2. The set of security behaviors $\\Omega$ is a $\\mathit{\\Omega}_{\\mathrm{\\iint}}$ - nite state automaton whose actions are drawn from $\\mathcal{P}$ and $\\boldsymbol{S}$ .  \n\nFrom now on, let us refer to a service client as Executor and to a service provider as Provider. We have identified the following abstract operator ( $\\Omega^{P}$ and $\\Omega^{E}$ indicate respectively the behavior of Provider and Executor):  \n\n• [Traces Operator] $\\mathcal{T}=\\mathsf{T r a c e s}\\left(\\Omega\\right)$ It returns the set $\\tau$ of all the possible sequences of actions that can be performed according to the security behavior $\\Omega$ .   \n• [Match Operator $\\boldsymbol{\\Xi}\\boldsymbol{\\rfloor}\\boldsymbol{\\Omega}^{E}\\subseteq\\boldsymbol{\\Omega}^{P}$ It returns 1 if the behavior specified by $\\Omega^{E}$ is among the behaviors that are allowed by $\\Omega^{P}$ , $0$ otherwise. In terms of the Traces operator: $\\Omega^{E}\\subseteq\\Omega^{P}\\Leftrightarrow{\\mathsf{T r a c e s}}\\left(\\Omega^{E}\\right)\\subseteq{\\mathsf{T r a c e s}}\\left(\\Omega^{P}\\right)$  \n\nFrom now on we will say that the security behavior $\\Omega^{E}$ of Executor matches the security behavior $\\Omega^{P}$ of Provider if and only if $\\Omega^{E}\\subseteq\\Omega^{P}$ returns true. A detailed discussion on such operator and its possible implementation is outside the scope of the paper. However, interested readers can find in [7] detailed algorithms for matching security behaviors.  \n\nWe assume agents have preferences over different negotiation alternatives, or proposals.  \n\nDefinition 2.3. Let $\\left\\langle S^{A},P^{A},\\Omega^{A}\\right\\rangle$ be a tuple representing $a$ proposal of a generic agent $A$ , where $S^{A}\\subset S$ is a set of services, $P^{A}\\subset{\\mathcal{P}}$ a set of privileges and $\\Omega^{A}$ a security behavior.  \n\nDefinition 2.4. Let $P S^{A}$ be the set of tuples $\\langle S^{A},P^{A},\\Omega^{A}\\rangle$ representing the policy space of $A$ .  \n\nEach Executor $\\mathsf{E}$ , resp. Provider $\\mathsf{P}$ , will have his own policy spaces $P S^{E}$ , resp. $P S^{P}$ . We also assume that preferences are specified by means of a partial order $\\ll_{E}$ over $P S^{E}$ (resp. $\\ll_{P}$ over $P S^{P}$ ).  \n\nAccording to the above definitions, from now on we will use $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ and $\\left<S^{P},P^{P},\\Omega^{P}\\right>$ to indicate a proposal of Executor and Provider, respectiv ely.  \n\nDefinition 2.5. $\\langle S,P,\\Omega\\rangle$ is an acceptable proposal for an Executor $\\mathsf{E}$ if there exists an $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ in $P S^{E}$ such that $S_{=}^{\\supset}S^{E}$ , $P\\subseteq P^{E}$ , and $\\Omega\\underline{{\\sqsubseteq}}~\\Omega^{E}$ . $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ will be $a$ solution for the Executor $\\mathsf{E}$ .  \n\nDefinition 2.6. $\\langle S,P,\\Omega\\rangle$ is an acceptable proposal for a Provider $\\mathsf{P}$ if there exists a $\\langle S^{P},P^{P},\\Omega^{P}\\rangle$ in $P S^{P}$ such that $S^{P}\\supseteq S$ , $P^{P}\\subseteq P$ , and $\\Omega^{P}\\subseteq\\Omega$ . $\\left\\langle{S^{P},P^{P},\\Omega^{P}}\\right\\rangle$ will be $a$ solution for the Provider $\\mathsf{P}$ .  \n\nNotice that an acceptable proposal for a provider is not necessary an acceptable proposal for an executor.  \n\nDefinition 2.7. $\\langle S,P,\\Omega\\rangle$ is the best solution for the agent $A$ if:  \n\n2. there is no another solution $\\langle{S^{\\prime},P^{\\prime},\\Omega^{\\prime}}\\rangle$ such that $\\langle S^{\\prime},P^{\\prime},\\Omega^{\\prime}\\rangle{\\ll}_{A}$ $\\langle S,P,\\Omega\\rangle$ .  \n\nTable 1 shows the type of messages agents can exchange during the overall negotiation protocol as well as their meaning. Essentially, agents send or accept a proposal by means of the propose and accept message, respectively. The ask message is used by an Executor to start a negotiation and the failure and no more proposals messages are used for terminating the negotiation when a protocol violation occurs or an agent cannot proceed for some reason (see Section 3 for details).  \n\nTable 1: Messages and their Meaning   \n\n\n<html><body><table><tr><td>Message</td><td>Meaning</td></tr><tr><td>ask(SE)</td><td>ExecutorasksProviderfor ser- vices SE</td></tr><tr><td>propose(SF, PF, ΩF)</td><td>Executorwantsatleastservices SE, gives at most privileges PP andaccepts atmosttobehave as QE.</td></tr><tr><td>propose(SP, PP, ΩP)</td><td>Provider offers services SP, re- quires at least privileges PP' and promises at most to behave as ΩP.</td></tr><tr><td>accept(SP, PF,QF)</td><td>Agent (Provider or Executor) accepts services SP, privileges PE , and security behavior QE</td></tr><tr><td>no_more_proposals(SE)</td><td>Agent (Provider or Executor) has no more proposals for the negotiation of SE</td></tr><tr><td>failure</td><td>A protocol violation has oc- curred or the negotiation termi- nates unsuccessfully.</td></tr></table></body></html>  \n\nTo guarantee safety and timely termination of trust negotiation no matter what policies the parties possess, our protocol requires the negotiation strategies used with it to enforce the following conditions throughout negotiations.  \n\n# Proposal Conditions.  \n\nLet $\\mathbb{P}_{s e n t}^{E}$ , $\\mathbb{P}_{s e n t}^{P}$ be the set of proposals sent by Executor and Provider at a given point in the negotiation process, respectively. To send a propose $(S^{E},{\\cal P}^{\\bar{E}},\\Omega^{E})$ (resp., $\\mathsf{p r o p o s e}(\\bar{S}^{P},~P^{\\bar{P}},~\\Omega^{P}))$ message, the following conditions must hold:  \n\n1. $S^{P}\\supseteq S^{E}$ From the point of view of Provider, this means that each proposal must contain at least the services required by Provider. For Executor, this means that it can not propose different services. To do this, Executor must exploit the ask message, restarting in this way the negotiation on a new set of services.   \n2. $(P^{E}\\neq\\emptyset)\\land(\\Omega^{E}\\neq\\emptyset)\\qquad((P^{P}\\neq\\emptyset)\\land(\\Omega^{P}\\neq\\emptyset))$ To avoid attacks that could cause the complete disclosure of a policy, both privileges and acceptable security behaviors must not be empty.   \n3. $\\left<S^{E},P^{E},\\Omega^{E}\\right>\\not\\in\\mathbb{P}_{s e n t}^{E}$ $(\\left<S^{P},P^{P},\\Omega^{P}\\right>\\notin\\mathbb{P}_{s e n t}^{P})$ A proposal can be sent at most once. Again, this condition prevents the full disclosure of an agent’s policy caused by possible attacks.  \n\nRemark. The above conditions guarantee attack resistance by requiring some progress in the negotiation process each time a proposal is sent by a negotiator. In other words, an agent gradually discloses its policy if and only if the other agent does the same, sending new proposals at each negotiation step (that is, each time a propose message is received).  \n\n# Acceptance Conditions.  \n\nLet $\\mathbb{P}_{r e c i v e d}^{E}$ , $\\mathbb{P}_{r e c e i v e d}^{P}$ be the set of proposals received by Executor and Provider at a given point in the negotiation process, respectively. To send an $\\mathsf{a c c e p t}(S^{P},P^{E},\\Omega^{E})$ message, the following conditions must hold:  \n\n• Executor: there exists $\\left\\langle{S}^{P},{P}^{P},{\\Omega}^{P}\\right\\rangle$ such that 1. $\\left<S^{P},P^{P},\\Omega^{P}\\right>\\in\\mathbb{P}_{r e c e i v e d}^{E}\\land$ 2. $\\left\\langle{S^{P},P^{P},\\Omega^{P}}\\right\\rangle$ is an acceptable proposal with solution $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$   \n• Provider: there exists $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ such that 1. $\\left<S^{E},P^{E},\\Omega^{E}\\right>\\in\\mathbb{P}_{r e c e i v e d}^{P}\\land$ 2. $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ is an acceptable proposal with solution $\\left\\langle{S^{P},P^{P},\\Omega^{P}}\\right\\rangle$  \n\nIn other words, the above conditions require that both Executor and Provider can accept only a received acceptable proposal. Note that we do not require that the acceptable proposal is the last received proposal, but only that it was received in the past. Indeed, we cannot force an agent to accept the first acceptable proposal he receives because this would prevent the agent to negotiate for a better solution.  \n\n# 3. PROTOCOL FLOW  \n\nThe intuition behind the protocol is that agents continuously exchange propose messages until an accept or failure message is sent by one party (terminating successfully or unsuccessfully the protocol). For the sake of readability and due to space limitations, we split the protocol flow in several Figures (1, 2, 3 and 4).  \n\nThe protocol works as follows:  \n\nExecutor starts the protocol sending the Provider a request of services $S^{E}$ by means of an $\\mathsf{a s k}\\big(S^{E}\\big)$ message (Figure 1).   \n• Provider can reply with one of the following two messages: – no more proposals $(S^{E})$ : Provider has no more proposals $\\left\\langle{S^{P},P^{P},\\Omega^{P}}\\right\\rangle$ for $S^{E}$ . In this particular case, this means that Provider does not have any bundle $S^{P}$ which contains $S^{E}$ . $-\\mathsf{p r o p o s e}(S^{P},P^{P},\\Omega^{P})$ : Provider offers the bundle $S^{P}$ (containing $S^{E}$ ), it requires at least privileges $P^{P}$ and it promises to behave at most as described by $\\Omega^{P}$ .  \n\n• When Executor receives a propose message, it can reply with one of the following messages:  \n\n$-\\mathsf{p r o p o s e}(S^{E},P^{E},\\Omega_{-}^{E})$ : Executor sends a new proposal for services $S^{E}$ . Note that this new proposal must follow the proposal conditions. $\\mathbf{\\Sigma}-\\mathsf{a c c e p t}(S^{P},P^{E},\\Omega^{E})$ : Executor accepts a Provider’s proposal and the protocol ends successfully. Note that the acceptance conditions must hold, so we are sure that Executor is accepting an acceptable proposal previously sent by Provider. We stress that the acceptable proposal could not be the last one made by Provider, since Executor could decide to postpone an accept message until it has no more counterproposals to send.  \n\n– failure: this message is sent if some protocol violation occurs, that is the received propose message does not satisfy the proposal conditions. The protocol ends unsuccessfully. – no more proposals $(S^{E})$ : the Executor has no more proposals for SE.   \n• When Executor receives a no more proposals $\\left(S^{E}\\right)$ message (Figure 2), it can reply with: $\\textrm{--}\\mathsf{a c c e p t}(S^{P},P^{E},\\Omega^{E})$ : Executor accepts an acceptable proposal previously received. Again, the acceptation conditions must hold and the protocols ends successfully. $-\\mathsf{a s k}\\bigl(S_{n e w}^{E}\\bigr)$ : Executor asks for a new set of services $S_{n e w}^{S}$ , restarting the protocol. – failure: a failure message is sent if Executor has no more services to ask for. The protocol ends unsuccessfully.   \n• When Provider receives a propose message the possible answers are the same as those of Executor, as shown in Figure 3.   \n• Finally, a Provider can reply to a no more proposals( $S^{E}$ ) message (Figure 4) with: $\\textrm{--}\\mathsf{a c c e p t}(S^{P},P^{E},\\Omega^{E})$ : subjected to the acceptation conditions already stated. The protocol ends successfully. – failure: if Provider has no solution for $S^{E}$ . In particular, this means that Provider didn’t receive any acceptable proposal from Executor. The protocol ends unsuccessfully.  \n\n# Protocol Termination..  \n\nThe negotiation ends successfully when an accept message is sent by an agent. Instead, the protocol ends unsuccessfully when an agent sends a failure message.  \n\n# 4. NEGOTIATION STRATEGIES  \n\nA Services-vs-Privileges Negotiation Strategy controls the exact content of messages, i.e. which proposals of the form $\\langle S,P,\\Omega\\rangle$ agent disclose and when disclose them. For the sake of readability and simplicity, in this Section we describe only the main ideas behind the Executor and Provider strategies, omitting technical and implementation details. The programs implement the protocol flow as well as the agents’ strategies we are going to discuss.  \n\nLet j, k, q be indexes referring to some sets $S$ , $P$ , $\\Omega$ , respectively. To describe an agent’s strategy, we assume agent’s policy is structured as shown in Figure 5. Basically, a policy is represented by a $S P\\Omega$ -structure composed by several $S P\\Omega$ -trees, that is trees having a bundle $S_{j}$ as root, then a level of privileges set $P_{j k}$ and finally a level of security rule sets $\\Omega_{j k q}$ . The $S P\\Omega$ -structure contains the policy space of an agent, say $A$ , and it is ordered according to the relation $\\ll A$ .  \n\nAn important remark is that, to describe the strategy of one party (Executor or Provider), we assume that its own policy is represented as a $S P\\Omega$ -structure, but we do not necessarily require the same for the other party. This because we consider the other party as a self interested agent which most reasonably will follow its own (best) strategy . In particular, we assume that one party does not know which policy’s representation and reasoning (i.e., search strategy in the policy search space) the other party will follow. The only information an agent knows of the other party is the set of messages it receives. The foregoing assumption has a significant impact on the design of an agent strategy, because knowing more information could improve the strategy by reducing the number of interactions.  \n\n![](/tmp/output/146_20250326161405/images/f118f01260e12634a0da6b62644816e99a1b9e2a9b86f05cdaa48fce5be7fb9c.jpg)  \nFigure 1: Trust Negotiation Protocol (part I)  \n\n![](/tmp/output/146_20250326161405/images/a9ecf697d696a1b848e144c59ce6edcdb632932b0c424cd4aa6d98ba3eb1d38c.jpg)  \nFigure 2: Executor: possible replies to a no more proposals message.  \n\n![](/tmp/output/146_20250326161405/images/9ab0c7ec436c46b00b39f7fbffe84fcb7f5ab29891432a463a51addebf7503a3.jpg)  \nFigure 3: Provider: possible replies to a propose message.  \n\n![](/tmp/output/146_20250326161405/images/bbd45bd453b2300aa75ef36c332d6b5545d03864bf7c64b4c801b3f6e0a32dec.jpg)  \nFigure 4: Provider: possible replies to a no more proposals message.  \n\n![](/tmp/output/146_20250326161405/images/4ff52fbd8aefdf7e44d98c6889bc258847eb252137f3960c5c0b6a2075272f40.jpg)  \nFigure 5: Agent’s Policy as SP Ω-structure.  \n\nOf course, given a negotiation protocol agents must follow, the number of possible strategies is almost limitless and the definition of a specific strategy usually depends on which properties the strategy wants to guarantee. In this paper, we have designed the agents’ strategies with two properties in mind.  \n\nProperty 4.1. (Sound Termination) Assuming cooperative and rational agents, if a solution exists then eventually agents will reach an agreement and the protocol will end successfully.  \n\nIn other words, if agent are cooperative and rational, then they will agree on a mutually liked bundle of services and privileges if one exists.  \n\nThe second property we want to guarantee is that, assuming self interested but not necessarily cooperative agents, strategies must be robust, that is they must resist to potentially malicious parties that would only like to discover the preferences of their opponents:  \n\nProperty 4.2. (Attack Resistance) Agents does not disclose their preferences to malicious parties.  \n\n# 4.1 Strategies  \n\nAccording to the protocol flow described in Section 3, Executor starts the negotiation sending Provider a request of services SjE and then it waits for Provider’s reply. It is reasonable to assume that Executor will start asking for his best bundle of services, i.e., S1E .  \n\nReceived a request for services $S^{E}$ , Provider searches in his $S P\\Omega$ -structure for the best bundle $S^{P}$ such that $S^{P}\\supseteq$ $S^{E}$ . If this is available, it replies with his best proposal, i.e. $\\left<S_{j}^{P},P_{j1}^{P},\\Omega_{j11}^{P}\\right>$ , otherwise it sends a no more proposals $(S^{E})$ message.  \n\nFrom this point on, agents negotiate by exchanging propose messages that satisfy the proposal conditions. According to the protocol flow, this exchange ends when one party accepts a proposal of the other party or some failure occurs. The strategies followed by both agents when they receive a propose message is summarized in the following Evaluate Proposal procedure, where parameters $S,P$ , Ωrepresent the received proposal and $\\mathrm{j}$ is the index of the current bundle of services $S_{j}$ :  \n\n1: procedure Evaluate $\\mathrm{PROPOSAL}(S,P,\\Omega,\\mathbf{j})$   \n2: Find a counterproposal for $S_{j}$   \n3: if counterproposal does not exist then   \n4: Find best proposal for $S_{j}$   \n5: Find a solution for $S_{j}$   \n6: Store solution if better than the previous one   \n7: if both proposal and solution exist then   \n8: if proposal $\\ll$ solution then   \n9: sendMsg(propose(proposal))   \n10: else   \n11: sendMsg(accept(solution)) // Success   \n12: else if proposal exists then   \n13: sendMsg(propose(proposal))   \n14: else if solution exists then   \n15: sendMsg(accept(solution)) // Success   \n16: else   \n17: sendMsg(no more proposals(Sj))  \n\nThe basic idea behind the above strategy is that, given a proposal sent by the other party, an agent searches in his $S P\\Omega$ -Structure for both a new proposal and a solution. If a solution is found, then it is stored if better than the old one. If both proposal and solution exist, then the agent will act following the best behavior according to his $S P\\Omega$ - structure, that is it will send a new proposal if better than the solution (proposal $\\ll$ solution in the $S P\\Omega$ -structure) or it will accept the solution otherwise (solution $\\ll$ proposal in the $S P\\Omega$ -structure). In this way we are sure that an agent will always act trying to get his best, but also that if a solution exists than sooner or later it will be proposed by a party and accepted by the other (as described in the protocol flow in Section 3).  \n\n# 4.1.1 Selection of a Proposal  \n\nAccording to the above strategy, an agent first searches for his best (not already proposed) counterproposal for $S_{j}$ , that is a proposal such that $P^{E}\\subset P^{P}$ . From the point of view of Executor, this means reducing the privileges requested by Provider, while for Provider this means asking for more privileges respect to those given by Executor. A sketch of the Find CounterProposal function of Provider follows, where Find Best Contract is a function that returns the best (not already proposed) contract under a given privilege set.  \n\n1: function Find CounterProposal $(P^{E},\\mathrm{j},\\mathrm{k})$   \n2: Search for $P_{j k}^{P}$ jPk such that P E ⊂ P jPk   \n3: if $P_{j k}^{P}\\neq\\emptyset$ then   \n4: $\\Omega_{j k q}^{P}\\Leftarrow$ Find Best Contract(j, k, 1)   \n5: if $\\Omega_{j k q}^{P}\\neq\\emptyset$ then   \n6: return(P jPk, ΩjPkq)   \n7: else   \n8: return(Find CounterProposal(P E, j, k+1))   \n9: else   \n10: return(∅, ∅)  \n\nAn example of counterproposal is shown in Figure 6. The algorithm visits the privileges sets in a BFS way from the left to the right (i.e., from the best to the worst privilege set) looking for the first $P_{j k}^{P}$ such that $P^{E}\\subset P_{j k}^{P}$ . Then it goes down to one level searching for the best (not already proposed) contract $\\Omega$ . If all the contracts have been already proposed, a new privilege set is searched starting from $P_{j k+1}^{P}$ . This is the case of $P_{11}$ in the Figure, since it is a counterproposal $(P_{11}\\supset P^{E}$ ), but it is not selected because all the contracts under it have been already sent (i.e., proposal $\\langle S_{1},P_{11},\\l_{-}\\rangle$ cannot be sent). Therefore, the best counterproposal is $\\langle S_{1},P_{13},\\Omega_{132}\\rangle$ (note also that $\\Omega_{131}$ can not be chosen because already proposed).  \n\n![](/tmp/output/146_20250326161405/images/0b42f19cfdf960905f97e5396b55d95ef4db18132ae3cd41d8a5e0739f5dd623.jpg)  \nFigure 6: Example of Counterproposal  \n\nIf a counterproposal is not available, then the agent searches for his best (not already sent) proposal for $S_{j}^{E}$ . An example of best proposal is shown in Figure 7, where the policy is the same as the one of the previous example (note that the best proposal is different from the counterproposal).  \n\nA sketch of the Find Best Proposal function of Provider follows.  \n\n1: function Find Best Proposal(j, k)  \n\n2: if P P $P_{j k}^{P}\\neq\\emptyset$ then   \n3: $\\Omega_{j k q}^{P}\\Leftarrow$ Find Best Contract(j, k, 1)   \n4: if $\\Omega_{j k q}^{P}\\neq\\emptyset$ then   \n5: return(P jPk, ΩjPkq)   \n6: else   \n7: return(Find Best Proposal(j, k+1))   \n8: else   \n9: return(∅, ∅)  \n\n![](/tmp/output/146_20250326161405/images/30df6640869df4c74bae3ea8d66ec36e6af61d353009950d49ab609146b27229.jpg)  \nFigure 7: Example of Best Proposal  \n\nIn both cases (counterproposal and best proposal), security rules are determined by searching for the best (not already proposed) security rule set $\\Omega$ , as shown in Figure 8. This is done by using the Find Best Contract function cited above. If all security rule sets $\\Omega$ under a privilege set $P$ have been already proposed, a new privilege set $P$ is searched (according to the current strategy, i.e. counterproposal or best proposal).  \n\n![](/tmp/output/146_20250326161405/images/1d60d45d3ea19ef6e09da8afb08d2ccda8fc3af9c36305522bc39c5ca35ae38f.jpg)  \nFigure 8: Selection of the Best Security Rule Set  \n\nRemark. At this stage we assume that agents negotiate in a cooperative way, i.e., proposing different privileges only if a counterproposal is not available.  \n\n# 4.1.2 Selection of a Solution  \n\nThe strategies exploited by Provider and Executor for finding a solution are basically the same, except for the search space they visit. Indeed, Executor just needs to search the solution in the $S P\\Omega$ -tree corresponding to the requested set of services $S^{E}$ , while Provider could need to search the solution in different $S P\\Omega$ -trees having as root a bundle $S^{P}\\supseteq S^{E}$ . The Provider must search for a solution in another $S P\\Omega$ -tree if it has not found any solution in the current $S P\\Omega$ -tree. Functions Find Solution in Tree and Find Solution implement the strategy for Provider while Executor uses only the function Find Solution in Tree.  \n\n1: function Find Solution in Tree( $P^{E}$ , ΩE, j, k)  \n\n2: Search for $P_{j k}^{P}:P_{j k}^{P}\\subseteq P^{E}$   \n3: if $P_{j k}^{P}\\neq\\emptyset$ then   \n4: $\\Omega_{j k q}^{P}\\Leftarrow\\mathrm{FIND\\mathrm{-}C o n r r R A C T}(\\Omega^{E},\\mathbf{j},\\mathbf{k},1)$   \n5: if $\\Omega_{j k q}^{P}\\neq\\emptyset$ then   \n6: $\\mathrm{return}(P_{j k}^{P},\\Omega_{j k q}^{P})$   \n7: else   \n8: return(Find Solution in Tree(P E,ΩE, j, k+1))   \n9: else   \n10: return(∅, ∅)  \n\n(P jPk, ΩjPkq) ⇐ Find Solution in Tree(P E, ΩE, j, k if $P_{j k}^{P}=\\emptyset$ then $S_{j}^{P}\\Leftarrow\\mathrm{FIND\\mathrm{-}B E S T\\mathrm{-}S E R V I C E}(S^{E},\\mathrm{j})$ $\\begin{array}{r l}&{\\mathbf i f S_{j}^{P}\\neq\\emptyset\\mathbf t h\\mathbf e n}\\ &{\\qquadP_{j k}^{P}\\gets\\mathrm{FIND\\mathrm{-}S O L U T I O N}\\big(S^{E},P^{E},\\Omega^{E},\\mathbf j,1\\big)}\\ &{\\mathrm{return}\\big(S_{j}^{P},P_{j k}^{P},\\Omega_{j k q}^{P}\\big)}\\end{array}$  \n\n12:   \n13:   \n14:   \n15:   \n16:   \n17:  \n\nStarting from an index $\\mathrm{j}$ in the $S P\\Omega$ -structure, the function Find Best Service searches for and returns the best set $S_{t}^{P}$ containing $S^{E}$ , with $\\ensuremath{\\mathrm{~t~}}\\geq\\ensuremath{\\mathrm{j}}$ . The function Find Contract searches for the best contract $\\Omega_{j k q}^{P}$ (under a privilege set $P_{j k}^{P}$ ) such that $\\Omega_{j k q}^{P}\\subseteq\\Omega^{E}$ .  \n\nThe search strategy of Find Solution in Tree is the same as the one exploited to find a counterproposal: the first $P_{j k}^{P}:P_{j k}^{P}\\subseteq P^{E}$ is searched in a BNF way and then the algorithm calls Find Contract to search for the best contract $\\Omega_{j k q}^{P}$ not already proposed such that $\\Omega_{j k q}^{P}\\subseteq\\Omega^{E}$ . If the contract is not found under $P_{j k}^{F}$ , then the search restarts from $P_{j k+1}^{P}$ .  \n\nExample 4.1. In the negotiation of Figure 9, a solution exists but Provider does not accept it until it receives a no more proposals message from Executor. Indeed, Provider always sent a proposal because better than that solution.  \n\n![](/tmp/output/146_20250326161405/images/8666a62904bd7e7ec843d257d8bac30fac97da9eb6389588180fd56c0e9732d9.jpg)  \nFigure 9: Example 4.1  \n\nExample 4.2. In this Example, Provider receives an acceptable proposal from Executor and accepts it because the solution it finds is better than any possible remaining proposals.  \n\n![](/tmp/output/146_20250326161405/images/02a9f103c2aeba10c18dc0380c43e0e03db74550d106f178742c440d852e38d8.jpg)  \nFigure 10: Example 4.2  \n\n# 4.1.3 Ensuring Strategy’s Properties  \n\nThe foregoing strategy ensures the Sound Termination property by searching a solution each time the agent receives a proposal. If the solution is found then it is stored if better than the old one. In this way we are sure that, if a solution exists, then sooner or later the agent will accept it. Note that this could happen at the latest when the agent receives a no more proposals message from the other party (as described by the protocol flow in Section 3). Indeed, before the receipt of this message, the agent will always send a proposal if better than the current solution, trying to get his best.  \n\nThe Attack Resistance property is ensured by checking the proposal conditions each time the agent receives a propose message. We stress that those conditions guarantee attack resistance by requiring some progress in the negotiation process. Therefore, the agent gradually discloses its policy if and only if the other agent does the same, sending new proposals at each negotiation step. As an example, the following Check Proposal function is used by Provider to perform this check, where $\\left\\langle S^{E},P^{E},\\Omega^{E}\\right\\rangle$ represents the received proposal and $S_{j}^{P}$ is the current bundle of services of Provider. Provider will continue the negotiation if and only if this function will return true.  \n\n$$\n\\begin{array}{r l}&{\\mathbf{unction~CHECK_{-}P R o P o s A L}(S^{E},P^{E},\\Omega^{E},S_{j}^{P})}\\ &{\\quad\\mathbf{if}(S_{j}^{P}\\supseteq S^{E})\\wedge(P^{E}\\neq\\varnothing)\\wedge(\\Omega^{E}\\neq\\varnothing)\\wedge\\langle S^{E},P^{E},\\Omega^{E}\\rangle}\\ &{\\frac{t}{\\mathrm{:}}\\mathbb{P}_{r e c e i v e d}^{P}\\mathbf{then}}\\ &{\\quad\\quad\\mathrm{return(true)}}\\ &{\\quad\\quad\\mathbf{else}}\\ &{\\quad\\quad\\mathrm{return(false)}}\\end{array}\n$$  \n\n# 5. RELATED WORKS AND CONCLUSIONS  \n\nRegulating access control over distributed systems (such as the web) has been the subject of intense research in the last few years. A classical way is to set up Privilege Management Infrastructures such as PERMIS [5], SPKI [8] or other hybrid models [1]. Trust management systems are just a different name for such PMIs where more sophisticated rules for access are used [3, 19]. In such systems credentials needed to access the system might be presented in push or pull mode or discovered interactively [11].  \n\nThe controlled disclosure of such credentials can be the subject of a complex negotiation protocol [20, 16, 4, 12, 11]. However all those works, including more recent ones appearing in the POLICY workshop this year still consider as a starting point of the negotiation the request for access to a single service. Mecella et al. [14] pointed out the importance to consider the access control and negotiation issues for the overall business process taking into account the dynamic aspect of the conversation.  \n\nOne of the limitation is essentially no paper considers the possibility of negotiating the disclosure of our privileges for (additional) services. This gap is also evident in the broad literature on service negotiations in multi-agent systems. Here the focus is mainly on one-to-many negotiation protocols for voting, auctions, bargaining and coalition formation (see [15] for a nice survey on the topic), therefore with emphasis on the negotiation strategy and how this strategy works when agents exploit some utility space (i.e., linear, nonlinear). Again, to the best of our knowledge, we don’t know papers which address the aforementioned problem.  \n\nThe contribution of this paper is twofold. First, we have proposed a negotiation framework that considers not only the negotiation of privileges but also the corresponding negotiation of services and the behavioral constraints on the disclosures of such privileges depending on the business process. In our framework services and required privileges are bundled together and clients and servers have a hierarchy of preferences among the different bundles. The framework also defines the type of messages participants can exchange during the overall negotiation, the type of information messages will contain and the general protocol flow.  \n\nAs second contribution of the paper, we have designed two possible strategies for the client and the service provider in order to show that the flow is actually executable. To take into account both cooperative and malicious agents, we have designed such strategies to be at the same time cooperative (they do negotiate a mutually liked bundle of services and credentials if one exists) and robust (they resist to potentially malicious parties that would only like to discover the preferences of their opponents).  \n\n# 6. REFERENCES  \n\n[1] C. Altenschmidt, J. Biskup, U. Flegel, and Y. Karabulut. Secure mediation: Requirements, design, and architecture. JCS, 11(3):365–398, 2003.   \n[2] T. Aura and M. Roe. Designing the mobile ipv6 security protocol. Annales des Telécommunications 61(3-4):332–356, 2006.   \n[3] M. Blaze, J. Feigenbaum, and J. Lacy. Decentralized trust management. In Proc. of IEEE SS&P’96, pages 164–173. IEEE Press, 1996.   \n[4] P. Bonatti and P. Samarati. A unified framework for regulating access and information release on the web. JCS, 10(3):241–272, 2002.   \n[5] D. W. Chadwick and A. Otenko. The PERMIS X.509 role-based privilege management infrastructure. In Proc. of SACMAT’02, pages 135–140. ACM Press, 2002.   \n[6] N. Damianou, N. Dulay, E. Lupu, and M. Sloman. The Ponder policy specification language. In Proc. of POLICY’01, pages 18–38. Springer, 2001.   \n[7] N. Dragoni, F. Massacci, K. Naliuka, and I. Siahaan. Security-by-contract: Toward a semantics for digital signatures on mobile code. In Proc. of EuroPKI’07, pages 297–312. Springer-Verlag, 2007.   \n[8] C. Ellison, B. Frantz, B. Lampson, R. Rivest, B. M. Thomas, and T. Ylonen. SPKI Certificate Theory, September 1999. IETF RFC 2693.   \n[9] R. Heckel and M. Lohmann. Towards contract-based testing of web services. In Proc. of the Int. Workshop on Test and Analysis of Component Based Systems (TACoS 2004), volume 82 of ENTCS. Elsevier Sci., 2004.   \n[10] H. Koshutanski and F. Massacci. An access control framework for business processes for web services. In XMLSEC ’03: Proceedings of the 2003 ACM workshop on XML security, pages 15–24. ACM Press, 2003.   \n[11] H. Koshutanski and F. Massacci. A negotiation scheme for access rights establishment in autonomic communication. J. of Net. and Sys. Managment, 15(1):117–136, 2007.   \n[12] J. Li, N. Li, and W. H. Winsborough. Automated trust negotiation using cryptographic credentials. In Proc. of CCS’05, pages 46–57. ACM Press, 2005.   \n[13] L. Lymberopoulos, E. Lupu, and M. Sloman. An adaptive policy based framework for network services management. J. of Net. and Sys. Managment, 11(3):277–303, 2003.   \n[14] M. Mecella, M. Ouzzani, F. Paci, and E. Bertino. Access control enforcement for conversation-based web services. In Proc. of WWW’06, pages 257–266. ACM Press, 2006.   \n[15] T. Sandholm. Distributed rational decision making. In G. Weiss, editor, Multiagent Systems, pages 201–259. The MIT Press, Cambridge, Massachusetts, 1999.   \n[16] K. Seamons and W. Winsborough. Automated trust negotiation. Technical report, US Patent and Trademark Office, 2002. IBM Corporation, patent application filed March 7, 2000.   \n[17] R. Sekar, V. Venkatakrishnan, S. Basu, S. Bhatkar, and D. DuVarney. Model-carrying code: a practical approach for safe execution of untrusted applications. In Proc. of ACM SOSP’03., pages 15–28. ACM Press, 2003.   \n[18] M. Sloman and E. Lupu. Policy specification for programmable networks. In Proce. of the 1st Int. Working Conf. on Active Networks, pages 73–84. Springer, 1999.   \n[19] W. Yao. Trust management for widely distributed systems. PhD thesis, Univ. of Cambridge, Computer Laboratory, 2004. Technical report UCAM-CL-TR-608, ISSN 1476-2986.   \n[20] T. Yu, M. Winslett, and K. E. Seamons. Supporting structured credentials and sensitive policies through interoperable strategies for automated trust negotiation. TISSEC, 6(1):1–42, 2003.  "}