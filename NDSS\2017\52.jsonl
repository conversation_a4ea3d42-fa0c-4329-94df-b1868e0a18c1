{"text": "# P2P Mixing and Unlinkable Bitcoin Transactions  \n\nAnonymity of the People, by the People, and for the People  \n\nTim Ruffing <NAME_EMAIL>-saarland.<PERSON>-<PERSON> Kate Purdue University <NAME_EMAIL> <EMAIL>  \n\nAbstract—Starting with Dining Cryptographers networks (DC-nets), several peer-to-peer (P2P) anonymous communication protocols have been proposed. However, despite their strong anonymity guarantees, none of them have been employed in practice so far: Most protocols fail to simultaneously address the crucial problems of slot collisions and disruption by malicious peers, while the remaining ones handle $f$ malicious peers with $\\Dot{O}(f^{2})$ communication rounds. We conceptualize these P2P anonymous communication protocols as $P2P$ mixing, and present a novel P2P mixing protocol, DiceMix, that needs only four communication rounds in the best case, and $4+2f$ rounds in the worst case with $f$ malicious peers. As every individual malicious peer can force a restart of a P2P mixing protocol by simply omitting his messages, we find DiceMix with its worst-case complexity of $O(f)$ rounds to be an optimal P2P mixing solution.  \n\nOn the application side, we employ DiceMix to improve anonymity in crypto-currencies such as Bitcoin. The public verifiability of their pseudonymous transactions through publicly available ledgers (or blockchains) makes these systems highly vulnerable to a variety of linkability and deanonymization attacks. We use DiceMix to define CoinShuffl $\\mathbf{e}{+}{+}$ , a coin mixing protocol that enables pseudonymous peers to perform unlinkable transactions in a manner fully compatible with the current Bitcoin system. Moreover, we demonstrate the efficiency of our protocols with a proof-of-concept implementation. In our evaluation, DiceMix requires less than eight seconds to mix 50 messages (160 bits, i.e., Bitcoin addresses), while the best protocol in the literature requires almost three minutes in the same setting.  \n\nFinally, we present a deanonymization attack on existing P2P mixing protocols that guarantee termination in the presence of disruptive peers. We generalize the attack to demonstrate that no P2P mixing protocol simultaneously supports arbitrary input messages, provides anonymity, and terminates in the presence of disruptive peers. DiceMix resists this attack by requiring fresh input messages, e.g., cryptographic keys never used before.  \n\nrouters to avoid individual messages getting traced through the network. The original mixnet protocol, as well as its successors such as onion routing [31], AN.ON [1], and Tor [23], inherently require access to a set of geographically distributed routers such that at least some of them are trusted to not break peers anonymity.  \n\nStarting with the Dining Cryptographers network (DCnet) protocol [17], another line of research on anonymous communication networks emerged, in which peers do not depend on any third-party routers and instead communicate with each other to send their messages anonymously. While the DC-net protocol can guarantee successful termination and anonymity against an honest-but-curious adversary controlling a subset of peers, it is prone to disruption by a single malicious peer who sends invalid protocol messages (active disruption), or simply omits protocol messages entirely (passive disruption). Moreover, a DC-net protects the anonymity of the involved malicious peers, making it impossible for honest peers to detect and exclude the malicious peer.  \n\nTo address this termination issue, recent successors of the DC-net protocol [15], [22], [27], [28], [32], [55] incorporate cryptographic accountability mechanisms against active disruptions. The employed techniques are either proactive, e.g., zeroknowledge proofs proving the validity of sent messages [32], or reactive, e.g, the revelation of session secrets to expose and exclude malicious disruptors after a failed protocol run [22]. These protocols have demonstrated that, for a set of mutually distrusting peers, sending their messages anonymously is feasible purely by communicating with each other in a peerto-peer (P2P) manner. Moreover, given the surging demand for anonymity in P2P crypto-currencies such as Bitcoin [5], [6], [40], [45], [46], [51], [53], these protocols have led to real-world P2P Bitcoin mixing systems [3], [42], [52].  \n\n# I. INTRODUCTION  \n\nChaum [18] introduced the concept of anonymous digital communication in the form of mixing networks (or mixnets). In the mixnet protocol, a batch of encrypted messages from users is decrypted, randomly permuted, and relayed by a sequence of  \n\nNevertheless, these solutions are still not ideal: with communication rounds quadratic in the worst case with many malicious peers, these current pure P2P solutions [22], [52], [55] do not scale as the number of participating peers grows. For instance, the state-of-the-art Bitcoin P2P mixing protocol CoinShuffle [52] requires a few minutes to anonymize the communication of 50 peers if every peer is honest, and even much longer in the presence of malicious peers. In this paper, it is our goal to bring P2P anonymous communication from the realm of feasibility to the realm of practicality.  \n\n# A. Contributions  \n\nWe compartmentalize our contributions in this paper into four key components.  \n\n1) P2P Mixing: As our first contribution, we conceptualize P2P mixing as a natural generalization of DC-nets [17]. A P2P mixing protocol enables a set of mutually distrusting peers to publish their messages simultaneously and anonymously without requiring any trusted or untrusted third-party anonymity proxy.  \n\n2) DiceMix Protocol: Although some DC-net successors [30], [41] as well as some anonymous group messaging systems [22], [52], [55] satisfy the P2P mixing requirements, we found those to be too inefficient for large-scale mixing As our second contribution, we present the new P2P mixing protocol DiceMix, which builds on the original DC-net protocol. P2P Mixing Protocol handles collisions by redundancy, and disruption by revealing session secrets to expose malicious peers. DiceMix requires only $4+2f$ rounds in the presence of $f$ malicious peers, i.e., only four rounds if every peer behaves honestly. The resulting communication round complexity is a linear factor better than in existing state-of-the-art approaches [22], [30], [41], [52].  \n\nWe provide a proof-of-concept implementation of the DiceMix protocol, and evaluate it in Emulab [59]. Our results show that in an Internet-like setting, 50 peers can anonymously broadcast their messages in about eight seconds, whereas previous state-of-the art protocols need several minutes.  \n\n3) CoinShuffle $^{++}$ Protocol: As our third contribution, we apply DiceMix to Bitcoin, the most popular crypto-currency. In particular, building on the CoinJoin paradigm [43] and DiceMix, we present CoinShuffle++, a practical decentralized mixing protocol for Bitcoin users. CoinShuffle $^{++}$ not only is considerably simpler and thus easier to implement than its predecessor CoinShuffle [52] but also inherits the efficiency of DiceMix and thus outperforms CoinShuffle significantly. In particular, in a scenario with 50 participants in the same evaluation setting, a successful transaction with CoinShuffle++ can be created in eight seconds, instead of the almost three minutes required with CoinShuffle.  \n\n4) A Generic Attack on P2P Mixing Protocols: As our fourth contribution, we present a deanonymization attack on existing P2P mixing protocols that guarantee termination in the presence of disruptive peers. We exemplify the attack on the Dissent shuffle protocol [22], [55] and then generalize the attack to demonstrate that no P2P mixing protocol simultaneously supports arbitrary input messages, provides anonymity, and terminates in the presence of disruptive peers.  \n\nThe proposed attack is similar to statistical disclosure attacks across several protocol runs (e.g., [14], [61]) but works with certainty, because a protocol, which is supposed to terminate successfully, can be forced to start a new run to ensure termination. Finally, we discuss how DiceMix resists this attack by requiring fresh input messages (e.g., cryptographic keys never used before), and we discuss why this is not a problem for applications such as coin mixing.  \n\n# II. CONCEPTUALIZING P2P MIXING  \n\nA P2P mixing protocol [22], [52], [62] allows a group of mutually distrusting peers, each having an input message, to simultaneously broadcast their messages in an anonymous manner without the help of a third-party anonymity proxy. An attacker controlling the network and some peers should not be able to tell which of the messages belongs to which honest peer. In more detail, the anonymity set of an individual honest peer should be the set of all honest participating peers, and we expect the size of this set to be at least two.  \n\nThe requirement to achieve sender anonymity without the help of any third-party anonymity proxy such as an onion router or a mix server makes P2P mixing fundamentally different from most well-known anonymous communication techniques in the literature. Unlike standard techniques such as onion routing or mix cascades, P2P mixing relies on a much weaker trust assumption and is expected to terminate successfully and provide a meaningful anonymity guarantee in the presence of an attacker controlling all but two peers. As a consequence, each peer must be actively involved in the anonymous communication process which comes with inherent restrictions and expense.  \n\n# A. Setup and Communication Model  \n\nWe assume that peers are connected via a bulletin board, e.g., a server receiving messages from each peer and broadcasting them to all other peers. We stress that sender anonymity will be required to hold even against a malicious bulletin board; the bulletin board is purely a matter of communication.  \n\nWe assume the bounded synchronous communication setting, where time is divided into fixed communication rounds such that all messages broadcast by a peer in a round are available to the peers by the end of the same round, and absence of a message on the bulletin board indicates that the peer in question failed to send a message during the round.  \n\nSuch a bulletin board can be seamlessly deployed in practice, and in fact already-deployed Internet Relay Chat (IRC) servers suffice.1 The bulletin board can alternatively be substituted by an (early stopping) reliable broadcast protocol [24], [54] if one is willing to accept the increased communication cost.  \n\nWe assume that all peers participating in a P2P mixing protocol are identified by verification keys of a digital signature scheme, and that the peers know each other’s verification keys at the beginning of a protocol execution.  \n\nTo find other peers willing to mix messages, a suitable bootstrapping mechanism is necessary. Note that a malicious bootstrapping mechanism may hinder sender anonymity by preventing honest peers from participating in the protocol and thereby forcing a victim peer to run the P2P mixing protocol with no or only a few honest peers, decreasing the size of her effective anonymity set. While this is a realistic threat against any anonymous communication protocol in general, we consider protection against a malicious bootstrapping mechanism orthogonal to our work.  \n\n# B. Input and Outputs of a P2P Mixing Protocol  \n\nOur treatment of a P2P mixing protocol is special with respect to inputs and outputs. Regarding inputs (the messages to mix), allowing the adversary to control all but two peers introduces an unexpected requirement, namely, that input messages must be fresh. Regarding outputs, a P2P mixing protocol according to our definitions provides the feature that the peers will have to explicitly agree on the protocol output, i.e., the set of anonymized messages.  \n\n1) Freshness of Input Messages: In contrast to state-of-theart anonymous and terminating P2P mixing protocols such as Dissent [22] and the protocol by Golle and Juels [32], we require that input messages to be mixed are freshly drawn from a distribution with sufficient entropy, e.g., input messages can be random bitstrings or public keys never used before. Furthermore, if the honest peers exclude a peer from the protocol, e.g., because the peer appears offline, all messages used so far will be discarded. Then, all remaining peers again generate fresh messages and are required to continue the protocol with them.  \n\nWhile this seems to be a severe restriction of functionality compared to the aforementioned protocols, a restriction of this kind is in fact necessary to guarantee anonymity. If instead peers can arbitrarily choose their messages in a P2P mixing protocol guaranteeing termination, the protocol is inherently vulnerable to an attack breaking sender anonymity. We will explain this attack in detail in Section VIII; it works against state-of-the-art P2P mixing protocols and has been overlooked in this form in the literature so far.  \n\n2) Explicit Confirmation of the Output: Anonymity-seeking P2P applications such as coin mixing [43], [52], [62] or identity mixing [26] require that the peers agree explicitly on the outcome of the mixing before it comes into effect, e.g., by collectively signing the set $M$ of anonymized messages.  \n\nWe call this additional functionality confirmation and incorporate it in our model. The form of the confirmation depends on the application and is left to be defined by the application which calls the protocol. For example in coin mixing, the confirmation is a special transaction signed by all peers; we will discuss this in detail in Section VI.  \n\nWhile the protocol cannot force malicious peers to confirm $M$ , those malicious peers should be excluded and the protocol should finally terminate successfully with a proper confirmation by all unexcluded peers.  \n\n# C. Interface and Execution Model  \n\nTo deploy a P2P mixing protocol in various anonymityseeking applications, our generic definition leaves it up to the application to specify exactly how fresh input messages are obtained and how the confirmation on the result is performed. We restrict our discussion here to terminology and a syntactic description of the interface between the anonymity-seeking application and an employed P2P mixing protocol, and leave the semantic requirements to the protocol construction later.  \n\nA protocol instance consists of one or several runs, each started by calling the user-defined algorithm $\\mathrm{{GEN}()}$ to obtain a fresh input message to be mixed. If a run is disrupted, the protocol can exclude peers that turned out to be malicious. Otherwise, the protocol will obtain a candidate result, i.e., a candidate output set $M$ of anonymized messages. Then it calls the user-defined confirmation subprotocol ${\\dot{\\mathbf{C}}}{\\dot{\\mathbf{ONFIRM}}}(i,P,M)$ whose task is to obtain confirmation for $M$ from the final peer set $P$ of all unexcluded peers. (The first argument $i$ is an identifier of the run.) Possible confirmations range from a signature on $M$ , to a complex task requiring interaction among the peers, e.g., the creation of a threshold signature in a distributed fashion.  \n\nIf confirmation can be obtained from everybody, then the run and the P2P mixing protocol terminates successfully. Otherwise, ${\\bf C}_{\\bf{O N F I R M}}(i,P,\\bar{M})$ by convention fails and reports the malicious peers deviating from the confirmation steps back to the P2P mixing protocol. In this case, the protocol can start a new run by obtaining a fresh message via $\\mathrm{{GEN}()}$ ; the malicious peers are excluded in this new run.  \n\nAn example execution is depicted in Fig. 1. Note that while in this example execution all runs are sequential, this is not a requirement. For improved efficiency, a P2P mixing protocol can perform several runs concurrently, e.g., to have an already-started second run in reserve in case the first fails Then the protocol can terminate with the first run that confirms successfully, and abort all other runs.  \n\n# D. Threat Model  \n\nIn general, we assume that the attacker controls some number $f$ of $n$ peers.  \n\nFor the sender anonymity property, we assume that the attacker additionally controls the bulletin board, i.e., the network. In particular, the attacker can partition the network and block messages from honest peers. In the case of successful termination, the anonymity set of each honest peer will be the set of unexcluded honest peers2. This means that we need $f<n-1$ at the end of the protocol, where $n$ is the number of unexcluded peers, to ensure that at least two honest peers are present and the anonymity guarantee is meaningful.  \n\nFor the termination property, we trust the bulletin board to relay messages reliably and honestly, because termination (or any liveness property) is impossible to achieve against a malicious bulletin board, which can just block all communication.  \n\n# E. Security Goals  \n\n# A P2P mixing protocol must provide two security properties.  \n\na) Sender Anonymity: If the protocol succeeds for honest peer $p$ in a run (as described in Section II-C) with message $m_{p}$ and final peer set $P$ , and $p^{\\prime}\\in P$ is another honest peer, then the attacker cannot distinguish whether message $m_{p}$ belongs to $p$ or to $p^{\\prime}$ .  \n\nb) Termination: If the bulletin board is honest and there are at least two honest peers, the protocol eventually terminates successfully for every honest peer.  \n\nOur definition of sender anonymity is only concerned with the messages in a successful run, i.e., no anonymity is guaranteed for messages discarded in failed runs (see Section II-C). This demands explanation, because giving up anonymity in the case of failed confirmation seems to put privacy at risk at first glance. However, the discarded messages are just randomly generated bitstrings and have never been and will never be used outside the P2P mixing protocol; in particular the messages have been not returned back to the application. So it is safe to give up sender anonymity for discarded messages. It turns out that this permissive definition is sufficient for a variety of applications and allows for very efficient constructions.  \n\n![](/tmp/output/52_20250327033053/images/6b4cd0dddd34006184b4eeafe09d30a88b6bc49dd2879cb4ab80c20def058f76.jpg)  \nFig. 1: Example Execution of a P2P Mixing Protocol. The figure shows the calls during the execution; time proceeds from left to right. The execution starts with the application calling the P2P mixing protocol with an initial set $P_{1}$ of peers. The P2P mixing protocol then starts $\\mathrm{Run}1$ by generating a new message $m_{1}$ (via calling $\\mathrm{{GEN}(),}$ ). Run 1 fails early (e.g., due to active disruption by a peer $p$ ) and $m_{1}$ is discarded. The P2P mixing protocol then starts Run 2 with peer set $P_{2}=P_{1}\\setminus\\{p\\}$ by generating a new message $m_{2}$ . Run 2 is initially not disrupted, and the P2P mixing protocol calls the confirmation subprotocol to confirm the set $M_{2}$ of mixed messages with the peers in $P_{2}$ . The confirmation subprotocol fails, because a set $P_{m a l,2}$ of peers refuse to confirm. The confirmation subprotocol reports those malicious peers back to the P2P mixing protocol, which in turn discards $m_{2}$ . The P2P mixing protocol then starts Run 3 with peer set $P_{3}=P_{2}\\setminus P_{m a l,2}$ . This time, the confirmation subprotocol succeeds and indicates this by returning an empty set (of malicious peers) to the P2P mixing protocol. That is, all peers in $P_{3}$ have confirmed that the set $M_{3}$ of anonymized messages is the final output. The P2P mixing protocol returns $P_{3}$ and $M_{3}$ to the application and terminates.  \n\n# III. SOLUTION OVERVIEW  \n\nOur core tool to design an efficient P2P mixing protocol is a Dining Cryptographers network (DC-net) [17]: Suppose that each pair of peers $(i,j)$ shares a symmetric key $k_{i,j}$ and that one of the peers (e.g., $p_{1}$ ) wishes to anonymously publish a message $m$ such that $|m|=|k_{i,j}|$ . For that, $p_{1}$ publishes $M_{1}:=m\\oplus k_{1,2}\\oplus k_{1,3}$ , $p_{2}$ publishes $M_{2}:=k_{1,2}\\oplus k_{2,3}$ and finally $p_{3}$ publishes $M_{3}:=k_{1,3}\\oplus k_{2,3}$ . Now, the peers (and observers) can compute $M_{1}\\oplus M_{2}\\oplus M_{3}$ , effectively recovering $m$ . However, the origin of the message $m$ is hidden: without knowing the secrets $k_{i,j}$ , no observer can determine which peer published $m$ . Additionally, the origin is also hidden for peers themselves (e.g., as $p_{2}$ does not know $k_{1,3}$ , she cannot discover whether $p_{1}$ or $p_{3}$ is the origin of the message). It is easy to extend this basic protocol to more users [32].  \n\nBesides the need for pairwise symmetric keys, which can be overcome by a key exchange mechanism, there are two key issues to overcome, namely, first making it possible that all peers can publish a message simultaneously, and second, ensuring termination of the protocol even in the presence of malicious disruptors, while preserving anonymity.  \n\n# A. Handling Collisions  \n\nEach peer $p\\in P$ in the mixing seeks to anonymously publish her own message $m_{p}$ . Naively, they could run $|P|$ instances (called slots) of a DC-net, where each peer randomly selects one slot to publish her message. However, even if all peers are honest, two peers can choose the same slot with high probability, and their messages are then unrecoverable [32].  \n\nOne proposed solution is to perform an anonymous reservation mechanism so that peers agree in advance on a slot assignment for publishing [30], [41]. However, this mechanism adds communication rounds among the peers and it must also provide anonymity, which typically makes it prone to the same issues (e.g., slot collisions) that we would like to overcome in the first place. Alternatively, it is possible to establish many more slots so that the probability of a collision decreases [21]. However, this becomes inefficient quickly, and two honest peers could still collide with some probability.  \n\nInstead, we follow the paradigm of handling collisions by redundancy [15], [19], [25], [38], [50]. Assume that messages to be mixed are encoded as elements of a finite field $\\mathbb{F}$ with $|\\mathbb{F}|>n$ , where $n$ is the number of peers. Given $n$ slots, each peer $i$ , with message $m_{i}$ , publishes $m_{i}^{j}$ (i.e., $m_{i}$ to the $j$ -th power) in the $j$ -th slot. This yields an intentional collision involving all peers in each of the slots. Using addition in $\\mathbb{F}$ instead of XOR to create DC-net messages, the $j$ -th slot contains the power sum $\\begin{array}{r}{S_{j}=\\sum_{i}m_{i}^{j}}\\end{array}$ .  \n\nNow, we require a mechanism to extract the messages $m_{j}$ from the power sums $S_{j}$ . Let $g(x)=a_{n}x^{n}+a_{n-1}\\overset{\\vartriangle}{x}^{n-1}\\overset{\\vartriangle}{+}$ $\\dots+a_{1}x+a_{0}$ be a polynomial with roots $m_{1},m_{2},\\ldots,m_{n}$ Newton’s identities [33] state  \n\n$$\n\\begin{array}{c}{a_{n}=1,}\\ {a_{n-1}=S_{1},}\\ {a_{n-2}=(a_{n-1}S_{1}-S_{2})/2,}\\ {a_{n-3}=(a_{n-2}S_{1}-a_{n-1}S_{2}+S_{3})/3,}\\ {\\vdots}\\end{array}\n$$  \n\nBy knowledge of all coefficients $a_{j}$ of the polynomial $g$ , we can find its $n$ roots, which are the $n$ input messages.  \n\nB. Handling Disruption and Ensuring Termination  \n\nRecovering the messages only works when all peers honestly follow the protocol. If a malicious peer disrupts the DC-net by simply using inconsistent DC-net messages, we must ensure that the protocol still terminates eventually.  \n\nWhen a candidate set $M$ is determined, every honest peer checks whether her input message is indeed in $M$ . Depending on the outcome of this check, the peer either starts the confirmation subprotocol to confirm a good $M$ , or reveals the secret key used in the key exchange to determine who is responsible for an incorrect $M$ . We face two challenges on the way to successful termination.  \n\n1) Consistent Detection of Disruption: The first challenge is to ensure that indeed $M$ does not contain any honest message Only then will all honest peers agree on whether disruption has occurred and are able to take the same control flow decision at this stage of the protocol, which is crucial for termination.  \n\nTo overcome this challenge, every peer must provide a non-malleable commitment (e.g., using a hash function) to its DC-net vector before she sees the vectors of other peers. In this manner, malicious peers are forced to create their DC-net vectors independently of the input messages of honest peers. The redundant encoding of messages using powers ensures that a malicious peer is not able to create a malformed DC-net vector that results in a distortion of only a subset of the messages of the honest peers. Intuitively, to distort some messages but keep some other message $m$ of a honest peer intact, the malicious peer must influence all power sums consistently. This, however, would require a DC-net vector that depends on $m$ (as we show in Section IV-D), which is prevented by the non-malleability of the commitments. This ensures that all honest peers agree on whether $M$ is correct, and take the same control flow decision  \n\n2) Exposing a Disruptor: The second challenge is that the misbehaving peer is not trivially detected given the sender anonymity property of DC-nets. To overcome this, every peer is required to reveal the ephemeral secret key used in the initial key exchange. Then every peer can replay the steps done by every other peer and eventually detect and expel the misbehaving peer from further runs.  \n\nNote that the revelation of the secret keys clearly breaks sender anonymity for the current run of the protocol. However, the failed run will be discarded and a new run with fresh cryptographic keys and fresh messages will be started without the misbehaving peer. This is in line with our definition of sender anonymity, which does not impose a requirement on failed runs.  \n\nAn important guarantee provided by DiceMix is that if a protocol run fails, the honest peers agree on the set of malicious peers to be excluded. Although this is critical for termination, this aspect has not been properly formalized or addressed in some previous P2P mixing protocols supposed to ensure termination [22], [52], [55].  \n\n# IV. THE DICEMIX PROTOCOL  \n\nIn this section we present DiceMix, an efficient P2P mixing protocol, which terminates in only $4+2f$ rounds in the presence of $f$ malicious peers.  \n\nA. Building Blocks  \n\n1) Digital Signatures: We require a digital signature scheme (KeyGen, Sign, Verify) unforgeable under chosen-message attacks (UF-CMA). The algorithm KeyGen returns a private signing key $s k$ and the corresponding public verification key $v k$ . On input message $m$ , $\\mathsf{S i g n}(s k,m)$ returns $\\sigma$ , a signature on message $m$ using signing key $s k$ . The verification algorithm Verify $(p k,\\sigma,m)$ outputs true iff $\\sigma$ is a valid signature for $m$ under the verification key $v k$ .  \n\n2) Non-interactive Key Exchange: We require a non-interactive key exchange (NIKE) mechanism (NIKE.KeyGen, NIKE.SharedKey) secure in the CKS model [16], [29]. The algorithm $\\mathsf{N I K E.K e y G e n(\\it i d)}$ outputs a public key npk and a secret key nsk for a given party identifier id. NIKE.SharedKey $(i d_{1},i d_{2},n s k_{1},n p k_{2},s i d)$ outputs a shared key for the two parties $i d_{1}$ and $i d_{2}$ and session identifier $s i d$ NIKE.SharedKey must fulfill the standard correctness requirement that for all session identifiers $s i d$ , all parties $i d_{1},i d_{2}$ , and all corresponding key pairs $(n p k_{1},n s k_{1})$ and $(n p k_{2},n s k_{2})$ it holds that NIKE.Sh $\\begin{array}{r l}{\\mathsf{a r e d K e y}(i d_{1},i d_{2},n s k_{1},n p k_{2},s i d)}&{=}\\end{array}$ NIKE.SharedKey $(i d_{2},i d_{1}$ , $n s k_{2}$ , npk 1, sid ). Additionally, we require an algorithm NIKE.Validate $\\mathsf{P}\\mathsf{K}(n p k)$ , which outputs true iff npk is a public key in the output space of NIKE.KeyGen, and we require an algorithm NIKE.ValidateKeyPair $(n p k,n s k)$ which outputs true iff $n s k$ is a valid secret key for the public key npk.  \n\nStatic Diffie-Hellman key exchange satifies these requirements [16], given a suitable key derivation algorithm such as NIKE.SharedKey $\\cdot(i d_{1},i d_{2},x,g^{y}):=\\mathsf{K}((g^{x y},\\{i d_{1},i d_{2}\\},s i d))$ for a hash function $\\mathsf{K}$ modeled as a random oracle.  \n\n3) Hash Functions: We require two hash functions $\\mathsf{H}$ and G both modeled as a random oracle.  \n\n4) Conventions and Notation for the Pseudocode: We use arrays written as ARR[i], where $i$ is the index. We denote the full array (all its elements) as ARR[ ].  \n\nA protocol message msg is broadcast using the instruction “broadcast $m^{,}$ . The instruction “receive ARR $\\Bar{[p]}$ from all $p\\in$ $P$ where $X(\\operatorname{ARR}[p])$ missing $C(P_{o f f})^{\\bullet}$ attempts to receive a message from all peers $p\\in P$ . The first message msg from peer $p$ that fulfills predicate $X(m s g)$ is accepted and stored as $\\operatorname{ARR}[p]$ ; all other messages from $p$ are ignored. When a timeout is reached, the command $C$ is executed, which has access to a set $P_{o f f}\\subseteq P$ of peers that have not sent a (valid) message.  \n\nRegarding concurrency, a thread $t$ that runs a procedure $\\mathrm{P}(a r g s)$ is started using “t ·= fork $P(a r g s)^{\\bf5}$ . A thread with handle $t$ can either be joined using ${\\bf\\nabla}^{\\leftarrow}r:={\\bf j}{\\bf o i n}~t^{\\prime}{\\bf\\nabla}^{,}$ , where $r$ is its return value, or it can be aborted using “abort $\\displaystyle t^{\\flat}$ . A thread can wait for a notification and receive a value from another thread using “wait”. The notifying thread uses “notify $t$ of $v^{,}$ to wake up thread $t$ and notify it of value $v$ .  \n\n# B. Contract with the Application  \n\nIn the following, we specify the contract between DiceMix and the application calling it. We start with two guarantees provided by DiceMix to the application and then we describe features required of the application by DiceMix.  \n\n![](/tmp/output/52_20250327033053/images/3ac41bf8fb2be83717a2972f0cf43df152b67da44485bfd3702f431ef1acbaa3.jpg)  \nFig. 2: Example of a DiceMix Execution. Run 1 fails due to DC-net disruption. Run 2 fails to confirm. Run 3 finally succeeds, and run 4 is then aborted. Rows represent protocol runs and columns represent communication rounds. Blue parts are for concurrency; the arrows depict the dependency between runs, i.e., when a run notifies the next run about the peers to exclude. KE: Key exchange; CM: Commitment; DC: DC-net; RV: Reveal pads; SK: Reveal secret key; CF: Confirmation.  \n\n1) Guarantees Provided to the Application: The confirmation subprotocol is provided with two guarantees. First, DiceMix ensures that all honest peers call the confirmation subprotocol in the same communication round with the same parameters; we call this property agreement.  \n\nSecond, to ensure that no peer refuses confirmation for a legitimate reason, e.g., an incorrect set final set $M$ not containing her message, our protocol ensures that all honest peers deliver the same and correct message set $M$ . Then, the confirmation subprotocol $\\mathbf{CoNFIRM}(i,P,M)$ can safely assume that peers refusing to confirm are malicious. We call this property validity.  \n\nThe purpose of both of these guarantees is to ensure correct functionality of the confirmation subprotocol, and the guarantees are only provided if the bulletin board is honest. As a consequence, it is up to the confirmation subprotocol to fail safely if they do not hold. The guarantees are detailed below.  \n\na) Agreement: Assume that the bulletin board is honest. Let $p$ and $p^{\\prime}$ be two honest peers in a protocol execution. If $p$ calls $\\mathrm{CoNFIRM}(i,P,M)^{3}$ in some communication round $r$ then $p^{\\prime}$ calls $\\mathbf{CoNFIRM}(i,P,M)$ with the same message set $M$ and final peer set $P$ in the same communication round $r$ .  \n\n$b$ ) Validity: Assume that the bulletin board is honest. If honest peer $p$ calls $\\mathbf{CoNFIRM}(i,P,M)$ with message set $M$ and final peer set $P$ , then $(i)$ for all honest peers $p^{\\prime}$ and their messages $m_{p^{\\prime}}$ , we have $m_{p^{\\prime}}\\in M$ , and $(i i)$ we have $|M|\\leq|P|$  \n\n2) Requirements of the Application: Next, we specify the guarantees that the application must provide to DiceMix to ensure proper function.  \n\nWe assume that input messages generated by $\\mathrm{GEN()}$ are encoded in a prime field $\\mathbb{F}_{q}$ , where $q$ is larger than the number of peers in the protocol. Also, we assume that the message $m$ returned by $\\mathrm{GEN()}$ has sufficient entropy such that it can be predicted only with negligible probability, which also implies that $q$ is at least as large as the security parameter.  \n\nWe require two natural properties from the confirmation subprotocol. The first property (correct confirmation) states that a successful call to the subprotocol indeed confirms that the honest peers in $P$ agree on $M$ . The second property (correct exclusion) states that in an unsuccessful call, the confirmation subprotocol identifies at least one malicious peer, and no honest peer is falsely identified as a malicious peer.  \n\na) Correct Confirmation: Even if the bulletin board is malicious,4 we require the following: If a call to $\\mathbf{CoNFIRM}(i,P,M)$ succeeds for peer $p$ (i.e., if the call returns an empty set $P_{m a l}=\\emptyset$ of malicious peers refusing confirmation), then all honest peers in $P$ have called $\\mathbf{CoNFIRM}(i,P,M)$ .  \n\n$b$ ) Correct Exclusion: Assume that the bulletin is honest. If $\\mathbf{CoNFIRM}(i,P,M)$ returns a set $P_{m a l}\\neq\\emptyset$ for honest peer $p$ , then $\\mathbf{CoNFIRM}(i,P,M)$ returns the same set $P_{m a l}$ for every honest peer $p^{\\prime}$ . Furthermore, the returned set $P_{m a l}$ does not contain honest peers.  \n\n# C. Protocol Description  \n\nWe describe the DiceMix protocol in Algorithm 1. The black code is the basic part of the protocol; the blue code handles concurrent runs and offline peers.  \n\n1) Single Run of the Protocol (Black Pseudocode): The protocol starts in DICEMIX(), which takes as input a set of other peers $P$ , the peer’s own identity my, an array VK[ ] of verification keys of all peers, the peer’s own signing key $s k$ and a predetermined unique session identifier sid. A single protocol run, implemented in $\\mathsf{R U N()}$ , consists of four rounds.  \n\nIn the first round (KE), the NIKE is used to establish pairwise symmetric keys between all peers (DC-KEYS()). Then each peer can derive the DC-net pads from these symmetric keys (DC-SLOT-PAD()) and use them to create the vector of messages for the DC-net (DC-MIX()). In the second round (CM), each peer commits to her DC-net vector using hash function H; adding randomness is not necessary, because we assume that the input messages contained in the DC-net vector have sufficient entropy. In the third round (DC), the peers open their commitments. They are non-malleable and their purpose is to prevent a rushing attacker from letting his DC-net vector depend on messages by honest peers, which will be crucial for the agreement property. After opening the commitments, every peer has enough information to solve the DC-net and extract the list of messages by solving the power sums (DC-MIX-RES())  \n\nFinally, every peer checks whether her input message is in the result of the DC-net, determining how to proceed in the fourth round. Agreement will ensure that either every peer finds her message or no honest peer finds it.  \n\nIf a peer finds her message, she proceeds to the confirmation subprotocol (CF). Otherwise, she outputs her secret key. In this case, every other peer publishes her secret key as well, and the peers can replay each other’s protocol messages for the current run. This will expose the misbehaving peer, and honest peers will exclude him from the next run (SK).  \n\n2) Concurrent Runs of the Protocol (Blue Pseudocode): A simple but inefficient way of having several runs is to start a single run of the protocol and only after misbehavior is detected, start a new run without the misbehaving peer. This approach requires $4+4f$ rounds, where $f$ is the number of disruptive peers (assuming that CONFIRM() takes one round). To reduce the number of communication rounds to $4+2f$ , we deploy concurrent runs as depicted in Fig. 2. We need to address two main challenges. First, when a peer disrupts the DC-net phase of run $i$ , it must be possible to patch the already-started run $i+1$ to discard messages from misbehaving peers in run $i$ . For that, run $i$ must reach the last round (SK or CF) before run $i+1$ reaches the DC round.  \n\nBefore its DC round, run $i+1$ can be patched as follows In the DC round of run $i+1$ , honest peers broadcast not only their DC-net messages, but also in parallel they reveal (RV) the symmetric keys shared in run $i+1$ with malicious peers detected in run $i$ . In this manner, DC-net messages can be partially unpadded, effectively excluding malicious peers from run $i+1$ . We note that a peer could reveal wrong symmetric keys in this step. This, however, leads to wrong output from the DC-net, which is then handled by revealing secret keys in round $i+1$ . Publishing partial symmetric keys does not compromise sender anonymity for unexcluded peers because messages remain partially padded with symmetric keys shared between the honest peers.  \n\n3) Handling Offline Peers (Blue Pseudocode): So far we have only discussed how to ensure termination against actively disruptive peers who send wrong messages. However, a malicious peer can also just send no message at all. This case is easy to handle in our protocol. If a peer $p$ has not provided a (valid) broadcast message to the bulletin board in time, all honest peers will agree on that fact, and exclude the unresponsive peer. In particular, it is easy to see that all criteria specifying whether a message is valid will evaluate the same for all honest peers (if the bulletin board is reliable, which we assume for termination).  \n\nTo be able to achieve termination $4+2f$ in communication rounds, it is crucial that missing messages in the first two broadcasts (KE and CM) do not require aborting the run. Luckily, the current run can be continued in those cases. Peers not sending KE are just ignored in the rest of the run; peers not sending CM are handled by revealing symmetric keys exactly as done with concurrent runs (see the code blocks starting with the “missing” instruction).  \n\n# D. Security and Correctness Analysis  \n\nIn this section, we discuss why DiceMix achieves all required properties, namely the security properties sender anonymity and termination as well as the guarantees of validity and agreement that the application may rely on.  \n\n1) Sender Anonymity: Consider a protocol execution in which an honest peer $p$ succeeds with message $m_{p}$ and final peer set $P$ , and let $p^{\\prime}\\in P$ be another honest peer. We have to argue that the attacker cannot distinguish whether $m_{p}$ belongs to $p$ or $p^{\\prime}$ .  \n\nSince both $p$ and $p^{\\prime}$ choose fresh messages $m_{p},m_{p^{\\prime}}$ , and fresh NIKE key pairs in each run, it suffices to consider only the successful run $i$ . Since $p$ succeeds in run $i$ , the call to $\\mathbf{CoNFIRM}(i,P,M)$ has succeeded. By the “correct confirmation” property of CONFIRM(), peer $p^{\\prime}$ has started CONFIRM $(i,P,M)$ in the same communication round as $p$ By construction of the protocol, this implies two properties about peer $p^{\\prime}\\colon(i)~p^{\\prime}$ will not reveal her secret key in round SK, and (ii) $p^{\\prime}$ assumes that $p$ is not excluded in run $i$ , and thus has not revealed the symmetric key shared with $p$ in round RV.  \n\nAs the key exchange scheme is secure in the CKS model and the exchanged public keys are authenticated using unforgeable signatures, the attacker cannot distinguish the pads derived from the symmetric key between $p$ and $p^{\\prime}$ from random pads.  \n\nThus, after opening the commitments on the pads, peer $p$ has formed a proper DC-net with at least peer $p^{\\prime}$ . The security guarantee of Chaum’s original DC-nets [17] implies that the attacker cannot distinguish $m_{p}$ from $m_{p^{\\prime}}$ before the call to $\\mathbf{CoNFIRM}(i,P,M)$ . Now, observe that the execution of subprotocol $\\mathbf{CoNFIRM}(i,P,M)$ does not help in distinguishing, since all honest peers call it with the same arguments, which follows by the “correct confirmation” property as we have already argued. This shows sender anonymity.  \n\n2) Validity: To show validity, we have to show that if honest peer $p$ calls CONFIRM $(i,P,M)$ with message set $M$ and final peer set $P$ , then $(i)$ for all honest peers $p^{\\prime}$ and their messages $m_{p^{\\prime}}$ , we have $m_{p^{\\prime}}\\in M$ , and $(i i)$ we have $|M|\\leq|P|$ .  \n\nFor part $(i)$ of validity, recall that we assume the bulletin board to be honest for validity, so every peer receives the same broadcast messages. Under this assumption and the assumption that the signature scheme is unforgeable, a code inspection shows that after receiving the DC message, the entire state of a protocol run $i$ is the same for every honest peer, except for the signing keys, the own identity $m y$ , and the message $m$ generated by $\\mathrm{GEN()}$ . From these three items, only $m$ influences the further state and control flow, and it does so only in the check $m\\in M$ at the end of $\\mathrm{RUN()}$ (Line 48 in Algorithm 1).  \n\nWe now show as intermediate step that in every run $i$ , the condition $m\\in M$ evaluates to true for all honest peers or false for all honest peers. Note that $M$ is entirely determined by broadcast messages and thus the same for all honest peers. Let $p$ and $p^{\\prime}$ be two honest peers with their input messages $m_{p}$ and $m_{p^{\\prime}}$ in run $i$ , and assume for contradiction that the condition is true for $p$ but not for $p^{\\prime}$ , i.e., $m_{p}\\in M$ but $m_{p^{\\prime}}\\notin M$ . This implies that at least one malicious peer $a$ has committed to an ill-formed DC-net vector in run $i$ , i.e., a vector which is not of the form $(m_{a},m_{a}^{2},\\ldots,m_{a}^{n})$ with $n\\geq3$ , because there is at the least malicious peer $a$ and two honest peers. Since $m_{p}\\in M$ this ill-formed vector left the message $m_{p}$ intact. This implies that the vector of $a$ was chosen depending on the other DCnet vectors. A simple algebraic argument shows that even for the second power sum in the second slot, it is not feasible to come up with an additive offset to the power sum that changes some of the encoded messages but leaves all others intact: To change $m_{p^{\\prime}}$ to $m_{p^{\\prime}}+\\Delta$ and leave all other messages intact, the correct offset for the first slot is $\\Delta$ , and the correct offset for the second slot is $(m_{p^{\\prime}}+\\Delta)^{2}-m_{p^{\\prime}}^{2}=2m_{p^{\\prime}}\\Delta+\\Delta^{2}$ , which depends on $m_{p^{\\prime}}$ for fixed $\\Delta$ . However, it is not feasible for the attacker to create one (or more) commitments on messages that depend on $m_{p^{\\prime}}$ , because the commitments are non-malleable  \n\n# Algorithm 1 DiceMix  \n\n1: proc $\\mathtt{D I C E M I X}(P,m y,\\mathrm{VK}[],s k,s i d)$   \n2: $s i d:=(s i d,P,\\mathbf{V}\\mathbf{K}[])$   \n3: if $m y\\in P$ then   \n4: fail “cannot run protocol with myself”   \n5: return $\\operatorname{RUN}(P,m y,\\bar{\\operatorname{VK}}[],s k,s i d,0)$   \n6: proc $\\operatorname{RUN}(P,m y,\\operatorname{VK}[],s k,s i d,r u n)$   \n7: if $P=\\emptyset$ then   \n8: fail “no honest peers”   \n9: $\\triangleright$ Exchange pairwise keys   \n10: $\\bigl(\\mathrm{NPK}\\bigl[m y\\bigr],\\mathrm{NSK}\\bigl[m y\\bigr]\\bigr):=\\mathsf{N I K E.K e y G e n}(m y)$   \n11: sidHp $r e:=\\mathsf{H}\\big(\\big(\\mathrm{sidHPre,}s i d,r u n\\big)\\big)$   \n12: broadca·st ( $\\langle\\mathrm{E},\\mathrm{NPK}[m y]$ $,\\mathsf{S i g n}(s k,(\\mathrm{NPK}[m y],s i d H p r e))$ )   \n13: receive $(\\mathrm{KE},\\mathrm{NPK}[p],\\sigma[p])$ from all $p\\in P$   \n14: where NIKE.ValidateP ${\\mathsf{K}}({\\mathsf{N P K}}[p])$   \n$\\setminus\\mathsf{V e r i f y}(\\mathrm{VK}[p],\\sigma[p],(\\mathrm{NPK}[p],s i d H p r e))$   \n15: missing $P_{o f f}$ do   \n16: $P:=P\\setminus P_{o f f}$ ▷ Exclude offline peers   \n17: $i d H:=\\mathsf{H}\\big(\\big(s\\mathrm{idH},s i d,P\\cup\\{m y\\},\\mathrm{NPK}[],r u n\\big)\\big)$   \n18: $\\begin{array}{r}{\\mathrm{K}[\\mathbf{\\\\theta}:=\\mathrm{DC}\\cdot\\mathrm{KEYS}(P,\\mathrm{NPK}[\\mathbf{\\theta}],m y,\\mathrm{NSK}[m y],s i d H))}\\end{array}$   \n19: $\\triangleright$ Generate fresh message to mix   \n20: $m:=\\mathbf{G}\\mathrm{{EN}}()$   \n21: $\\mathrm{DC}[m y][]:=\\mathrm{DC}\\mathbf{-MIX}(P,m y,\\mathrm{K}[],m)$   \n22: $P_{e x}:=\\emptyset\\quad\\triangleright$ Malicious (or offline) peers for later exclusion   \n23: ▷ Commit to DC-net vector   \n24: $\\mathrm{CoM}[m y]:=\\mathsf{H}(\\left(\\mathrm{CM},\\mathrm{DC}[m y][]\\right))$   \n25: broadcast $\\big(\\mathbb{C}\\mathbb{M},\\mathbf{C}\\mathrm{OM}\\big[m y\\big]$ , $\\mathsf{S i g n}\\big(s k,\\big(\\mathrm{CoM}[m y],s i d H\\big)\\big)$   \n26: receive $\\mathbf{\\Phi}^{\\prime}(\\mathrm{CM},\\mathrm{COM}[p],\\sigma[p])$ from all $p\\in P$   \n27: where Verify $\\cdot(\\mathrm{VK}[p],\\sigma[p],(\\mathrm{CoM}[p],s i d H))$   \n28: missing $P_{o f f}$ do $\\triangleright$ Store offline peers for exclusion   \n29: $P_{e x}:=P_{e x}\\cup P_{o f f}$   \n30: if $r u n>0$ then   \n31: $\\triangleright$ Wait for prev. run to notify us of malicious peers   \n32: $\\begin{array}{l}{P_{e x P r e\\nu}:=\\mathbf{w}\\mathbf{a}\\mathbf{i}\\mathbf{t}}\\ {P_{e x}:=P_{e x}\\cup P_{e x P r e\\nu}}\\end{array}$   \n33:   \n34: $\\triangleright$ Collect shared keys with excluded peers   \n35: for all $p\\in P_{e x}$ do   \n36: $\\mathrm{K}_{e x}[m y][p]:=\\mathrm{K}[p]$   \n37: $\\triangleright$ Start next run (in case this one fails)   \n38: 39: $\\begin{array}{r l}&{P:=P\\setminus P_{e x}}\\ &{n e x t:=\\mathbf{fork}\\mathrm{RUN}(P,m y,\\mathrm{VK}[],s k,s i d,r u n+1)}\\end{array}$   \n40: ▷ Open commitments and keys with excluded peers   \n41: broadcast $\\left(\\mathrm{DC},\\mathrm{DC}[m y][\\mathbf{\\Lambda}]\\right.$ , $\\mathrm{K}_{e x}[m y][]$ , $\\mathsf{S i g n}(s k,\\mathrm{K}_{e x}[m y][]))$ )   \n42: receive (DC, $\\mathrm{DC}[p][]$ , $\\mathrm{K}_{e x}[p][],\\sigma[p],$ ) from all $p\\in P$   \n43: where $\\mathsf{H}((\\mathrm{CM},\\mathrm{DC}[p][]))=\\mathrm{CoM}[p]$   \n$\\wedge\\{p^{\\prime}:\\mathrm{K}_{e x}[p][p^{\\prime}]\\neq\\perp\\}=P_{e x}$   \n$\\wedge$ Verify $\\mathrm{\\nabla{VK}}[p]$ , Kex[p][ ], σ[p])   \n44: missing $P_{o f f}$ do $\\triangleright$ Abort and rely on next run   \n45: return RESULT-OF-NEXT-RUN ${\\cal P}_{o f f}.$ next)   \n46: $M:=\\mathrm{DC}\\cdot\\mathbf{M}\\mathbf{I}\\mathbf{X}\\cdot\\mathrm{RES}\\big(P\\cup\\big\\{m y\\big\\},\\mathrm{DC}\\big[\\big]\\big[\\big],P_{e x},\\mathrm{K}_{e x}\\big[\\big]\\big[\\big]\\big)$   \n47: $\\triangleright$ Check if our output is contained in the result   \n48: if $m\\in M$ then   \n49: $P_{m a l}:=\\mathrm{CoNFIRM}(i,P,M,m y,\\mathrm{vK}[],s k,s i d)$   \n50: if $P_{m a l}=\\emptyset$ then ▷ Success?   \n51: abort next   \n52: return m   \n53: else   \n54: broadcast (SK, NSK[my]) ▷ Reveal secret key   \n55: receive $(\\mathrm{SK},\\mathrm{NSK}[p])$ from all $p\\in P$   \n56: where NIKE.ValidateKeyPai $\\boldsymbol{\\cdot}(\\mathrm{NPK}[p],\\mathrm{NSK}[p])$   \n57: missing $P_{o f f}$ do $\\triangleright$ Abort and rely on next run   \n58: return RESULT-OF-NEXT- $\\mathbf{\\deltaRUN}(P_{o f f},n e x t)$   \n59: $\\triangleright$ Determine malicious peers using the secret keys   \n60: $\\begin{array}{r}{P_{m a l}:=\\mathrm{BLAME}\\big(P,\\mathrm{NPK[\\big]},m y,\\mathrm{NSK[\\big]},\\mathrm{DC[\\big][\\big]},s i d H}\\end{array}$   \n, $\\left.P_{e x},\\mathrm{K}_{e x}\\right|$ [ ][ ])   \n61: return RESULT-OF-NEXT-R $\\mathrm{{\\ell}}\\mathrm{{UN}}(P_{m a l},n e x t)$   \n62: proc DC- $\\mathbf{\\cdotMIX}(P,m y,\\mathbf{K}[],m)$   \n63: ▷ Create power sums in individual slots   \n64: for $i:=1,\\ldots,|P|+1$ do   \n65: $\\begin{array}{r}{\\mathrm{DCMY}[j]:=m^{i}+\\mathrm{DC-SLOT}\\mathrm{-}\\mathrm{PAD}(P,m y,\\mathrm{K}[],i)}\\end{array}$   \n66: return DCMY[ ]   \n67: proc DC-M $\\mathrm{IIX}\\mathrm{-RES}(P_{a l l},\\mathrm{DCMIX}[][],P_{e x},\\mathrm{K}_{e x}[][])$   \n68: for $s:=1,\\dots,|P_{a l l}|$ do   \n69: $\\mathbf{M}^{*}[s]:=\\scriptstyle\\mathrm{{DC-SLOT-OPEN}}\\displaystyle\\big(P_{a l l},\\scriptstyle\\mathrm{{DCMIX}}\\big[\\big][\\big],s,P_{e x},\\mathrm{K}_{e x}\\big[\\big][\\big]\\big)$   \n70: $\\triangleright$ Solve equation system for array $\\mathbf{M}\\big[\\big]$ of messages   \n71: $\\mathbf{M}[\\mathbf{\\Gamma}]:={\\sf S o l v e}(\\forall s\\in\\{1,\\dots,|P_{a l l}|\\}$ . $\\begin{array}{r}{\\mathbf{M}^{*}[s]=\\sum_{i=1}^{|\\check{P}_{a l l}|}\\mathbf{M}[i]^{s})}\\end{array}$ et   \n72: return ${\\mathsf{S e t}}(\\mathbf{M}[])~{\\mathsf{\\triangle}}\\triangleright{\\mathsf{C o n v e r t~M}}[]$ to an (uno rdered) multiset   \n73: proc DC-SLOT-PAD(P, my, K[ ], s)   \n74: return $\\begin{array}{r}{\\sum_{p\\in P}\\operatorname{sgn}(m y-p)\\cdot{\\sf G}(({\\bf K}[p],s))}\\end{array}$ ▷ in F   \n75: proc DC-SLOT- $\\mathrm{OPEN}(P_{a l l},\\mathrm{DC}[][],s,P_{e x},\\mathrm{K}_{e x}[][])$   \n76: ▷ Pads cancel out for honest peers   \n77: $\\begin{array}{r}{m^{*}:=\\sum_{p\\in P_{a l l}}\\mathrm{DC}[p][s]}\\end{array}$ ▷ in $\\mathbb{F}$   \n78: $\\triangleright$ Remove pads for excluded peers   \n79: $\\begin{array}{r}{m^{*}:={m^{*}}^{\\cdot}-\\sum_{p\\in P_{a l l}}\\mathrm{DC}\\mathrm{-SLOT}\\mathrm{-}\\mathrm{PAD}\\big(P_{e x},p,\\mathrm{K}_{e x}\\big[\\big],s\\big)}\\end{array}$   \n80: return $m^{*}$   \n81: proc DC-KE $\\mathrm{YS}(P,\\mathrm{NPK[]},m y,n s k,s i d H)$   \n82: for all $p\\in P$ do   \n83: $\\mathrm{K}[p]:={\\mathbb{N}}||\\mathrm{K}\\mathrm{E}.S\\mathsf{h a r e d K e y}(m y,p,n s k,\\mathrm{NPK}[p],s i d H)$   \n84: return K[ ]   \n85: proc BLAME(P, NPK[ ], my, NSK[ ], DC[ ][ ], sidH, Pex, Kex[ ][ ])   \n86: $P_{m a l}:=\\emptyset$   \n87: for all $p\\in P$ do   \n88: $P^{\\prime}\\overset{\\cdot}{:=}(P\\cup\\{m y\\}\\cup P_{e x})\\setminus\\{p\\}$   \n89: $\\mathrm{K}^{\\prime}[\\mathbf{\\Sigma}]:=\\mathrm{DC}\\mathbb{-}\\mathrm{KEYS}(P^{\\prime},\\mathrm{NPK}[\\mathbf{\\Sigma}],p,\\mathrm{NSK}[p],s i d H)$   \n90: ▷ Reconstruct purported message $m^{\\prime}$ of $p$   \n91: $m^{\\prime}:=\\mathrm{DC}[p][1]-\\mathrm{DC}\\mathrm{-SLOT}\\mathrm{-}\\mathrm{PAD}(P^{\\prime},p,\\mathrm{K}^{\\prime}[],1)$   \n92: ▷ Replay DC-net messages of $p$   \n93: $\\mathrm{DC^{\\prime}}[\\hat{]}:=\\mathrm{DC}\\mathbf{-}\\mathbf{M}\\mathrm{IX}\\big(P^{\\prime},\\bar{p},\\mathbf{K}^{\\prime}[\\hat{]},m^{\\prime}\\big)$   \n94: if $\\mathrm{DC^{\\prime}}[]\\neq\\mathrm{DC}[p][]$ then ▷ Exclude inconsistent $p$   \n95: Pnat := Pmat U {p}   \n96: $\\triangleright$ Verify that $p$ has published correct symmetric keys   \n97: for all $p_{e x}\\in P_{e x}$ do   \n98: if $\\mathrm{K}_{e x}[p][p_{e x}]\\neq\\mathrm{K}^{\\prime}[p_{e x}]$ then   \n99: $P_{m a l}:=P_{m a l}\\cup\\{p\\}$   \n100: return $P_{m a l}$   \n101: proc RESULT-OF-NEXT-R $\\mathrm{JN}(P_{e x N e x t},n e x t)$   \n102: $\\triangleright$ Hand over to next run and notify of peers to exclude   \n103: notify next of $P_{e x N e x t}$   \n104: $\\triangleright$ Return result of next run   \n105: result $:=\\mathrm{{join}}$ next   \n106: return result  \n\nand $m_{p^{\\prime}}$ cannot be predicted. This argument can be generalized to changing exactly $d$ messages for $1\\leq d<n$ .  \n\nAs the message ${\\mathsf{H}}\\big(\\big(\\mathrm{CM},\\mathrm{DC}[m y][]\\big)\\big)$ implements a hiding, binding and non-malleable commitment on $\\mathrm{DC}[m y][]$ (recall that adding randomness is not necessary because there is sufficient entropy in $\\mathrm{DC}[m y][])$ , it is infeasible, even for a rushing malicious peer $a$ , to have committed to an ill-formed vector that leaves $m_{p}$ intact. This is a contradiction, and thus the condition $m\\in M$ evaluates equivalently for all honest peers.  \n\nNow observe that the condition $m\\in M$ determines whether CONFIRM() is called. That is, whenever $\\mathbf{CoNFIRM}(i,P,M)$ is called by some honest peer $p$ , then $m_{p^{\\prime}}\\in M$ for all honest peers $p^{\\prime}$ . This shows part $(i)$ of validity.  \n\nFor part $(i i)$ $|M|\\leq|P|)$ observe that in the beginning of an execution and whenever $P$ changes, a new run with $|P|$ peers is started, each of which submits exactly one message. Thus $|M|=|P|$ . This shows validity.  \n\n3) Agreement: To show agreement, we have to show that for each run $i$ , if one honest peer $p$ calls $\\mathbf{CoNFIRM}(i,P,M)$ in some round, then every honest peer $p^{\\prime}$ calls $\\mathbf{CoNFIRM}(i,P,M)$ in the same round. This follows from validity: By part $(i)$ of validity, we know that if some honest peer calls CONFIRM $(i,P,M)$ , then $m_{p^{\\prime}}~\\in~M$ for every peer $p^{\\prime}$ in run $i$ . By construction of the protocol (Line 48), the condition $m_{p^{\\prime}}~\\in~M$ is exactly what determines whether $p^{\\prime}$ calls $\\mathbf{CoNFIRM}(i,P,M)$ . Thus every honest peer $p^{\\prime}$ calls CONFIRM $(i,P,M)$ in the same round, which shows agreement.  \n\n4) Termination: Now, we show why the protocol terminates for every honest peer. We first show that at least one malicious peer is excluded in each failed run. We have already argued above (for validity) that in the presence of an honest bulletin board, all honest peers take the same control flow decision (whether to call $\\mathrm{{CoNFIRM}(\\boldsymbol{\\mathbf{\\rho}})}$ or not at the end of each run). We can thus distinguish cases on this control flow decision.  \n\nIf CONFIRM() is called in a failed run, then it returns the same non-empty set of malicious peers (by the “correct exclusion” property), and those peers will be excluded by every honest peer. If CONFIRM() is not called in a run, then there must have been disruption by at least one malicious peer. Replaying all protocol messages of this run (with the help of then-revealed secret keys) clearly identifies at least one malicious peer, and since all honest peers run the same deterministic code (BLAME()) on the same inputs to do so, they will all exclude the same set of malicious peers.  \n\nWe have shown that in each failed run, all honest peers exclude the same non-empty set of malicious peers. Eventually, we reach one of two cases. In the first case, the number of unexcluded peers will drop below two; in that case the protocol is allowed to fail and thus there is nothing to show. In the second case, we reach a run in which all peers behave honestly (independently of whether they are controlled by the attacker). This run will successfully terminate, which shows termination.  \n\n# E. Variants of the Protocol  \n\nThe design of DiceMix follows the P2P paradigm, and consequently, we do not expect the bulletin board to implement any real functionality or perform any computation. The bulletin board is a simple broadcast mechanism and may be replaced by a suitable reliable broadcast protocol [54]. However, if one is willing to depend on a more sophisticated bulletin board with dedicated functionality, the efficiency of DiceMix can be improved. It is important to note that even a dedicated bulletin board is still only trusted for termination and not for anonymity.  \n\n![](/tmp/output/52_20250327033053/images/ea1e13408dbc7ab4cb06c875d68e93d1be31a664fc77c531f676a2291528cc9a.jpg)  \nFig. 3: Example of a DiceMix Execution with a Dedicated Bulletin Board. Run 1 fails due to DC-net disruption. Run 2 fails to confirm. Run 3 finally succeeds and run 4 is then aborted. Rows represent protocol runs and columns represent communication rounds. The blue arrows depict dependencies between runs, i.e., some run informs the next run about the peers to exclude. KE: Key exchange; CM: Commitment; DC: DC-net; SK: Reveal secret key; CF: Confirmation.  \n\n1) Dropping the Commitment Phase: Recall that the purpose of the non-malleable commitments is to prevent malicious peers from choosing their DC-net vectors depending on the DC-net vectors of the honest peers.  \n\nAssume that the bulletin board supports secure channels, and broadcasts the messages in the DC round only after all peers have submitted their messages. Then independence is ensured with an honest bulletin board, and we can drop the CM (commitment) round. This is secure because the independence of the DC-net vectors is necessary for termination but not for anonymity, and we trust the bulletin board for termination already. A serial protocol execution (without concurrency) will then follow the pattern “KE (DC $\\mathrm{CE}/\\mathrm{SK})+^{,}$ , where the plus indicates that these phases are performed once or several times. With the help of concurrency, we can run the key exchange (KE) concurrently to the confirmation phase (CF/SK), and reduce the number of rounds to $3+2f$ (assuming that the confirmation phase takes one round). An example run is depicted in Fig. 3.  \n\nNote that a revelation of symmetric keys (RV in the original protocol) will not be necessary anymore, because the malicious peers to exclude are determined before the DC round of the second run (see Section IV-C2 for an explanation of RV).  \n\n2) Bulletin Board Performs Expensive Computation: Moreover, a dedicated bulletin board can perform the expensive computation of solving the equation system involving the power sums, and broadcast the result instead of the DC-net vectors. The bulletin board would then also be responsible for handling inconsistent messages in the SK run; it would then announce the malicious peers after having received all secret keys. This saves communication in the rounds DC and SK. Again, security is preserved, because we trust the bulletin board for termination.  \n\n# V. PERFORMANCE ANALYSIS  \n\nIn this section, we analyze the performance of DiceMix We first analyze the communication costs, and then evaluate the running time with the help of a prototype implementation Our results show that DiceMix is practical and outperforms existing solutions.  \n\n# A. Communication  \n\nUsing concurrent runs, DiceMix needs $(c+3)+(c+1)f$ communication rounds, where $f$ is the number of peers actually disrupting the protocol execution, and $c$ is the number of rounds of the confirmation subprotocol. In the case $c=1$ , such as in our Bitcoin mixing protocol (Section VI), DiceMix needs just $4+2f$ rounds.  \n\nThe communication costs per run and per peer are dominated by the broadcast of the DC-net array $\\mathrm{DC}[m y][]$ of size $n\\cdot|m|$ bits, where $n$ is the number of peers and $|m|$ is the length of a mixed message. All three other broadcasts have constant size at any given security level. These communication costs have been shown to be asymptotically optimal for P2P mixing [20].  \n\n# B. Prototype Implementation  \n\nWe developed a proof-of-concept implementation of the DiceMix protocol. Our unoptimized implementation encompasses the complete functionality to enable testing a successful run of DiceMix without disruptions.  \n\nThe implementation is written in Python and uses OpenSSL for ECDSA signatures on the $\\mathtt{s e c p256k1}$ elliptic curve (as used in Bitcoin) at a security level of 128 bits. We use a Python wrapper for the PARI/GP library [47], [56] to find the roots of the power sum polynomial by the Kaltofen-Shoup algorithm for polynomial factorization [37].  \n\n1) Testbed: We tested our DiceMix implementation in Emulab [59]. Emulab is a testbed for distributed systems that enables a controlled environment with easily configurable parameters such as network topology or bandwidth of the communication links. We simulated a network setting in which all peers (10 Mbit/s) have pre-established TCP connections to a bulletin board (1 Gbit/s); all links had a delay of $50~\\mathrm{ms}$ . We used different Emulab machines $(2.2{-}3.0~\\mathrm{GHz})$ to simulate the peers; note that the slowest machine is the bottleneck due to the synchronization enforced by the broadcasts.  \n\n![](/tmp/output/52_20250327033053/images/0e1927357835c6aa1c7e22850c3a9fd8ca7e770f111e1857f61c8af30f35439d.jpg)  \nFig. 4: Wall-clock time and computation times. All peers have a bandwidth of 10 Mbit/s; the bulletin board has a total of 1 Gbit/s; all links have $50~\\mathrm{ms}$ latency.  \n\nWe ran the protocol with a varying number of peers, ranging from 20 to 100. Each peer had as input for the mixing a 160-bit message (e.g., a Bitcoin address).  \n\n2) Results: First, we measured wall-clock time, averaged over all peers. As shown in Fig. 4, we observe that with a moderate size of 50 participants, DiceMix runs in about 8 seconds. Second, we measured computation time; the results are depicted in Fig. 4. We considered the average total computation time spent by a peer and average computation time only for polynomial factorization, i.e., solving the equation system involving the power sums.  \n\n3) Optimization: We observe that solving the equation system is quite expensive, e.g., about one second for 100 peers. To demonstrate that this is mostly due to lack of optimization, we developed an optimized stand-alone application for this step in $\\mathrm{C}{+}{+}$ using the FLINT number theory library [34], which provides a highly optimized implementation of the Kaltofen-Shoup algorithm for polynomial factorization over finite fields [37]. Our optimized application solves the equation system involving the power sums in about 0.32 seconds for 100 peers on a $2.70\\:\\mathrm{GHz}$ (Intel Core i7-4800MQ) machine, using 6 MB of DDR3-1600 RAM. This shows that optimizations can reduce the running time of the protocol further.  \n\n4) Conclusion: The experimental results show that even our unoptimized implementation of DiceMix scales to a large number of peers and outperforms state-of-the-art P2P mixing solutions such as CoinShuffle [52] and Dissent [22] considerably. In comparison, CoinShuffle (as an tailored variant of the Dissent shuffle protocol) needs slightly less than three minutes to complete a successful run of the P2P mixing protocol in a very similar test environment with 50 peers.  \n\n# VI. EFFICIENT COIN MIXING IN BITCOIN  \n\nSeveral different heuristics to link Bitcoin payments sent or received by a particular user have been proposed in the literature [5], [6], [40], [46], [51], [53]. Ultimately, cryptocurrencies such as Bitcoin using a public blockchain may in fact provide less anonymity than traditional banking, as the deployment of proposed heuristics to the blockchain opens the possibility to know who paid what to whom. Coin mixing has emerged as a technique to overcome this problem while maintaining full compatibility with the current Bitcoin protocol.  \n\nA promising solution in this direction is CoinShuffle [52], a P2P mixing protocol based on a mixnet run by the peers to ensure the unlinkability of input and output accounts in a jointly created mixing transaction (a so-called CoinJoin transaction [43]). However, a run with a decent anonymity set of $n=50$ peers takes about three minutes to complete [52], assuming that every peer is honest. In the presence of $f$ disruptive peers aiming at impeding the protocol, $O(n f)$ communication rounds are required, most of them inevitably taking longer due to the disruptive peers delaying their messages intentionally. For instance, assume that there are $f\\mathrm{~=~}10$ disrupting peers; then the protocol needs more than 30 minutes to succeed, which arguably prohibits a practical deployment of CoinShuffle. As a consequence, we lack a coin mixing protocol for crypto-currencies that is efficient enough for practical deployment.  \n\nAs a solution, we propose CoinShuffle $^{;++}$ , a highly efficient coin mixing protocol resulting from the application of DiceMix to the Bitcoin setting.  \n\n# A. The Bitcoin System  \n\nBitcoin [2], [12], [49] is a crypto-currency run by a P2P network. An account in the Bitcoin system is associated with an ECDSA key pair; accounts are publicly identified by a 160-bit hash of the verification key, called an address. Every peer can create new accounts by creating fresh key pairs.  \n\nA peer can spend funds associated with her account by creating Bitcoin transactions, which associate funds with another account. In its simplest form, a Bitcoin transaction is composed of a transaction input (a reference to unspent funds in the blockchain associated with some account), a newly created transaction output, and the amount of funds to be transferred from the input to the output. For a transaction to be fully valid, it must be signed with the signing key of the input account.  \n\nBitcoin transactions can include multiple input and output accounts to spend funds simultaneously. In this case, the transaction must be signed with the all signing keys of the input accounts.  \n\n# B. Security Goals  \n\nApart from the security goals for a P2P mixing protocol (see Section II-E), a coin mixing protocol must guarantee correct balance. It ensures that no funds can be stolen from honest peers.  \n\nCorrect Balance: For every honest peer $p$ , the total balance of all accounts of peer $p$ is not reduced by running the coin mixing protocol (ignoring transaction fees).  \n\n# C. The CoinShuffle $^{++}$ Protocol  \n\nCoinShuffle $^{++}$ leverages DiceMix to perform a Bitcoin transaction where the input and output accounts for any given honest peer cannot be linked. In particular, CoinShuffle $^{;++}$ creates a fresh pair of signing-verification Bitcoin keys and returns the verification key to implement $\\mathrm{{GEN}()}$ .  \n\nThen, for the confirmation subprotocol CONFIRM(), CoinShuffle $^{\\mathrel{\\phantom{.}}\\mathbf{++}}$ uses CoinJoin [43], [45] to perform the actual mixing A CoinJoin transaction allows a set of peers to mix their coins without the help of a third party. In such a transaction, peers set their current Bitcoin accounts as input and a mixed list of fresh Bitcoin accounts as output. Crucially, peers can verify whether the transaction thereby constructed transfers the correct amount of funds to their fresh output account. Only if all peers agree and sign the transaction, it becomes valid. So in the case of CoinShuffle $^{\\mathrel{\\phantom{+}}\\mathrel{+{+}}}$ , the explicit confirmation provided by DiceMix is a list of valid signatures, one from each peer, on the CoinJoin transaction.  \n\nNote that DiceMix guarantees that everybody receives the correct list of output accounts in the confirmation subprotocol. So a peer refusing to sign the CoinJoin transaction can safely be considered malicious and removed. This is a crucial property for an anonymous CoinJoin-based approach; otherwise, a single malicious peer can refuse to sign the transaction and thus mount a DoS attacl on all other peers who cannot exclude the malicious peer if not convinced of his guilt.  \n\nWe define CoinShuffle $^{++}$ in Algorithm 2. There, we denote by CoinJoin $\\Gamma\\times\\big(V\\boldsymbol{K}_{i n}\\big[\\big],V\\boldsymbol{K}_{o u t},\\beta\\big)$ a CoinJoin transaction that transfers $\\beta$ bitcoins from every input account in $V K_{i n}{\\left[\\right]}$ to the output accounts, where $\\beta$ is a pre-arranged parameter; note that if there are $|P|$ unexcluded peers, then the P2P mixing protocol guarantees that there will be $|M|\\leq|P|$ output accounts. Moreover, we denote by Submit $(t x,\\sigma[])$ the submission of $t x$ including all signatures to the Bitcoin network.  \n\n1) Security Analysis: CoinShuffle $^{++}$ adheres to the requirements specified in Section IV-B. Thus, sender anonymity and termination in CoinShuffle $^{++}$ are immediate. (We refer the reader to [45] for a detailed taint-based analysis on the privacy implications of CoinJoin-based coin mixing protocols.) Correct balance is enforced by the CoinJoin paradigm: by construction, a peer signs only transactions that will transfer her funds from her input address to her output address.  \n\n2) Performance Analysis: In our performance analysis of DiceMix (Section V), GEN() creates a new ECDSA key pair and CONFIRM() obtains ECDSA signatures from all peers (using their initial ECDSA key pairs) on a bitstring of 160 bits. This is almost exactly CoinShuffl $^{3++}$ , so the performance analyses of DiceMix carries over to CoinShuffl $^{3++}$ .  \n\n3) Practical Considerations: There are several considerations when deploying CoinShuffle $^{:++}$ in practice. First, Bitcoin charges a small fee to prevent transaction flooding attacks. Second, the mixing amount $\\beta$ must be the same for all peers, but peers typically do not hold the exact mixing amount in their input Bitcoin account and thus may need a change address. Finally, after honestly performing the CoinShuffl $^{;++}$ protocol, a peer could spend her bitcoins in the input account before the CoinJoin transaction is confirmed, in a double-spending attempt. All these challenges are easy to overcome. We refer the reader to the literature on CoinJoin-based coin mixing, e.g., [43], [45], [52], for details.  \n\nDiceMix does not rely on any external anonymous channel (e.g., Tor network [23]) for mixing coins. Nevertheless, to ensure unlinkability of inputs of the CoinJoin transaction with network-level details such as IP addresses, using an external anonymous channel is highly recommended both for running DiceMix and actually spending the mixed funds later.  \n\n<html><body><table><tr><td>Algorithm2 CoinShuffle++</td></tr><tr><td>proc GEN() (uk, sk):= AccountGen() >Storesskinthewallet return vk</td></tr><tr><td>proc CONFIRM(i, P, VKout, my, VKin[], skin, sid)</td></tr><tr><td>tx := CoinJoinTx(VKin[],VKout,β)</td></tr><tr><td>o[my]:=Sign(skin, tx) broadcast o[my]</td></tr><tr><td>receive o[p] from all p E P</td></tr><tr><td>where Verify(VKin [P], o[p], tx) missing Pofr do → Peers refusing to sign are malicious</td></tr><tr><td>return Poff.</td></tr><tr><td>Submit(tx,o[]) return 0 > Success!</td></tr></table></body></html>  \n\n4) Compatibility and Extensibility: Since CoinJoin transactions work in the current Bitcoin network, CoinShuffle $^{++}$ is immediately deployable without any change to the system. Moreover, the fact that DiceMix is generic in the CONFIRM() function makes it possible to define variants of CoinShuffle $^{:++}$ to support a wide range of crypto-currencies and signature algorithms, including interactive signature protocols.  \n\nFor example, the integration of Schnorr signatures is planned in an upcoming Bitcoin software release [10]. This modification will enable aggregate signatures using an interactive two-round protocol among the peers in a CoinJoin transaction [44]. The first round of this two-round protocol does not depend on the details of the transactions and can be run in parallel to the third round (DC) of CoinShuffle $^{\\mathrel{\\phantom{+}}\\mathrel{+}\\mathrel{+}}$ ; this keeps the number of required communication rounds at $4f+2$ .  \n\nGiven that signatures are often the largest individual part of the transactions, aggregate signatures greatly reduce the size of transactions and thus the transaction fee, thereby making mixing using CoinJoin transactions even cheaper.  \n\n5) Resistance against DoS Attacks by Sybils: CoinShuffl $^{;++}$ makes sure that disruptive peers in a mixing will be excluded in due course. To avoid that the same peers cannot disrupt further protocol runs either, the bootstrapping mechanism (if executed on the bulletin board) can block the unspent transaction outputs in the blockchain used by the disruptive peers for a predefined period of time, e.g., an hour. (They should not blocked forever because peers could be unresponsive for legitimate reasons, e.g., unreliable connectivity.)  \n\nThis ensures that the number of unspent transactions outputs belonging to the attacker limits his ability to disrupt CoinShuffle $^{:++}$ on a particular bulletin board. The attacker can try to overcome the blocking by spending the corresponding funds to create new unspent transaction outputs (child outputs of the blocked outputs); however, this is expensive because he needs to pay transactions fees. Moreover, the bootstrapping mechanism can block not only the used transaction outputs but also their child outputs.  \n\n# VII. RELATED WORK IN CRYPTO-CURRENCIES  \n\nWe give an overview of the literature on privacy-preserving protocols for crypto-currencies. Related work for P2P mixing protocols is discussed throughout the paper.  \n\n# A. Tumblers  \n\nA tumbler provides a backwards-compatible centralized mixing service [11] to unlink users from their funds: several users transfer their funds to the tumbler, which returns them to the users at fresh addresses. The main advantage of a centralized approach is that it scales well to large anonymity sets, because the anonymity set is the set of all users using the service in some predefined time window. However, by using these services naively, a user must fully trust the tumbler: First, anonymity is restricted towards external observers, i.e., the mixing service itself can still determine the owner of the funds. Second and more important, the users have to transfer their funds to the tumbler, which could just steal them by refusing to return them.  \n\n1) Accountable Tumblers: Mixcoin [13] mitigates the second problem by holding the tumbler accountable if it steals the funds, but theft is still possible. Blindcoin [57] improves upon Mixcoin in that the tumbler additionally cannot break anonymity.  \n\n2) Blindly Signed Contracts and TumbleBit: Blindly Signed Contracts [36] and its successor TumbleBit [35] propose an untrusted tumbler based on the combination of blind signatures and smart contracts to solve both aforementioned challenges, i.e., theft and anonymity. To perform ordinary mixing this approach requires at least two transactions to be confirmed sequentially (in two different blocks), whereas CoinShuffle $^{++}$ requires just one transaction.  \n\nTumbleBit supports using the second transaction to send a payment to a recipient directly, which is then on par with CoinShuffl $^{;++}$ , which also requires one transaction for mixing and one transaction for sending a payment to a recipient. However, this mode of TumbleBit comes with limitations. First, it requires coordination between the tumbler and the recipient. Second, it requires more fees than CoinShuffl $\\mathrm{e}{++}$ because the CoinJoin transaction used in CoinShuffle $^{++}$ is cheap, in particular if using aggregate signatures. Third, it requires the payment amount to be exactly the mixing amount, which hinders availability severely, because it is very difficult to find enough users that are willing to send the exact same amount of funds at a similar time. With CoinShuffle $^{\\mathrel{\\phantom{.}}++}$ , instead, the second transaction, i.e., the actual spending transaction is a normal transaction and supports change addresses, at which peers get their remaining funds back.  \n\n# B. Other P2P Mixing Approaches  \n\nIn CoinParty [62], a set of mixing peers is used to mix funds of users. It is assumed that $1/3$ of the mixing parties are honest. This trust assumption is not in line with the philosophy of Bitcoin, which works in a P2P setting without strong identities, where Sybil attacks are easily possible.  \n\nCoinShuffle $^{;++}$ , instead, does not make any trust assumption on the mixing participants, except that there must be two honest peers, which is a fundamental requirement for any protocol providing anonymity.  \n\nXim [8] improves on its related previous work [6] in that it uses a fee-based advertisement mechanism to pair partners for mixing, and provides evidence of the agreement that can be leveraged if a party aborts. Even in the simple case of a mixing between two peers, Xim requires publishing several Bitcoin transactions in the Bitcoin blockchain, which takes on average at least ten minutes for each transaction.  \n\nIn contrast, CoinShuffle $^{++}$ requires to submit a single transaction to the Bitcoin blockchain independently on the number of peers.  \n\n# C. Privacy-preserving Crypto-currencies  \n\nBitcoin is by far the most widespread crypto-currency and will most probably retain this status in the foreseeable future, so users are in need of solutions enhancing privacy in Bitcoin. Nevertheless, several promising designs of crypto-currencies with built-in privacy features are available.  \n\n1) Zerocoin and Zerocash: Zerocoin [48] and its followup work Zerocash [7], whose implementation Zcash has been deployed recently [4], are crypto-currency protocols that provide anonymity by design. Although these solutions provide strong privacy guarantees, it is not clear whether Zcash will see widespread adoption, in particular given its reliance on a trusted setup due to the use of zkSNARKS.  \n\n2) CryptoNote: The CryptoNote design [58] relies on ring signatures to provide anonymity for the sender of a transaction. In contrast to CoinShuffle $^{:++}$ , an online mixing protocol is not necessary and a sufficient anonymity set can be created using funds of users currently not online. However, this comes with two important drawbacks for scalability.  \n\nFirst, CryptoNote requires each transaction to contain a ring signature of size $O(n)$ , where $n$ is the size of the anonymity set, whereas our approach based on CoinJoin needs only constant space per user. Storing the ring signatures requires a lot of precious space in the blockchain, and verifying them puts a large burden on all nodes in the currency network. (In other words, the advantage of CoinShuffle $^{++}$ is that it moves the anonymization work to an online mixing protocol, which is independent of the blockchain.)  \n\nSecond, CryptoNote is not compatible with pruning, a feature supported by the Bitcoin Core client [9]. Pruning reduces the storage requirements of nodes drastically by deleting spent transactions from local storage once verified. This is impossible in CryptoNote because it is not entirely clear whether funds in the blockchain have been spent or not. A CoinJoin-based approach such as CoinShuffle $^{++}$ does not suffer from this problem and is compatible with pruning.  \n\n# VIII. A DEANONYMIZATION ATTACK ON STATE-OF-THE-ART P2P MIXING PROTOCOLS  \n\nIn this section, we show a deanonymization attack on stateof-the-art P2P mixing protocols.  \n\nAt the core of the problem is handling of peers that appear to be offline. They cannot be handled like active disruptors: While sacrificing the anonymity of some peer $p$ is not at all a problem if peer $p$ is proven to be an active disruptor and thus malicious, sacrificing the anonymity of $p$ is a serious issue and can renders a protocol insecure if $p$ goes offline. Peer $p$ could in fact be honest, because there is no “smoking gun” that allows the other peers to conclude that $p$ is malicious.  \n\nOur attack is based on the well-known and very basic observation that an offline peer cannot possibly have sent a message, which comes in many shapes in basically every anonymous communication protocol with reasonable latency [14], [61]. While the attack relies on this very basic observation, it has been overlooked in the literature that a hard requirement to terminate successfully in the presence of offline peers makes existing P2P mixing protocols vulnerable.  \n\n![](/tmp/output/52_20250327033053/images/87717dcb486abde53b38db4246198486af655b01f372cf8dc6afbc836d2eb975.jpg)  \nFig. 5: A P2P Mixing Protocol under Attack. Peer $p$ is partitioned from the bulletin board $B B$ . The dashed rectangles indicate the message sets $M$ and $M^{\\prime}$ of the peers in the respective rectangle.  \n\n# A. Example: A Deanonymization Attack on Dissent  \n\nWe illustrate the attack on the Dissent shuffle protocol [22], [55].5 In the last communication round of the Dissent shuffle protocol, every peer publishes a decryption key. All decryption keys taken together enable the peers to decrypt anonymized ciphertexts, resulting in the final set $M$ of anonymized messages. (The rest of the protocol is not relevant for our attack.) The attack on the shuffle protocol now proceeds as follows (Fig. 5):  \n\n1) The network attacker does not interfere with the protocol until the last communication round. In the last round, the attacker partitions the network into a part with only one honest peer $p$ and a part with the remaining peers. Consequently, the last protocol message by peer $p$ (containing her decryption key) does not reach the other peers. As the attacker has learned all decryption keys (including that of $p$ ), he can decrypt the final set of messages $M$ , but nobody else can.6 However, anonymity is not broken so far.  \n\n2) The remaining peers must eventually conclude that peer $p$ is offline and exclude her; otherwise they will not be able to continue the protocol, because they cannot assume that $p$ will ever be reachable again. The strategy by which Dissent provides termination in such a situation is through a wrapper protocol that instructs the remaining peers to attempt a second run of Dissent without peer $p$ . In this second run, the remaining peers resubmit their input messages used in the first run [22, Section 5.4]. The attacker does not interfere with this second run, and so the run will succeed with a final set $M^{\\prime}$ of mixed messages.   \n3) Observe that $M^{\\prime}\\setminus M=\\{m_{p}\\}$ , since $p$ is the only peer present in the first run but not in the second. This breaks anonymity of $p$ .  \n\nThe issue on the formal side is an arguably too weak security definition. The core of the Dissent protocol [22], [55] does not provide termination on its own but just a form of accountability, which states that at least one active disruptor can be exposed in every failed run of the protocol. The underlying idea is to use the wrapper protocol to ensure termination by starting a new run of Dissent without the exposed disruptor whenever a run has failed.  \n\nThe formal analysis of the Dissent, however, does not cover the wrapper protocol. It considers only a single run of Dissent, and correctly establishes anonymity and accountability for a single run. It has been overlooked that anonymity is lost under sequential composition of several runs of Dissent using the same input messages, as prescribed in the wrapper protocol.  \n\nWhile Corrigan-Gibbs and Ford [22] acknowledge and mention the problem that the last protocol message may be withheld and thus some peer (or the network attacker) may learn the result of the protocol while denying it to others [22, Section 5.5], their discussion is restricted to reliability and fails to identify the consequences for anonymity.  \n\n# B. Generalizing the Attack  \n\nThe underlying reason for this intersection-like attack is a fairness issue: the attacker, possibly controlling some malicious peers, can learn (parts of) the final message set $M$ of a protocol run while denying $M$ to the other peers. If now some peer $p$ appears to be offline, e.g., because the attacker blocks network messages, the remaining peers must finish the protocol without $p$ with a message set $M^{\\prime}$ , which unlike $M$ does not contain $m_{p}$ . Thus the attacker has learned that $m_{p}$ belongs to $p$ .  \n\nSince fairness is a general problem in cryptography without an honest majority, it is not surprising that the attack can be generalized. Next we show a generic attack that breaks anonymity for every P2P mixing protocol that provides termination and supports arbitrarily chosen input messages.  \n\nAttack Description: We assume an execution of a P2P mixing protocol with peer set $P=\\{p_{1},...,p_{n}\\}$ and their set of fixed input messages $M=\\{m_{1},\\ldots,m_{n}\\}$ . We further assume that the attacker controls the network and a majority $A\\subset P$ of peers in the execution such that $|P|/2<|A|\\le|P|-3$ .  \n\nFor the sake of presentation, we assume that no two peers send a protocol message in the same communication round. (This models that the network attacker can determine the order of simultaneous messages arbitrarily.)  \n\nFor some $i$ , let $r$ be the first communication round after which input message $m_{i}$ of peer $p_{i}$ is known to a collusion of a minority $S$ of peers with $p_{i}\\notin S$ .7 Such a round exists, because every peer outputs $M$ at the end of a successful protocol execution, and $M$ contains $m_{i}$ . Note that knowledge of $m_{i}$ does not imply that the collusion $S$ of peers collectively knows that $m_{i}$ belongs to peer $p_{i}$ ; it just means that the collusion knows that the bitstring $m_{i}$ is one of the peers’ input messages.  \n\nAssume that $S\\subset A$ , i.e., $S$ is entirely controlled by the attacker. The attacker lets the first $r-1$ protocol rounds run normally. In round $r$ , he collects the protocol message and learns $m_{i}$ (by control of $S$ ). Then the attacker selects an index $i^{*}$ from the set of honest peers. Starting with round $r$ , the attacker only delivers protocol messages not from $p_{i^{*}}$ and not from his own peers in $A$ ; all these peers appear offline for the remaining peers in $R:=P\\setminus(\\{p_{i^{*}}\\}\\cup A)$ . By assumption, $|R|\\ge2$ , and hence by the termination property, those remaining peers in $R$ will finish the protocol with a public result set $M^{\\prime}\\subsetneq M$ .  \n\nWe distinguish cases. If $i^{*}=i$ , then $p_{i}\\notin R$ . Since additionally $R$ is a minority, which has not seen any protocol messages from $p_{i}$ after round $r\\mathrm{~-~}1$ , the peers in $R$ do not know $m_{i}$ , and thus $m_{i}\\notin M^{\\prime}$ . If instead $i^{*}\\neq i$ , then $p_{i}\\in R$ and the correctness of the protocol implies $m_{i}\\in M^{\\prime}$ .  \n\nIn other words, the attacker learns whether $m_{i}$ belongs to peer $p_{i^{*}}$ or not by checking whether $m_{i}\\notin M^{\\prime}$ . This breaks the anonymity of $p_{i^{*}}$ .  \n\n# C. How DiceMix Avoids the Attack  \n\nTo avoid the intersection of message sets, DiceMix draws fresh messages in each run. Also, whenever some honest peer $p$ excludes an unreachable honest peer $p^{\\prime}$ (and sacrifices the anonymity of $p^{\\prime}$ ), the correct confirmation property will ensure that the current run will not terminate successfully for peer $p^{\\prime}$ , because $p^{\\prime}$ and $p$ will have different views on the current set $P$ of unexcluded peers. Thus no anonymity is required for the current run and malicious and offline peers can be handled equally (as done throughout the previous sections).  \n\n# IX. CONCLUSIONS  \n\nIn this work we present DiceMix, a P2P mixing protocol based on DC-nets that enables participants to anonymously publish a set of messages ensuring sender anonymity and termination. DiceMix avoids slot reservation and still ensures that no collisions occur, not even with a small probability. This results in DiceMix requiring only $4+2f$ communication rounds in the presence of $f$ misbehaving peers. We implemented DiceMix and showed its practicality even for a large number of 50 to 100 peers.  \n\nWe use DiceMix to design CoinShuffle $^{++}$ , a practical decentralized coin mixing protocol for Bitcoin. Our evaluation results show that CoinShuffle $^{++}$ is a promising approach to ensure unlinkability of Bitcoin transaction while requiring no change to the current Bitcoin protocol.  \n\n# ACKNOWLEDGMENTS  \n\nWe thank Bryan Ford for insightful discussions, and the anonymous reviewers for their helpful comments. We also thank Henry Corrigan-Gibbs and Dan Boneh for sharing the manuscript of [20]. This work was supported by the German Ministry for Education and Research (BMBF) through funding for the German Universities Excellence Initiative.  \n\n# REFERENCES  \n\n[1] “AN.ON (anonymity.online),” https://anon.inf.tu-dresden.de/.   \n[2] “Bitcoin Developer Guide,” https://bitcoin.org/en/developer-guide.   \n[3] “NXT 1.7 release,” http://www.nxtinfo.org/2015/11/30/nxts-upcoming1-7-release-featuring-coin-shuffling-singleton-assets-account-controland-an-improved-forging-algorithm/.   \n[4] “Zcash,” https://z.cash/.   \n[5] E. Androulaki, G. O. Karame, M. Roeschlin, T. Scherer, and S. Capkun, “Evaluating user privacy in bitcoin,” in FC’13.   \n[6] S. Barber, X. Boyen, E. Shi, and E. Uzun, “Bitter to better. how to make Bitcoin a better currency,” in FC’12.   \n[7] E. Ben-Sasson, A. Chiesa, C. Garman, M. Green, I. Miers, E. Tromer, and M. Virza, “Zerocash: Decentralized anonymous payments from Bitcoin,” in S&P’14. [8] G. Bissias, A. P. Ozisik, B. N. Levine, and M. Liberatore, “Sybil-resistant mixing for Bitcoin,” in WPES’14.   \n[9] Bitcoin Core, “0.11.0 release notes,” https://github.com/bitcoin/bitcoin/ blob/v0.11.0/doc/release-notes.md#block-file-pruning.   \n[10] ——, “Segregated witness: the next steps,” https://bitcoincore.org/en/ 2016/06/24/segwit-next-steps/#schnorr-signatures.   \n[11] Bitcoin Wiki, “Mixing services,” https://en.bitcoin.it/wiki/Category: Mixing Services.   \n[12] J. Bonneau, A. Miller, J. Clark, A. Narayanan, J. A. Kroll, and E. W. Felten, “SoK: Research perspectives and challenges for bitcoin and cryptocurrencies,” in $S\\&P^{\\prime}I5$ .   \n[13] J. Bonneau, A. Narayanan, A. Miller, J. Clark, J. Kroll, and E. Felten, “Mixcoin: Anonymity for Bitcoin with accountable mixes,” in FC’14.   \n[14] N. Borisov, G. Danezis, P. Mittal, and P. Tabriz, “Denial of service or denial of security?” in CCS’07.   \n[15] J. Bos and B. den Boer, “Detection of disrupters in the DC protocol,” in EUROCRYPT’89.   \n[16] D. Cash, E. Kiltz, and V. Shoup, “The twin Diffie-Hellman problem and applications,” J. Cryptol., vol. 22, no. 4, 2009.   \n[17] D. Chaum, “The dining cryptographers problem: Unconditional sender and recipient untraceability,” J. Cryptol., vol. 1.   \n[18] ——, “Untraceable electronic mail, return addresses, and digital pseudonyms,” Comm. ACM, vol. 4, no. 2, 1981.   \n[19] R. Chien and W. Frazer, “An application of coding theory to document retrieval,” IEEE Trans. Inf. Theor., vol. 12, no. 2, 1966.   \n[20] H. Corrigan-Gibbs and D. Boneh, “Bandwidth-optimal DC-nets,” private communication.   \n[21]  H. Corrigan-Gibbs, D. Boneh, and D. Mazieres, Riposte: An anonymous messaging system handling millions of users,” in S&P’15.   \n[22] H. Corrigan-Gibbs and B. Ford, “Dissent: Accountable anonymous group messaging,” in CCS’10.   \n[23] R. Dingledine, N. Mathewson, and P. Syverson, “Tor: The secondgeneration onion router,” in USENIX Security’04.   \n[24] D. Dolev, R. Reischuk, and H. R. Strong, “Early stopping in byzantine agreement,” J. ACM, vol. 37, no. 4, 1990.   \n[25] L. A. Dunning and R. Kresman, “Privacy preserving data sharing with anonymous ID assignment,” IEEE Trans. Inf. Forensic Secur., vol. 8, no. 2, 2013.   \n[26] M. Florian, J. Walter, and I. Baumgart, “Sybil-resistant pseudonymization and pseudonym change without trusted third parties,” in WPES’15.   \n[27] C. Franck, “Dining cryptographers with 0.924 verifiable collision resolution,” Annales UMCS, Informatica, vol. 14, no. 1, 2014.   \n[28] C. Franck and J. van de Graaf, “Dining cryptographers are practical,” arXiv CoRR abs/1402.2269, https://arxiv.org/abs/1402.2269.   \n[29] E. S. V. Freire, D. Hofheinz, E. Kiltz, and K. G. Paterson, “Noninteractive key exchange,” in PKC’13, 2013.   \n[30] S. Goel, M. Robson, M. Polte, and E. G. Sirer, “Herbivore: A scalable and efficient protocol for anonymous communication,” Cornell University, Tech. Rep. 2003-1890.   \n[31] D. M. Goldschlag, M. Reed, and P. Syverson, “Hiding Routing Information,” in Information Hiding: First International Workshop, 1996.   \n[32] P. Golle and A. Juels, “Dining cryptographers revisited,” in EUROCRYPT’04.   \n[33] H. W. Gould, “The Girard-Waring power sum formulas for symmetric functions and Fibonacci sequences,” Fibonacci Quarterly, vol. 37, no. 2, 1999, http://www.fq.math.ca/Issues/37-2.pdf.   \n[34] W. Hart, F. Johansson, and S. Pancratz, “FLINT: Fast Library for Number Theory,” 2015, version 2.5.2, http://flintlib.org.   \n[35] E. Heilman, F. Baldimtsi, L. Alshenibr, A. Scafuro, and S. Goldberg, “TumbleBit: An untrusted tumbler for Bitcoin-compatible anonymous payments,” in NDSS’17.   \n[36] E. Heilman, F. Baldimtsi, and S. Goldberg, “Blindly signed contracts: Anonymous on-blockchain and off-blockchain Bitcoin transactions,” in BITCOIN’16.   \n[37] E. Kaltofen and V. Shoup, “Fast polynomial factorization over high algebraic extensions of finite fields,” in ISSAC’97.   \n[38] W. Kautz and R. Singleton, “Nonrandom binary superimposed codes,” IEEE Trans. Inf. Theor., vol. 10, no. 4, pp. 363–377, 2006.   \n[39] S. Kochen, A. Sokolov, and K. Fuller, “IRCv3.2 server-time extension,” 2012, http://ircv3.net/specs/extensions/server-time-3.2.html.   \n[40] P. Koshy, D. Koshy, and P. McDaniel, “An analysis of anonymity in Bitcoin using P2P network traffic,” in FC’14.   \n[41] A. Krasnova, M. Neikes, and P. Schwabe, “Footprint scheduling for dining-cryptographer networks,” in FC’16.   \n[42] D. Krawisz, “Mycelium Shufflepuff (an inplementation of CoinShuffle),” https://github.com/DanielKrawisz/Shufflepuff.   \n[43] G. Maxwell, “CoinJoin: Bitcoin privacy for the real world,” Post on Bitcoin Forum, 2013, https://bitcointalk.org/index.php?topi $\\v{U}:=\\mathbf{0},$ 279249.   \n[44] —, “Signature aggregation for improved scalablity,” 2016, https:// bitcointalk.org/index.php?topic $\\v u=$ 1377298.0.   \n[45] S. Meiklejohn and C. Orlandi, “Privacy-enhancing overlays in Bitcoin,” in BITCOIN’15.   \n[46] S. Meiklejohn, M. Pomarole, G. Jordan, K. Levchenko, D. McCoy, G. M. Voelker, and S. Savage, “A fistful of bitcoins: Characterizing payments among men with no names,” in IMC’13.   \n[47] A. Mellit, “PARI/GP Python interface,” https://code.google.com/archive/ p/pari-python/.   \n[48] I. Miers, C. Garman, M. Green, and A. D. Rubin, “Zerocoin: Anonymous distributed e-cash from Bitcoin,” in S&P’13.   \n[49] S. Nakamoto, “Bitcoin: A peer-to-peer electronic cash system,” https: //bitcoin.org/bitcoin.pdf, 2008.   \n[50] V. Y. Pan, “Faster solution of the key equation for decoding BCH error-correcting codes,” in STOC’97, 1997, pp. 168–175.   \n[51] F. Reid and M. Harrigan, “An analysis of anonymity in the Bitcoin system,” in SXSW’13.   \n[52] T. Ruffing, P. Moreno-Sanchez, and A. Kate, “CoinShuffle: Practical decentralized coin mixing for Bitcoin,” in ESORICS’14, 2014.   \n[53] M. Spagnuolo, F. Maggi, and S. Zanero, “BitIodine: Extracting intelligence from the Bitcoin network,” in FC’14, 2014.   \n[54] T. K. Srikanth and S. Toueg, “Simulating authenticated broadcasts to derive simple fault-tolerant algorithms,” Distributed Computing, vol. 2, no. 2, 1987.   \n[55] E. Syta, H. Corrigan-Gibbs, S.-C. Weng, D. Wolinsky, B. Ford, and A. Johnson, “Security analysis of accountable anonymity in Dissent,” TISSEC, vol. 17, no. 1, 2014.   \n[56] PARI/GP 2.3, The PARI Group, Bordeaux, http://pari.math.ubordeaux.fr/.   \n[57] L. Valenta and B. Rowan, “Blindcoin: Blinded, accountable mixes for Bitcoin,” in BITCOIN’15.   \n[58] N. van Saberhagen, “CryptoNote,” 2013, https://cryptonote.org/ whitepaper.pdf.   \n[59] B. White, J. Lepreau, L. Stoller, R. Ricci, S. Guruprasad, M. Newbold, M. Hibler, C. Barb, and A. Joglekar, “An integrated experimental environment for distributed systems and networks,” SIGOPS Oper. Syst. Rev., vol. 36, 2002.   \n[60] D. I. Wolinsky, H. Corrigan-Gibbs, B. Ford, and A. Johnson, “Dissent in numbers: Making strong anonymity scale,” in OSDI’12.   \n[61] D. I. Wolinsky, E. Syta, and B. Ford, “Hang with your buddies to resist intersection attacks,” in CCS’13.   \n[62] J. H. Ziegeldorf, F. Grossmann, M. Henze, N. Inden, and K. Wehrle, “CoinParty: Secure multi-party mixing of bitcoins,” in CODASPY’15.  "}