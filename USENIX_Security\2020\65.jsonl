{"text": "# High Accuracy and High Fidelity Extraction of Neural Networks  \n\n<PERSON>, Northeastern University, Google Brain; <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Google Brain  \n\nhttps://www.usenix.org/conference/usenixsecurity20/presentation/jagielski  \n\nThis paper is included in the Proceedings of the 29th USENIX Security Symposium. August 12–14, 2020 978-1-939133-17-5  \n\nOpen access to the Proceedings of the 29th USENIX Security Symposium is sponsored by USENIX.  \n\n# High Accuracy and High Fidelity Extraction of Neural Networks  \n\n<PERSON>†,∗, <PERSON>\\*, <PERSON>\\*, <PERSON>\\*, and <PERSON>\\*  \n\n†Northeastern University \\*Google Research  \n\n# Abstract  \n\nIn a model extraction attack, an adversary steals a copy of a remotely deployed machine learning model, given oracle prediction access. We taxonomize model extraction attacks around two objectives: accuracy, i.e., performing well on the underlying learning task, and fidelity, i.e., matching the predictions of the remote victim classifier on any input.  \n\nTo extract a high-accuracy model, we develop a learningbased attack exploiting the victim to supervise the training of an extracted model. Through analytical and empirical arguments, we then explain the inherent limitations that prevent any learning-based strategy from extracting a truly high-fidelity model—i.e., extracting a functionally-equivalent model whose predictions are identical to those of the victim model on all possible inputs. Addressing these limitations, we expand on prior work to develop the first practical functionally-equivalent extraction attack for direct extraction (i.e., without training) of a model’s weights.  \n\nWe perform experiments both on academic datasets and a state-of-the-art image classifier trained with 1 billion proprietary images. In addition to broadening the scope of model extraction research, our work demonstrates the practicality of model extraction attacks against production-grade systems.  \n\n# 1 Introduction  \n\nMachine learning, and neural networks in particular, are widely deployed in industry settings. Models are often deployed as prediction services or otherwise exposed to potential adversaries. Despite this fact, the trained models themselves are often proprietary and are closely guarded.  \n\nThere are two reasons models are often seen as sensitive. First, they are expensive to obtain. Not only is it expensive to train the final model [1] (e.g., Google recently trained a model with 340 million parameters on hardware costing 61,000 USD per training run [2]), performing the work to identify the optimal set of model architecture, training algorithm, and hyper-parameters often eclipses the cost of training the final model. Further, training these models also requires investing in expensive collection process to obtain the training datasets necessary to obtain an accurate classifier [3–6]. Second, there are security [7, 8] and privacy [9, 10] concerns for revealing trained models to potential adversaries.  \n\nConcerningly, prior work found that an adversary with query access to a model can steal the model to obtain a copy that largely agrees with the remote victim models [8, 11–16]. These extraction attacks are therefore important to consider.  \n\nIn this paper, we systematize the space of model extraction around two adversarial objectives: accuracy and fidelity. Accuracy measures the correctness of predictions made by the extracted model on the test distribution. Fidelity, in contrast, measures the general agreement between the extracted and victim models on any input. Both of these objectives are desirable, but they are in conflict for imperfect victim models: a high-fidelity extraction should replicate the errors of the victim, whereas a high-accuracy model should instead try to make an accurate prediction. At the high-fidelity limit is functionally-equivalent model extraction: the two models agree on all inputs, both on and off the underlying data distribution.  \n\nWhile most prior work considers accuracy [7, 11, 13], we argue that fidelity is often equally important. When using model extraction to mount black-box adversarial example attacks [7], fidelity ensures the attack is more effective because more adversarial examples transfer from the extracted model to the victim. Membership inference [9, 10] benefits from the extracted model closely replicating the confidence of predictions made by the victim. Finally, a functionally-equivalent extraction enables the adversary to inspect whether internal representations reveal unintended attributes of the input—that are statistically uncorrelated with the training objective, enabling the adversary to benefit from overlearning [17].  \n\nWe design one attack for each objective. First, a learningbased attack, which uses the victim to generate labels for training the extracted model. While existing techniques already achieve high accuracy, our attacks are $16\\times$ more queryefficient and scale to larger models. We perform experiments that surface inherent limitations of learning-based extraction attacks and argue that learning-based strategies are ill-suited to achieve high-fidelity extraction. Then, we develop the first practical functionally-equivalent attack, which directly recovers a two-layer neural network’s weights exactly given access to double-precision model inference. Compared to prior work, which required a high-precision power side-channel [18] or access to model gradients [19], our attack only requires inputoutput access to the model, while simultaneously scaling to larger networks than either of the prior methods. We make the following contributions:  \n\nWe taxonomize the space of model extraction attacks by exploring the objective of accuracy and fidelity. • We improve the query efficiency of learning attacks for accuracy extraction and make them practical for millionsof-parameter models trained on billions of images. • We achieve high-fidelity extraction by developing the first practical functionally-equivalent model extraction. We mix the proposed methods to obtain a hybrid method which improves both accuracy and fidelity extraction.  \n\n# 2 Preliminaries  \n\nWe consider classifiers with domain $\\boldsymbol{\\chi}\\subseteq\\mathbb{R}^{d}$ and range $\\mathcal{Y}\\subseteq$ $\\mathbb{R}^{K}$ ; the output of the classifier is a distribution over $K$ class labels. The class assigned to an input $x$ by a classifier $f$ is ar $\\mathsf{g m a x}_{i\\in[K]}f(x)_{i}$ (for $n\\in\\mathbb{Z}$ , we write $[n]=\\{1,2,\\dots n\\},$ . In order to satisfy the constraint that a classifier’s output is a distribution, a softmax $\\upsigma(\\cdot)$ is typically applied to the output of an arbitrary function $f_{L}:\\mathcal{X}\\to\\mathbb{R}^{K}$ :  \n\n$$\n\\upsigma(f_{L}(x))_{i}=\\frac{\\exp(f_{L}(x)_{i})}{\\sum_{j}\\exp(f_{L}(x)_{j})}.\n$$  \n\nWe call the function $f_{L}(\\cdot)$ the logit function for a classifier $f$ To convert a class label into a probability vector, it is common to use one-hot encoding: for a value $j\\in[K]$ , the one-hot encoding $O H(j;K)$ is a vector in $\\mathbb{R}^{K}$ with $O H(j;K)_{i}=\\mathbb{1}(i=$ $j)$ —that is, it is 1 only at index $j$ , and 0 elsewhere.  \n\nModel extraction concerns reproducing a victim model, or oracle, which we write $O:X\\rightarrow Y$ . The model extraction adversary will run an extraction algorithm $\\boldsymbol{\\mathcal{A}}(\\boldsymbol{O})$ , which outputs the extracted model $\\hat{O}$ . We will sometimes parameterize the oracle (resp. extracted model) as $O_{\\Theta}$ (resp. $\\hat{O}_{\\Theta}\\dot{}$ ) to denote that it has model parameters θ—we will omit this when unnecessary or apparent from context.  \n\nIn this work, we consider $o$ and $\\hat{O}$ to both be neural networks. A neural network is a sequence of operations— alternatingly applying linear operations and non-linear operations—a pair of linear and non-linear operations is called a layer. Each linear operation projects onto some space $\\mathbb{R}^{h}$ — the dimensionality $h$ of this space is referred to as the width of the layer. The number of layers is the depth of the network. The non-linear operations are typically fixed, while the linear operations have parameters which are learned during training. The function computed by layer $i.$ , $f_{i}(a)$ , is therefore computed as $f_{i}(a)=\\bar{g_{i}(A^{(i)}a+\\bar{B}^{(i)})}$ , where $g_{i}$ is the ith non-linear function, and $A^{(i)},B^{(i)}$ are the parameters of layer i $\\mathbf{\\nabla}_{A}(i)$ is the weights, $B^{(i)}$ the biases). A common choice of activation is the rectified linear unit, or ReLU, which sets $\\mathrm{ReLU}(x)=\\operatorname*{max}(0,x)$ . Introduced to improve the convergence of optimization when training neural networks, the ReLU activation has established itself as an effective default choice for practitioners [20]. Thus, we consider primarily ReLU networks in this work.  \n\nThe network structure described here is called fully connected because each linear operation “connects\" every input node to every output node. In many domains, such as computer vision, this is more structure than necessary. A neuron computing edge detection, for example, only needs to use information from a small region of the image. Convolutional networks were developed to combat this inefficiency—the linear functions become filters, which are still linear, but are only applied to a small (e.g., 3x3 or $5\\mathrm{x}5$ ) window of the input. They are applied to every window using the same weights, making convolutions require far fewer parameters than fully connected networks.  \n\nNeural networks are trained by empirical risk minimization. Given a dataset of $n$ samples $\\mathcal{D}=\\{x_{i},y_{i}\\}_{i=1}^{n}\\subseteq\\mathcal{X}\\times\\mathcal{Y}$ , training involves minimizing a loss function $L$ on the dataset with respect to the parameters of the network $f$ . A common loss function is the cross-entropy loss $H$ for a sample $(x,y)$ : $\\begin{array}{r}{H(y,f(x))=-\\sum_{k\\in[K]}y_{k}\\log(f(x)_{k})}\\end{array}$ , where $y$ is the probability (or one-hot) vector for the true class. The cross-entropy loss on the full dataset is then  \n\n$$\nL(\\mathcal D;f)=\\frac{1}{n}\\sum_{i=1}^{n}H(y_{i},f(x_{i}))=-\\frac{1}{n}\\sum_{i=1}^{n}\\sum_{k\\in[K]}y_{k}\\log(f(x)_{k}).\n$$  \n\nThe loss is minimized with some form of gradient descent, often stochastic gradient descent (SGD). In SGD, gradients of parameters θ are computed over a randomly sampled batch $B$ , averaged, and scaled by a learning rate $\\boldsymbol{\\mathrm{n}}$ :  \n\n$$\n\\boldsymbol{\\Theta}_{t+1}=\\boldsymbol{\\Theta}_{t}-\\frac{\\boldsymbol{\\upeta}}{|B|}\\sum_{i\\in B}\\nabla_{\\boldsymbol{\\Theta}}H(y_{i},f(x_{i})).\n$$  \n\nOther optimizers [21–23] use gradient statistics to reduce the variance of updates which can result in better performance.  \n\nA less common setting, but one which is important for our work, is when the target values $y$ which are used to train the network are not one-hot values, but are probability vectors output by a different model $g(x)$ . When training using the dataset $\\dot{\\mathcal{D}}_{g}=\\{x_{i},g(x_{i})^{1/T}\\}_{i=1}^{n}$ , we say the trained model is distilled from $g$ with temperature $T$ , referring to the process of distillation introduced in Hinton et al. [24]. Note that the values of $g(x_{i})^{1/T}$ are always scaled to sum to 1.  \n\n# 3 Taxonomy of Threat Models  \n\nWe now address the spectrum of adversaries interested in extracting neural networks. As illustrated in Table 1, we taxonomize the space of possible adversaries around two overarching goals—theft and reconnaissance. We detail why extraction is not always practically realizable by constructing models that are impossible to extract, or require a large number of queries to extract. We conclude our threat model with a discussion of how adversarial capabilities (e.g., prior knowledge of model architecture or information returned by queries) affect the strategies an adversary may consider.  \n\n# 3.1 Adversarial Motivations  \n\nModel extraction attacks target the confidentiality of a victim model deployed on a remote service. A model refers here to both the architecture and its parameters. Architectural details include the learning hypothesis (i.e., neural network in our case) and corresponding details (e.g., number of layers and activation functions for neural networks). Parameter values are the result of training.  \n\nFirst, we consider theft adversaries, motivated by economic incentives. Generally, the defender went through an expensive process to design the model’s architecture and train it to set parameter values. Here, the model can be viewed as intellectual property that the adversary is trying to steal. A line of work has in fact referred to this as “model stealing” [11].  \n\nIn the latter class of attacks, the adversary is performing reconnaissance to later mount attacks targeting other security properties of the learning system: e.g., its integrity with adversarial examples [7], or privacy with training data membership inference [9,10]. Model extraction enables an adversary previously operating in a black-box threat model to mount attacks against the extracted model in a white-box threat model. The adversary has—by design—access to the extracted model’s parameters. In the limit, this adversary would expect to extract an exact copy of the oracle.  \n\nThe goal of exact extraction is to produce $\\hat{O}_{\\Theta}=O_{\\Theta}$ , so that the model’s architecture and all of its weights are identical to the oracle. This definition is purely a strawman—it is the strongest possible attack, but it is fundamentally impossible for many classes of neural networks, including ReLU networks, because any individual model belongs to a large equivalence class of networks which are indistinguishable from input-output behavior. For example, we can scale an arbitrary neuron’s input weights and biases by some $c>0$ , and scale its output weights and biases by $c^{-1}$ ; the resulting model’s behavior is unchanged. Alternatively, in any intermediate layer of a ReLU network, we may also add a dead neuron which never contributes to the output, or might permute the (arbitrary) order of neurons internally. Given access to input-output behavior, the best we can do is identify the equivalence class the oracle belongs to.  \n\n![](/tmp/output/65_20250325154860/images/d448f563c1d37619d4130554e9c2442362bae444ae09776446fb19f689f83bc9.jpg)  \n\nFigure 1: Illustrating fidelity vs. accuracy. The solid blue line is the oracle; functionally equivalent extraction recovers this exactly. The green dash-dot line achieves high fidelity: it matches the oracle on all data points. The orange dashed line achieves perfect accuracy: it classifies all points correctly.  \n\n# 3.2 Adversarial Goals  \n\nThis perspective yields a natural spectrum of realistic adversarial goals characterizing decreasingly precise extractions.  \n\nFunctionally Equivalent Extraction The goal of functionally equivalent extraction is to construct an $\\hat{O}$ such that $\\forall x\\in\\mathcal{X}$ , ${\\hat{O}}(x)=O(x)$ . This is a tractable weakening of the exact extraction definition from earlier—it is the hardest possible goal using only input-output pairs. The adversary obtains a member of the oracle’s equivalence class. This goal enables a number of downstream attacks, including those involving inspection of the model’s internal representations like overlearning [17], to operate in the white-box threat model.  \n\nFidelity Extraction Given some target distribution $\\mathcal{D}_{F}$ over $\\chi$ , and goal similarity function $S(p_{1},p_{2})$ , the goal of fidelity extraction is to construct an $\\hat{O}$ that maximizes $\\mathbf{Pr}_{\\boldsymbol{x}\\sim\\mathcal{D}_{F}}\\left[S(\\hat{O}(\\boldsymbol{x}),O(\\boldsymbol{x}))\\right]$ . In this work, we consider only label agreement, where $S(p_{1},p_{2})=\\mathbb{1}(\\arg\\operatorname*{max}(p_{1})=$ argmax $\\left(p_{2}\\right),$ ); we leave exploration of other similarity functions to future work.  \n\nA natural distribution of interest $\\mathcal{D}_{F}$ is the data distribution itself—the adversary wants to make sure the mistakes and correct labels are the same between the two models. A reconnaissance attack for constructing adversarial examples would care about a perturbed data distribution; mistakes might be more important to the adversary in this setting. Membership inference would use the natural data distribution, including any outliers. These distributions tend to be concentrated on a low-dimension manifold of $\\chi$ , making fidelity extraction significantly easier than functionally equivalent extraction.  \n\nTable 1: Existing Model Extraction Attacks. Model types are abbreviated: $\\mathbf{LM}=$ Linear Model, $\\mathbf{NN}=\\mathbf{\\Psi}.$ Neural Network, $\\mathrm{DT=}$ Decision Tree, $\\mathrm{CNN}=\\mathrm{1}$ Convolutional Neural Network.   \n\n\n<html><body><table><tr><td>Attack</td><td>Type</td><td>Model type</td><td>Goal</td><td>Query Output</td></tr><tr><td>Lowd&Meek[8]</td><td>DirectRecovery</td><td>LM</td><td>FunctionallyEquivalent</td><td>Labels</td></tr><tr><td>Trameret al.[11]</td><td>(Active)Learning</td><td>LM, NN</td><td>Task Accuracy,Fidelity</td><td>Probabilities,labels</td></tr><tr><td>Tramer et al.[11]</td><td>Path finding</td><td>DT</td><td>Functionally Equivalent</td><td>Probabilities,labels</td></tr><tr><td>Milliet al.[19] (theoretical)</td><td>DirectRecovery</td><td>NN (2 layer)</td><td>Functionally Equivalent</td><td>Gradients, logits</td></tr><tr><td>Milli et al. [19]</td><td>Learning</td><td>LM, NN</td><td>TaskAccuracy</td><td>Gradients</td></tr><tr><td>Pal et al. [15]</td><td>Activelearning</td><td>NN</td><td>Fidelity</td><td>Probabilities,labels</td></tr><tr><td>Chandrasekharanetal.[13]</td><td>Activelearning</td><td>LM</td><td>Functionally Equivalent</td><td>Labels</td></tr><tr><td>Copycat CNN [16]</td><td>Learning</td><td>CNN</td><td>Task Accuracy,Fidelity</td><td>Labels</td></tr><tr><td>Papernot et al. [7]</td><td>Activelearning</td><td>NN</td><td>Fidelity</td><td>Labels</td></tr><tr><td>CSI NN [25]</td><td>DirectRecovery</td><td>NN</td><td>FunctionallyEquivalent</td><td>PowerSideChannel</td></tr><tr><td>Knockoff Nets [12]</td><td>Learning</td><td>NN</td><td>TaskAccuracy</td><td>Probabilities</td></tr><tr><td>Functionally equivalent (this work)</td><td>DirectRecovery</td><td>NN (2 layer)</td><td>FunctionallyEquivalent</td><td>Probabilities,logits</td></tr><tr><td>Eficient learning (this work)</td><td>Learning</td><td>NN</td><td>Task Accuracy,Fidelity</td><td>Probabilities</td></tr></table></body></html>  \n\nIndeed, functionally equivalent extraction achieves a perfect fidelity of 1 on all distributions and all similarity functions.  \n\nTask Accuracy Extraction For the true task distribution $\\mathcal{D}_{A}$ over $\\chi\\times\\mathcal{Y}$ , the goal of task accuracy extraction is to construct an $\\hat{O}$ maximizing $\\mathrm{Pr}_{(x,y)\\sim\\mathcal{D}_{A}}$ $\\left[\\mathrm{argmax}(\\hat{O}(x))=y\\right]$ This goal is to match (or exceed) the accuracy of the target model, which is the easiest goal to consider in this taxonomy (because it doesn’t need to match the mistakes of $o$ ).  \n\nExisting Attacks In Table 1, we fit previous model extraction work into this taxonomy, as well as discuss their techniques. Functionally equivalent extraction has been considered for linear models [8, 13], decision trees [11], both given probabilities, and neural networks [19, 25], given extra access. Task accuracy extraction has been considered for linear models [11] and neural networks [12, 16, 19], and fidelity extraction has also been considered for linear models [11] and neural networks [7, 15]. Notably, functionally equivalent attacks require model-specific techniques, while task accuracy and fidelity typically use generic learning-based approaches.  \n\n# 3.3 Model Extraction is Hard  \n\nBefore we consider adversarial capabilities in Section 3.4 and potential corresponding approaches to model extraction, we must understand how successful we can hope to be. Here, we present arguments that will serve to bound our expectations. First, we will identify some limitations of functionally equivalent extraction by constructing networks which require arbitrarily many queries to extract. Second, we will present another class of networks that cannot be extracted with fidelity without querying a number of times exponential in its depth. We provide intuition in this section and later prove these statements in Appendix A.  \n\nExponential hardness of functionally equivalent attacks. In order to show that functionally equivalent extraction is intractable in the worst case, we construct of a class of neural networks that are hard to extract without making exponentially many queries in the network’s width.  \n\nTheorem 1. There exists a class of width $3k$ and depth 2 neural networks on domain $[0,1]^{d}$ (with precision $p$ numbers) with $d\\geq k$ that require, given logit access to the networks, $\\Theta(p^{k})$ queries to extract.  \n\nThe precision $p$ is the number of possible values a feature can take from $[0,1]$ . In images with 8-bit pixels, we have $p=256$ . The intuition for this theorem is that a width $3k$ network can implement a function that returns a non-zero value on at most a $p^{-k}$ fraction of the space. In the worst case, $p^{k}$ queries are necessary to find this fraction of the space.  \n\nNote that this result assumes the adversary can only observe the input-output behavior of the oracle. If this assumption is broken then functionally equivalent extraction becomes practical. For example, Batina et al. [25] perform functionally equivalent extraction by performing a side channel attack (specifically, differential power analysis [26]) on a microprocessor evaluating the neural network.  \n\nWe also observe in Theorem 2 that, given white-box access to two neural networks, it is NP-hard in general to test if they are functionally equivalent. We do this by constructing two networks that differ only in coordinates satisfying a subset sum instance. Then testing functional equivalence for these networks is as hard as finding the satisfying subset.  \n\nTheorem 2 (Informal). Given their weights, it is NP-hard to test whether two neural networks are functionally equivalent.  \n\nAny attack which can claim to perform functionally equivalent extraction efficiently (both in number of queries used and in running time) must make some assumptions to avoid these pathologies. In Section 6, we will present and discuss the assumptions of a functionally equivalent extraction attack for two-layer neural network models.  \n\nLearning approaches struggle with fidelity. A final difficulty for model extraction comes from recent work in learnability [27]. Das et al. prove that, for deep random networks with input dimension $d$ and depth $h$ , model extraction approaches that can be written as Statistical Query (SQ) learning algorithms require $\\exp(O(h))$ samples for fidelity extraction. SQ algorithms are a restricted form of learning algorithm which only access the data with noisy aggregate statistics; many learning algorithms, such as (stochastic) gradient descent and PCA, are examples. As a result, most learning-based approaches to model extraction will inherit this inefficiency. A sample-efficient approach therefore must either make assumptions about the model to be extracted (to distinguish it from a deep random network), or must access its dataset without statistical queries.  \n\nTheorem 3 (Informal [27]). Random networks with domain $\\{0,1\\}^{d}$ and range $\\{0,1\\}$ and depth h require $\\exp(O(h))$ samples to learn in the SQ learning model.  \n\n# 3.4 Adversarial Capabilities  \n\nWe organize an adversary’s prior knowledge about the oracle and its training data into three categories—domain knowledge, deployment knowledge, and model access.  \n\n# 3.4.1 Domain Knowledge  \n\nDomain knowledge describes what the adversary knows about the task the model is designed for. For example, if the model is an image classifier, then the model output should not change under standard image data augmentations, such as shifts, rotations, or crops. Usually, the adversary should be assumed to have as much domain knowledge as the oracle’s designer.  \n\nIn some domains, it is reasonable to assume the adversary has access to public task-relevant pretrained models or datasets. This is often the case for learning-based model extraction, which we develop in Section 4. We consider an adversary using part of a public dataset of 1.3 million images [4] as unlabeled data to mount an attack against a model trained on a proprietary dataset of 1 billion labeled images [28].  \n\nLearning-based extraction is hard without natural data In learning-based extraction, we assume that the adversary is able to collect public unlabeled data to mount their attack. This is a natural assumption for a theft-motivated adversary who wishes to steal the oracle for local use—the adversary has data they want to learn the labels of without querying the model! For other adversaries, progress in generative modeling is likely to offer ways to remove this assumption [29]. We leave this to future work because our overarching aim in this paper is to characterize the model extraction attacker space around the notions of accuracy and fidelity. All progress achieved by our approaches is complementary to possible progress in synthetic data generation.  \n\n# 3.4.2 Deployment Knowledge  \n\nDeployment knowledge describes what the adversary knows about the oracle itself, including the model architecture, training procedure, and training dataset. The adversary may have access to public artifacts of the oracle—a distilled version of the oracle may be available (such as for OpenAI GPT [30]) or the oracle may be transfer learned from a public pretrained model (such as many image classifiers [31] or language models like BERT [32]).  \n\nIn addition, the adversary may not even know the features (the exact inputs to the model) or the labels (the classes the model may output). While the latter can generally be inferred by interacting with the model (e.g., making queries and observing the labels predicted by the model), inferring the former is usually more difficult. Our preliminary investigations suggest that these are not limiting assumptions, but we leave proper treatment of these constraints to future work.  \n\n# 3.4.3 Model Access  \n\nModel access describes the information the adversary obtains from the oracle, including bounds on how many queries the adversary may make as well as the oracle’s response:  \n\n• label: only the label of the most-likely class is revealed. • label and score: in addition to the most-likely label, the confidence score of the model in its prediction for this label is revealed. top-k scores: the labels and confidence scores for the $k$ classes whose confidence are highest are revealed. scores: confidence scores for all labels are revealed. logits: raw logit values for all labels are revealed.  \n\nIn general, the more access an adversary is given, the more effective they should be in accomplishing their goal. We instantiate practical attacks under several of these assumptions. Limiting model access has also been discussed as a defensive measure, as we elaborate in Section 8.  \n\n# 4 Learning-based Model Extraction  \n\nWe present our first attack strategy where the victim model serves as a labeling oracle for the adversary. While many attack variants exist [7, 11], they generally stage an iterative interaction between the adversary and the oracle, where the adversary collects labels for a set of points from the oracle and uses them as a training set for the extracted model. These algorithms are typically designed for accuracy extraction; in this section, we will demonstrate improved algorithms for accuracy extraction, using task-relevant unlabeled data.  \n\nWe realistically simulate large-scale model extraction by considering an oracle that was trained on 1 billion Instagram images [28] to obtain (at the time of the experiment) stateof-the-art performance on the standard image classification benchmark, ImageNet [4]. The oracle, with 193 million parameters, obtained $84.2\\%$ top-1 accuracy and $97.2\\%$ top-5 accuracy on the 1000-class benchmark—we refer to the model as the \"WSL model\", abbreviating the paper title. We give the adversary access to the public ImageNet dataset. The adversary’s goal is to use the WSL model as a labeling oracle to train an ImageNet classifier that performs better than if we trained the model directly on ImageNet. The attack is successful if access to the WSL model—trained on 1 billion proprietary images inaccessible to the adversary—enables the adversary to extract a model that outperforms a baseline model trained directly with ImageNet labels. This is accuracy extraction for the ImageNet distribution, given unlabeled ImageNet training data.  \n\nWe consider two variants of the attack: one where the adversary selects $10\\%$ of the training set (i.e., about 130,000 points) and the other where the adversary keeps the entire training set (i.e., about 1.3 million points). To put this number in perspective, recall that each image has a dimension of $224\\mathrm{x}224$ pixels and 3 color channels, giving us $224\\cdot224\\cdot3=150,528$ total input features. Each image belongs to one of 1,000 classes. Although ImageNet data is labeled, we always treat it as unlabeled to simulate a realistic adversary.  \n\n# 4.1 Fully-supervised model extraction  \n\nThe first attack is fully supervised, as proposed by prior work [11]. It serves to compare our subsequent attacks to prior work, and to validate our hypothesis that labels from the oracle are more informative than dataset labels.  \n\nThe adversary needs to obtain a label for each of the points it intends to train the extracted model with. Then it queries the oracle to label its training points with the oracle’s predictions. The oracle reveals labels and scores (in the threat model from Section 3) when queried.  \n\nThe adversary then trains its model to match these labels using the cross-entropy loss. We used a distillation temperature of $T=1.5$ in our experiments after a random search. Our experiments use two architectures known to perform well on image classification: ResNet-v2-50 and ResNet-v2-200.  \n\nResults. We present results in Table 2. For instance, the adversary is able to improve the accuracy of their model by $1.0\\%$ for ResNetv2-50 and $1.9\\%$ for ResNet_v2_200 after having queried the oracle for $10\\%$ of the ImageNet data. Recall that the task has 1,000 labels, making these improvements significant. The gains we are able to achieve as an adversary are in line with progress that has been made by the computer vision community on the ImageNet benchmark over recent years, where the research community improved the state-of-the-art top-1 accuracy by about one percent point per year.1  \n\n# 4.2 Unlabeled data improves query efficiency  \n\nFor adversaries interested in theft, a learning-based strategy should minimize the number of queries required to achieve a given level of accuracy. A natural approach towards this end is to take advantage of advances in label-efficient ML, including active learning [33] and semi-supervised learning [34].  \n\nActive learning allows a learner to query the labels of arbitrary points—the goal is to query the best set of points to learn a model with. Semi-supervised learning considers a learner with some labeled data, but much more unlabeled data—the learner seeks to leverage the unlabeled data (for example, by training on guessed labels) to improve classification performance. Active and semi-supervised learning are complementary techniques [35, 36]; it is possible to pick the best subset of data to train on, while also using the rest of the unlabeled data without labels.  \n\nThe connection between label-efficient learning and learning-based model extraction attacks is not new [11,13,15], but has focused on active learning. We show that, assuming access to unlabeled task-specific data, semi-supervised learning can be used to improve model extraction attacks. This could potentially be improved further by leveraging active learning, as in prior work, but our improvements are overall complementary to approaches considered in prior work. We explore two semi-supervised learning techniques: rotation loss [37] and MixMatch [38].  \n\nRotation loss. We leverage the current state-of-the-art semisupervised learning approach on ImageNet, which augments the model with a rotation loss [37]. The model contains two linear classifiers from the second-to-last layer of the model: the classifier for the image classification task, and a rotation predictor. The goal of the rotation classifier is to predict the rotation applied to an input—each input is fed in four times per batch, rotated by $\\{0^{\\circ},90^{\\circ},180^{\\circ},270^{\\circ}\\}$ . The classifier should output onehot encodings $\\{O H(0;4),O H(1;4),O H(2;4),O H(3;4)\\}$ , respectively, for these rotated images. Then, the rotation loss is written:  \n\n$$\nL_{R}(X;f_{\\theta})={\\frac{1}{4N}}\\sum_{i=0}^{N}\\sum_{j=1}^{r}H(f_{\\theta}(R_{j}(x_{i})),j)\n$$  \n\nwhere $R_{j}$ is the $j$ th rotation, $H$ is cross-entropy loss, and $f_{\\uptheta}$ is the model’s probability outputs for the rotation task. Inputs need not be labeled, hence we compute this loss on unlabeled data for which the adversary did not query the model. That is, we train the model on both unlabeled data (with rotation loss), and labeled data (with standard classification loss), and both contribute towards learning a good representation for all of the data, including the unlabeled data.  \n\nWe compare the accuracy of models trained with the rotation loss on data labeled by the oracle and data with ImageNet labels. Our best performing extracted model, with an accuracy  \n\n<html><body><table><tr><td>Architecture</td><td>DataFraction</td><td>ImageNet</td><td>WSL</td><td>WSL-5</td><td>ImageNet+ Rot</td><td>WSL+Rot</td><td>WSL-5+Rot</td></tr><tr><td>Resnet_v2_50</td><td>10%</td><td>(81.86/82.95)</td><td>(82.71/84.18)</td><td>(82.97/84.52)</td><td>(82.27/84.14)</td><td>(82.76/84.73)</td><td>(82.84/84.59)</td></tr><tr><td>Resnet_v2_200</td><td>10%</td><td>(83.50/84.96)</td><td>(84.81/86.36)</td><td>(85.00/86.67)</td><td>(85.10/86.29)</td><td>(86.17/88.16)</td><td>(86.11/87.54)</td></tr><tr><td>Resnet_v2_50</td><td>100%</td><td>(92.45/93.93)</td><td>(93.00/94.64)</td><td>(93.12/94.87)</td><td>N/A</td><td>N/A</td><td>N/A</td></tr><tr><td>Resnet_v2_200</td><td>100%</td><td>(93.70/95.11)</td><td>(94.26/96.24)</td><td>(94.21/95.85)</td><td>N/A</td><td>N/A</td><td>N/A</td></tr></table></body></html>  \n\nTable 2: Extraction attack (top-5 accuracy/top-5 fidelity) of the WSL model [28]. Each row contains an architecture and fraction of public ImageNet data used by the adversary. ImageNet is a baseline using only ImageNet labels. WSL is an oracle returning WSL model probabilities. WSL-5 is an oracle returning only the top 5 probabilities. Columns with $(+\\operatorname{Rot})$ use rotation loss on unlabeled data (rotation loss was not run when all data is labeled). An adversary able to query WSL always improves over ImageNet labels, even when given only top 5 probabilities. Rotation loss does not significantly improve the performance on ResNet_v2_50, but provides a (1.36/1.80) improvement for ResNet_v2_200, comparable to the performance boost given by WSL labels on $10\\%$ data. In the high-data regime, where we observe a (0.56/1.13) improvement using WSL labels.  \n\n<html><body><table><tr><td>Dataset</td><td>Algorithm</td><td>250 Queries</td><td>1000Queries</td><td>4000Queries</td></tr><tr><td>SVHN</td><td>FS</td><td>(79.25/79.48)</td><td>(89.47/89.87)</td><td>(94.25/94.71)</td></tr><tr><td>SVHN</td><td>MM</td><td>(95.82/96.38)</td><td>(96.87/97.45)</td><td>(97.07/97.61)</td></tr><tr><td>CIFAR10</td><td>FS</td><td>(53.35/53.61)</td><td>(73.47/73.96)</td><td>(86.51/87.37)</td></tr><tr><td>CIFAR10</td><td>MM</td><td>(87.98/88.79)</td><td>(90.63/91.39)</td><td>(93.29/93.99)</td></tr></table></body></html>  \n\nTable 3: Performance (accuracy/fidelity) of fully supervised (FS) and MixMatch (MM) extraction on SVHN and CIFAR10. MixMatch with 4000 labels performs nearly as well as the oracle for both datasets, and MixMatch at 250 queries beats fully supervised training at 4000 queries for both datasets.  \n\nof $64.5\\%$ , is trained with the rotation loss on oracle labels whereas the baseline on ImageNet labels only achieves $62.5\\%$ accuracy with the rotation loss and $61.2\\%$ without the rotation loss. This demonstrates the cumulative benefit of adding a rotation loss to the objective and training on oracle labels for a theft-motivated adversary.  \n\nWe expect that as semi-supervised learning techniques on ImageNet mature, further gains should be reflected in the performance of model extraction attacks.  \n\nMixMatch. To validate this hypothesis, we turn to smaller datasets where semi-supervised learning has made significant progress. We investigate a technique called MixMatch [38] on two datasets: SVHN [39] and CIFAR10 [40]. MixMatch uses a combination of techniques, including training on \"guessed\" labels, regularization, and image augmentations.  \n\nFor both datasets, inputs are color images of 32x32 pixels belonging to one of 10 classes. The training set of SVHN contains 73257 images and the test set contains 26032 images. The training set of CIFAR10 contains 50000 images and the test set contains 10000 images. We train the oracle with a WideResNet-28-2 architecture on the labeled training set. The oracles achieve $97.36\\%$ accuracy on SVHN and $95.75\\%$ accuracy on CIFAR10.  \n\nThe adversary is given access to the same training set but without knowledge of the labels. Our goal is to validate the effectiveness of semi-supervised learning by demonstrating that the adversary only needs to query the oracle on a small subset of these training points to extract a model whose accuracy on the task is comparable to the oracle’s. To this end, we run 5 trials of fully supervised extraction (no use of unlabeled data), and 5 trials of MixMatch, reporting for each trial the median accuracy of the 20 latest checkpoints, as done in [38].  \n\nResults. In Table 3, we find that with only 250 queries ( $293\\mathbf{x}$ smaller label set than the SVHN oracle and $200\\mathbf{x}$ smaller for CIFAR10), MixMatch reaches $95.82\\%$ test accuracy on SVHN and $87.98\\%$ accuracy on CIFAR10. This is higher than fully supervised training that uses 4000 queries. With 4000 queries, MixMatch is within $0.29\\%$ of the accuracy of the oracle on SVHN, and $2.46\\%$ on CIFAR10. The variance of MixMatch is slightly higher than that of fully supervised training, but is much smaller than the performance gap. These gains come from the prior MixMatch is able to build using the unlabeled data, making it effective at exploiting few labels. We observe similar gains in test set fidelity.  \n\n# 5 Limitations of Learning-Based Extraction  \n\nLearning-based approaches have several sources of nondeterminism: the random initializations of the model parameters, the order in which data is assembled to form batches for SGD, and even non-determinism in GPU instructions [41, 42]. Non-determinism impacts the model parameter values obtained from training. Therefore, even an adversary with full access to the oracle’s training data, hyperparameters, etc., would still need all of the learner’s non-determinism to achieve the functionally equivalent extraction goal described in Section 3. In this section, we will attempt to quantify this: for a strong adversary, with access to the exact details of the training setup, we will present an experiment to determine the limits of learning-based algorithms to achieving fidelity extraction.  \n\nWe perform the following experiment. We query an oracle to obtain a labeled substitute dataset $\\mathcal{D}$ . We use $\\mathcal{D}$ for a learning-based extraction attack which produces a model $f_{\\boldsymbol{\\Theta}}^{1}(\\boldsymbol{x})$ . We run the learning-based attack a second time using $\\mathcal{D}$ , but with different sources of non-determinism to obtain a new set of parameters $f_{\\boldsymbol{\\Theta}}^{2}(\\boldsymbol{x})$ . If there are points $x$ such that $f_{\\theta}^{1}(x)\\neq f_{\\theta}^{2}(\\bar{x})$ , then the prediction on $x$ is dependent not on the oracle, but on the non-determinism of the learning-based attack strategy—we are unable to guarantee fidelity.  \n\n<html><body><table><tr><td>Query Set</td><td>Init&SGD</td><td>SameSGD</td><td>SameInit</td><td>Different</td></tr><tr><td>Test</td><td>93.7%</td><td>93.2%</td><td>93.1%</td><td>93.4%</td></tr><tr><td>AdvEx</td><td>73.6%</td><td>65.4%</td><td>65.3%</td><td>67.1%</td></tr><tr><td>Uniform</td><td>65.7%</td><td>60.2%</td><td>59.0%</td><td>60.2%</td></tr></table></body></html>\n\nTable 4: Impact of non-determinism on extraction fidelity. Even models extracted using the same SGD and initialization randomness as the oracle do not reach $100\\%$ fidelity.  \n\nWe independently control the initialization randomness and batch randomness during training on Fashion-MNIST [43] with fully supervised SGD (we use Fashion-MNIST for training speed). We repeated each run 10 times and measure agreement between the ten obtained models on the test set, adversarial examples generated by running FGSM with $\\varepsilon=25/255$ with the oracle model and the test set, and uniformly random inputs. The oracle uses initialization seed 0 and SGD seed 0—we also use two different initialization and SGD seeds.  \n\nEven when both training and initialization randomness are fixed (so that only GPU non-determinism remains), fidelity peaks at $93.7\\%$ on the test set (see Table 4). With no randomness fixed, extraction achieves $93.4\\%$ fidelity on the test set. (Agreement on the test set should should be considered in reference to the base test accuracy of $90\\%$ .) Hence, even an adversary who has the victim model’s exact training set will be unable to exceed ${\\sim}93.4\\%$ fidelity. Using prototypicality metrics, as investigated in Carlini et al. [44], we notice that test points where fidelity is easiest to achieve are also the most prototypical (i.e., more representative of the class it is labeled as). This connection is explored further in Appendix B. The experiment of this section is also related to uncertainty estimation using deep ensembles [42]; we believe a deeper connection may exist between the fidelity of learning-based approaches and uncertainty estimation. Also relevant is the work mentioned earlier in Section 3, that shows that random networks are hard for learning-based approaches to extract. Here, we find that learning-based approaches have limits even for trained networks, on some portion of the input space.  \n\nIt follows from these arguments that non-determinism of both the victim and extracted model’s learning procedures potentially compound, limiting the effectiveness of using a learning-based approach to reaching high fidelity.  \n\n# 6 Functionally Equivalent Extraction  \n\nHaving identified fundamental limitations that prevent learning-based approaches from perfectly matching the oracle’s mistakes, we now turn to a different approach where the adversary extracts the oracle’s weights directly, seeking to achieve functionally-equivalent extraction. This attack can be seen as an extension of two prior works.  \n\nMilli et al. [19] introduce an attack to extract neural network weights under the assumption that the adversary is able to make gradient queries. That is, each query the adversary makes reveals not only the prediction of the neural network, but also the gradient of the neural network with respect to the query. To the best of our knowledge this is the only functionally-equivalent extraction attack on neural networks with one hidden layer, although it was not actually implemented in practice. • Batina et al. [25], at USENIX Security 2019, develop a side-channel attack that extracts neural network weights through monitoring the power use of a microprocessor evaluating the neural network. This is a much more powerful threat model than made by any of the other model extraction papers. To the best of our knowledge this is the only practical direct model extraction result—they manage to extract essentially arbitrary depth networks.  \n\nIn this section we introduce an attack which only requires standard queries (i.e., that return the model’s prediction instead of its gradients) and does not require any side-channel leakages, yet still manages to achieve higher fidelity extraction than the side-channel extraction work for two-layer networks, assuming double-precision inference.  \n\nAttack Algorithm Intuition. As in [19], our attack is tailored to work on neural networks with the ReLU activation function (the ReLU is an effective default choice of activation function [20]). This makes the neural network a piecewise linear function. Two samples are within the same linear region if all ReLU units have the same sign, illustrated in Figure 2.  \n\nBy finding adjacent linear regions, and computing the difference between them, we force a single ReLU to change signs. Doing this, it is possible to almost completely determine the weight vector going into that ReLU unit. Repeating this attack for all ReLU units lets us recover the first weight matrix completely. (We say almost here, because we must do some work to recover the sign of the weight vector.) Once the first layer of the two-layer neural network has been determined, the second layer can be uniquely solved for algebraically through least squares. This attack is optimal up to a constant factor—the query complexity is discussed in Appendix D.  \n\n# 6.1 Notation and Assumptions  \n\nAs in [19], we only aim to extract neural networks with one hidden layer using the ReLU activation function. We denote the model weights by $A^{(0)}\\in\\mathbb{R}^{d\\times h},A^{(1)}\\in\\mathbb{R}^{h\\times K}$ and biases by $B^{(0)}\\in\\mathbb{R}^{h},\\bar{B^{(1)}}\\in\\dot{\\mathbb{R}}^{K}$ . Here, $d,h$ , and $K$ respectively refer to the input dimensionality, the size of the hidden layer, and the number of classes. This is found in Table 6.1.  \n\nTable 5: Parameters for the functionally-equivalent attack.   \n\n\n<html><body><table><tr><td>Symbol</td><td>Definition</td></tr><tr><td>p</td><td>Input dimensionality</td></tr><tr><td>h</td><td>Hidden layer dimensionality (h < d)</td></tr><tr><td>K</td><td>Numberofclasses</td></tr><tr><td>A(0) ∈ Rdxh</td><td>Inputlayerweights</td></tr><tr><td>B(O) ∈ Rh</td><td>Input layer bias</td></tr><tr><td>A(1) ∈ RhxK</td><td>Logit layer weights</td></tr><tr><td>B(1) ∈ RK</td><td>Logit layer bias</td></tr></table></body></html>  \n\n![](/tmp/output/65_20250325154860/images/cf9ce37fd9ffb58afc92a58b793ba3a818d34a4bd4a3ae4d3848087c58cc2a16.jpg)  \nFigure 2: 2-dimension intuition for the functionally equivalent extraction attack.  \n\nWe say that $\\mathrm{ReLU}(x)$ is at a critical point if $x=0$ ; this is the location at which the unit’s gradient changes from 0 to 1. We assume the adversary is able to observe the raw logit outputs as 64-bit floating point values. We will use the notation $O_{L}$ to denote the logit oracle. Our attack implicitly assumes that the rows of $A^{(0)}$ are linearly independent. Because the dimension of the input space is larger than the hidden space by at least 100, it is exceedingly unlikely for the rows to be linearly dependent (and we find this holds true in practice).  \n\nNote that our attack is not an SQ algorithm, which would only allow us to look at aggregate statistics of our dataset. Instead, our algorithm is very particular in its analysis of the network, computing the differences between linear regions, for example, cannot be done with aggregate statistics. This structure allows us to avoid the pathologies of Section 3.3.  \n\n# 6.2 Attack Overview  \n\nThe algorithm is broken into four phases:  \n\n• Critical point search identifies inputs $\\{x_{i}\\}_{i=1}^{n}$ to the neural network so that exactly one of the ReLU units is at a critical point (i.e., has input identically 0). Weight recovery takes an input $x$ which causes the ith neuron to be at a critical point. We use this point $x$ to compute the difference between the two adjacent linear regions induced by the critical point, and thus the weight vector row $A_{i}^{(0)}$ . By repeating this process for each ReLU we obtain the complete matrix $A^{(0)}$ . Due to technical reasons discussed below, we can only recover the rowvector up to sign. • Sign recovery determines the sign of each row-vector $A_{j}^{(0)}$ for all $j$ using global information about $A^{(0)}$ . Final layer extraction uses algebraic techniques (least squares) to solve for the second layer of the network.  \n\n# 6.3 Critical Point Search  \n\nFor a two layer network, observe that the logit function is given by the equation $O_{L}(x)=A^{(1)}\\mathrm{ReLU}(A^{(0)}\\bar{x}+B^{(0)})+B^{(1)}$ To find a critical point for every ReLU, we sample two random vectors $u,\\nu\\in\\mathbb{R}^{\\bar{d}}$ , and consider the function  \n\n$$\nL(t;u,\\nu,O_{L})=O_{L}(u+t\\nu).\n$$  \n\nfor $t$ varying between a small and large appropriately selected value (discussed below). This amounts to drawing a line in the inputs of the network; passed through ReLUs, this line becomes the piecewise linear function $L(\\cdot)$ . The points $t$ where $L(t)$ is non-differentiable are exactly locations where some ${\\mathrm{ReLU}}_{i}$ is changing signs (i.e., some ReLU is at a critical point). Figure 3 shows an example of what this sweep looks like on a trained MNIST model.  \n\nFurthermore, notice that given a pair $u,\\nu$ , there is exactly one value $t$ for which each ReLU is at a critical point, and if $t$ is allowed to grow arbitrarily large or small that every ReLU unit will switch sign exactly once. Intuitively, the reason this is true is that each ReLU’s input, (say $w x+b$ for some $w,b,$ ), is a monotone function of $t$ $(w^{\\bar{T}}u t+w^{\\bar{T}}\\nu+b)\\/$ . Thus, by varying $t$ , we can identify an input $x_{i}$ that sets the ith ReLU to 0 for every relu $i$ in the network. This assumes we are not moving parallel to any of the rows (where $w^{T}u=0,$ ), and that we vary $t$ within a sufficiently large interval (so the $w^{T}u t$ term may overpower the constant term). The analysis of [19] suggests that these concerns can be resolved with high probability by varying $t\\in\\left[-h^{2},h^{2}\\right]$ .  \n\nWhile in theory it would be possible to sweep all values of $t$ to identify the critical points, this would require a large number of queries. Thus, to efficiently search for the locations  \n\n![](/tmp/output/65_20250325154860/images/7c84fb1020eb75c614557ef4fe0ec723c5f4e2627ad66245f0ca5ffabe6549a6.jpg)  \n\nFigure 3: An example sweep for critical point search. Here we plot the partial derivative across $t$ and see that $O_{L}(u+t\\nu)$ is piecewise linear, enabling a binary search.  \n\n![](/tmp/output/65_20250325154860/images/37ebd93c30e5c52f1034d5e6a543d2e65264bbd14f477cf647d1b43a86058a82.jpg)  \nFigure 4: Efficient and accurate 2-linear testing subroutine in Algorithm 1. Left shows a successful case where the algorithm succeeds; right shows a potential failure case, where there are multiple nonlinearities. We detect this by observing the expected value of $O(x)$ is not the observed (queried) value.  \n\nof critical points, we introduce a refined search algorithm which improves on the binary search as used in [19]. Standard binary search requires $O(n)$ model queries to obtain $n$ bits of precision. Therefore, we propose a refined technique which does not have this restriction and requires just $O(1)$ queries to obtain high ( $^{20+}$ bits) precision. The key observation we make is that if we are searching between two values $\\left[t_{1},t_{2}\\right]$ and there is exactly one discontinuity in this range, we can precisely identify the location of that discontinuity efficiently.  \n\nAn intuitive diagram for this algorithm can be found in Figure 4 and the algorithm can be found in Algorithm 1. The property this leverages is that the function is piecewise linear– if we know the range is composed of two linear segments, we can identify the linear segments and compute their intersection. In Algorithm 1, lines 1-3 describe computing the two linear regions’ slopes and intercepts. Lines 4 and 5 compute the intersection of the two lines (also shown in the red dotted line of Figure 4). The remainder of the algorithm performs the correctness check, also illustrated in Figure 4; if there are more than 2 linear components, it is unlikely that the true function value will match the function value computed in line 5, and we can detect that the algorithm has failed.  \n\nAlgorithm 1 Algorithm for 2-linearity testing. Computes the location of the only critical point in a given range or rejects if there is more than one.   \n\n\n<html><body><table><tr><td colspan=\"2\">Function f, range [1,t2], ε</td></tr><tr><td>f(t1+e)-f(t1) m1= 3 f(t2)-f(t2-E)</td><td>>Gradient at t1</td></tr><tr><td>m2=↓ y1 = f(a),y2 = f(b)</td><td>Gradient at t2</td></tr><tr><td>y2-y1-(b-a)m2 +=x m1-m2</td><td>>Candidate critical point</td></tr><tr><td>y2-y1-(b-a)m2 y=y1+m1² m1-m2</td><td>>Expectedvalue at candidate</td></tr><tr><td>y = f(x)</td><td>>Truevalueatcandidate</td></tr><tr><td colspan=\"2\">if y = y then return x else return\"More than one critical point\"</td></tr></table></body></html>  \n\n# 6.4 Weight Recovery  \n\nAfter running critical point search we obtain a set $\\{x_{i}\\}_{i=1}^{h}$ where each critical point corresponds to a point where a single ReLU flips sign. In order to use this information to learn the weight matrix $A^{(0)}$ we measure the second derivative of $O_{L}$ in each input direction at the points $x_{i}$ . Taking the second derivative here corresponds to measuring the difference between the linear regions on either side of the ReLU. Recall that prior work assumed direct access to gradient queries, and thus did not require any of the analysis in this section.  \n\n# 6.4.1 Absolute Value Recovery  \n\nTo formalize the intuition of comparing adjacent hyperplanes, observe that for the oracle $O_{L}$ and for a critical point $x_{i}$ (corresponding to ${\\mathrm{ReLU}}_{i}$ being zero) and for a random input-space direction $e_{j}$ we have  \n\n$$\n\\begin{array}{r l}&{\\frac{\\partial^{2}O_{L}}{\\partial e_{j}^{2}}\\Bigg\\vert_{x_{i}}=\\frac{\\partial O_{L}}{\\partial e_{j}}\\Bigg\\vert_{x_{i}+c_{e j}}-\\frac{\\partial O_{L}}{\\partial e_{j}}\\Bigg\\vert_{x_{i}-c_{e j}}}\\ &{\\quad\\quad=\\sum_{k}A_{k}^{(1)}\\mathbb{1}(A_{k}^{(0)}(x_{i}+c\\cdot e_{j})+B_{k}^{(0)}>0)A_{k j}^{(0)}}\\ &{\\quad\\quad-\\sum_{k}A_{k}^{(1)}\\mathbb{1}(A_{k}^{(0)}(x_{i}-c\\cdot e_{j})+B_{k}^{(0)}>0)A_{k j}^{(0)}}\\ &{\\quad\\quad=A_{i}^{(1)}\\left(\\mathbb{1}(A_{i}^{(0)}\\cdot e_{j}>0)-\\mathbb{1}(-A_{i}^{(0)}\\cdot e_{j}>0)\\right)A_{j i}^{(0)}}\\ &{\\quad\\quad=\\pm(A_{j i}^{(0)}A_{i}^{(1)})}\\end{array}\n$$  \n\nfor a $c>0$ small enough so that $x_{i}\\pm c\\cdot e_{j}$ does not flip any other ReLU. Because $x_{i}$ is a critical point and $c$ is small, the sums in the second line differ only in the contribution of ${\\mathrm{ReLU}}_{i}$ . However at this point we only have a product involving both weight matrices. We now show this information is useful.  \n\nIf we compute ${|A_{1i}^{(0)}A^{(1)}|}$ and $|A_{2i}^{(0)}A^{(1)}|$ by querying along directions $e_{1}$ and $e_{2}$ , we can divide these quantities to obtain the value ${|A_{1i}^{(0)}/A_{2i}^{(0)}|}$ , the ratio of the two weights. By repeating the above process for each input direction we can, for all $k$ , obtain the pairwise ratios $\\big|A_{1i}^{(0)}\\big/A_{k i}^{(0)}\\big|$ .  \n\nRecall from Section 3 that obtaining the ratios of weights is the theoretically optimal result we could hope to achieve. It is always possible to multiply all of the weights into a ReLU by a constant $c>0$ and then multiply all of the weights out of the ReLU by $c^{-1}$ . Thus, without loss of generality, we can assign $A_{1i}^{(0)}=\\stackrel{.}{1}$ and scale the remaining entries accordingly. Unfortunately, we have lost a small amount of information here. We have only learned the absolute value of the ratio, and not the value itself.  \n\n# 6.4.2 Weight Sign Recovery  \n\nOnce we reconstruct the values $|A_{j i}^{(0)}/A_{1i}^{(0)}|$ for all $j$ we need to recover the sign of these values. To do this we consider the following quantity:  \n\n$$\n\\left.{\\frac{\\partial^{2}O_{L}}{\\partial(e_{j}+e_{k})^{2}}}\\right|_{x_{i}}=\\pm(A_{j i}^{(0)}A_{i}^{(1)}\\pm A_{k i}^{(0)}A_{i}^{(1)}).\n$$  \n\nThat is, we consider what would happen if we take the second partial derivative in the direction $(e_{j}+e_{k})$ . Their contributions to the gradient will either cancel out, indicating A0ji) and A(ki0) are of opposite sign, or they will compound on each other, indicating they have the same sign. Thus, to recover signs, we can perform this comparison along each direction $(e_{1}+e_{j})$ .  \n\nHere we encounter one final difficulty. There are a total of $n$ signs we need to recover, but because we compute the signs by comparing ratios along different directions, we can only obtain $n-1$ relations. That is, we now know the correct signed value of $A_{i}^{(0)}$ up to a single sign for the entire row.  \n\nIt turns out this is to be expected. What we have computed is the normal direction to the hyperplane, but because any given hyperplane can be described by an infinite number of normal vectors differing by a constant scalar, we can not hope to use local information to recover this final sign bit.  \n\nPut differently, while it is possible to push a constant $c>$ 0 through from the first layer to the second layer, it is not possible to do this for negative constants, because the ReLU function is not symmetric. Therefore, it is necessary to learn the sign of this row.  \n\n# 6.5 Global Sign Recovery  \n\nOnce we have recovered the input vector’s weights, we still don’t know the sign for the given inputs—we only measure the difference between linear functions at each critical point, but do not know which side is the positive side of the ReLU [19]. Now, we need to leverage global information in order to reconcile all of inputs’ signs.  \n\nNotice that recovering A(@) allows us to obtain $B_{i}^{(0)}$ by using the fact that $A_{i}^{(0)}\\cdot x_{i}+B_{i}^{(0)}=0$ . Then we can compute $\\hat{B}_{i}^{(0)}$ up tot same global sigasisalid t  \n\nNow, to begin recovering sign, we search for a vector $z$ that is in the null space of $\\bar{\\hat{A}}^{(0)}$ , that is, $\\hat{A}^{(0)}z=\\vec{0}$ . Because the neural network has $h<d$ , the null-space is non-zero, and we can find many such vectors using least squares. Then, for each ${\\mathrm{ReLU}}_{i}$ , we search for a vector $\\nu_{i}$ such that $\\nu_{i}A^{(0)}=e_{i}$ where here $e_{i}$ is the ith basis vector in the hidden space. That is, moving along the $\\nu_{i}$ direction only changes ${\\mathrm{ReLU}}_{i}$ ’s input value. Again we can search for this through least squares.  \n\nGiven $z$ and these $\\nu_{i}$ we query the neural network for the values of $O_{L}(z)$ , $O_{L}(z+\\nu_{i})$ , and $O_{L}(z-\\nu_{i})$ . On each of these three queries, all hidden units are 0 except for ${\\mathrm{ReLU}}_{i}$ which recieves as input either 0, 1, or $^{-1}$ by the construction of $\\nu_{i}$ . However, notice that the output of ${\\mathrm{ReLU}}_{i}$ can only be either 0 or 1, and the two $\\{-1,0\\}$ cases collapse to just output 0. Therefore, if $O_{L}(z+\\nu_{i})=O_{L}(z)$ , we know that $A_{i}^{(0)}\\cdot\\nu_{i}<0$ Otherwise, we will find $O_{L}(z-\\nu_{i})=O_{L}(z)$ and $A_{i}^{(0)}\\cdot\\nu_{i}>0$ . This allows us to recover the sign bit for ${\\mathrm{ReLU}}_{i}$ .  \n\n# 6.6 Last Layer Extraction  \n\nGiven the completely extracted first layer, the logit function of the network is just a linear transformation which we can recover with least squares, through making $h$ queries where each ReLU is active at least once. In practice, we use the critical points discovered in the previous section so that we do not need to make additional neural network queries.  \n\n# 6.7 Results  \n\nSetup. We train several one-layer fully-connected neural networks with between 16 and 512 hidden units (for 12,000 and 100,000 trainable parameters, respectively) on the MNIST [45] and CIFAR-10 datasets [40]. We train the models with the Adam [23] optimizer for 20 epochs at batch size 128 until they converge. We train five networks of each size to obtain higher statistical significance. Accuracies of these networks can be found in the supplement in Appendix C. In Section 4, we used $140,000\\approx2^{17}$ queries for ImageNet model extraction. This is comparable to the number of queries used to extract the smallest MNIST model in this section, highlighting the advantages of both approaches.  \n\nMNIST Extraction. We implement the functionallyequivalent extraction attack in JAX [46] and run it on each trained oracle. We measure the fidelity of the extracted model, comparing predicted labels, on the MNIST test set.  \n\nResults are summarized in Table 6. For smaller networks, we achieve $100\\%$ fidelity on the test set: every single one of the 10, 000 test examples is predicted the same. As the network size increases, low-probability errors we encounter become more common, but the extracted neural network still disagrees with the oracle on only 2 of the 10, 000 examples.  \n\nInspecting the weight matrix that we extract and comparing it to the weight matrix of the oracle classifier, we find that we manage to reconstruct the first weight matrix to an average precision of 23 bits—we provide more results in Appendix C.  \n\nCIFAR-10 Extraction. Because this attack is dataindependent, the underlying task is unimportant for how well the attack works; only the number of parameters matter. The results for CIFAR-10 are thus identical to MNIST when controlling for model size: we achieve $100\\%$ test set agreement on models with fewer than 200, 000 parameters and and greater than $99\\%$ test set agreement on larger models.  \n\nComparison to Prior Work. To the best of our knowledge, this is by orders of magnitude the highest fidelity extraction of neural network weights.  \n\nThe only fully-implemented neural network extraction attack we are aware of is the work of Batina et al. [25], who uses an electromagnetic side channels and differential power analysis to recover an MNIST neural network with neural network weights with an average error of 0.0025. In comparison, we are able to achieve an average error in the first weight matrix for a similarly sized neural network of just 0.0000009—over two thousand times more precise. To the best of our knowledge no functionally-equivalent CIFAR-10 models have been extracted in the past.  \n\nWe are unable to make a comparison between the fidelity of our extraction attack and the fidelity of the attack presented in Batina et al. because they do not report on this number: they only report the accuracy of the extracted model and show it is similar to the original model. We believe this strengthens our observation that comparing across accuracy and fidelity is not currently widely accepted as best practice.  \n\nInvestigating Errors. We observe that as the number of parameters that must be extracted increases, the fidelity of the model decreases. We investigate why this happens and discovered that a small fraction of the time (roughly 1 in 10,000) the gradient estimation procedure obtains an incorrect estimate ofthe gradient andthrfreonoftheextractedweghts is incorrect by a non-insignificant margin.  \n\nIntroducing an error into just one of the weights of the first matrix $\\hat{A(0)}$ should not induce significant further errors. However, because of this error, when we solve for the bias vector, the extracted bias $\\hat{B}_{i}^{(0)}$ will have error proportional to the error of $\\hat{A}_{i j}^{(0)}$ . And when the bias is wrong, it impacts every calculation, even those where this edge is not in use.  \n\nResolving this issue completely either requires reducing the failure rate of gradient estimation from 1 in 10,000 to practically 0, or would require a complex error-recovery procedure. Instead, we will introduce in the following section an improvement which almost completely solves this issue.  \n\n<html><body><table><tr><td># of Parameters</td><td>12,500</td><td>25,000</td><td>50,000</td><td>100,000</td></tr><tr><td>Fidelity</td><td>100%</td><td>100%</td><td>100%</td><td>99.98%</td></tr><tr><td>Queries</td><td>217.2</td><td>218.2</td><td>219.2</td><td>220.2</td></tr></table></body></html>  \n\nTable 6: Fidelity of the functionally-equivalent extraction attack across different test distributions on an MNIST victim model. Results are averaged over five extraction attacks. For small models, we achieve perfect fidelity extraction; larger models have near-perfect fidelity on the test data distribution, but begins to lose accuracy at 100, 000 parameters.  \n\nDifficulties Extending the Attack. The attack is specific to two layer neural networks; deeper networks pose multiple difficulties. In deep networks, the critical point search step of Section 6.3 will result in critical points from many different layers, and determining which layer a critical point is on is nontrivial. Without knowing which layer a critical point is on, we cannot control inputs to the neuron, which we need to do to recover the weights in Section 6.4. Even given knowledge of what layer a critical point is on, the inputs of any neuron past layer 1 are the outputs of other neurons, so we only have indirect control over their inputs. Finally, even with the ability to recover these weights, small numerical errors occur in the first layer extraction. These cause errors in every finite differences computation in further layers, causing the second layer to have even larger numerical errors than the first (and so on). Therefore, extending the attack to deeper networks will require at least solving each of the following: producing critical points belonging to a specific layer, recovering weights for those neurons without direct control of their inputs, and significantly reducing numerical errors in these algorithms.  \n\n# 7 Hybrid Strategies  \n\nUntil now the strategies we have developed for extraction have been pure and focused entirely on learning or entirely on direct extraction. We now show that there is a continuous spectrum from which we can draw attack strategies, and these hybrid strategies can leverage both the query efficiency of learning extraction, and the fidelity of direct extraction.  \n\n# 7.1 Learning-Based Extraction with Gradient Matching  \n\nMilli et al. demonstrate that gradient matching helps extraction by optimizing the objective function  \n\n$$\n\\sum_{i=1}^{n}H(O(x_{i}),f(x_{i}))+\\alpha|\\nabla_{x}O(x_{i})-\\nabla_{x}f(x_{i})|_{2}^{2},\n$$  \n\nassuming the adversary can query the model for $\\nabla_{x}O(x)$ . This is more model access than we permit our adversary, but is an example of using intuition from direct recovery to improve extraction. We found in preliminary experiments that this technique can improve fidelity on small datasets (increasing fidelity from $95\\%$ to $96.5\\%$ on Fashion-MNIST), but we leave scaling and removing the model access assumption of this technique to future work. Next, we will show another combination of learning and direct recovery, using learning to alleviate some of the limitations of the previous functionallyequivalent extraction attack.  \n\n<html><body><table><tr><td># of Parameters</td><td>50,000</td><td>100,000</td><td>200,000</td><td>400,000</td></tr><tr><td>Fidelity</td><td>100%</td><td>100%</td><td>99.95%</td><td>99.31%</td></tr><tr><td>Queries</td><td>219.2</td><td>220.2</td><td>221.2</td><td>222.2</td></tr></table></body></html>\n\nTable 7: Fidelity of extracted MNIST model is improved with the hybrid strategy. Note when comparing to Table 6 the model sizes are $4\\times$ larger.  \n\n# 7.2 Error Recovery through Learning  \n\nRecall from earlier that the functionally-equivalent extraction attack fidelity degrades as the model size increases. This is a result of low-probability errors in the first weight matrix inducing incorrect biases on the first layer, which in turn propagates and causes worse errors in the second layer.  \n\nWe now introduce a method for performing a learningbased error recovery routine. While performing a fullylearning-based attack leaves too many free variables so that functionally-equivalent extraction is not possible, if we fix many of the variables to the values extracted through the direct recovery attack, we now show it is possible to learn the remainder of the variables.  \n\nFormally, $\\operatorname{let}{\\hat{A}}^{(0)}$ be the extracted weight matrix for the first layer and $\\overset{\\cdot}{B}^{(0)}$ be the extracted bias vector for the first layer. Previously, we used least squares to directly solve for $\\bar{\\hat{A}}^{(1)}$ and $\\hat{B}^{(1)}$ assuming we had extracted the first layer perfectly. Here, we relax this assumption. Instead, we perform gradient descent optimizing for parameters $W_{0..2}$ that minimize  \n\n$$\n\\mathbb{E}_{{x}\\in\\mathcal{D}}\\big\\|\\int_{0}(x)-W_{1}\\mathrm{ReLU}(\\hat{A}^{(0)}x+\\hat{B}^{(0)}+W_{0})+W_{2}\\big\\|\n$$  \n\nThat is, we use a single trainable parameter to adjust the bias term of the first layer, and then solve (via gradient descent with training data) for the remaining weights accordingly.  \n\nThis hybrid strategy increases the fidelity of the extracted model substantially, detailed in Table 8. In the worstperforming example from earlier (with only direct extraction) the extracted 128-neuron network had $80\\%$ fidelity agreement with the victim model. When performing learning-based recovery, the fidelity agreement jumps all the way to $99.75\\%$ .  \n\n<html><body><table><tr><td># of Parameters</td><td>50,000</td><td>100,000</td><td>200,000</td><td>400,000</td></tr><tr><td>Transferability</td><td>100%</td><td>100%</td><td>100%</td><td>100%</td></tr></table></body></html>  \n\nTable 8: Transferability rate of adversarial examples using the extracted neural network from our Section 7 attack.  \n\n# 7.2.1 Transferability  \n\nAdversarial examples transfer: an adversarial example [47] generated on one model often fools different models, too. Transferability is higher when the models are more similar [7].  \n\nWe should therefore expect that we can generate adversarial examples on our extracted model, and that these will fool the remote oracle nearly always. In order to measure transferability, we run 20 iterations of PGD [48] with $\\ell_{\\infty}$ distortion set to the value most often used in the literature: for MNIST: 0.1, and for CIFAR-10: 0.03.  \n\nThe attack achieves functionally equivalent extraction (modulo floating point precision errors in the extracted weights), so we expect it to have high adversarial example transferability. Indeed, we find we achieve a $100\\%$ transferability success rate for all extracted models.  \n\n# 8 Related Work  \n\nDefenses for model extraction have fallen into two camps: limiting the information gained per query, and differentiating extraction adversaries from benign users. Approaches to limiting information include perturbing the probabilities returned by the model [11, 13, 49], removing the probabilities for some of the model’s classes [11], or returning only the class output [11, 13]. Another proposal has considered sampling from a distribution over model parameters [13,50]. The other camp, differentiating benign from malicious users, has focused on analyzing query patterns [51, 52]. Non-adaptive attacks (such as supervised or MixMatch extraction) bypass query patternbased detection, and are weakened by information limiting. We demonstrate the impact of removing complete access to probability values by considering only access to top 5 probabilities from WSL in Table 2. Our functionally-equivalent attack is broken by all of these measures. We leave consideration of defense-aware attacks to future work.  \n\nQueries to a model can also reveal hyperparameters [53] or architectural information [14]. Adversaries can use side channel attacks to do the same [18, 25]. These are orthogonal to, but compatible with, our work—information about a model, such as assumptions made in Section 6, empowers extraction.  \n\nWatermarking neural networks has been proposed [54, 55] to identify extracted models. Model extraction calls into question the utility of cryptographic protocols used to protect model weights. One unrealized approach is obfuscation [56], where an equivalent program could be released and queried as many times as desired. A practical approach is secure multiparty computation, where each query is computed by running a protocol between the model owner and querier [57].  \n\n# 9 Conclusion  \n\nThis paper characterizes and explores the space of model extraction attacks on neural networks. We focus this paper specifically around the objectives of accuracy, to measure the success of a theft-motivated adversary, and fidelity, an oftenoverlooked measure which compares the agreement between models to reflect the success of a recon-motivated adversary.  \n\nOur learning-based methods can effectively attack a model with several millions of parameters trained on a billion images, and allows the attacker to reduce the error rate of their model by $10\\%$ . This attack does not match perfect fidelity with the victim model due to what we show are inherent limitations of learning-based approaches: nondeterminism (including only the nondeterminism on the GPU) prohibits training identical models. In contrast, our direct functionally-equivalent extraction returns a neural network agreeing with the victim model on $100\\%$ of the test samples and having $100\\%$ fidelity on transfered adversarial examples.  \n\nWe then propose a hybrid method which unifies these two attacks, using learning-based approaches to recover from numerical instability errors when performing the functionallyequivalent extraction attack.  \n\nOur work highlights many remaining open problems in model extraction, such as reducing the capabilities required by our attacks and scaling functionally-equivalent extraction.  \n\n# Acknowledgements  \n\nWe would like to thank Ilya Mironov for lengthy and fruitful discussions regarding the functionally equivalent extraction attack. We also thank Úlfar Erlingsson for helpful discussions on positioning the work, and Florian Tramèr for his comments on an early draft of this paper.  \n\n# References  \n\n[1] E. Strubell, A. Ganesh, and A. McCallum, “Energy and policy considerations for deep learning in nlp,” arXiv preprint arXiv:1906.02243, 2019.   \n[2] Z. Yang, Z. Dai, Y. Yang, J. Carbonell, R. R. Salakhutdinov, and Q. V. Le, “Xlnet: Generalized autoregressive pretraining for language understanding,” in Advances in neural information processing systems, 2019, pp. 5754– 5764.   \n[3] A. Halevy, P. Norvig, and F. Pereira, “The unreasonable effectiveness of data,” 2009.   \n[4] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei, “Imagenet: A large-scale hierarchical image database,” in 2009 IEEE conference on computer vision and pattern recognition. Ieee, 2009, pp. 248–255.   \n[5] I. Sutskever, O. Vinyals, and Q. V. Le, “Sequence to sequence learning with neural networks,” in Neural information processing systems, 2014, pp. 3104–3112.   \n[6] A. Van Den Oord, S. Dieleman, H. Zen, K. Simonyan, O. Vinyals, A. Graves, N. Kalchbrenner, A. W. Senior, and K. Kavukcuoglu, “Wavenet: A generative model for raw audio.” SSW, vol. 125, 2016.   \n[7] N. Papernot, P. McDaniel, I. Goodfellow, S. Jha, Z. B. Celik, and A. Swami, “Practical black-box attacks against machine learning,” in Proceedings of the 2017 ACM on Asia conference on computer and communications security. ACM, 2017, pp. 506–519.   \n[8] D. Lowd and C. Meek, “Adversarial learning,” in Proceedings of the eleventh ACM SIGKDD international conference on Knowledge discovery in data mining. ACM, 2005, pp. 641–647.   \n[9] R. Shokri, M. Stronati, C. Song, and V. Shmatikov, “Membership inference attacks against machine learning models,” in 2017 IEEE Symposium on Security and Privacy (SP). IEEE, 2017, pp. 3–18.   \n[10] A. Salem, Y. Zhang, M. Humbert, P. Berrang, M. Fritz, and M. Backes, “Ml-leaks: Model and data independent membership inference attacks and defenses on machine learning models,” arXiv preprint arXiv:1806.01246, 2018.   \n[11] F. Tramèr, F. Zhang, A. Juels, M. K. Reiter, and T. Ristenpart, “Stealing machine learning models via prediction apis,” in 25th {USENIX} Security Symposium ({USENIX} Security 16), 2016, pp. 601–618.   \n[12] T. Orekondy, B. Schiele, and M. Fritz, “Knockoff nets: Stealing functionality of black-box models,” in Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, 2019, pp. 4954–4963.   \n[13] V. Chandrasekaran, K. Chaudhuri, I. Giacomelli, S. Jha, and S. Yan, “Model extraction and active learning,” CoRR, vol. abs/1811.02054, 2018. [Online]. Available: http://arxiv.org/abs/1811.02054   \n[14] S. J. Oh, M. Augustin, B. Schiele, and M. Fritz, “Towards reverse-engineering black-box neural networks,” arXiv preprint arXiv:1711.01768, 2017.   \n[15] S. Pal, Y. Gupta, A. Shukla, A. Kanade, S. K. Shevade, and V. Ganapathy, “A framework for the extraction of deep neural networks by leveraging public data,”  \n\nCoRR, vol. abs/1905.09165, 2019. [Online]. Available: http://arxiv.org/abs/1905.09165  \n\n[16] J. R. Correia-Silva, R. F. Berriel, C. Badue, A. F. de Souza, and T. Oliveira-Santos, “Copycat cnn: Stealing knowledge by persuading confession with random non-labeled data,” in 2018 International Joint Conference on Neural Networks (IJCNN). IEEE, 2018.   \n[17] C. Song and V. Shmatikov, “Overlearning reveals sensitive attributes,” arXiv preprint arXiv:1905.11742, 2019.   \n[18] S. Hong, M. Davinroy, Y. Kaya, S. N. Locke, I. Rackow, K. Kulda, D. Dachman-Soled, and T. Dumitras, “Security analysis of deep neural networks operating in the presence of cache side-channel attacks,” arXiv preprint arXiv:1810.03487, 2018.   \n[19] S. Milli, L. Schmidt, A. D. Dragan, and M. Hardt, “Model reconstruction from model explanations,” arXiv preprint arXiv:1807.05185, 2018.   \n[20] V. Nair and G. E. Hinton, “Rectified linear units improve restricted boltzmann machines,” in Proceedings of the 27th international conference on machine learning (ICML-10), 2010, pp. 807–814.   \n[21] Y. E. Nesterov, “A method for solving the convex programming problem with convergence rate o $(1/\\mathrm{k}^{\\cdot}2)$ ,” in Dokl. akad. nauk Sssr, vol. 269, 1983, pp. 543–547.   \n[22] J. Duchi, E. Hazan, and Y. Singer, “Adaptive subgradient methods for online learning and stochastic optimization,” Journal of Machine Learning Research, vol. 12, no. Jul, pp. 2121–2159, 2011.   \n[23] D. P. Kingma and J. Ba, “Adam: A method for stochastic optimization,” arXiv preprint arXiv:1412.6980, 2014.   \n[24] G. Hinton, O. Vinyals, and J. Dean, “Distilling the knowledge in a neural network,” arXiv preprint arXiv:1503.02531, 2015.   \n[25] L. Batina, S. Bhasin, D. Jap, and S. Picek, “Csi neural network: Using side-channels to recover your artificial neural network information,” arXiv preprint arXiv:1810.09076, 2018.   \n[26] P. Kocher, J. Jaffe, and B. Jun, “Differential power analysis,” in Annual International Cryptology Conference. Springer, 1999, pp. 388–397.   \n[27] A. Das, S. Gollapudi, R. Kumar, and R. Panigrahy, “On the learnability of deep random networks,” CoRR, vol. abs/1904.03866, 2019.   \n[28] D. Mahajan, R. Girshick, V. Ramanathan, K. He, M. Paluri, Y. Li, A. Bharambe, and L. van der Maaten, “Exploring the limits of weakly supervised pretraining,”  \n\nin Proceedings of the European Conference on Com  \n\nputer Vision (ECCV), 2018, pp. 181–196.   \n[29] P. Micaelli and A. Storkey, “Zero-shot knowledge transfer via adversarial belief matching,” arXiv preprint arXiv:1905.09768, 2019.   \n[30] A. Radford, J. Wu, R. Child, D. Luan, D. Amodei, and I. Sutskever, “Language models are unsupervised multitask learners,” OpenAI Blog, vol. 1, no. 8, 2019.   \n[31] A. Sharif Razavian, H. Azizpour, J. Sullivan, and S. Carlsson, “Cnn features off-the-shelf: an astounding baseline for recognition,” in Proceedings of the IEEE conference on computer vision and pattern recognition workshops, 2014, pp. 806–813.   \n[32] J. Devlin, M.-W. Chang, K. Lee, and K. Toutanova, “Bert: Pre-training of deep bidirectional transformers for language understanding,” arXiv preprint arXiv:1810.04805, 2018.   \n[33] D. Angluin, “Queries and concept learning,” Machine learning, vol. 2, no. 4, pp. 319–342, 1988.   \n[34] A. Blum and T. Mitchell, “Combining labeled and unlabeled data with co-training,” in Proceedings of the eleventh annual conference on Computational learning theory. Citeseer, 1998, pp. 92–100.   \n[35] S. Song, D. Berthelot, and A. Rostamizadeh, “Combining mixmatch and active learning for better accuracy with fewer labels,” 2020. [Online]. Available: https://openreview.net/forum?id=HJxWl0NKPB   \n[36] O. Siméoni, M. Budnik, Y. Avrithis, and G. Gravier, “Rethinking deep active learning: Using unlabeled data at model training,” 2020. [Online]. Available: https://openreview.net/forum?id=rJehllrtDS   \n[37] X. Zhai, A. Oliver, A. Kolesnikov, and L. Beyer, “S4l: Self-supervised semi-supervised learning,” arXiv preprint arXiv:1905.03670, 2019.   \n[38] D. Berthelot, N. Carlini, I. Goodfellow, N. Papernot, A. Oliver, and C. Raffel, “Mixmatch: A holistic approach to semi-supervised learning,” arXiv preprint arXiv:1905.02249, 2019.   \n[39] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. $\\mathrm{Ng}$ , “Reading digits in natural images with unsupervised feature learning,” 2011.   \n[40] A. Krizhevsky et al., “Learning multiple layers of features from tiny images,” Citeseer, Tech. Rep., 2009.   \n[41] D. Sculley, G. Holt, D. Golovin, E. Davydov, T. Phillips, D. Ebner, V. Chaudhary, M. Young, J.-F. Crespo, and D. Dennison, “Hidden technical debt in machine learning systems,” in Advances in neural information processing systems, 2015, pp. 2503–2511.   \n[42] B. Lakshminarayanan, A. Pritzel, and C. Blundell, “Simple and scalable predictive uncertainty estimation using deep ensembles,” in Advances in Neural Information Processing Systems, 2017, pp. 6402–6413.   \n[43] H. Xiao, K. Rasul, and R. Vollgraf. (2017) Fashionmnist: a novel image dataset for benchmarking machine learning algorithms.   \n[44] N. Carlini, U. Erlingsson, and N. Papernot, “Prototypical examples in deep learning: Metrics, characteristics, and utility,” 2019. [Online]. Available: https://openreview. net/forum?id=r1xyx3R9tQ   \n[45] Y. LeCun, L. Bottou, Y. Bengio, P. Haffner et al., “Gradient-based learning applied to document recognition,” Proceedings of the IEEE, vol. 86, no. 11, pp. 2278–2324, 1998.   \n[46] Google, “Jax,” https://github.com/google/jax, 2019.   \n[47] C. Szegedy, W. Zaremba, I. Sutskever, J. Bruna, D. Erhan, I. Goodfellow, and R. Fergus, “Intriguing properties of neural networks,” arXiv preprint arXiv:1312.6199, 2013.   \n[48] A. Madry, A. Makelov, L. Schmidt, D. Tsipras, and A. Vladu, “Towards deep learning models resistant to adversarial attacks,” arXiv preprint arXiv:1706.06083, 2017.   \n[49] T. Lee, B. Edwards, I. Molloy, and D. Su, “Defending against model stealing attacks using deceptive perturbations,” arXiv preprint arXiv:1806.00054, 2018.   \n[50] I. M. Alabdulmohsin, X. Gao, and X. Zhang, “Adding robustness to support vector machines against adversarial reverse engineering,” in Proceedings of the $23r d$ ACM International Conference on Conference on Information and Knowledge Management. ACM, 2014, pp. 231–240.   \n[51] M. Juuti, S. Szyller, A. Dmitrenko, S. Marchal, and N. Asokan, “Prada: protecting against dnn model stealing attacks,” arXiv preprint arXiv:1805.02628, 2018.   \n[52] M. Kesarwani, B. Mukhoty, V. Arya, and S. Mehta, “Model extraction warning in mlaas paradigm,” in Proceedings of the 34th Annual Computer Security Applications Conference. ACM, 2018, pp. 371–380.   \n[53] B. Wang and N. Z. Gong, “Stealing hyperparameters in machine learning,” in 2018 IEEE Symposium on Security and Privacy (SP). IEEE, 2018, pp. 36–52.   \n[54] J. Zhang, Z. Gu, J. Jang, H. Wu, M. P. Stoecklin, H. Huang, and I. Molloy, “Protecting intellectual property of deep neural networks with watermarking,” in Proceedings of the 2018 on Asia Conference on Computer and Communications Security. ACM, 2018, pp. 159–172.   \n[55] Y. Uchida, Y. Nagai, S. Sakazawa, and S. Satoh, “Embedding watermarks into deep neural networks,” in Proceedings of the 2017 ACM on International Conference on Multimedia Retrieval. ACM, 2017, pp. 269–277.   \n[56] B. Barak, O. Goldreich, R. Impagliazzo, S. Rudich, A. Sahai, S. Vadhan, and K. Yang, “On the (im) possibility of obfuscating programs,” in Annual international cryptology conference. Springer, 2001, pp. 1–18.   \n[57] M. Barni, C. Orlandi, and A. Piva, “A privacy-preserving protocol for neural-network-based computation,” in Proceedings of the 8th workshop on Multimedia and security. ACM, 2006, pp. 146–151.   \n[58] G. Katz, C. Barrett, D. L. Dill, K. Julian, and M. J. Kochenderfer, “Reluplex: An efficient smt solver for verifying deep neural networks,” in International Conference on Computer Aided Verification. Springer, 2017, pp. 97–117.  \n\n# A Formal Statements for Section 3.3  \n\nHere, we give the formal arguments for the difficulty of model extraction to support informal statements from Section 3.3.  \n\nTheorem 1. There exists a class of width $3k$ and depth 2 neural networks on domain $[0,1]^{d}$ (with precision $p$ numbers) with $d\\geq k$ that require, given logit access to the networks, $\\Theta(p^{k})$ queries to extract.  \n\nIn order to prove Theorem 1, we introduce a family of functions we call $k$ -rectangle bounded functions, which we will show satisfies this property.  \n\nDefinition A.1. A function $f$ on domain $[0,1]^{d}$ with range $\\mathbb{R}$ is a rectangle bounded function if there exists two vectors $a,b$ such that $f(x)\\neq0\\implies a\\preceq x\\preceq b$ , where $\\preceq$ denotes elementwise comparison. The function $f$ is a $k$ -rectangle bounded function if there are $k$ indices i such that $a_{i}\\neq0$ or $b_{i}\\neq1$ .  \n\nIntuitively, a $k$ -rectangle function only outputs a non-zero value on a multidimensional rectangle that is constrained in only $k$ coordinates. We begin by showing that we can implement $k$ -rectangle functions for any $a,b$ using a ReLU network of width $k$ and depth 2.  \n\nLemma 1. For any $a,b$ with $k$ indices i such that $a_{i}\\neq0$ or $b_{i}\\neq1$ , we can construct a $k$ -rectangle bounded function for $^{a,b}$ with a ReLU network of width $3k$ and depth 2.  \n\nProof. We will start by constructing a 3-ReLU gadget with output $\\geq1$ only when $a_{i}\\leq x_{i}\\leq b_{i}$ . We will then show how to compose $k$ of these gadgets, one for each index of the $k$ -rectangle, to construct the $k$ -rectangle bounded function.  \n\nThe 3-ReLU gadget only depends on $x_{i}$ , so weights for all other ReLUs will be set to 0. Observe that the function $T_{i}(x;a,b)=\\mathrm{{ReLU}}(x-a)+\\mathrm{{ReLU}}(x_{i}-b_{i})-2\\mathrm{{ReLU}}(x_{i}-$ $\\left(a_{i}+b_{i}\\right)/2)$ is nonzero only on the interval $\\left(a_{i},b_{i}\\right)$ . This is easier to see when it is written as  \n\n$$\n\\begin{array}{r l}&{\\mathrm{ReLU}(x_{i}-a_{i})-\\mathrm{ReLU}(x_{i}-(a_{i}+b_{i})/2)}\\ &{\\quad\\quad\\quad-\\left(\\mathrm{ReLU}(x_{i}-(a_{i}+b_{i})/2)-\\mathrm{ReLU}(x_{i}-b_{i})\\right).}\\end{array}\n$$  \n\nThe function $\\mathrm{ReLU}(x-x_{1})-\\mathrm{ReLU}(x-x_{2})$ with $x_{1}<x_{2}$ looks like a sigmoid, and has the following form:  \n\n$$\n\\mathrm{ReLU}(x-x_{1})-\\mathrm{ReLU}(x-x_{2})=\\left\\{\\begin{array}{l l}{0}&{x\\leq x_{1}}\\ {x-x_{1}}&{x_{1}\\leq x\\leq x_{2}}\\ {x_{2}-x_{1}}&{x\\geq x}\\end{array}\\right.\n$$  \n\nNow, $T_{i}(x;a_{i},b_{i})\\cdot1/(b_{i}-a_{i})$ has range $[0,1]$ for any value of $a_{i},b_{i}$ . Then the function  \n\n$$\nf_{a,b}(\\boldsymbol{x})=\\mathrm{ReLU}(\\sum_{i}(T_{i}(\\boldsymbol{x};a_{i},b_{i})/(b_{i}-a_{i}))-(k-1))\n$$  \n\nis $k$ -rectangle bounded for vectors $a,b$ . To see why, we need that no input $x$ not satisfying $a\\preceq x\\preceq b$ has $\\begin{array}{r}{\\sum_{i}(T_{i}(x;a_{i},b_{i})/(b_{i}-a_{i}))>k-1}\\end{array}$ . This is simply because each term $T_{i}(x;a_{i},b_{i})\\le1$ , so unless all $k$ such terms are $>0$ , the inequality cannot hold. 口  \n\nNow that we know how to construct a $k$ -rectangle bounded function, we will introduce a set of $p^{k}$ disjoint $k$ -rectangle bounded functions, and then show that any one requires $p^{k}$ queries to extract when the others are also possible functions.  \n\nLemma 2. There exists a family of $k$ -rectangle bounded functions $\\mathcal{F}$ such that extracting an element of $\\mathcal{F}$ requires $p^{k}$ queries in the worst case.  \n\nHere, $p$ is the feature precision; images with 8-bit pixels have $p=256$ .  \n\nProof. We begin by constructing $\\mathcal{F}$ . The following $p$ ranges are clearly pairwise disjoint: $\\{(\\frac{\\bar{i}-1}{p},\\frac{i}{p})\\}_{i=1}^{p}$ . Then pick any $k$ indices, and we can construct $p^{k}$ distinct $k$ -rectangle bounded functions - one for each element in the Cartesian product of each index’s set of ranges. Call this set $\\mathcal{F}$ .  \n\nThe set of inputs with non-zero output is distinct for each function, because their rectangles are distinct. Now consider the information gained from any query. If the query returns a non-zero value, the function is learned. If not, at most one function from $\\mathcal{F}$ is ruled out - the function whose rectangle was queried. Then any sequence of $n$ queries to an oracle can rule out at most $n$ of the functions of $\\mathcal{F}$ , so that at least $|{\\mathcal{F}}|=p^{k}$ queries are required in the worst case. 口  \n\n![](/tmp/output/65_20250325154860/images/be07ffc1c4e3c7b10121a7a2bd89ebed28897a8faddefadc854f7108dce9e664.jpg)  \nNumber of Agreeing Models by Prototypicality   \nFigure 5: Fidelity is easier on more prototypical examples.  \n\nPutting Lemma 1 and 2 together gives us Theorem 1.  \n\nTheorem 2. Checking whether two networks with domains $\\{0,1\\}^{d}$ are functionally equivalent is NP-hard.  \n\nProof. We prove this by reduction to subset sum. A similar reduction (reducing to 3-SAT instead of Subset Sum) for a different statement appears in [58].  \n\nSuppose we receive a subset sum instance $T,p,[\\nu_{1},\\nu_{2},\\cdot\\cdot\\cdot,\\nu_{d}]$ - the set is $\\nu.$ , the target sum is $T$ and the problem’s precision is $p$ . We will construct networks $f_{1}$ and $f_{2}$ such that checking if $f_{1}$ and $f_{2}$ are functionally equivalent is equivalent to solving the subset sum instance. We start by setting $f_{1}=0$ - it never returns a non-zero value. We now construct a network $f_{2}$ that has nonzero output only if the subset sum instance can be solved (and finding an input with nonzero output reveals the satisfying subset).  \n\nThe network $f_{2}$ has three hidden units in the first layer with incoming weight for the ith feature equal to $\\nu_{i}$ . This means the dot product of the input $x$ with weights will be the sum of the subset $\\{i|x_{i}=1\\}$ . We want to force this to accept iff there is an input where this sum is $T$ . To do so, we use the same 3-ReLU gadget as in the proof of Theorem 1:  \n\n$$\n\\begin{array}{r l}&{f_{2}(\\boldsymbol{x};T,p,\\nu)=\\mathrm{ReLU}(\\boldsymbol{x}\\cdot\\nu-(T-p/2))}\\ &{\\qquad+\\mathrm{ReLU}(\\boldsymbol{x}\\cdot\\nu-(T+p/2))-2\\mathrm{ReLU}(\\boldsymbol{x}\\cdot\\nu-T).}\\end{array}\n$$  \n\nAs before, this will only be nonzero in the range $[T-p/2,T+$ $p/2]$ , and we are done.  \n\n# B Prototypicality and Fidelity  \n\nWe know from Section 5 that learning strategies struggle to achieve perfect fidelity due to non-determinism inherent in learning. What remains to be understood is whether some samples are more difficult than others to achieve fidelity on. We investigate using recent work on identifying prototypical data points. Using each metric developed in Carlini et al. [44], we can rank the Fashion-MNIST test set in order of increasing prototypicality. Binning the prototypicality ranking into percentiles, we can measure how many of the 90 models we trained for Section 5 agree with the oracle’s prediction. The intuition here is that more prototypical examples should be more consistently learnable, whereas more outlying points may be harder to consistently classify. Indeed, we find that this is the case - all metrics find a correlation between prototypicality and model agreement (fidelity), as seen in Figure 5. Interestingly, the metrics which do not use ensembles of models (adversarial distance and holdout-retraining) have the best correlation with the model agreement metric—roughly the top $50\\%$ of prototypical examples by these metrics are classified the same by nearly all 90 models.  \n\n# C Supplement for Section 6  \n\nAccuracies for the oracles in Section 6 are found in Table 9.   \n\n\n<html><body><table><tr><td colspan=\"2\">MNIST</td><td colspan=\"2\">CIFAR-10</td></tr><tr><td>Parameters</td><td>Accuracy</td><td>Parameters</td><td>Accuracy</td></tr><tr><td>12,500</td><td>94.3%</td><td>49,000</td><td>29.2%</td></tr><tr><td>25,000</td><td>95.6%</td><td>98,000</td><td>34.2%</td></tr><tr><td>50,000</td><td>97.2%</td><td>196,000</td><td>40.3%</td></tr><tr><td>100,000</td><td>97.7%</td><td>393,000</td><td>42.6%</td></tr><tr><td>200,000</td><td>98.0%</td><td>786,000</td><td>43.1%</td></tr><tr><td>400,000</td><td>98.3%</td><td>1,572,000</td><td>45.9%</td></tr></table></body></html>\n\nTable 9: Statistics for the oracle models we train to extract.  \n\nFigure 6 shows a distribution over the bits of precision in the difference between the logits (i.e., pre-softmax prediction) of the 16 neuron oracle neural network and the extracted network. Formally, we measure the magnitude of the gap $|f_{\\uptheta}(x)-f_{\\hat{\\uptheta}}(x)|$ . Notice that this is a different (and typically stronger) measure of fidelity than used elsewhere in the paper.  \n\n# D Query Complexity of Functionally Equivalent Extraction  \n\nIn this section, we briefly analyze the query complexity of the attack from Section 6. We assume that a simulated partial derivative requires $O(1)$ queries using finite differences.  \n\n1. Critical Point Search. This step is the most nontrivial to analyze, but fortunately this was addressed in [19]. They found this step requires $O(h\\log(h))$ gradient queries, which we simulate with $O(h\\log(h))$ model queries.   \n2. Weight Recovery. This piece is significantly complicated by not having access to gradient queries. For each  \n\n![](/tmp/output/65_20250325154860/images/18bdd1e0df9d1063702426c71690decd8e5ff32b5150605478555eec9b5b3510.jpg)  \nFigure 6: For a 16-neuron MNIST model the attack works. Plotted here is number of bits of precision on the logits normalized by the value of the lot as done in the prior figure.  \n\nReLU, absolute value recovery requires $O(d)$ queries and weight sign recovery requires an additional $O(d)$ , making this step take $O(d h)$ queries total.  \n\n3. Global Sign Recovery. For each ReLU, we require only three queries. Then this step is $O(h)$ . 4. Last Layer Extraction. This step requires $h$ queries to make the system of linear equations full rank (although in practice we reuse previous queries here, making this step require 0 queries).  \n\nOverall, the algorithm requires $O(h\\log(h)+d h+h)=$ $O(d h)$ queries. Extraction requires $\\Omega(d h)$ queries without auxillary information, as there are $d h$ parameters in the model. Then the algorithm is query-optimal up to a constant factor, removing logarithmic factors from Milli et al. [19].  "}