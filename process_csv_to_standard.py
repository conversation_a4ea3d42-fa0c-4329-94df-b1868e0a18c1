import csv

INPUT_FILE = '论文数据集_rag.csv'
OUTPUT_FILE = '论文数据集_rag_standard.csv'
EXPECTED_FIELDS = 12

def clean_row(row):
    # 去除每个字段首尾空格
    return [col.strip() for col in row]

with open(INPUT_FILE, 'r', encoding='utf-8') as infile, \
     open(OUTPUT_FILE, 'w', encoding='utf-8', newline='') as outfile:
    reader = csv.reader(infile)
    writer = csv.writer(outfile, quoting=csv.QUOTE_ALL)
    for row in reader:
        row = clean_row(row)
        # 修正字段数
        if len(row) < EXPECTED_FIELDS:
            row += [''] * (EXPECTED_FIELDS - len(row))
        elif len(row) > EXPECTED_FIELDS:
            row = row[:EXPECTED_FIELDS]
        writer.writerow(row)

print(f'处理完成，标准化文件已保存为 {OUTPUT_FILE}') 