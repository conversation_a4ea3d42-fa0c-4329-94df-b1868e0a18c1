{"text": "# Keynote Talk  \n\n# Measuring Up: How to Keep Security Metrics Useful and Realistic  \n\n<PERSON><PERSON> <PERSON> RAND Corporation Arlington, VA, USA  \n\n# Abstract  \n\nSoftware quality measurement has a long and not always happy history. Eager to measure many aspects of software quality, researchers sometimes have measured what was expedient or available instead of what was useful and realistic. In this talk, <PERSON><PERSON> <PERSON> reviews software quality measurement, pointing out lessons that can be applied to current attempts to measure the security of systems and networks. She offers guidelines for effective security measurement that take into account not only the technology but also the business context in which the measurement is done.  \n\nCategories & Subject Descriptors:  D.2.8. Software Engineering. Metrics [product metrics, security metrics]; G.3. Probability and Statistics [reliability and life testing];  \n\nGeneral Terms:  Security, Reliability, Measurement  \n\n# B<PERSON>hari <PERSON> (Ph.D., Information Technology and Engineering, George Mason University; M.S., Planning, The Pennsylvania State University; M.A., Mathematics, The Pennsylvania State University; B.A., Mathematics with high honors, Harpur College, Binghamton, NY) is a senior researcher at RAND's Arlington, VA office where she helps organizations and government agencies understand whether and how information technology supports their mission and goals.  Dr<PERSON> began her career as a mathematician and then a software developer and maintainer for real-time, business-critical software systems. From 1982 to 2002, Dr<PERSON> was president of Systems/Software, Inc., a consultancy specializing in software engineering and technology. From 1997 to 2000, she was also a visiting professor at the University of Maryland's computer science department. In the past, she has been founder and director of Howard University's Center for Research in Evaluating Software Technology (CREST), a visiting scientist at the City University (London) Centre for Software Reliability, principal scientist at MITRE Corporation's Software Engineering Center, and manager of the measurement program at the Contel Technology Center (named by the Software Engineering Institute as one of the best such programs in the country). Dr. Pfleeger is well-known for her work in software quality, software assurance, and empirical studies of software engineering; she is particularly known for her multi-disciplinary approach to solving information technology problems. Her current projects include investigations of cybersecurity economics, insider threat, and a framework for defining and using cybersecurity metrics.  "}