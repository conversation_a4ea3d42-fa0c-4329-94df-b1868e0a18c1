{"text": "# Position Paper: The Science of Boxing  \n\nAnalysing Eval using Staged Metaprogramming  \n\n<PERSON>  \n\nDepartment of Computer Science, University <NAME_EMAIL>  \n\n# Abstract  \n\nThe ubiquity of Web 2.0 applications handling sensitive information means that static analysis of applications written in JavaScript has become an important security problem. The highly dynamic nature of the language makes this difficult. The eval construct, which allows execution of a string as program code, is particularly notorious in this regard. eval is a form of metaprogramming construct: it allows generation and manipulation of program code at run-time. Other metaprogramming formalisms are more principled in their behaviour and easier to reason about; consider, for example, Lispstyle code quotations, which we call staged metaprogramming. We argue that, instead of trying to reason directly about uses of eval, we should first transform them to staged metaprogramming, then analyse the transformed program. To demonstrate the feasibility of this approach, we describe an algorithm for transforming uses of eval on strings encoding program text into uses of staged metaprogramming with quoted program terms. We present our algorithm in the context of a JavaScript-like language augmented with staged metaprogramming.  \n\nCategories and Subject Descriptors D.3.3 [Language Constructs and Features]: Metaprogramming  \n\nGeneral Terms Languages, Verification, Security  \n\nKeywords JavaScript, eval, staged metaprogramming, static analysis, program transformation  \n\n# 1. Introduction  \n\nThe eval construct of JavaScript takes a string and parses and executes it as program code. For several years, authors of static analyses for JavaScript argued that they could ignore it because it was rarely used or used only in trivial ways [9]. Their real reason was probably that analysis of such a powerful construct seemed utterly hopeless, particularly when considering the language’s lack of protection mechanisms, as it allows arbitrary behaviours to result from an unstructured data value.  \n\nHowever, a recent survey [18] showed that a majority of the most popular websites use eval and that they use it in many varied (and often misguided) ways. Correspondingly there has been a surge of interest in techniques for dealing with eval. Some authors argue that static analysis of eval really is hopeless and instead develop dynamic analyses that monitor execution of evaled code [11, 20], enforcing security policies by terminating the program immediately before a violation. Indeed, this is probably the most reliable and mature technology currently available [6], but given the benefits of catching violations early in development or deployment, the attitude seems somewhat fatalistic.  \n\nFurthermore, there are fundamental limits to dynamic monitors: they can only enforce safety properties. For example, noninterference [8], which is one of the best known information flow security properties, is not a safety property [22]. Consequently, it can never be precisely enforced by a monitor [19], although it is possible to enforce more restrictive properties such as isolation of untrusted code [16].  \n\nSome recent work [13, 17] considers how to determine what arguments may be passed to uses of eval and hence how to replace occurrences of eval with code that does not use it. However, these approaches seem limited to handling certain fixed patterns of usage. We argue that reasoning about eval could better be viewed as two distinct problems: the first is determining what code may be executed; the second is reasoning about the behaviour of that code. As each is difficult in its own right, we are more likely to be successful if we focus on one problem at a time. In previous work [15] we addressed the second problem by showing how to analyse information flow in a JavaScript-like language augmented with Lispstyle code quotations or staged metaprogramming. We now tackle the first problem by showing how to translate uses of eval in this language into staged metaprogramming.  \n\nWe begin in Section 2 by explaining what staged metaprogramming is and why it is a suitable formalism for most uses of eval. We describe our algorithm for translating eval into staged metaprogramming in Section 3 and discuss future directions in Section 4. We briefly review related work in Section 5, before concluding in Section 6.  \n\n# 2. Metaprogramming  \n\n# 2.1 Staged Metaprogramming  \n\nThe language Lisp allows programs to construct code templates as data values, splice them together and run the resulting code. We refer to these features collectively as staged metaprogramming. Following the language $\\lambda_{S}$ of Choi and others [3], we can add these features to a programming language with three constructs:  \n\n• box $e$ turns the expression $e$ into a code value; it does not evaluate $e$ .   \n• run $e$ evaluates the code value $e$ .   \n• unbox $e$ may only occur inside a box expression. It forces evaluation of $e$ ; the resulting code value is spliced into the surrounding code template.   \nBooleans ${\\begin{array}{l l l}{b}&{::=}&{{\\mathrm{~true~}}|~\\mathbf{fa}|_{\\mathbf{S}}}\\ {s}&{\\in}&{S t r i n g}\\ {n}&{\\in}&{N u m b e r}\\ {x}&{\\in}&{N a m e}\\ {k}&{:=}&{{\\mathrm{~undef~}}|~\\mathbf{null~}|~b~|~s~|~n}\\ {e}&{:=}&{k~|~x~|~\\mathrm{~box~}e~|~\\mathbf{~unbox~}e~|~\\mathbf{~run~}e~}\\ {|~\\mathbf{fun}(x)[e]~|~e(e)~|~\\mathbf{~if}(e)~{\\big\\{~e\\}}~\\mathbf{e}|\\leq\\mathbf{ls}(e)}\\ {|~\\{\\overline{{s:e}}}{\\big\\}}&{|~e[e]~|~e[e]~=e~|~\\mathbf{del}~e[e]~}\\end{array}}$   \nStrings   \nNumbers   \nNames   \nConstants   \nExpressions   \nlet $x=e_{1}$ in $e_{2}$ is an abbreviation for ${\\bf f u n}(x)\\{e_{2}\\}(e_{1})$  \n\nIn previous work [15], we took a subset of $\\lambda_{J S}$ , the core calculus for JavaScript developed by Guha and others [10], and added staged metaprogramming with box, unbox and run. We called the resulting language SLamJS . An abbreviated syntax is in Figure 1.  \n\nFor example, in SLamJS , box $(x*2)$ evaluates to a code value, which can be stored in a variable or passed to a function like any other value: let $y=\\mathsf{b o x}\\left(x*2\\right)$ . When that code value is run, for example with run $y$ , it evaluates to double the value of $_x$ in the current scope, which may be different from the scope in which the code value was defined. If we let $z=\\mathbf{b}\\mathbf{o}\\times2$ , we can create the same code value with let $y=\\mathbf{box}(x*(\\mathbf{unbox}z))$ .  \n\n# 2.2 Expressivity of Metaprogramming  \n\nAlthough JavaScript’s eval is only a single language construct, metaprogramming can increase the expressivity of a programming language in several distinct ways. In most cases, we find the expressivity is matched by staged metaprogramming:  \n\nComposing Code Templates In JavaScript, code templates can be encoded as strings and spliced together using string concatenation. For example:  \n\nvar $\\textbf{f}=$ function(z) { return 3 \\* z };   \nvar ${\\tt{y}}=\"2\"$ ;   \nvar $\\texttt{x}=\\texttt{\"f}(\\texttt{\"}+\\texttt{y}+\\texttt{\"})\\texttt{\"}$ ;   \neval ${\\bf\\Psi}({\\bf x})$ ;  \n\nAdding box, unbox and run to JavaScript, we could express this as:  \n\nvar $\\textbf{f}=$ function(z) { return $3*\\textsf{z}\\}$ ;   \nvar $\\textbf{y}=\\mathtt{b o x}(2)$ ;   \nvar $\\textbf{x}=\\mathtt{b o x}(\\mathtt{f}(\\mathtt{u n b o x}(\\mathtt{y})))$ ;   \n$\\tt r u n(x)$ ;  \n\nWe show how to automate this transformation in Section 3.  \n\nChanging Scoping When code is run with eval, it is evaluated in the scope of the eval, not the scope in which the code was defined (as is the case for functions). This example (left) returns 2, not 1; again, it can be modelled in staged metaprogramming (right):  \n\n$$\n\\begin{array}{r l}&{\\mathrm{(function(x)\\{\\qquad\\quad}}\\quad\\quad\\mathrm{(function(x)\\{\\qquad}}}\\ &{\\mathrm{~\\underline{{return}}~(f u n c t i o n(y)~\\{\\qquad~\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{return}}~(f u n c t i o n(x)~\\{\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{return}}~(f u n c t i o n(x)~\\{\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{return}}~\\mathrm{~\\mathrm{~\\mathrm{~\\varepsilon~}~}}~}}\\ &{\\mathrm{~\\underline{{~\\xi~}}\\mathrm{~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{~\\xi~}}\\mathrm{~}\\mathrm{~)~\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}}}\\ &{\\mathrm{~\\underline{{~\\xi~}}\\mathrm{~}\\mathrm{)~\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{~\\xi~}}\\mathrm{~}\\mathrm{~}\\mathrm{~)~\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~}~}}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}\\mathrm{~\\mathrm{~\\varepsilon~}~}}\\ &{\\mathrm{~\\underline{{~\\xi~}}\\mathrm{~}\\mathrm{~}\\mathrm{~}\\mathrm{~}\\mathrm{~\\varepsilon~}\\mathrm{~}\\mathrm{~\\mathrm{~\\varepsilon~}\n$$  \n\nThe precise choice of scoping rules may vary between languages. For example, $\\lambda_{S}$ has less permissive variable capture rules than $S L a m J S$ ; the latter’s are closer to those of JavaScript. However, JavaScript’s eval has the peculiarity that, if it is called via an alias, it evaluates in the global scope, rather than the local one.  \n\nChanging Evaluation Order It is folklore that metaprogramming facilities allow a programmer to mix uses of call-by-name and callby-value evaluation in a single program. This intuition is made more concrete by Inoue and Taha [12]. While this is expressible in both JavaScript eval and staged metaprogramming, the analyses we consider are not sensitive to the change, so we do not discuss it further here.  \n\nSerialisation In metaprogramming terminology, turning a data value into its corresponding code value is called lifting [23]. This is not generally possible in JavaScript: there is no facility to turn a function into its source code. It is possible for primitive data values as these can be converted into strings. In fact, JavaScript string concatenation does this implicitly.  \n\nTo model this behaviour in staged metaprogramming, we need an operation lift $e$ that evaluates $e$ and, if it evaluates to a primitive value, turns it into the corresponding code value. (Note that this is in contrast to box $e$ , which does not evaluate $e.$ .) Using lift, this example:  \n\nvar ${\\tt y}=2$ ; var $\\texttt{x}=\\texttt{\"f}(\\texttt{\"}+\\texttt{y}+\\texttt{\"})\\texttt{\"}_{\\mathrm{i}}$ would become:  \n\n$$\n\\begin{array}{r l}{\\mathrm{~y~=~2~;~}}\\ {\\mathrm{~x~=~box(\\mathtt{f}(u n b o x(\\mathtt{l i f t}(y))))~;~}}\\end{array}\n$$  \n\nWe did not consider lift in our original work on SLamJS . Fortunately, for primitive datatypes, it can be added to our language and analysed in the same way as basic arithmetic operations, by defining a new primitive operation.  \n\nDeserialisation If code can be treated as a data value, it can be read from files or the network and dynamically loaded using metaprogramming facilities during program execution. Some JavaScript libraries support incremental loading in this way. This is also possible using staged metaprogramming, although it requires support from the language runtime environment to turn data from the network into code values, in much the same way that it already provides support for turning strings passed to eval into code.  \n\nStatic analysis of code using incrementally loaded libraries is difficult because not all of the code will necessarily be available at once. However, this is not a problem peculiar to eval or even JavaScript, although the haphazard infrastructure of Web applications does complicate it. We discuss this and related concerns further in Section 4.3.  \n\nAside: Reading the popular JSON format is one simple example of deserialisation; JSON encodes data as the JavaScript source code string that evaluates to that data. However, because of security concerns, current “best practice” is not to eval JSON code, but instead to use a library function JSON .parse(). Perhaps a new best practice should be not to manipulate code templates with string concatenation, but to use (as yet unwritten) library functions $S t a g e.b o x()$ and Stage.unbox ()? This would provide a path for developers using eval to migrate to staged metaprogramming, making it less likely that they would unwittingly introduce security holes through sloppy use of eval.  \n\nIntensional Uses Some metaprogramming formalisms, such as the SF calculus of Given-Wilson and Jay [7], allow code values to be examined internally and broken into their component parts. This is not possible in our staged metaprogramming formalism. It is possible in JavaScript but is rare, presumably because it is difficult to achieve using unstructured strings. A JSON parser written in JavaScript using regexes and substring operations would be an example of an intensional use.  \n\n# 3. The Boxing Algorithm  \n\n# 3.1 Overview  \n\nWe now describe our algorithm for transforming uses of eval into staged metaprogramming. We illustrate our algorithm with examples in SLamJS , although we believe that the idea is sufficiently general that it could be applied to many other languages with an eval construct, such as Perl and PHP.  \n\n• code constants into box expressions;   \n• concatenation of code strings into splicing using unbox;  \n\n• eval into run.  \n\nFor example: becomes:   \nlet $x=\"\\mathrm{\\tty\"}\\$ n let $x=\\mathbf{b}\\mathbf{o}\\times y$ in   \neval $x$ run $_x$   \nwhile: becomes:   \nlet $f=\\mathbf{fun}(z)\\{3*z\\}$ in let $f=\\mathbf{fun}(z)\\{3*z\\}$ in   \nlet $y=\"2\"$ in let $y=\\mathbf{b}\\mathbf{o}\\times\\mathrm{~2~}$ in   \nlet $x=\"\\pounds(\"+y+\")\"$ in let $x=\\mathbf{box}(f(\\mathbf{unbox}y))$ in   \neval $x$ run $_x$  \n\nIn order to use our algorithm, certain conditions must hold:  \n\n• We need a sound dataflow analysis for the target language, including metaprogramming constructs. • We need a string analysis for the target language that will give a sound over-approximation of the string values that may occur at different program points or be bound to different variables. • The language must be parseable using lex and yacc or similar.  \n\nIn previous work, we showed how to apply 0CFA [21] to SLamJS , so we reuse that analysis here. As 0CFA over-approximates the flow control of a program with a regular graph, it is easy to extract a grammar-based string analysis from the results. This is what we do in our implementation, although there are many techniques that could improve upon this [2].  \n\n# 3.2 Parsing  \n\nOur algorithm relies on using the existing parser for a language for part of the transformation; this makes our technique easily applicable to other languages. Typically, languages are parsed in two phases. The first phase, lexicographic analysis, transforms the program text into a sequence of tokens; the tool lex generates a lexer that does this using a deterministic finite state automaton. In the second phase, the actual parser turns the sequence of tokens into an abstract syntax tree; yacc generates a LALR parser to do this with a restricted form of pushdown automaton.  \n\nNormally, the lexer processes the characters in the program text in order from beginning to end. Similarly, the parser processes the token sequence in order from left to right (hence the second L in LALR). Our algorithm abuses these tools to process evaled text out of order in fragments. Effectively, this gives us a finite way of parsing a string abstraction, which may encode infinitely many concrete strings of unbounded length. However, there is a price: we risk changing the meaning of the evaled code. To avoid this (and hence keep our transformation sound), we must check that the lexing and parsing phases are unaffected by the change of order; we describe these checks in Section 3.4.  \n\n# 3.3 Algorithm Description  \n\nWe must first modify the grammar of the language slightly: we add a new token $\\mathtt{H O L E}(\\mathbf{x})$ and a rule that this token constitutes an expression with corresponding abstract syntax tree hole $x$ . An outline of a cycle of the algorithm is as follows; examples of some steps are shown in Figure 3:  \n\nlet $f o o=\"{\\circ}\"$ in   \nlet $x=$ \"foo\" in   \nif $(b)\\{S t r i n g.s u b s t r(x,0,1)\\}~{\\sf e l s e}~{\\left\\{\\sf e v a l}~{\\boldsymbol{x}}\\right\\}}$  \n\nFigure 2. An example of an intensional use, which could not be transformed by our algorithm; $^b$ is a Boolean variable.  \n\n1. Use the dataflow analysis to determine which string constants $S$ and results of string concatenations $C$ flow into uses of eval.   \n2. Use the same analysis to determine which strings $I$ flow directly to other string operations, such as object property lookup $(e[e])$ or substring operations. Check that $I\\cap({\\bar{S}}\\cup{\\bar{C}})=\\emptyset$ ; otherwise fail. (Failure here usually corresponds to an attempted intensional use of eval; see Figure 2 for an example.)   \n3. Use the string analysis to approximate the values of strings used at different points in the program.   \n4. If a string value eventually flows to an eval, and the string analysis tells us it is a constant, then, if possible, statically parse the constant string to an expression $e$ and replace the string value with box $e$ .   \n5. To handle the remaining cases, where evaled string values are not constant: (a) At a string concatenation $c$ that flows to an eval, build a list of subexpressions that are concatenated together. (b) Use the string analysis to determine whether each subexpression refers to a constant string; if it does, replace it in the list with that string. (c) Merge adjacent constant strings in the list. (d) Run the lexer on each constant string in the list; replace it with the resulting sequence of tokens (removing EOF if necessary). Give up if the lexer fails. (e) Replace each non-constant subexpression $e$ in the list with a unique HOLE $\\mathbf{\\rho}(\\mathbf{x})$ ; record the mapping as $H(x):=e$ ; perform the lexing check described in Section 3.4 with $e$ and the items immediately preceding and following it in the list; give up if it fails. (f) Concatenate all sequences of tokens in the list and parse the result with the modified parser. Give up if it fails. (g) In the resulting parse tree $e$ , replace each subexpression hole $x$ with unbox $H(x)$ . (Recall $H(x)$ is the expression that generated the non-constant string corresponding to the hole.) (h) Taking the modified parse tree $e^{\\prime}$ , replace the whole concatenation $c$ with box $e^{\\prime}$ .   \n6. Check that all string expressions flowing to an eval have been transformed. If so, transform all instances of eval to our staged  \n\nThe algorithm also needs to transform any primitive operations that operate on code values. For example, if we wish to model JavaScript’s conversion of concatenated values to strings, we will need to replace uses of string conversion with lift.  \n\nA single cycle may be insufficient. The evaled code may manipulate values that are used as code in an eval. So we must analyse the transformed program again and check that any transformations already performed are still valid in the presence of any new behaviours introduced by evaled code. If they are not valid, the algorithm fails. If they are, we also need to check if any new evals have been introduced; if so, we cycle through the algorithm again until no new uses are introduced.  \n\n![](/tmp/output/225_20250326091405/images/21df891b6a9bb631cf798eaefb510d56e5f08c4ac9be6e4c63f457edc443f0bd.jpg)  \nFigure 3. Some steps in a run of the boxing algorithm.  \n\n# 3.4 Correctness  \n\nWe now address some concerns as to whether the algorithm is sound; that is, whether it is possible that it alters the behaviour of a program. We do not formally prove soundness.  \n\nSide Effects If it is possible that we transform an expression $e$ with side effects to a code value $e^{\\prime}$ without side effects, we must be careful to preserve them. We can do this by executing $e$ and discarding the result; that is, transforming $e$ not to $e^{\\prime}$ but to let $x=e$ in $e^{\\prime}$ , where $_x$ is fresh.  \n\nLexing Suppose we have a language where (unlike in SLamJS ) function application can be written as concatenation (without brackets) and consider the following:  \n\nlet $x=\\mathbf{if}{\\bigl(}y{\\bigr)}{\\bigl\\{}^{*}(\\mathbf{g}(3))^{*}{\\bigr\\}}\\mathbf{e}|\\mathbf{s}\\mathbf{e}{\\bigl\\{}^{*}\\mathbf{g}(3)\"{\\bigr\\}}$ in   \nlet $z=\"\\mathbf{f}\"+x$ in   \neval $z$  \n\nDepending on the value of $y$ , the values of $z$ and its tokenisations might be:  \n\n$$\n{\\begin{array}{r l}{^{\"}{\\mathbf{f}}\\left({\\mathbf{g}}(3)\\right)\"}&{\\longmapsto{\\mathbf{V}}{\\mathbf{A}}{\\mathbf{R}}({\\mathbf{f}}){\\mathrm{~LP~}}{\\mathbf{V}}{\\mathbf{A}}{\\mathbf{R}}({\\mathbf{g}}){\\mathrm{~LP~}}{\\mathbf{I}}{\\mathbf{N}}{\\mathbf{T}}(3){\\mathrm{~RP~}}{\\mathrm{~RP~}}}\\ {\"{\\mathbf{f}}\\left.{\\mathbf{g}}(3)\"\\right.}&{\\longmapsto{\\mathbf{V}}{\\mathbf{A}}{\\mathbf{R}}({\\mathbf{f}}{\\mathbf{g}}){\\mathrm{~LP~}}{\\mathrm{~INT}}(3){\\mathrm{~RP~}}}\\end{array}}\n$$  \n\nSuppose we try to transform the concatenation that is assigned to $z$ As $x$ is not constant, we tokenise it as HOLE ${\\bf\\Psi}({\\bf x})$ ; the concatenation then tokenises as VAR(f) HOLE ${\\bf\\Psi}({\\bf x})$ . Treating HOLE ${\\bf\\Psi}({\\bf x})$ as a wildcard, this matches the first case, but not the second. The problem is that the use of the hole has changed the tokenisation of the string.  \n\nConceptually, we can view the lexer as a deterministic finite state transducer $T$ . When strings $x$ and $y$ encoding program text are concatenated, we want to check that $T(x\\cdot y)=\\bar{T}(x)\\cdot T(y)$ .  \n\nThis hides some details of the problem, as in practice tokens that are identical in the automaton model often carry some data that distinguishes them (as with VAR(f) and $\\mathtt{V A R}(\\mathtt{f g})$ in the above example). What we really need to check is that concatenation does not change the positions of token boundaries in the program text. We can enforce this by checking (using the obvious algorithm) that, if $X$ and $Y$ are regular abstractions of strings that may be concatenated, whatever state the lexer’s DFA may be in at the end of a string in $X$ , it will emit a token and restart upon seeing any character that may occur at the start of a string in $Y$ .  \n\nParsing Consider arithmetic expressions in the program:  \n\nlet $x=\"2\"$ in   \nlet $y=\"3~-~1\"$ in   \nlet $z=x+\"*\"+y$ in   \neval $z$  \n\nThe result will be $2*3-1$ , which is 5. We might be tempted to transform this to:  \n\nlet $x=\\mathtt{b o x}2$ in   \nlet $y=\\mathsf{b o x}\\left(3-1\\right)$ in   \nlet $z=\\boldsymbol{\\mathsf{b o x}}\\left((\\boldsymbol{\\mathsf{u n b o x}}~x)*(\\boldsymbol{\\mathsf{u n b o x}}~y)\\right)$ in   \nrun $z$  \n\nHowever, the evaled expression then becomes $2*(3-1)$ , which is 4. (Arguably this may have been the intent of the author of the program.) The problem arises because the grammar of expressions in the language is ambiguous. The proposed transformation corresponds to one possible parsing of $2*3-1$ , but not the one chosen by the language’s yacc parser.  \n\nWe can avoid this problem by requiring that the language has an unambiguous grammar. This is not an onerous requirement as, when yacc accepts ambiguous grammar specifications, it resolves the grammar ambiguity (perhaps arbitrarily) and produces a parser for a more restricted, unambiguous grammar, which would itself be valid as a grammar specification.  \n\nDisambiguation is often achieved by adding extra syntactic classes of expression. The example above might not be transformable in this case, as while $x$ and $y$ should clearly encode valid expressions, it might no longer be permissible to multiply arbitrary expressions. Replacing the final concatenation with $\"(\"+x+\")*(\"+y+\")\"$ would allow the transformation.  \n\nTermination Unless the program has an infinite sequence of evals that generate new evals, the algorithm is guaranteed to terminate. Even then, if using a dataflow analysis such as 0CFA, each cycle will add new edges to the dataflow graph, so it seems likely it will eventually be saturated and a fixed point will be reached. This is not obviously certain, as replacing strings with code can introduce new program points, increasing the number of nodes in the graph. If possible nontermination is an issue, the algorithm can simply arbitrarily terminate after a fixed number of cycles; it seems unlikely that a useful, realistic program will feature a pathological infinite sequence of nested evals.  \n\n# 4. Evaluation and Future Work  \n\n# 4.1 Precision and Scalability  \n\nWe have implemented our algorithm, but have so far only tested it on a few examples. Consequently, we do not yet know how good it is in practice. This work was motivated by a desire to check information flow properties in JavaScript; we intend to chain the boxing algorithm with our information flow analysis for SLamJS to evaluate its effectiveness. We are confident we can handle most situations where a use of eval receives only a constant string, but we are more interested in situations where there may be many strings encoding expressions of different shapes.  \n\nOur current implementation is based on 0CFA, which is fast, but also relatively imprecise. If this turns out to be a problem, we can apply the same approach to CFA2 or some other, more precise analysis.  \n\nSome aspects of our algorithm are not fully specified. For example, we do not prescribe in what order to transform code stringmanipulating expressions in the source program, and this may affect whether the algorithm succeeds. In general, we would like to transform the smallest possible code-manipulating subexpression, as this results in a transformed program that most closely resembles the original program; our current implementation uses this greedy heuristic. However, there are situations where this causes the algorithm to fail (for example, because it results in the lexing check being violated), but other choices would succeed. Tuning such aspects of our algorithm is another area for future investigation.  \n\n# 4.2 Soundness and Applicability  \n\nAlthough we have argued for the soundness of the algorithm, we have not yet proved formally that it is correct or that it always terminates. This is an important next step, particularly if the transformation is to be used in security-related analyses.  \n\nAs with many more theoretical works on programming languages, we handle an idealised version of the language that interests us (JavaScript). It would take further effort to apply our work to the entirety of a real-world programming language. There is a compositional, semantics-preserving translation from JavaScript to $\\lambda_{J S}$ , on which SLamJS is based, so we think we can reasonably claim applicability. INRIA’s Prosecco has produced an unambiguous Menhir (yacc for Ocaml) grammar for JavaScript, so it meets the criteria for our algorithm to be applied.  \n\nOne area that would require particular attention in handling full JavaScript is the range of different possible scoping behaviours for eval. In $\\bar{S}L a m J S$ , we currently model how eval executes code with dynamic variable scoping, but do not consider quirks such as running code in the global scope when eval is called indirectly via an alias. The latest version of JavaScript complicates matters by introducing a new “strict” mode of eval (which generally behaves better) but retaining the old behaviours for backwards-compatibility. The latest version of $\\lambda_{J S}$ , called S5 [14], goes to some lengths to model the different variations in scoping rules. To take account of the different scoping rules, the boxing transformation would need to include a static analysis to determine in which modes each instance of eval can be run.  \n\n# 4.3 Infrastructural Issues  \n\nIn considering applications of the algorithm, one should also consider when it will be run and by whom. So let us now discuss more generally the infrastructural requirements for static analysis of Web applications. In the context of information flow analysis of JavaScript, the precision of dynamic analysis is good enough for many practical situations, even though there are inherent limitations. By definition, dynamic analysis occurs when code is run. For client-side JavaScript, that means being run by a user, in a browser; this usually requires a modified browser, although monitoring within JavaScript is possible [16].  \n\nThe primary advantage of static analysis of code is its ability to identify possible errors or security problems before code is run, ideally during development. So while one could imagine a static analysis being part of a browser, it would make more sense for it to be used on the webserver or by the developer.  \n\nThis scenario introduces further challenges, as a complete Web application is unlikely to be a single, monolithic piece of code. For example, it may use libraries, which may be loaded incrementally during execution. This is not especially problematic, as the developer presumably knows which libraries could be loaded, has access to their code and may have control over the server from which they are loaded. So in principle, the application could be treated as a single piece of code (including all libraries), although this might not scale well to large libraries.  \n\nIn the event that the developer does not control which version of the libraries is served, the application would need a mechanism for identifying which library has been loaded and a policy to follow if an unknown library is loaded. For example, the application code could include hashes of libraries against which it has been analysed. The browser would be responsible for checking hashes of libraries as they are loaded. In the event that an unknown library is loaded, either the application could fail or it could fall back to some dynamic analysis.  \n\nThe situation becomes more difficult when third-party content (such as adverts or user-supplied plugins) is involved. In this case, some of the code might not even be written until after development of the core application. If all code, including adverts and plugins, is served from the same webserver, then it is still the case that in principle the application could be treated as a single piece of code. However, authors of adverts and plugins might need to take some responsibility for ensuring their code was amenable to static analysis.  \n\nOnce again, if the code is not centrally distributed, the application needs a mechanism for identifying well-behaved code, but it is no longer possible to use a whitelist. Instead, the application would have to include some form of software contract specifying acceptable behaviours of third-party content and the browser would need to stage static analysis of code as it was loaded to check this contract. In order for this to be practical, either the contract would need to be easily verifiable syntactically [4], or the content would need to include an easily verifiable certificate witnessing that it satisfies the contract.  \n\n# 5. Related Work  \n\nDynamic Analyses of Eval There are various dynamic or monitoring analyses for enforcing information flow policies in JavaScript that tackle eval. Chugh and others [4] propose initially using a static analysis of a program, but then running it in a monitor that performs a simpler static analysis of code when it is evaled. Hedin and Sabelfeld [11] develop a dynamic type system that enforces termination insensitive noninterference. However, note that a monitor that immediately stops any program will also enforce this.  \n\nFundamentally, we think it is preferable (although harder) to verify properties of programs before running them rather than to terminate them when an error occurs at run-time.  \n\nTools for Removing Eval Jensen and others [13] produce a tool called Unevalizer, which uses the JavaScript analysis framework TAJS to identify and automatically remove uses of eval, so that other static analyses may be used. They have few problems with constant strings, but only seem able to handle non-constant strings if they match one of a fixed number of built-in usage patterns.  \n\nThe tool Evalorizer [17] is also aimed at removing uses of eval, but the use case is different: the aim is to assist a developer in removing eval from code. The developer runs and tests JavaScript code in a browser configured to use a proxy provided by the tool. The tool collects uses of eval and categorises them to suggest how they might be rewritten.  \n\nStaged Metaprogramming There is a wide body of literature discussing metaprogramming, although it is still relatively poorly understood from a semantic perspective. There is not so much work on automated verification and analysis of metaprogramming. Choi and others [3] propose a translation for removing staged metaprogramming, so that existing automated tools can be used. However, their limited variable capture semantics may make it hard to apply their work to SLamJS or JavaScript. Berger and Tratt develop a Hoare-style program logic for a version of ML with metaprogramming [1], but there do not seem to be any automated tools based on this work.  \n\nString Analysis of Code A common source of security vulnerabilities in Web applications is accidental execution of untrusted code resulting from careless splicing of user-supplied data into code templates. String analyses are a popular method for detecting such vulnerabilities, and are used effectively to analyse the shape of parse trees [24], but the behaviour of the resulting code is not usually analysed. Like us, Doh and others [5] use string abstraction and a modification to the language’s parser to analyse dynamically generated code. Their string analysis is more advanced than ours, but the end result is a grammar, rather than a translation to staged metaprogramming. As Choi and others [3] point out, it is difficult to analyse the behaviour of code generated by such a grammar, as it is not clear how to concretise it in a precise way.  \n\n# 6. Conclusion  \n\nIt is both necessary and possible to analyse statically the behaviour of JavaScript’s metaprogramming construct eval, but the complexity of the task means it is best split into two problems: a transformation to a more principled form of metaprogramming and an analysis of this more principled form. Taking into account our previous work on analysis of staged metaprogramming, we have now developed the theory to handle both problems. In particular, this demonstrates how to analyse information flow statically in languages like JavaScript in the presence of eval.  \n\n[17] F. Meawad, G. Richards, F. Morandat, and J. Vitek. Eval begone!: semi-automated removal of eval from javascript programs. In OOPSLA, pages 607–620, 2012.   \n[18] G. Richards, C. Hammer, B. Burg, and J. Vitek. The eval that men do - a large-scale study of the use of eval in javascript applications. In ECOOP, pages 52–78, 2011.   \n[19] A. Russo and A. Sabelfeld. Dynamic vs. static flow-sensitive security analysis. In CSF, pages 186–199, 2010.   \n[20] A. Sabelfeld and A. Russo. From dynamic to static and back: Riding the roller coaster of information-flow control research. In Ershov Memorial Conference, pages 352–365, 2009.   \n[21] O. Shivers. Control-flow analysis in scheme. In PLDI, pages 164–174, 1988.   \n[22] T. Terauchi and A. Aiken. Secure information flow as a safety problem. In SAS, pages 352–367, 2005.   \n[23] L. Tratt. Compile-time meta-programming in a dynamically typed oo language. In DLS, pages 49–63, 2005.   \n[24] G. Wassermann and Z. Su. Sound and precise analysis of web applications for injection vulnerabilities. In PLDI, pages 32–41, 2007.  \n\n# Acknowledgments  \n\nWe thank our anonymous reviewers for their comments, particularly with regard to future work.  \n\n# References  \n\n[1] M. Berger and L. Tratt. Program logics for homogeneous metaprogramming. In LPAR (Dakar), pages 64–81, 2010.   \n[2] T.-H. Choi, O. Lee, H. Kim, and K.-G. Doh. A practical string analyzer by the widening approach. In APLAS, pages 374–388, 2006.   \n[3] W. Choi, B. Aktemur, K. Yi, and M. Tatsuta. Static analysis of multistaged programs via unstaging translation. In POPL, pages 81–92, 2011.   \n[4] R. Chugh, J. A. Meister, R. Jhala, and S. Lerner. Staged information flow for javascript. In PLDI, pages 50–62, 2009.   \n[5] K.-G. Doh, H. Kim, and D. A. Schmidt. Abstract lr-parsing. In Formal Modeling: Actors, Open Systems, Biological Systems, pages 90–109, 2011.   \n[6] J. S. Fenton. Memoryless subsystems. Comput. J., 17(2):143–147, 1974.   \n[7] T. Given-Wilson and B. Jay. A combinatory account of internal structure. J. Symb. Log., 76(3):807–826, 2011.   \n[8] J. A. Goguen and J. Meseguer. Security policies and security models. In IEEE Symposium on Security and Privacy, pages 11–20, 1982.   \n[9] S. Guarnieri and V. B. Livshits. Gatekeeper: Mostly static enforcement of security and reliability policies for javascript code. In USENIX Security Symposium, pages 151–168, 2009.   \n[10] A. Guha, C. Saftoiu, and S. Krishnamurthi. The essence of javascript. In ECOOP, pages 126–150, 2010.   \n[11] D. Hedin and A. Sabelfeld. Information-flow security for a core of javascript. In CSF, pages 3–18, 2012.   \n[12] J. Inoue and W. Taha. Reasoning about multi-stage programs. In ESOP, pages 357–376, 2012.   \n[13] S. H. Jensen, P. A. Jonsson, and A. Møller. Remedying the eval that men do. In ISSTA, pages 34–44, 2012.   \n[14] S. Krishnamurthi. Semantics and analyses for javascript and the web. In SAS, page 4, 2012.   \n[15] M. Lester, L. Ong, and M. Schaefer. Information flow analysis for a dynamically typed functional language with staged metaprogramming. In CSF, 2013. To appear.   \n[16] S. Maffeis and A. Taly. Language-based isolation of untrusted javascript. In CSF, pages 77–91, 2009.  "}