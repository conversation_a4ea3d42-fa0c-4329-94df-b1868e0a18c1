{"text": "# Dependence-Preserving Data Compaction for Scalable Forensic Analysis  \n\nM<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, Stony Brook University https://www.usenix.org/conference/usenixsecurity18/presentation/hossain  \n\nThis paper is included in the Proceedings of the 27th USENIX Security Symposium. August 15–17, 2018 • Baltimore, MD, USA ISBN 978-1-931971-46-1  \n\nOpen access to the Proceedings of the 27th USENIX Security Symposium is sponsored by USENIX.  \n\n# Dependence-Preserving Data Compaction for Scalable Forensic Analysis∗  \n\n<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> {mdn<PERSON><PERSON>n,junawang,sekar,stoller}@cs.stonybrook.edu Stony Brook University, Stony Brook, NY, USA.  \n\n# Abstract  \n\nLarge organizations are increasingly targeted in long-running attack campaigns lasting months or years. When a break-in is eventually discovered, forensic analysis begins. System audit logs provide crucial information that underpins such analysis. Unfortunately, audit data collected over months or years can grow to enormous sizes. Large data size is not only a storage concern: forensic analysis tasks can become very slow when they must sift through billions of records. In this paper, we first present two powerful event reduction techniques that reduce the number of records by a factor of 4.6 to 19 in our experiments. An important benefit of our techniques is that they provably preserve the accuracy of forensic analysis tasks such as backtracking and impact analysis. While providing this guarantee, our techniques reduce on-disk file sizes by an average of $35\\times$ across our data sets. On average, our in-memory dependence graph uses just 5 bytes per event in the original data. Our system is able to consume and analyze nearly a million events per second.  \n\n# 1 Introduction  \n\nMany large organizations are targets of stealthy, long-term, multi-step cyber-attacks called Advanced Persistent Threats (APTs). The perpetrators of these attacks remain below the radar for long periods, while exploring the organization’s IT infrastructure and exfiltrating or compromising sensitive data. When the attack is ultimately discovered, a forensic analysis is initiated to identify the entry points of the attack and its system-wide impact. The spate of APTs in recent years has fueled research on efficient collection and forensic analysis of system logs [13, 14, 15, 9, 16, 17, 18, 22, 42, 30, 10].  \n\nAccurate forensic analysis requires logging of system activity across the enterprise. Logs should be detailed enough to track dependencies between events occurring on different hosts and at different times, and hence needs to capture all information-flow causing operations such as network/file accesses and program executions. There are three main options for collecting such logs: (1) instrumenting individual applications, (2) instrumenting the operating system (OS), or (3) using network capture techniques. The rapid increase in encrypted traffic has greatly reduced the effectiveness of network-capture based forensic analysis. In contrast,  \n\nOS-layer logging is unaffected by encryption. Moreover, OS-layer logging can track the activities of all processes on a host, including any malware that may be installed by the attackers. In contrast, application-layer logs are limited to a handful of benign applications (e.g., network servers) that contain the instrumentation for detailed logging. For these reasons, we rely on OS-based logging, e.g., the Linux audit and Windows ETW (Event Tracing for Windows) systems.  \n\n# 1.1 Log Reduction  \n\nAPT campaigns can last for many months. With existing systems, such as Linux auditing and Windows ETW, our experience as well as that of previous researchers [42] is that the volume of audit data is in the range of gigabytes per host per day. Across an enterprise with thousands of hosts, total storage requirements can easily go up to the petabyte range in a year. This has motivated a number of research efforts on reducing log size.  \n\nSince the vast majority of I/O operations are reads, ProTracer’s [22] reduction strategy is to log only the writes. In-memory tracking is used to capture the effect of read operations. Specifically, when a process performs a read, it acquires a taint identifier that captures the file, network or IPC object read. If the process reads $n$ files, then its taint set can be of size $O(n)$ . Write operations are logged, together with the taint set of the process at that point. This means that write records can, in general, be of size $O(n)$ and hence a process performing $m$ writes can produce a log of size $O(m n)$ . This contrasts with the $O(m+n)$ log size that would result with traditional OS-level logging of both reads and writes. Thus, for ProTracer’s strategy to reduce log size, it is necessary to narrow the size of taint sets of write operations to be close to 1. They achieve this using a fine-grained taint-tracking technique called unit-based execution partitioning [17], where a unit corresponds to a loop iteration. MPI [21] proposes a new form of execution partitioning, based on annotated data structures instead of loops. However, fine-grained taint-tracking via execution partitioning would be difficult to deploy on the scale of a large enterprise running hundreds of applications or more. Without fine-grained taint-tracking, the analysis above, as well as our experiments, indicate that this strategy of “alternating tainting with logging” leads to substantial increases in log size.  \n\nLogGC [18] develops a “garbage collection” strategy, which identifies and removes operations that have no persistent effect. For instance, applications often create temporary files that they subsequently delete. Unless these files are accessed by other processes, they don’t introduce any new dependencies and hence aren’t useful for forensic analysis. However, some temporary files do introduce dependencies, e.g., malware code that is downloaded, executed and subsequently removed by another attack script. Operations on such files need to be logged, so LogGC introduces a notion of exclusive ownership of files by processes, and omits only the operations on exclusively owned files. Although they report major reductions in log size using this technique, this reduction is realized only in the presence of the unit instrumentation [17] described above. If only OS-layer logging is available, which is the common case, LogGC does not produce significant reductions. (See the “Basic GC” column in Table 5 of [18].)  \n\n![](/tmp/output/41_20250325210069/images/b9c51f11f8267dae941fc946c93eaecc450d1397c40d2954527e5d429c0a07e3.jpg)  \nFig. 1: An example (time-stamped) dependence graph.  \n\nWhile LogGC removes all events on a limited class of objects, Xu et al [42] explore a complementary strategy that can remove some (repeated) events on any object. To this end, they developed the concept of trackability equivalence of events in the audit log, and proved that, among a set of equivalent events, all but one can be removed without affecting forensic analysis results. Across a collection of several tens of Linux and Windows hosts, their technique achieved about a $2\\times$ reduction in log size. This is impressive, considering that it was achieved without any application-specific optimizations.  \n\nWhile trackability equivalence [42] provides a sufficient basis for eliminating events, we show that it is far too strict, limiting reductions in many common scenarios, e.g., communication via pipes. The central reason is that trackability is based entirely on a local examination of edges incident on a single node in the dependence graph, without taking into account any global graph properties. In contrast, we develop a more general formulation of dependence preservation that can leverage global graph properties. It achieves 3 to 5 times as much reduction as Xu et al.’s technique.  \n\n• In Section 3, we formulate dependence-preserving log reduction in terms of reachability preservation in the dependence graph. As in previous works (e.g., [13, 42]), nodes in our dependence graph represent objects (files, sockets and IPCs) and subjects (processes), while edges represent operations (also called events) such as read, write, load, and execute. Edges are timestamped and are oriented in the direction of information flow. We say that a node $\\nu$ depends on node $u$ if there is a (directed) path from $u$ to $\\nu$ with non-decreasing edge timestamps. In Fig. $1,P$ denotes a process that connects to a.com, and downloads and saves a file $C$ . It also connects to b.com, and writes to a log file $L$ and a pipe $E$ . Process $Q$ reads from the pipe and also writes to the same log file. Based on timestamps, we can say that $C$ depends on a.com but not b.com.  \n\n![](/tmp/output/41_20250325210069/images/1edfaeafc89babdd62cb41cb21ed322c3f18afb1dc202fae47945de58f567f00.jpg)  \nFig. 2: Dependence graph resulting after our FD log reduction. SD reduction will additionally remove the edge from $Q$ to $L$ . In this reduced graph, dependence can be determined using standard graph reachability. Edge timestamps are dropped, but nodes may be annotated with a timestamp.  \n\nBased on this formulation, we present two novel dependency preserving reductions, called full dependence preservation $(F D)$ and source dependence preservation $(S D)$ . We prove that FD preserves the results of backward as well as forward forensic analysis. We also prove that SD preserves the results of the most commonly used forensic analysis, which consists of running first a backward analysis to find the attacker’s entry points, and then a forward analysis from these entry points to identify the full impact of the attack.  \n\nOur experimental evaluation used multiple data sets, including logs collected from (a) our laboratory servers, and (b) a red team evaluation carried out in DARPA’s Transparent Computing program. On this data, FD achieved an average of $7\\times$ reduction in the number of events, while SD achieved a $\\ {\\bf9.2\\times}$ reduction. In comparison, Xu et al.’s algorithm [42], which we reimplemented, achieved only a $1.8\\times$ reduction. For the example in Fig. 1, our technique combines all edges between the same pair of nodes, leading to the graph shown in Fig. 2, while $\\mathrm{Xu}$ et al’s technique is able to combine only the two edges with timestamps 1 and 2.  \n\n# 1.2 Efficient Computation of Reductions  \n\nOur log reductions (FD and SD) rely on global properties of graphs such as reachability. Such global properties are expensive to compute, taking time that is linear in the size of the (very large) dependence graph. Moreover, due to the use of timestamped edges, reachability changes over time, and hence the results cannot be computed once and cached for subsequent use.  \n\nTo overcome these computational challenges posed by timestamped graphs, we show in Section 4 how to transform them into standard graphs. Fig. 2 illustrates the result of this conversion followed by our FD reduction. Note how the edge timestamps have been eliminated. Moreover, $P$ has been split into two versions connected by a dashed edge, with each version superscripted with its timestamp. Note the absence of a path from a.com to $C$ , correctly capturing the reachability information in the timestamped graph in Fig. 1.  \n\nVersioning has been previously studied in file system and provenance research [31, 26, 25]. In these contexts, versioning systems typically intervene to create file versions that provide increased recoverability or reproducibility. Provenance capture systems may additionally intervene to break cyclic dependencies [24, 25], since cyclic provenance is generally considered meaningless.  \n\nIn our forensic setting, we cannot intervene, but can only observe events. Given a timestamped event log, we need to make sound inferences about dependencies of subjects as well as objects. We then encode these dependencies into a standard graph in order to speed up our reduction algorithms. The key challenge in this context is to minimize the size of the standard graph without dropping any existing dependency, or introducing a spurious one. Specifically, the research described in Section 4 makes the following contributions:  \n\nEfficient reduction algorithms. By working with standard graphs, we achieve algorithms that typically take constant time per event. In our experiments, we were able to process close to a million events per second on a singlecore on a typical laptop computer.   \n• Minimizing the number of versions. We present several optimization techniques in Section 4.2 to reduce the number of versions. Whereas naive version generation leads to an explosion in the number of versions, our optimizations are very effective, bringing down the average number of versions per object and subject to about 1.3. Fig. 2 illustrates a few common cases where we achieve substantial reductions by combining many similar operations: – multiple reads from the same network connection (a.com, b.com) interleaved with multiple writes to files ( $C$ and $L$ ), – series of writes to and reads from pipes $(E)$ , and – series of writes to log files by multiple processes $(L)$ .   \n• Avoiding spurious dependencies. While it is important to reduce the space overhead of versions, this should not come at the cost of inaccurate forensic analysis. We therefore establish formally that results of forensic analysis (specifically, forward and backward analyses) are fully preserved by our reduction.   \n• Optimality. We show that edges and versions retained by our reduction algorithm cannot be removed without introducing spurious dependencies.  \n\nAn interesting aspect of our work is that we use versioning to reduce storage and runtime, whereas versioning is normally viewed as a performance cost to be paid for better recoverability or reproducibility.  \n\n# 1.3 Compact Graph and Log Representations  \n\nA commonly suggested approach for forensic analysis is to store the dependence graph in a graph database. The database’s query capabilities can then be used to perform backward or forward searches, or any other custom forensic analysis. Graph databases such as OrientDB, Neo4j and Titan are designed to provide efficient support for graph queries, but experience suggests that their performance degrades dramatically on graphs that are large relative to main memory. For instance, a performance evaluation study on graph databases [23] found that they are unable to complete simple tasks, such as finding shortest paths on graphs with 128M edges, even when running on a computer with 256GB main memory and sufficient disk storage. Log reduction techniques can help, but may not be sufficient on their own: our largest dataset, representing just one week of data, already contains over 70M edges. Over the span of an APT (many months or a year), graph sizes can approach a billion edges even after log reduction. We therefore develop a compact inmemory representation for our versioned dependence graphs.  \n\n• Section 5.2 describes our approach for realizing a compact dependence graph representation. By combining our log reduction techniques with compact representations, our system achieves very high density: it uses about 2 bytes of main memory per event on our largest data set. This dataset, with 72M edges, is comparable in size to the 128M edges used in the graph database evaluation [23] mentioned above. Yet, our memory utilization was just 111MB, in comparison with the 256GB available in that study.  \n\n• We also describe the generation of compact event logs based on our event reduction techniques (Section 5.1). We began with a space-efficient log format that was about $\\mathbf{8}\\times$ smaller than a Linux audit log containing roughly the same information. With FD reduction, it became $\\ {\\pmb{35.3\\times}}$ smaller, while SD increased the reduction factor to about $41.4\\times$ . These numbers are before the application of any data compression techniques such as gzip, which can provide further reductions.  \n\n# 1.4 Paper Organization  \n\nWe begin with some background on forensic analysis in Section 2. The formulation of dependence-preserving reductions, together with our FD and SD techniques, are presented in Section 3. Efficient algorithms for achieving FD and SD are described in Section 4, together with a treatment of correctness and optimality. Section 5 summarizes a compact main-memory dependence graph and offline event log formats based on our event reductions. Implementation and experimental evaluation are described in Section 6, followed by related work discussion in Section 7 and concluding remarks in Section 8.  \n\n# 2 Background  \n\nDependence graphs. System logs refer to two kinds of entities: subjects and objects. Subjects are processes, while objects correspond to passive entities such as files, network connections and so on. Entries in the log correspond to events, which represent actions (typically, system calls) performed by subjects, e.g., read, write, and execute.  \n\nIn most work on forensic analysis [13, 15, 42], the log contents are interpreted as a dependence graph: nodes in the graph correspond to entities, while edges correspond to events. Edges are oriented in the direction of information flow and have timestamps. When multiple instances of an event are aggregated into a single instance, its timestamp becomes the interval between the first and last instances. Fig. 1 shows a sample dependence graph, with circles denoting subjects, and the other shapes denoting objects. Among objects, network connections are indicated by a diamond, files by ovals, and pipes by rectangles. Edges are timestamped, but their names omitted. Implicitly, in-edges of subjects denote reads, and out-edges of subjects denote writes.  \n\nBackward and Forward Analysis. Forensic analysis is concerned with the questions of what, when and how. The what question concerns the origin of a suspected attack, and the entities that have been impacted during an attack. The origin can be identified using backward analysis, starting from an entity flagged as suspicious, and tracing backward in the graph. This analysis, first proposed in BackTracker [13], uses event timestamps to focus on paths in dependence graphs that represent causal chains of events. A backward analysis from file $C$ at time 5 will identify $P$ and a.com. Of these, a.com is a source node, i.e., an object with no parent nodes, and hence identified as the likely entry point of any attack on $C$ .  \n\nAlthough b.com is backward reachable from $C$ in the standard graph-theoretic sense, it is excluded because the path from b.com to $C$ does not always go forward in time.  \n\nThe set of entities impacted by the attack can be found using forward analysis [43, 1, 15] (a.k.a. impact analysis), typically starting from an entry point identified by backward analysis. In the sample dependence graph, forward analysis from network connection a.com will reach all nodes in the graph, while a forward analysis from b.com will leave out $C$  \n\nThe when question asks when each step in the attack occurred. Its answer is based on the timestamps of edges in the subgraph computed by forward and backward analyses. The how question is concerned with understanding the steps in an attack in sufficient detail. To enable this, audit logs need to capture all key operations (e.g., important system calls), together with key arguments such as file names, IP addresses and ports, command-line options to processes, etc.  \n\n# 3 Dependence Preserving Reductions  \n\nWe define a reduction of a time-stamped dependence graph $G$ to be another graph $G^{\\prime}$ that contains the same nodes but a subset of the events. Such a reduction may remove “redundant” events, and/or combine similar events. As a result, some events in $G$ may be dropped in $G^{\\prime}$ , while others may be aggregated into a single event. When events are combined, their timestamps are coalesced into a range that (minimally) covers all of them.  \n\nA log reduction needs to satisfy the following conditions: • it won’t change forensic analysis results, and $\\bullet$ it won’t affect our understanding of the results.  \n\nTo satisfy the second requirement, we apply reductions only to read, write1, and load events. All other events, e.g., fork, execve, remove, rename and chmod, are preserved. Despite being limited to reads, writes and loads, our reduction techniques are very effective in practice, as these events typically constitute over $95\\%$ of total events.  \n\nFor the first requirement, our aim is to preserve the results of forward and backward forensic analysis. We ensure this by preserving forward and backward reachability across the original graph $G$ and the reduced graph $G^{\\prime}$ . We begin by formally defining reachability in these graphs.  \n\n# 3.1 Reachability in time-stamped dependence graphs  \n\nDependence graph $G$ is a pair $(V,E)$ where $V$ denotes the nodes in the graph and $E$ denotes a set of directed edges. Each edge $e$ is associated with a start time start $(e)$ and an end time end $(e)$ . Reachability in this graph is defined as follows:  \n\nDefinition 1 (Causal Path and Reachability) $A$ node $\\nu$ is reachable from another node u if and only if there is (directed) path $e_{1},e_{2},\\ldots,e_{n}$ from u to v such that:  \n\n$$\n\\begin{array}{r}{\\forall1\\leq i<n\\quad s t a r t(e_{i})\\leq e n d(e_{i+1})}\\end{array}\n$$  \n\nWe refer to a path satisfying this condition as a causal path. It captures the intuition that information arriving at a node through event $e_{i}$ can possibly flow out through the event $e_{i+1}$ , i.e., successive events on this path $e_{1},e_{2},\\ldots,e_{n}$ can be causally related. In Fig. 1, the path consisting of edges with timestamps 1,6,8 and 11 is causal, so $L$ is reachable from a.com. In contrast, the path corresponding to the timestamp sequence 4,3 is not causal because the first edge occurs later than the second. Hence $C$ is unreachable from b.com.  \n\nIn forensics, we are interested in reachability of a node at a given time, so we extend the above definition as follows:  \n\n# Definition 2 (Forward/Backward Reachability at t)  \n\nA node v is forward reachable from a node u at time $t$ , denoted $u@t\\longrightarrow\\nu,$ , iff there is a causal path $e_{1},e_{2},\\ldots,e_{n}$ from u to v such that $t\\leq e n d(e_{i})$ for all $i.$ .   \n• A node u is said to be backward reachable from v at time t, denoted $u\\longrightarrow\\nu@t_{;}$ , iff there is a causal path $e_{1},e_{2},\\ldots,e_{n}$ from u to v such that $t\\geq s t a r t(e_{i})$ for all $i.$ . 1There can be many types of read or write events, some used on files,   \nothers used on network sockets, and so on. For example, Linux audit system   \ncan log over a dozen distinct system calls used for input or output of data.   \nFor the purposes of this description, we map them all into reads and writes.  \n\nIntuitively, $u@t\\longrightarrow\\nu$ means u’s state at time $t$ can impact $\\nu$ . Similarly, $u\\longrightarrow\\nu@t$ means $\\nu$ ’s state at $t$ can be caused/explained by $u.$ . In Fig. $1,P@6\\longrightarrow Q$ , but $P@11\\not\\to$ $Q$ . Similarly, $\\mathrm{a.com\\longrightarrow}C\\ @3$ but b.com ${\\mathcal{H}}C@3$ .  \n\nBased on reachability, we present three dependencypreserving reductions: CD, which is close to $\\mathrm{Xu}$ et al’s full trackability, and FD and SD, two new reductions we introduce in this paper.  \n\n# 3.2 Continuous dependence (CD) preservation  \n\nThis reduction aims to preserve forward and backward reachability at every instant of time.  \n\nDefinition 3 (Continuous Dependence Preservation) Let $G$ be a dependence graph and $G^{\\prime}$ be a reduction of G. $G^{\\prime}$ is said to preserve continuous dependence iff forward and backward reachability is identical in both graphs for every pair of nodes at all times.  \n\nIn Fig. 3, S reads from a file $F$ at $t=2$ and $t=4$ , and writes to another file $F^{\\prime}$ at $t=3$ and $t=6$ . Based on the above definition, continuous dependence is preserved when the reads by $S$ are combined, as are the writes, as shown in the lower graph.  \n\n![](/tmp/output/41_20250325210069/images/bcc9fb636cc4eed21420812d5d53294736267e83751f9a510f473e060a655f4c.jpg)  \nFig. 3: Reduction that preserves continuous dependence.  \n\nFig. 4 shows a reduction that does not preserve continuous dependence. In the original graph, $F@3\\not\\rightarrow H$ : the earliest time $F@3$ can affect $S$ is at $t=4$ , and this effect can propagate to $F^{\\prime}@6$ , but by this time, the event from $F^{\\prime}$ to $H$ has already terminated. In contrast, in the reduced graph, $F@3$ affects $H@5$ .  \n\n![](/tmp/output/41_20250325210069/images/4de8bcf7f217f3fd8e712b31e4b16c73d78ec7e0d9f05e564117f1a4a1f5fcc0.jpg)  \nFig. 4: Reduction that violates continuous dependence.  \n\nOur definition of continuous dependence preservation is similar to $\\mathrm{Xu}$ et al.’s definition of full trackability equivalence [42]. However, their definition is a bit stricter, and does not allow the reductions shown in Fig. 3. They would permit those reductions only if node $S$ had (a) no incoming edges between its outgoing edges and (b) no outgoing edges between its incoming edges2.  \n\nTheir stricter definition was likely motivated by efficiency considerations. Specifically, their definition ensures that reduction decisions can be made locally, e.g., by examining the edges incident on S. Thus, their criteria does not permit the combination of reads in either Fig. 3 or Fig. 4, since they share the same local structure at node S. In contrast, our continuous dependence definition is based on the more powerful global reachability properties, and hence can discriminate between the two examples to safely permit the aggregation in Fig. 3 but not Fig. 4. The downside of this power is efficiency, as continuous dependence may need to examine every path in the graph before deciding which edges can be removed.  \n\nAlthough the checking of global properties can be more time-consuming, the resulting reductions can be more powerful (i.e., achieve greater reduction). This is why we devote Section 4 to development of efficient algorithms to check the more powerful global properties used in the two new reductions presented below.  \n\nBecause of the similarity of Xu et al’s full trackability and our continuous dependence, we will henceforth refer to their approach as local continuous dependence (LCD) preservation. We end this discussion with examples of common scenarios where LCD reduction is permitted:  \n\nSequence of reads without intervening writes: When an application reads a file, its read operation results in multiple read system calls, each of which is typically logged as a separate event in the audit log. As long as there are no write operations performed by the application at the same time, LCD will permit the reads to be combined. • Sequence of writes without intervening reads: The explanation in this case mirrors the previous case.  \n\nHowever, if reads and writes are interleaved, then LCD does not permit the reads (or writes) to be combined. In contrast, the FD notion presented below can support reductions in cases where an application is reading from one or more files while writing to one or more files.  \n\n# 3.3 Full Dependence (FD) Preservation  \n\nCD does not permit the reduction in Fig. 4, because it changes whether the state of $F$ at $t=3$ propagates to $H$ . But does this difference really matter in the context of forensic analysis? To answer this question, note that there is no way for $F$ to become compromised at $t=3$ if it was not already compromised before. Indeed, there is no basis for the state of $F$ to change between $t=0$ and $t=6$ because nothing happens to $F$ during this period.  \n\nMore generally, subjects and objects don’t spontaneously become compromised. Instead, compromises happen due to input consumption from a compromised entity, such as a network connection, compromised file, or user3. This observation implies that keeping track of dependencies between entities at times strictly in between events is unnecessary, because nothing relevant changes at those times. Therefore, we focus on preserving dependencies at times when a node could become compromised, namely, when it acquires a new dependency.  \n\nFormally, let $A n c(\\nu,t)$ denote the set of ancestor nodes of $\\nu$ at time $t$ , i.e., they are backward reachable from $\\nu$ at $t$ .  \n\n$$\nA n c(\\nu,t)=\\{u\\mid u\\longrightarrow\\nu@t\\}.\n$$  \n\nLet $N e w A n c(\\nu)$ be the set of times when this set changes, i.e.:  \n\n$$\nN e w A n c(\\nu)=\\{t\\mid\\forall t^{\\prime}<t,A n c(\\nu,t)\\supset A n c(\\nu,t^{\\prime})\\}.\n$$  \n\nWe define $N e w A n c(\\nu)$ to always include $t=0$ .  \n\n# Definition 4 (Full Dependence (FD) Preservation) A  \n\nreduction $G^{\\prime}$ of $G$ is said to preserve full dependence iff for every pair of nodes u and $\\nu$ :  \n\n• forward reachability from $u@t$ to $\\nu$ is preserved for all $t\\in N e w A n c(u)$ , and backward reachability of u from v@t is preserved at all t.  \n\nIn other words, when FD-preserving reductions are applied:  \n\n• the result of backward forensic analysis from any node $\\nu$ will identify the exact same set of nodes before and after the reduction.   \n• the result of forward analysis carried out from any node $u$ will yield the exact same set of nodes, as long as the analysis is carried out at any of the times when there is a basis for $u$ to get compromised.  \n\nTo illustrate the definition, observe that FD preservation allows the reduction in Fig. 4, since (a) backward reachability is unchanged for every node, and (b) $N e w A n c(F)=\\{0\\}$ , and $F@0$ flows into $S$ , $F^{\\prime}$ and $H$ in the original as well as the reduced graphs.  \n\n# 3.4 Source Dependence (SD) Preservation  \n\nWe consider further relaxation of dependence preservation criteria in order to support more aggressive reduction, based on the following observation about the typical way forensic analysis is applied. An analyst typically flags an entity as being suspicious, then performs a backward analysis to identify likely root causes. Root causes are source nodes in the graph, i.e., nodes without incoming edges. Source nodes represent network connections, preexisting files, processes started before the audit subsystem, pluggable media devices, and user (e.g., terminal) input. Then, the analyst performs an impact (i.e., forward) analysis from these source nodes. To carry out this task accurately, we need to preserve only information flows from source nodes; preserving dependencies between all pairs of internal nodes is unnecessary.  \n\nDefinition 5 (Source Dependence (SD) Preservation) A reduction $G^{\\prime}$ of $G$ is said to preserve source dependence iff for every node $\\nu$ and a source node $u$ :  \n\n• forward reachability from $u@0$ to $\\nu$ is preserved, and • backward reachability of u from $\\nu@t$ is preserved at all t.  \n\nNote that SD coincides with FD applied to source nodes. The second conditions are, in fact, identical. The first conditions coincide as well, when we take into account that $N e w A n c(u)=\\{0\\}$ for any source node $u$ . (A source node does not have any ancestors, but since we have defined NewAnc to always include zero, NewAnc of source nodes is always $\\{0\\}$ .)  \n\nFig. 5 shows a reduction that preserves SD but not FD. In the figure, $F$ and $F^{\\prime}$ are two distinct files, while $S,S^{\\prime}$ and $S^{\\prime\\prime}$ denote three distinct processes. Note that FD isn’t preserved because a new flow arrives at $S^{\\prime}$ at $t=2$ , and this flow can reach $F^{\\prime}$ in the original graph but not in the reduced graph. However, SD is preserved because the reachability of $S,S^{\\prime}$ , $S^{\\prime\\prime}$ and $F^{\\prime}$ from the source node $F$ is unchanged.  \n\n![](/tmp/output/41_20250325210069/images/2a37b52c7b692a6f6345a528a005deed4a498bedbd4a7d0bac3b213bccda09a1.jpg)  \nFig. 5: Source dependence preserving reduction.  \n\nNote that the first condition in Defn. 5 is redundant, as it is implied by the second: If $u$ is backward reachable from a node $\\nu$ at $t$ , then, by definition of backward reachability, there exists a causal path from $e_{1},e_{2},\\ldots,e_{n}$ from $u$ to $\\nu$ . Since 0 is the smallest possible timestamp, $0\\leq e n d(e_{i})$ for all $i,$ , and hence, using the causal path $e_{1},e_{2},\\ldots,e_{n}$ and the first part of Defn. 2, we conclude $u@0\\longrightarrow\\nu$ , thus satisfying the first condition. We also point out that the first condition does not imply the second. To see this, note that if we only need to preserve forward reachability from $F@0$ in Fig. 5, then we can drop any two of the three edges coming into $F^{\\prime}$ . However, the backward reachability condition limits us to dropping the edges from $S^{\\prime}$ and $S^{\\prime\\prime}$ , as we would otherwise change backward reachability of the source node $F$ from $F^{\\prime}@4$ .  \n\nDespite being unnecessary, we kept the first condition in Defn. 5 because its presence makes the forensic analysis preservation properties of SD more explicit. (Unlike Defn. 5, there is no redundancy in Defn. 4.)  \n\n# 4 Efficient Computation of Reductions  \n\nFull dependence and source dependence reductions rely on global properties of graph reachability. Such global properties are expensive to compute, taking time that can be linear in the size of the (very large) dependence graph. Moreover, due to the use of timestamped edges, reachability changes over time and hence must be computed many times. This mutability also means that results cannot be computed once and cached for subsequent use, unlike standard graphs, where we can determine once that $\\nu$ is a descendant of $u$ and reuse this result in the future.  \n\nTo overcome these computational challenges posed by timestamped graph, we show how to transform them into standard graphs. The basic idea is to construct a graph in which objects as well as subjects are versioned. Versioning is widely used in many domains, including software configuration management, concurrency control, file systems [31, 26] and provenance [25, 24, 4, 29]. In these domains, versioning systems typically intervene to create file versions, with the goal of increased recoverability or reproducibility. In contrast, we operate in a forensic setting, where we can only observe the order in which objects (as well as subjects) were accessed. Our goal is to (a) make sound inferences about dependencies through these observations, and (b) encode these dependencies in a standard (rather than time-stamped) graph. This encoding serves as the basis for developing efficient algorithms for log reduction. Specifically, this section addresses the following key problems.  \n\n• Formally establishing that versioned graphs produce the same forensic analysis results as timestamped graphs. • Developing a suite of optimizations that reduce the number of versions while preserving dependencies. • Showing that our algorithms generate the optimal number of versions while preserving FD or SD.  \n\nUsing versioning, we realize algorithms that are both faster and use less storage than their unversioned counterparts. Specifically, we realize substantial reduction in the size of the dependence graph by relying on versioning. Runtime is also reduced because the reduction operations typically take constant time per edge (See Section 6.6.1). In contrast, a direct application of Defn. 4 on timestamped graphs would be unacceptably slow4.  \n\n# 4.1 Naive Versioned Dependence Graphs  \n\nThe simplest approach for versioning is to create a new version of a node whenever it gets a new incoming edge, similar to creating a new file version each time the file is written. Fig. 6 shows an example of an unversioned graph and its corresponding naive versioned graph. Versions of a node are stacked vertically in the example so as to make it easier to see the correspondence between nodes in the timestamped and versioned graphs.  \n\nNote that timestamps in versioned graphs are associated with nodes (versions), not with edges. A version’s start time is the start time of the event that caused its creation. We show this time using a superscript on the node label.  \n\n![](/tmp/output/41_20250325210069/images/ecbfdacaf519832965c4b55888ef441b7ea97a3579fda1bf3f1a000cfc86f8a6.jpg)  \nFig. 6: A timestamped graph and equivalent naive versioned graph.  \n\n# 4.1.1 Algorithm for naive versioned graph construction  \n\nWe treat the contents of the audit log as a timestamped graph $G=(V,E_{T})$ . The subscript $T$ on $E$ is a reminder that the edges are timestamped. The corresponding (naive) versioned graph $\\mathbf{G}=\\left(\\mathbf{V},\\mathbf{E}\\right)$ is constructed using the algorithm shown below. Without loss of generality, we assume that every edge in the audit log has a unique timestamp and/or sequence number. We denote a directed edge from $u$ to $\\nu$ with timestamp $t$ as a triple $(u,\\nu,t)$ . Let $u^{<t}$ denote the latest version of $u$ in the versioned graph before $t$ .  \n\n<html><body><table><tr><td>1. BuildVer(V,Er) 2. V= {v|v∈ V}; E={}; 3. for each (u,v,t) ∈ Er 4. add v toV 5. add u<t,f) toE 6. add toE 7. return (V,E)</td></tr></table></body></html>  \n\nWe intend BuildVer and its optimized versions to be online algorithms, i.e., they need to examine edges one-at-a-time, and decide immediately whether to create a new version, or to add a new edge. These constraints are motivated by our application in real-time attack detection and forensic analysis.  \n\nFor each entity $\\nu.$ , an initial version $\\nu^{0}$ is added to the graph at line 2.5 The for-loop processes log entries (edges) in the order of increasing timestamps. For an edge $(u,\\nu)$ with timestamp $t$ , a new version $\\nu^{t}$ of the target node $\\nu$ is added to the graph at line 4. Then an edge is created from the latest version of $u$ to this new node (line 5), and another edge created to link the last version of $\\nu$ to this new version (line 6).  \n\n# 4.1.2 Forensic analysis on versioned graphs  \n\nIn a naive versioned graph, each object and subject gets split into many versions, with each version corresponding to the time period between two consecutive incoming edges to that entity in the unversioned graph. To flag an entity $\\nu$ as suspicious at time $t$ , the analyst marks the latest version v≤t of $\\nu$ at or before $t$ as suspicious. Then the analyst can use standard graph reachability in the versioned graph to perform backward and forward analysis. For the theorem and proof, we use the notation $\\nu^{<\\infty}$ to refer to the latest version of $\\nu$ so far. In addition, we make the following observation that readily follows from the description of BuildVer.  \n\nObservation 6 For any two node versions $u^{t}$ and $u^{s}$ , there is a path from $u^{t}$ to us if and only if $s\\geq t$ .  \n\nTheorem 7 Let $\\mathbf{G}=\\left(\\mathbf{V},\\mathbf{E}\\right)$ be the versioned graph constructed from $G=(V,E_{T})$ . For all nodes $u,\\nu$ and times $t$ :  \n\nv is forward reachable from $u@t$ iff there is a simple path in G from $u^{\\le t}1\\sigma\\nu^{<\\infty}$ ; and   \nu is backward reachable from $\\nu@t$ iff there is a path in G from $u^{0}\\mathrm{~}t o\\mathrm{~}\\nu^{\\le t}$ .  \n\nProof: For uniformity of notation in the proof, let $t=t_{0},u=w_{0}$ and $\\nu=w_{n}$ . The definition of reachability in timestamped graphs (specifically, Definitions 1 and 2), when limited to instantaneous events, states that $w_{0}@t\\longrightarrow w_{n}$ holds in $G$ if and only if there is a path  \n\n$$\n\\big(w_{0},w_{1},t_{1}\\big),\\big(w_{1},w_{2},t_{2}\\big),\\dots,\\big(w_{n-1},w_{n},t_{n}\\big)\n$$  \n\nin $G$ such that $t_{i-1}\\leq t_{i}$ for $1\\leq i\\leq n$ . For each timestamped edge $\\left(w_{i-1},w_{i},t_{i}\\right)$ , BuildVer adds a (standard) edge $(w_{i-1}^{<t_{i}},w_{i}^{t_{i}})$ to $\\mathbf{G}$ . In addition, by Observation 6, there is a path from $w_{i}^{t_{i}}$ to wi<ti+1. Putting these edges and paths together, we can construct a path in $\\mathbf{G}$ from $w_{0}^{<t_{0}}$ to wtn . Also, by Observation 6, there is a path from $w_{n}^{t_{n}}$ to $w_{n}^{<\\infty}$ . Putting all these pieces together, we have a path from $w_{0}^{<t_{0}}=u^{<t_{0}}$ to $w_{n}^{<\\infty}=\\nu^{<\\infty}$ . A path from $u^{<t_{0}}$ to $\\nu^{<\\infty}$ clearly implies a path from $u^{\\le t_{0}}$ to $\\nu^{<\\infty}$ , thus satisfying the “only if” part of the forward reachability condition.  \n\nNote that the “only if” proof constructed a one-to-one correspondence between the paths in $G$ and $\\mathbf{G}$ . This correspondence can be used to establish the “if” part of the forward reachability condition as well.  \n\nThe proof of the backward reachability condition follows the same steps as the proof of forward reachability, so we omit the details.  \n\n# 4.2 Optimized Versioning and FD Preservation  \n\nNaive versioning is simple but offers no benefits in terms of data reduction. In fact, it increases storage requirements. In this section, we introduce several optimizations that reduce the number of versions and edges. These optimizations cause node timestamps to expand to an interval. A node $\\nu$ with timestamp interval $[t,s]$ will be denoted $\\nu^{t,s}$ .  \n\n![](/tmp/output/41_20250325210069/images/ed1282028e5c35f2b608830e6b7d9746e0647dca75a3715a886d87dccd8ec000.jpg)  \nFig. 7: The naive versioned graph from Fig. 6 (top), and the result of applying redundant edge optimization (REO) (middle) and then redundant node optimization (RNO) (bottom) to it. When adding the edge $(S,G,5)$ , we find that there is already an edge from the latest version $S^{2}$ of $S$ to $G$ , so we skip this edge. For the same reason, the edge $(G,T,6)$ can be skipped, and this results in the graph shown in the middle. For the bottom graph, note that when adding the edge $(F,S,2)$ , $S$ has no descendants, so we simply update $S^{0}$ by $S^{0,2}$ , and avoid the generation of a new version. For the same reason, we can update $G^{0}$ and $T^{0}$ as well, resulting in the graph at the bottom.  \n\n# 4.2.1 Redundant edge optimization (REO)  \n\nBefore adding a new edge between $u$ and $\\nu$ , we check if there is already an edge from the latest version of $u$ to some version of $\\nu.$ . In this case, the new edge is redundant: in particular, reachability is unaffected by the addition of the edge, so we discard the edge. This also means that no new version of $\\nu$ is generated. Specifically, consider the addition of an event $(u,\\nu,t)$ to the graph. Let $u^{r,s}$ be the latest version of $u$ . We check if there is already an edge from $u^{r,s}$ to an existing version of $\\nu$ . If so, we simply discard this event. We leave the node timestamp unchanged. Thus, for a node $u^{r,s}\\in\\mathbf{G}$ , $r$ represents the timestamp of the first edge coming into this node, while $s$ represents the timestamp of the last. Alternatively, $r$ denotes the start time of this version, while $s$ denotes the last time it acquired a new incoming edge (i.e., an edge that wasn’t eliminated by a reduction operation). Fig. 7 illustrates redundant edge (REO) optimization.  \n\n# 4.2.2 Global Redundant Edge Optimization $(R E O^{*})$  \n\nWith REO, we check whether there is already a direct edge from $u$ to $\\nu$ before deciding to add a new edge. With global redundant edge, we generalize to check whether $u$ is an ancestor of $\\nu.$ Specifically, before adding an event $(u,\\nu,t)$ to the graph, we check whether the latest version of $u$ is already an ancestor of the latest version of $\\nu$ . If so, we simply discard the event.  \n\nThe condition in $\\mathrm{REO^{*}}$ optimization is more expensive to check: it may take time linear in the size of the graph. Also, it did not lead to any significant improvement over REO in our experiments, so we did not evaluate it in detail. However, it is of conceptual significance because the resulting graph is optimal with respect to FD, i.e., any further reduction would violate FD-preservation.  \n\n# 4.2.3 Redundant node optimization (RNO)  \n\nThe goal of this optimization is to avoid generating additional versions if they aren’t necessary for preserving dependence. We create a new version $\\nu^{s}$ of a vertex because, in general, the descendants of $\\nu^{s}$ could be different from those of $\\nu^{l}$ , the latest version of $\\nu$ so far. If we overzealously combine $\\nu^{l}$ and $\\nu^{s}$ , then a false dependency will be introduced, e.g., a descendant of $\\nu^{l}$ may backtrack to a node that is an ancestor of $\\nu^{s}$ but not $\\nu^{l}$ . This possibility exists as long as (a) the ancestors of $\\nu^{l}$ and $\\nu^{s}$ aren’t identical, and (b) $\\nu^{l}$ has non-zero number of descendants. We already considered (a) in designing REO optimizations described above, so we consider (b) here. Note that RNO needs to be checked only on edges that aren’t eliminated by REO (or $\\mathrm{REO^{*}}$ ).  \n\nSpecifically, let $\\nu^{r,s}$ be the latest version of $\\nu$ so far. Before creating a new version of $\\nu$ due to an event at time $t$ , we check whether $\\nu^{r,s}$ has any outgoing edge (i.e., any descendants). If not, we replace $\\nu^{r,s}$ with $\\nu^{r,t}$ , instead of creating a new version of $\\nu.$ . Fig. 7 illustrates the result of applying this optimization.  \n\nRNO preserves dependence for descendants of $\\nu$ , but it can change backward reachability of the node $\\nu$ itself. For instance, consider the addition of an edge at time $t$ from $u^{p,q}$ to $\\nu^{r,s}$ . This edge is being added because it is not redundant, i.e., a backward search from $\\nu@s$ does not reach $u^{p,q}$ . However, when we add the new edge and update the timestamp to $\\nu^{r,t}$ , there is now a backward path from $\\nu@s$ to $u^{p,q}$ . The simplest solution is to retain the edge timestamp on edges added with RNO, and use them to prune out false dependencies.6  \n\n# 4.2.4 Cycle-Collapsing Optimization (CCO)  \n\nOccasionally, cyclic dependencies are observed, e.g., a process that writes to and reads from the same file, or two processes that have bidirectional communication. As observed by previous researchers [25, 24], such dependencies can lead to an explosion in the number of versions. The typical approach is to detect cycles, and treat the nodes involved as an equivalence class. A simple way to implement this approach is as follows. Before adding an edge from a version $u^{r}$ to $\\nu^{s}$ , we check if there is a cycle involving $u$ and $\\nu$ . If so, we simply discard the edge. Our experimental results show that cycle detection has a dramatic effect on some data sets.  \n\nCycle detection can take time linear in the size of the graph. Since the dependence graph is very large, it is expensive to run full cycle detection before the addition of each edge. Instead, our implementation only checks for cycles involving two entities. We found that this was enough to address most sources of version explosion. An alternative would be to search for larger cycles when a spurt in version creation is observed.  \n\n# 4.2.5 Effectiveness of FD-optimizations  \n\nREO and RNO optimizations avoid new versions in most common scenarios that lead to an explosion of versions with naive versioning:  \n\n• Output files: Typically, these files are written by a single subject, and not read until the writes are completed. Since all the write operations are performed by one subject, REO avoids creating multiple versions. In addition, all the write operations are combined.   \n• Log files: Typically, log files are written by multiple subjects, but are rarely read, and hence by RNO, no new versions need to be created.   \nPipes: Pipes are typically written by one subject and read by another. Since the set of writers does not change, a single version is sufficient, as a result of REO. Moreover, all the writes on the pipe can be combined into one operation, and so can all the reads.  \n\nWe found that most savings were obtained by REO, RNO, and CCO. As mentioned above, $\\mathrm{REO^{*}}$ is more significantly more expensive than REO and provided little additional benefit. Another undesirable aspect of $\\mathrm{REO^{*}}$ (as well as the SD optimization) is that it may change the paths generated during a backward or forward analysis. Such changes have the potential to make attack interpretation more difficult. In contrast, REO, RNO and CCO preserve all cycle-free paths.  \n\n# 4.2.6 Correctness and Optimality  \n\nTheorem 8 BuildVer, together with RNO and REO\\* optimizations, preserves full dependence $(F D)$ .  \n\nProof: We already showed that BuildVer preserves forward and backward reachability between the timestamped graph $G$ and the naive versioned graph G. Hence it suffices to show that the edges and nodes eliminated by $\\mathrm{REO^{*}}$ and RNO don’t change forward and backward reachability in G. Now, REO\\* optimization drops an edge $(u,\\nu,t)$ only if there is already an edge from the latest version of $u$ to the latest or a previous version of $\\nu$ in G. In other words, no new ancestors will result from adding this edge. Since no new ancestors are added, by definition of FD, any additional paths created in the original graph due to the addition of this edge do not have to be preserved. Thus $\\mathrm{{REO}^{\\ast}}$ optimization satisfies the forward reachability condition of FD. Moreover, since this edge does not add new ancestors to $\\nu.$ , it won’t change backward reachability of any node from $\\nu$ or its descendants. Thus, the backward reachability preservation condition of FD is also satisfied.  \n\nRegarding RNO optimization, note that it is applied only when a node $\\nu$ has no descendants. In such a case, preservation of backward and forward reachability from v’s descendants holds vacuously.  \n\nOptimality with respect to FD. We now show that the combination of $\\mathrm{{REO}^{\\ast}}$ and RNO optimizations results in reductions that are optimal with respect to FD preservation. This means that any algorithm that drops versions or edges retained by this combination does not preserve full dependence. In contrast, this combination preserves FD.  \n\nThe main reasoning behind optimality is that $\\mathrm{{REO}^{\\ast}}$ creates a new version of an entity $\\nu$ whenever it acquires a new dependency from another entity $u$ . In particular, $\\mathrm{{REO}^{\\ast}}$ adds an edge from (the latest version of) $u$ to (the latest version of) $\\nu$ only when there is no existing path between them. In other words, this edge corresponds to a time instance when $\\nu$ acquires a new ancestor $u$ . For this reason, reachability from $u$ to $\\nu$ needs to be captured at this time instance for FD preservation. Thus, an algorithm that omits this edge would not preserve FD. On the other hand, if we create an edge but not a new version of $\\nu$ , then there will be a single instance of $\\nu$ in the versioned graph that represents two distinct dependencies. In particular, there will be a path from $u^{t}$ to $\\nu^{s}$ , the version of $\\nu$ that existed before the time $t$ of the current event. As a result, $u^{t}$ would incorrectly be included in a backward analysis result starting at the descendants of $\\nu^{s}$ The only way to avoid this error is if $\\nu^{s}$ had no descendants, the condition specified in RNO. Thus, if either $\\mathrm{{REO}^{\\ast}}$ or RNO optimizations were violated, then, forensic analysis of the versioned graph will yield incorrect results.  \n\n# 4.3 Source Dependence Preservation  \n\nIn this section, we show how to realize source-dependence preserving reduction. Recall that a source is an entity that has no incoming edges. With this definition, sources consist primarily of pre-existing files and network endpoints; subjects (processes) are created by parents and hence are not sources, except for the very first subject. While this is the default definition, broader definitions of source can easily be used, if an analyst considers other nodes to be possible sources of compromise.  \n\nWe use a direct approach to construct a versioned graph that preserves SD. Specifically, for each node $\\nu$ , we maintain a set $S r c(\\nu)$ of source entities that $\\nu$ depends on. This set is initialized to $\\{\\nu\\}$ for source nodes. Before adding an event $(u,\\nu,t)$ to the graph, we check whether $S r c(u)\\subseteq S r c(\\nu)$ If so, all sources that can reach $u$ are already backward reachable sources of $\\nu$ , so the event can simply be discarded. Otherwise, we add the edge, and update $S r c(\\nu)$ to include all elements of $S r c(u)$ .  \n\nAlthough the sets $S r c(\\nu)$ can get large, note that they need to be maintained only for active subjects and objects. For example, the source set for a process is discarded when it exits. Similarly, the source set for a network connection can be discarded when it is closed.  \n\nTo save space, we can limit the size of $S r c$ . When the size limit is exceeded for a node $\\nu$ , we treat $\\nu$ as having an unknown set of additional ancestors beyond $S r c(\\nu)$ . This ensures soundness, i.e., that our reduction never drops an edge that can add a new source dependence. However, size limits can cause some optimizations to be missed. In order to minimize the impact of such misses, we first apply REO, RNO and CCO optimizations, and skip the edges and/or versions skipped by these optimizations. Only when they determine an edge to be new, we apply the SD check based on Src sets.  \n\nTheorem 9 BuildVer, together with redundant edge and redundant node optimizations and the source dependence optimization, preserves source dependence.  \n\nProof: Since full dependence preservation implies source dependence preservation, it is clear that redundant edge and redundant node optimizations preserve source dependence, so we only need to consider the effects of source dependence optimization. The proof is by induction on the number of iterations of the loop that processes events. The induction hypothesis is that, after $k$ iterations, (a) $S r c(\\nu)$ contains exactly the source nodes that are ancestors of $\\nu$ , and (b) that SD has been preserved so far. Now, in the induction step, note that the algorithm will either add an edge $(u,\\nu)$ and update $S r c(\\nu)$ to include all of $S r c(u)$ , or, discard the event because $S r c(\\nu)$ already contains all elements of $S r c(u)$ . In either case, we can show from induction hypothesis that $S r c(\\nu)$ correctly captures all source nodes backward reachable from $\\nu$ . It is also clear that that when the edge is discarded by the SD algorithm, it is because the edge does not change the sources that are backward reachable, and hence it is safe to drop the edge.  \n\nOptimality of SD algorithm. Note that when SD adds an edge $(u,\\nu)$ , that is because $S r c(u)$ includes at least one source that is not in $S r c(\\nu)$ . Clearly, if we fail to add this edge, then source dependence of $\\nu$ is no longer preserved. This implies that the above algorithm for SD preservation is optimal.  \n\n# 5 Compact Representations  \n\nIn this section, we describe how to use the techniques described so far, together with others, to achieve highly compact log file and main-memory dependence graph representations.  \n\n# 5.1 Compact Representation of Reduced Logs  \n\nAfter reduction, logs can be stored in their original format, e.g., Linux audit records. However, these formats aren’t space-efficient, so we developed a simple yet compact format called CSR. CSR stands for Common Semantic Representation, signifying that a unified format is used for representing audit data from multiple OSes, such as Linux and Windows. Translators can easily be developed to translate CSR to standard log formats, so that standard log analyzers, or simple tools such as grep, can be used.  \n\nIn CSR, all subjects and objects are referenced using a numeric index. Complex data values that get used repeatedly, such as file names, are also turned into indices. A CSR file begins with a table that maps strings to indices. Following this table is a sequence of operations, each of which correspond to the definition of an object (e.g., a file, network connection, etc.) or a forensic-relevant operation such as open, read, write, chmod, fork, execve, etc. Operations deemed redundant by REO, REO\\* and CCO can be omitted.  \n\nEach operation record consist of abbreviated operation name, arguments (mostly numeric indices or integers), and a timestamp. All this data is represented in ASCII format for simplicity. Standard file compression can be applied on top of this format to obtain further significant size reduction, but this is orthogonal to our work.  \n\n# 5.2 Compact Main Memory Representation  \n\nForensic analysis requires queries over the dependence graph, e.g., finding shortest path(s) to the entry node of an attack, or a depth-first search to identify impacted nodes. The graph contains roughly the same information that might be found in Linux audit logs. In particular, the graph captures information pertaining to most significant system calls. Key argument values are stored (e.g., command lines for execve, file names, and permissions), while the rest are ignored (e.g., the contents of buffers in read and write operations).  \n\nNodes in the dependence graph correspond to subjects and objects. Nodes are connected by bidirectional edges corresponding to events (typically, system calls). To obtain a compact representation, subjects, objects, and most importantly edges must be compactly encoded. Edges typically outnumber nodes by one to two orders of magnitude, so compactness of edges is paramount.  \n\nThe starting point for our compact memory representation is the SLEUTH [10] system for forensic analysis and attack visualization. The graph structure used in this paper builds on some of the ideas from SLEUTH, such as the use of compact identifiers for referencing nodes and node attributes. However, we did away with many other aspects of that implementation, such as the (over-)reliance on compact, variable length encoding for events, based on techniques drawn from data compression and encoding. These techniques increased complexity and reduced runtime performance. Instead, we rely primarily on versioned graphs and the optimizations in Section 4 to achieve compactness. This approach also helped improve performance, as we can achieve graph construction rates about three times faster than SLEUTH’s. Specifically, the main techniques we rely on to reduce memory use in this paper are:  \n\n• Edge reductions: The biggest source of compaction is the redundant edge optimization. Savings are also achieved because we don’t need timestamps on most edges. Instead, timestamps are moved to nodes (subject or object versions). This enables most stored edges to use just 6 bytes in our implementation, encoding an event name and about a 40-bit subject or object identifier.  \n\n• Node reductions: The second biggest source of compaction is node reduction, achieved using RNO and CCO optimizations. In addition, our design divides nodes into two types: base versions and subsequent versions. Base versions include attributes such as name, owner, command line, etc. New base versions are created only when these attributes change. Attribute values such as names and command lines tend to be reused across many nodes, so we encode them using compact ids. This enables a base version to be stored in 32 bytes or less.  \n\n• Compact representation for versions: Subsequent versions derived from base versions don’t store node attributes, but just the starting and ending timestamps. By using relative timestamps and sticking to a 10ms timestamp granularity7, we are able to represent a timestamp using 16-bits in most cases. This enables a version to fit within the same size as an edge, and hence it can be stored within the edge list of a base version. In particular, let $S$ be the set of edges occurring between a version $\\nu$ and the next version appearing in the edge list. Then $S$ is the set of edges incident on version $\\nu$ in the graph.  \n\nEdge lists are maintained as vectors that can grow dynamically for active nodes (i.e., running processes and open files) but are frozen at their current size for inactive nodes. This technique, together with the technique of storing versions within the edge list, reduces fragmentation significantly. As a result, we achieve a very compact representation that often takes just a few bytes per edge in the original data.  \n\n# 6 Experimental Evaluation  \n\nWe begin this section by summarizing our implementation in Section 6.1. The data sets used in our evaluation are described in Section 6.2. In Section 6.3, we evaluate the effectiveness of FD and SD in reducing the number of events, and compare it with Xu et al.’s technique (LCD). We then evaluate the effect of these reductions on the CSR log size and the inmemory dependence graph in Sections 6.4 and 6.5. Runtimes for dependence graph construction and forensic analysis are discussed in Section 6.6. The impact of our optimizations on forensic analysis accuracy is evaluated in Section 6.7.  \n\n# 6.1 Implementation  \n\nOur implementation consists of three front-ends and a backend written in $\\mathrm{C}{+}{+}$ . The front-ends together contain about 6KLoC; the back-end, about 7KLoC. The front-ends process data from audit sources. One front-end parses Linux audit logs, while the other two parse Linux and Windows data from the red team engagement. The back-end uses our BuildVer algorithm, together with (a) the REO, RNO, and CCO optimizations (Section 4.2) to realize FD preservation, and (b) the source dependence preservation technique described in Section 4.3. It uses the compact main-memory representation presented in Section 5.2. Our implementation can also generate event logs in our CSR format, described in Section 5.1.  \n\nThe back-end can also read data directly from CSR logs. We used this capability to carry out many of our experiments, because data in CSR format can be consumed much faster than data in Linux audit log format or the OS-neutral format in which red team engagement data was provided. A few key points about our implementation are:  \n\nNetwork connections: We treat each distinct combination of (remote IP, port, time window) as a distinct source node. Currently, time windows are set to about 10 minutes. This means that when we read from any IP/port combination, all reads performed within a 10-minute period are treated as coming from a single source. Thus, FD and SD can aggregate them. After 10 minutes, it is considered a new source, thus allowing us to reason about remote sites whose behavior may change over time (e.g., the site may get compromised). A similar approach is applicable for physical devices. Handling execve: Execve causes the entire memory image of a process to be overwritten. This suggests that dependences acquired before the execve will be less of a factor in the behavior of the process, compared to dependences acquired after. We achieve this effect by limiting REO from traversing past execve edges.8 $R E O^{*}$ optimization: Almost all edges in our graph are between subjects and objects. Consider a case when a subject $s$ reads an object $o$ . The only case where $o$ could be an ancestor but not a parent is if $o$ was read by another subject $s^{\\prime}$ that then wrote to an object $o^{\\prime}$ that is being read by $s$ . Since this relationship looks distant, we did not consider that $\\mathrm{{REO}^{\\ast}}$ would be very useful in practice.9  \n\n# 6.2 Data Sets  \n\nOur evaluation uses data from live servers in a small laboratory, and from a red team evaluation led by a government agency. We describe these data sets below.  \n\n# 6.2.1 Data from Red Team Engagement  \n\nThis data was collected as part of the $2^{\\mathrm{nd}}$ adversarial engagement organized in the DARPA Transparent Computing program. Several teams were responsible for instrumenting OSes and collecting data, while our team (and others) performed attack detection and forensic analysis using this data. The red team carried out attack campaigns that extended over a period of about a week. The red team also generated benign background activity, such as web browsing, emailing, and editing files.  \n\nLinux Engagement Data (Linux Desktop). Linux data (Linux Desktop) captures activity on an Ubuntu desktop machine over two weeks. The principal data source was the built-in Linux auditing framework. The audit data was transformed into a OS-neutral format by another team and then given to us for analysis. The data includes all system calls considered important for forensic analysis, including open, close, clone, execve, read, write, chmod, rm, rename, and so on. Table 8 shows the total number of events in the data, along with a breakdown of important event types. Since reads and writes provide finer granularity information about dependencies than open/close, we omitted open/close from our analysis and do not include them in our figures.  \n\nTable 8: Data sets used in evaluation.   \n\n\n<html><body><table><tr><td>Dataset</td><td>Total Events</td><td>Read</td><td>Write</td><td>Clone/ Exec</td><td>Other</td></tr><tr><td>Linux Desktop</td><td>72.6M</td><td>72.4%</td><td>26.2%</td><td>0.5%</td><td>0.9%</td></tr><tr><td>WindowsDesktop</td><td>14.6M</td><td>77.1%</td><td>14.5%</td><td>1.2%</td><td>7.2%</td></tr><tr><td>SSH/FileServer</td><td>14.4M</td><td>38.2%</td><td>58.3%</td><td>1.2%</td><td>2.3%</td></tr><tr><td>WebServer</td><td>2.8M</td><td>64.3%</td><td>30.3%</td><td>1.5%</td><td>3.9%</td></tr><tr><td>MailServer</td><td>3M</td><td>70%</td><td>23.6%</td><td>1.7%</td><td>4.7%</td></tr></table></body></html>  \n\nWindows Engagement Data (Windows Desktop). Windows data covers a period of about 8 days. The primary source of this data is Event Tracing for Windows (ETW). Events captured in this data set are similar to those captured on Linux. The data was provided to us in the same OS-neutral format as the Linux data. Nevertheless, some differences remained. For examples, network reads and network writes were omitted (but network connects and accepts were reported). Also reported were a few Windows-specific events, such as CreateRemoteThread. Registry events were mapped into file operations. From Table 8, it can be seen that the system call distribution is similar as for Linux, except for a much higher volume of “other” calls, due to higher numbers of renames and removes.  \n\n# 6.2.2 Data From Laboratory Servers  \n\nAn important benefit of the red team data is that it was collected by teams with expertise in instrumenting and collecting data for forensic analysis. A downside is that some details of their audit system configurations are unknown to us. To compensate for this, we supplemented the engagement data sets with audit logs collected in our research lab. Audit data was collected on a production web server, mail server, and general purpose file and remote access server (SSH/File Server) used by a dozen users in a small academic research laboratory. All of these systems were running Ubuntu Linux. Audit data was collected over a period of one week using the Linux audit system, configured to record open, close, read, write, rename, link, unlink, chmod, etc.  \n\n# 6.3 Event Reduction: Comparison of LCD, FD and SD  \n\nFig. 9 shows the event reduction factor (i.e., ratio of number of events before and after the reduction) achieved by our two techniques, FD and SD. For comparison, we reimplemented $\\mathrm{Xu}$ et al.’s full-trackability reduction as described by Algorithms 1, 2 and 3 in [42]. As discussed before, full-trackability equivalence is like a localized version of our continuous dependence preservation criteria, and hence we refer to it as LCD for consistency of terminology. LCD, FD and SD achieve an average reduction factor of 1.8, 7 and 9.2 respectively. Across the data sets, LCD achieves reduction factors between 1.6 and 2.7, FD ranges from 4.6 to 15.4, and SD from 5.4 to 19.1.  \n\n![](/tmp/output/41_20250325210069/images/1837ba7b787557387f074789f1c2c9b6f5dfa12e3ce201955ddc202b9becad71.jpg)  \nFig. 9: Event reduction factors achieved by LCD, FD, and SD.   \nTable 10: Log size on disk. The second column reports the log size of original audit data. Each remaining column reports the factor of decrease in CSR log size achieved by the indicated optimization, relative to the size on disk.  \n\nAs illustrated by these results, FD provides much more reduction than LCD. To understand the reason, consider a simple example of a process $P$ that repeatedly reads file $A$ and then writes file $B$ . The sequence of $P$ ’s operations may look like read $(A)$ ; $w r i t e(B)$ ; read $(A)$ ; write $\\cdot(B)$ ; . Note that there is an outgoing (i.e., write) edge between every pair of incoming (i.e., read) edges into $P$ . This violates Xu et al.’s condition for merging edges, and hence none of these edges can be merged. Our FD criteria, on the other hand, can utilize non-local information that shows that $A$ has not changed during this time period, and hence can aggregate all of the reads as well as the writes.  \n\nWe further analyzed the data to better understand the high reduction factors achieved by FD and SD. We found that on Linux, many applications open the same object multiple times. On average, a process opened the same object approximately two times on the laboratory servers. Since the objects typically did not change during the period, FD was typically able to combine the reads following distinct opens, thus explaining a factor of about 2. Next, we observed that on average, each open was accompanied by 3 to 5 reads/writes. Again, FD was able to aggregate most of them, thus explaining a further factor of 2 to 4. We see that the actual reduction achieved by FD is within this explainable range for the laboratory servers. For Windows desktop, the reduction factor was less, mainly because the Windows data does not include reads or writes on network data. For Linux desktop data set, FD reduction factor is significantly higher. This is partly because long-running processes (e.g., browsers) dominate in this data. Such processes typically acquire a new dependency when they make a new network connection, but subsequent operations don’t add new dependencies, and hence most of them can be reduced.  \n\nOur implementation of SD is on top of FD: if an edge cannot be removed by FD, then the SD criterion is tried. This is why SD always has higher reduction factor than FD. SD provides noticeable additional benefits over FD.  \n\n<html><body><table><tr><td rowspan=\"3\">Dataset</td><td rowspan=\"3\">Sizeon Disk</td><td rowspan=\"3\">CSR</td><td colspan=\"2\">Reductionfactor</td></tr><tr><td>FD</td><td>SD</td></tr><tr><td></td><td></td></tr><tr><td>Linux Desktop</td><td>12.9GB</td><td>5.6x</td><td>66.1x</td><td>76.8x</td></tr><tr><td>WindowsDesktop</td><td>2.1GB</td><td>2.4×</td><td>4.46x</td><td>4.54x</td></tr><tr><td>SSH/Fileserver</td><td>6.7GB</td><td>15.1x</td><td>91.5x</td><td>122.5x</td></tr><tr><td>Webserver</td><td>1.3GB</td><td>13.3x</td><td>49.3 ×</td><td>57.9x</td></tr><tr><td>Mailserver</td><td>1.2GB</td><td>11.9x</td><td>41x</td><td>49.2×</td></tr><tr><td colspan=\"2\">Average (Geometric mean)</td><td>x8</td><td>35.3x</td><td>41.4x</td></tr></table></body></html>  \n\n# 6.4 Log Size Reduction  \n\nTable 10 shows the effectiveness of our techniques in reducing the on-disk size of log data. The second column shows the size of the original data, i.e., Linux audit data for laboratory servers, and OS-neutral intermediate format for red team engagement data. The third column shows the reduction in size achieved by our CSR representation10, before any reductions are applied. The next two columns show the size reductions achieved by CSR together with FD and SD respectively.  \n\nFrom the table, it can be seen that the reduction factors from FD and CD are somewhat less than that shown in Fig. 9. This is expected, because they compress only events, not nodes. Nevertheless, we see that the factors are fairly close, especially on the larger data sets. For instance, on the Linux desktop data, where FD produces about $15\\times$ reduction, the CSR log size shrinks by about $12\\times$ over base CSR size. Similarly, on SSH/File server, FD event reduction factor is $8\\times$ , and the CSR size reduction is about $6\\times$ . In addition, the log sizes are $35.3\\times$ to $41.4\\times$ smaller than the input audit logs.  \n\n# 6.5 Dependence Graph Size  \n\nTable 11 illustrates the effect of different optimizations on memory use. On the largest dataset (Linux desktop), our memory use with FD is remarkably low: less than two bytes per event in the original data. On the other two larger data sets (Windows desktop and SSH/file server), it increases to 3.3 to 6.8 bytes per event. The arithmetic and geometric means (across all the data sets) are both less than 5 bytes/event.  \n\nExamining the Linux desktop and Windows desktop numbers closely, we find that the memory use is closely correlated with the reduction factors in Fig. 9. In particular, for the Linux desktop, there are about 4.7M events left after FD reduction. Each event results in a forward and backward edge, each taking 6 bytes in our implementation (cf. Section 5). Subtracting this $4.7\\mathbf{M}^{*}12\\mathbf{B}=56.4\\mathbf{M}\\mathbf{B}$ from the 111MB, we see that the 1.1M nodes occupy about 55MB, or about 50 bytes per node. Recall that each node takes 32 bytes in our implementation, plus some additional space for storing file names, command lines, etc. A similar analysis of Windows data shows that about 2M events are stored occupying about 24MB, and that the 781K nodes take up about 53B/node.  \n\n<html><body><table><tr><td>Dataset</td><td>Total No. of Nodes</td><td>Total Events</td><td>FD (MB)</td><td>SD (MB)</td></tr><tr><td>Linux Desktop</td><td>1.1M</td><td>72.6M</td><td>111</td><td>107</td></tr><tr><td>Windows Desktop</td><td>781K</td><td>10.3M</td><td>67</td><td>67</td></tr><tr><td>SSH/FileServer</td><td>430K</td><td>14.4M</td><td>45</td><td>39</td></tr><tr><td>WebServer</td><td>141K</td><td>2.8M</td><td>16</td><td>15</td></tr><tr><td>MailServer</td><td>189K</td><td>3M</td><td>21</td><td>20</td></tr><tr><td>Total</td><td>2.64M</td><td>103.1M</td><td>260</td><td>248</td></tr></table></body></html>\n\nTable 11: Memory usage. The second column gives the total number of nodes in the dependence graph before any versioning. The third column gives the total number of events. The fourth and fifth columns give the total memory usages for FD and SD. Average memory use across these data sets is less than 5 bytes/event.  \n\n# 6.5.1 Effectiveness of Version Reduction Optimizations  \n\nTable 12 shows the number of node versions created with the naive versioning algorithm and our optimized algorithms. The second column shows that naive versioning leads to a version explosion, with about 26 versions per node. However, FD and SD drastically reduce the number versions: with FD, we create just about 1.3 versions per node, on average.  \n\nTable 13 breaks out the effects of optimizations individually. Since some optimizations require other optimizations, we show the four most meaningful combinations: (a) no optimizations, (b) all optimizations except redundant node (RNO), (c) all optimizations except cycle-collapsing (CCO), and (d) all optimizations. These figures were computed in the context of FD. When all optimizations other than RNO are enabled, the number of versions falls to about $3.6\\times$ from $25.6\\times$ (unoptimized). Enabling all optimizations except CCO leads to about 3 versions on average per node. Comparing these with the last column, we can conclude that RNO contributes about a $3\\times$ reduction and CCO a $2.4\\times$ reduction in the number of versions, with the remaining $2.8\\times$ coming from REO. It should be noted that REO and CCO both remove versions as well as edges, whereas RNO removes only nodes.  \n\n# 6.6 Runtime Performance  \n\nAll results in our entire evaluation were obtained on a laptop with Intel Core i7 7500U running at 2.7GHz with 16GB RAM and 1TB SSD, running Ubuntu Linux. All experiments were run on a single core.  \n\n<html><body><table><tr><td rowspan=\"3\">Dataset</td><td colspan=\"3\">Versions per node</td></tr><tr><td>Naive</td><td>FD</td><td>SD</td></tr><tr><td>Linux Desktop</td><td>68.65</td><td>1.05</td><td>1.02</td></tr><tr><td>Windows Desktop</td><td>13.9</td><td>1.37</td><td>1.35</td></tr><tr><td>SSH/FileServer</td><td>34.36</td><td>1.31</td><td>1.06</td></tr><tr><td>WebServer</td><td>20.62</td><td>1.29</td><td>1.10</td></tr><tr><td>MailServer</td><td>16.20</td><td>1.32</td><td>1.22</td></tr><tr><td>Average</td><td>25.58</td><td>1.26</td><td>1.14</td></tr></table></body></html>  \n\n<html><body><table><tr><td rowspan=\"2\">Dataset</td><td colspan=\"4\">Versions per node</td></tr><tr><td>None</td><td>NoRNO</td><td>No CCO</td><td>FD</td></tr><tr><td>Linux Desktop</td><td>68.65</td><td>4.56</td><td>17.75</td><td>1.05</td></tr><tr><td>Windows Desktop</td><td>13.9</td><td>2.60</td><td>1.38</td><td>1.37</td></tr><tr><td>SSH/FileServer</td><td>34.36</td><td>4.32</td><td>2.21</td><td>1.31</td></tr><tr><td>WebServer</td><td>20.62</td><td>3.46</td><td>2.15</td><td>1.29</td></tr><tr><td>MailServer</td><td>16.20</td><td>3.57</td><td>2.12</td><td>1.32</td></tr><tr><td>Average</td><td>25.58</td><td>3.63</td><td>3.01</td><td>1.26</td></tr></table></body></html>\n\nTable 13: Effectiveness of different versioning optimizations. Geometric means are reported on the last row of the table.  \n\n# 6.6.1 Dependence Graph Construction Time with FD  \n\nWith our FD-preserving optimizations, this time depends on (a) the size of cycles considered by CCO, and (b) the maximum number of edges examined by REO. For (a), we have not come across cycles involving more than two nodes that meaningfully increased the size or runtime. So, our current implementation only considers cycles of length two. To evaluate the effect of (b), we placed a limit $k$ , called the $F D$ window size, on the number of edges examined by REO before it reports that a dependence does not exist; this is safe but may reduce the benefit. With this limit in place, each edge is processed in at most $O(k)$ time, yielding a graph construction algorithm that is linear in the size of the input audit log.  \n\nFig. 14 shows the dependence graph construction time as a function of FD window size. We use the notation $F D=c$ to represent the runtime when $k$ is set to $c$ . We use $k=1$ as the base, and show the other runtimes relative to this base. Note that runtime can initially dip with increasing $k$ because it leads to significant reductions in memory use, which translates into less pressure on the cache, and consequently, (slightly) improved runtime. But as $k$ is increased beyond 100, the runtime begins to increase noticeably.  \n\nThe runtime and the reduction factor both increase with window size. Fig. 15 plots the relationship between reduction factor and window size. In particular, $\\mathrm{FD}{=}1$ means that REO can eliminate the edge $(u,\\nu)$ only if the previous edge coming into $\\nu$ is also from $u$ . The average reduction achieved by FD in this extreme case is 1.96, about the same as the maximum rate achieved by LCD. Another observation is that for the laboratory servers, with $_{\\mathrm{FD}=25}$ , we achieve almost the full reduction potential of FD. For the desktop systems used in the red team engagements, full potential is achieved only at $\\mathrm{FD}{=}500$ . We hypothesize that this is partly due to the nature of red team exercises, and partly due to workload differences between desktops and servers.  \n\n![](/tmp/output/41_20250325210069/images/fadfef6ffb0dd6899b2775ec5e6e8f3513bdb2d631e4813ad359601d79b61519.jpg)  \nTable 12: Impact of naive and optimized versioning. Geometric means are reported on the last row of the table.   \nFig. 14: Dependence graph construction time with different FD window sizes. Y-axis is the normalized runtime, relative to base of $\\mathrm{FD}=1$ . These base times are $77.54\\mathrm{s}$ for Linux desktop, $19.02s$ for Windows desktop, 11.86s for Web server, 15.11s for Mail server and $41.77s$ for SSH/File server.  \n\n![](/tmp/output/41_20250325210069/images/16d50f072afe5fa0402e3e1eb8b31cf1e24b13e75052bbdb4d10d796b8cd1dc6.jpg)  \nFig. 15: Effect of FD window size on event reduction factor.  \n\nComparing the two charts, we conclude that a range of $_{\\mathrm{FD}=25}$ to $\\mathrm{FD}{=}100$ represents a good trade-off for a real-time detection and forensic analysis system such as SLEUTH [10], with most of the size reduction benefits realized, and with runtime almost the same as $\\mathrm{FD}{=}1$ . At $_{\\mathrm{FD}=25}$ , our implementation processes the 72M records in the Linux Desktop data set in 84 seconds, corresponding to a rate of 860K events/second. For applications where log size is the primary concern, $\\mathrm{FD}{=}500$ would be a better choice.  \n\n# 6.6.2 Dependence Graph Construction Time with SD  \n\nFor SD, the sizes of Src sets become the key factor influencing runtime. SD requires frequent computation of set unions, which takes linear time in the sizes of the sets. Moreover, increased memory use (due to large sets) significantly increases the pressure on the cache, leading to further performance degradation. We therefore studied the effect of placing limits on the maximum size of Src sets. Overflows past this limit are treated conservatively, as described in Section 4.3.  \n\nFigs. 16 and 17 show the effect of varying the source set size limit on the runtime and reduction factor, respectively. Recall that SD runs on top of FD, so the runtime of FD matters as well. However, since SD is significantly slower than FD, we did not limit the FD window size in these experiments. From the chart, the peak reduction factor is reached by $\\mathrm{SD}{=}500$ for all data sets except Linux desktop. The Linux desktop behaves differently, and we attribute this to the much higher level of activity on it, which means that a single long-running process can acquire a very large number of source dependencies. Nevertheless, the chart suggests that $\\mathrm{SD}{=}500$ is generally a good choice, as the overall runtime is almost unchanged from $\\scriptstyle\\mathrm{SD}=50$ .  \n\nAt $\\mathrm{SD}{=}500$ , it takes 144 seconds to process 72M records from Linux, for an event processing rate of about 500K/second. Thus, although SD is slower than FD, it is quite fast in absolute terms, being able to process events at least two orders of magnitude faster than the maximum event production rate observed across all of our data sets.  \n\n![](/tmp/output/41_20250325210069/images/4be1fac74ba9bec98b652251cdec680129e11760c1e5513291bc9c32c63503bd.jpg)  \nFig. 16: Dependence graph construction time with different source set size limits. Y-axis is the runtime relative to the runtime with $\\mathrm{SD}{=}50$ (size limit of 50), which is $143.68\\mathrm{s}$ for Linux desktop, $23.59\\mathrm{s}$ for Windows desktop, 12.86s for Web server, 15.43s for Mail server and 42.81s for SSH/File server.  \n\n![](/tmp/output/41_20250325210069/images/669d0c44aac2ed7b37a2f3e48128efd18d4d09b35f18dbb2610bf3a4cd1af7bb.jpg)  \nFig. 17: Effect of source set size limit on event reduction factor.  \n\n# 6.6.3 Backward and Forward Forensic Analysis  \n\nOnce the dependence graph is constructed, forensic analysis is very fast, because the whole graph currently resides in memory. To evaluate the performance, we randomly tagged 100K nodes in the dependence graph for the Linux desktop system. From each of these nodes, we performed  \n\na backward analysis to identify the source node closest to the tagged node. This search used a shortest path algorithm.   \na forward analysis to identify the nodes reachable from the tagged node. In case of searches that could return very large graphs, we terminated the search after finding 10K nodes (in most cases, the search terminated without hitting this limit).  \n\nThis entire test suite took 112 seconds to run. In other words, each forward plus backward analysis on a dependence graph corresponding to 72M events took just 1.12 milliseconds on average.  \n\n# 6.7 Preserving Forensic Analysis Results  \n\n# 6.7.1 Reproducing Analysis Results from SLEUTH [10]  \n\nIn our previous work [10], we performed real-time attack detection and forensic analysis of multi-step APT-style attack campaigns carried out in the $1^{\\mathrm{st}}$ adversarial engagement in the DARPA Transparent Computing program. As described in Table 6 in [10], there were 8 distinct attack campaigns, each of which involved most of the seven stages in APT life cycle, including drop & load, intelligence gathering, backdoor insertion, privilege escalation, data exfiltration, and cleanup.  \n\nTable 18: Results of forward and backward analyses carried out from the entry and exit points of attacks used in the red team attacks. The exact same set of entities were identified with and without the FD and SD event reductions.   \n\n\n<html><body><table><tr><td rowspan=\"2\">Dataset</td><td rowspan=\"2\">Attack Scenario</td><td rowspan=\"2\">Analysis Type</td><td colspan=\"3\">Number of Entities</td></tr><tr><td>Naive</td><td>FD</td><td>SD</td></tr><tr><td rowspan=\"4\">Linux Desktop</td><td rowspan=\"2\">A</td><td>Backward</td><td>7</td><td>7</td><td>7</td></tr><tr><td>Forward</td><td>15</td><td>15</td><td>15</td></tr><tr><td rowspan=\"2\">B</td><td>Backward</td><td>3</td><td>3</td><td>3</td></tr><tr><td>Forward</td><td>10</td><td>10</td><td>10</td></tr><tr><td rowspan=\"5\">Windows Desktop</td><td rowspan=\"2\">A</td><td>Backward</td><td>4</td><td>4</td><td>4</td></tr><tr><td>Forward</td><td>17</td><td>17</td><td>17</td></tr><tr><td rowspan=\"2\">B</td><td>Backward</td><td>2</td><td>2</td><td>2</td></tr><tr><td>Forward</td><td>9</td><td>9</td><td>9</td></tr><tr><td rowspan=\"2\">C</td><td>Backward</td><td>4</td><td>4</td><td>4</td></tr><tr><td>Forward</td><td>7</td><td>7</td><td>7</td></tr></table></body></html>  \n\nSLEUTH assigns integrity and confidentiality tags to objects. These tags propagate as a result of read, write and execute operations. It detects attacks using tag-based policies that were developed in the context of our earlier work on whole-system integrity protection [19, 34, 35, 36] and policy-based defenses [39, 32]. It then uses a backward analysis to identify the entry point, and then a forward analysis to determine attack impact, and then a set of simplification passes to generate a graph depicting the attack, and to list the entities involved. Across these 8 attacks, a total of 176 entities were identified as relevant by the red team, and our original analysis in [10] identified 174 of them.  \n\nWe carried out the investigation again, with FD and SD reductions in place. We were able to obtain the same results as in [10], showing that FD and SD reductions do not affect forensics results. This should come as no surprise, given that we proved that they both preserve the results of backward analysis followed by forward analysis. Nevertheless, the experimental results are reassuring.  \n\n# 6.7.2 Forensic Analysis Results on Table 8 Data Set  \n\nWe then turned our attention to the Engagement 2 data set. (We did not use Engagement 1 data set in our reduction experiments because it was far smaller in size than Engagement 2.) There were 2 attacks within the Linux dataset and 3 attacks within the Windows data set. For each attack, we ran a forward analysis from the attack entry point, and then a backward analysis from attack exfiltration point (which is one of the last steps in these attacks). As shown Table 18, these analyses identified the exact same set of entities, regardless of whether any data reduction was used.  \n\n# 7 Related Work  \n\nInformation-flow Tracking. Numerous systems construct dependence graphs [13, 9, 15, 22] or provenance graphs [25, 24, 8, 4, 29] that capture information flow at the coarse granularity of system calls. In particular, if a subject reads from a network source, then all subsequent writes by the subject are treated as (potentially) dependent on the network source. This leads to a dependence explosion, especially for long-running processes, as every output operation becomes dependent on every input operation. Fine-grained taint tracking [28, 41, 2, 12] can address this problem by accurately tracking the source of each output byte to a single input operation (or a few). Unfortunately, these techniques slow down programs by a factor of 2 to 10 or more. BEEP [17, 21] developed an alternative fine-grained tracking approach called unit-based execution partitioning that is much more efficient. However, as compared to taint-tracking techniques, execution partitioning generally requires some human assistance, and moreover, makes optimistic assumptions about the program behavior.  \n\nThe main drawback shared by all fine-grained tracking approaches is the need for instrumenting applications. In enterprises that run hundreds of applications from multiple vendors, this instrumentation requirement is difficult to meet, and hence it is much more common for enterprises to rely on coarse-grained tracking.  \n\nLog Reduction. BackTracker [13, 14, 15] pioneered the approach of using system logs for forensic investigation of intrusions. Their focus was on demonstrating effectiveness of attack investigation, so they did not pursue log reduction beyond simple techniques such as omitting “low-control” (less important) events, such as changing a file’s access time.  \n\nLogGC [18] proposed an interesting approach for log reduction based on the concept of garbage collection, i.e., removing operations involving removed files (“garbage”). Additional restrictions were imposed to ensure that files of interest in forensic analysis, such as malware downloads, aren’t treated as garbage. They report remarkable log reduction with this approach, provided it is used in conjunction with their unit instrumentation. Without such fine-grained instrumentation, the savings they obtain are modest. To further evaluate the potential of this approach, we analyzed the data set used in this paper (Table 8). We found that less than $3\\%$ of the operations in this data set were on files that were subsequently removed. Although not all of these files satisfy their definition of “garbage,” $3\\%$ is an upper bound on the savings achievable using this garbage collection technique on our data.  \n\nProTracer [22] proposed another new reduction mechanism that was based on logging only the write operations. Read operations, as well as some memory-related operations tracked by their unit instrumentation, were not logged. In the presence of their unit instrumentation, they once again show a dramatic reduction in log sizes using their strategy. However, as discussed in the introduction, this strategy of selective logging of writes can actually increase log sizes in the absence of unit instrumentation. Indeed, our experiments with this strategy11 resulted in more than an order of magnitude increase in log sizes.  \n\nXu et al.’s notion of full-trackability equivalence (LCD-preservation in our terminology) [42] is similar to our CD-preservation, as discussed in Section 3.2. We implemented their LCD-preserving reduction algorithm and found that our FD and SD optimizations achieve significantly more reduction, as detailed in Section 6.3. The reasons for this difference were also discussed in Section 6.3.  \n\nProvenance capture systems, starting from PASS [25], incorporate simple reduction techniques such as the removal of duplicate records. PASS also describes the problem of cyclic dependencies and their potential to generate a very large number of versions. They avoid cycles involving multiple processes by merging the nodes for those processes. Our cycle-collapsing optimization is based on a very similar idea.  \n\nProvWalls [5] is targeted at systems that enforce Mandatory Access Control (MAC) policies. It leverages the confinement properties provided by the MAC policy to identify the subset of provenance data that can be safely omitted, leading to significant savings on such systems.  \n\nWinnower [38] learns compact automata-based behavioral models for hosts running similar workloads in a cluster. Only the subset of provenance records that deviate from the model need to be reported to a central monitoring node, thereby dramatically reducing the network bandwidth and storage space needed for intrusion detection across the cluster. These models contain sufficient detail for intrusion detection but not forensics. Therefore, Winnower also stores each host’s full provenance graph locally at the host. In contrast, our system generates compact logs that preserve all the information needed for forensics.  \n\nFile Versioning. The main challenge for file versioning systems is to control the number of versions, while the challenge for forensic analysis is to avoid false dependencies. Unfortunately, these goals conflict. Existing strategies that avoid false dependencies, e.g., creating a new version of a file on each write [33], generate too many versions. Strategies that significantly reduce the number of versions, e.g., openclose versioning [31],12 can introduce false dependencies.  \n\nMany provenance capture systems use versioning as well. Like versioning file systems, they typically use either simple versioning that creates many versions (e.g., [4, 29]) or coarse-grained versioning that does not accurately preserve dependencies (e.g., [25]). In contrast, we presented an approach that provably preserves dependencies, while generating only a small number of versions in practice.  \n\nProvenance capture systems try to avoid cycles in the provenance graph, since cyclic provenance is meaningless. Causality-based versioning [24] discusses two techniques for cycle avoidance. The first of these performs global cycle detection across all objects and subjects on a system. The second operates with a view that is local to an object. It uses a technique similar to our redundant edge optimization, but is aimed at cycle avoidance rather than dependency preservation. They do not consider the other techniques we discuss in this paper, such as $\\mathrm{{REO}^{\\ast}}$ , RNO, and SD preservation, nor do they establish optimality results.  \n\nGraph Compression and Summarization. Several techniques have been proposed to compress data provenance graphs by sharing identical substructures and storing only the differences between similar substructures, e.g., [6, 40, 7]. Bao et al. [3] compress provenance trees for relational query results by optimizing the selection of query tree nodes where provenance information is stored. These compression techniques, which preserve every detail of the graph, are orthogonal to our techniques, which can drop or merge edges. Graph summarization [27, 37] is intended mainly to facilitate understanding of large graphs but can also be regarded as lossy graph compression. However, these techniques are not applicable in our context because they do not preserve dependencies.  \n\nAttack Scenario Investigation. Several recent efforts have been aimed at recreating the full picture of a complex, multi-step attack campaign. HERCULE [30] uses community discovery techniques to correlate attack steps that may be dispersed across multiple logs. SLEUTH [10] assigns trustworthiness and confidentiality tags to objects, and its attack detection and reconstruction are both based on an analysis of how these tags propagate. PrioTracker [20] speeds up backward and forward analysis by prioritizing exploration of paths involving rare or suspicious events. RAIN [11] uses record-replay technology to support on-demand fine-grained information-flow tracking, which can assist in detailed reconstruction of low-level attack steps.  \n\n# 8 Conclusion  \n\nIn this paper, we formalized the notion of dependencypreserving data reductions for audit data and developed efficient algorithms for dependency-preserving audit data reduction. Using global context available in a versioned graph, we are able to realize algorithms that are optimal with respect to our notions of dependency preservation. Our experimental results demonstrate the power and effectiveness of our techniques. Our reductions that preserve full dependence and source dependence reduce the number of events by factors of 7 and 9.2, respectively, on average in our experiments, compared to a factor 1.8 using an existing reduction algorithm [42]. Our experiments also confirm that our reductions preserve forensic analysis results.  \n\n# References  \n\n[1] Paul Ammann, Sushil Jajodia, and Peng Liu. Recovery from malicious transactions. IEEE Transactions on Knowledge and Data Engineering, 2002.   \n[2] Steven Arzt, Siegfried Rasthofer, Christian Fritz, Eric Bodden, Alexandre Bartel, Jacques Klein, Yves Le Traon, Damien Octeau, and Patrick McDaniel. Flowdroid: Precise context, flow, field, object-sensitive and lifecycle-aware taint analysis for android apps. SIGPLAN Not., 2014.   \n[3] Zhifeng Bao, Henning Kohler, Liwei Wang, Xiaofang Zhou, and Shazia Sadiq. Efficient provenance storage for relational queries. In CIKM, 2012.   \n[4] Adam Bates, Dave Jing Tian, Kevin RB Butler, and Thomas Moyer. Trustworthy whole-system provenance for the Linux kernel. In USENIX Security, 2015.   \n[5] Adam Bates, Dave (Jing) Tian, Grant Hernandez, Thomas Moyer, Kevin R. B. Butler, and Trent Jaeger. Taming the costs of trustworthy provenance through policy reduction. ACM Trans. Internet Technol., 2017.   \n[6] Adriane P. Chapman, H. V. Jagadish, and Prakash Ramanan. Efficient provenance storage. In ACM SIGMOD, 2008.   \n[7] Chen Chen, Harshal Tushar Lehri, Lay Kuan Loh, Anupam Alur, Limin Jia, Boon Thau Loo, and Wenchao Zhou. Distributed provenance compression. In ACM SIGMOD, 2017.   \n[8] Ashish Gehani and Dawood Tariq. Spade: support for provenance auditing in distributed environments. In International Middleware Conference, 2012.   \n[9] Ashvin Goel, Kenneth Po, Kamran Farhadi, Zheng Li, and Eyal de Lara. The Taser intrusion recovery system. In SOSP, 2005.   \n[10] Md Nahid Hossain, Sadegh M Milajerdi, Junao Wang, Birhanu Eshete, Rigel Gjomemo, R Sekar, Scott D Stoller, and VN Venkatakrishnan. Sleuth: real-time attack scenario reconstruction from cots audit data. In USENIX Security, 2017.   \n[11] Yang Ji, Sangho Lee, Evan Downing, Weiren Wang, Fazzini Mattia, Taesoo Kim, Alessandro Orso, and Wenke Lee. Rain: Refinable attack investigation with on-demand inter-process information flow tracking. In ACM CCS, 2017.   \n[12] Vasileios P. Kemerlis, Georgios Portokalidis, Kangkook Jee, and Angelos D. Keromytis. Libdft: Practical Dynamic Data Flow Tracking for Commodity Systems. SIGPLAN Not., 2012.   \n[13] Samuel T. King and Peter M. Chen. Backtracking intrusions. In SOSP, 2003.   \n[14] Samuel T. King and Peter M. Chen. Backtracking intrusions. ACM Transactions on Computer Systems, 2005.   \n[15] Samuel T. King, Zhuoqing Morley Mao, Dominic G. Lucchetti, and Peter M. Chen. Enriching intrusion alerts through multi-host causality. In NDSS, 2005.   \n[16] Srinivas Krishnan, Kevin Z. Snow, and Fabian Monrose. Trail of bytes: Efficient support for forensic analysis. In ACM CCS, 2010.   \n[17] Kyu Hyung Lee, Xiangyu Zhang, and Dongyan Xu. High accuracy attack provenance via binary-based execution partition. In NDSS, 2013.   \n[18] Kyu Hyung Lee, Xiangyu Zhang, and Dongyan Xu. LogGC: Garbage collecting audit log. In ACM CCS, 2013.   \n[19] Zhenkai Liang, Weiqing Sun, V. N. Venkatakrishnan, and R. Sekar. Alcatraz: An Isolated Environment for Experimenting with Untrusted Software. In TISSEC, 2009.   \n[20] Yushan Liu, Mu Zhang, Ding Li, Kangkook Jee, Zhichun Li, Zhenyu Wu, Junghwan Rhee, and Prateek Mittal. Towards a timely causality analysis for enterprise security. In NDSS, 2018.   \n[21] Shiqing Ma, Juan Zhai, Fei Wang, Kyu Hyung Lee, Xiangyu Zhang, and Dongyan Xu. MPI: Multiple perspective attack investigation with semantic aware execution partitioning. In USENIX Security, 2017.   \n[22] Shiqing Ma, Xiangyu Zhang, and Dongyan Xu. ProTracer: Towards practical provenance tracing by alternating between logging and tainting. In NDSS, 2016.   \n[23] Robert Campbell McColl, David Ediger, Jason Poovey, Dan Campbell, and David A Bader. A performance evaluation of open source graph databases. In PPAA, 2014.   \n[24] Kiran-Kumar Muniswamy-Reddy and David A Holland. Causalitybased versioning. ACM Transactions on Storage (TOS), 2009.   \n[25] Kiran-Kumar Muniswamy-Reddy, David A Holland, Uri Braun, and Margo I Seltzer. Provenance-aware storage systems. In USENIX ATC, 2006.   \n[26] Kiran-Kumar Muniswamy-Reddy, Charles P Wright, Andrew Himmer, and Erez Zadok. A versatile and user-oriented versioning file system. In USENIX FAST, 2004.   \n[27] Saket Navlakha, Rajeev Rastogi, and Nisheeth Shrivastava. Graph summarization with bounded error. In ACM SIGMOD, 2008.   \n[28] James Newsome and Dawn Song. Dynamic taint analysis for automatic detection, analysis, and signature generation of exploits on commodity software. In NDSS, 2005.   \n[29] Thomas Pasquier, Xueyuan Han, Mark Goldstein, Thomas Moyer, David Eyers, Margo Seltzer, and Jean Bacon. Practical whole-system provenance capture. In SoCC, 2017.   \n[30] Kexin Pei, Zhongshu Gu, Brendan Saltaformaggio, Shiqing Ma, Fei Wang, Zhiwei Zhang, Luo Si, Xiangyu Zhang, and Dongyan Xu. HERCULE: Attack story reconstruction via community discovery on correlated log graph. In ACSAC, 2016.   \n[31] Douglas S Santry, Michael J Feeley, Norman C Hutchinson, Alistair C Veitch, Ross W Carton, and Jacob Ofir. Deciding when to forget in the elephant file system. In SOSP, 1999.   \n[32] R. Sekar, V. Venkatakrishnan, S. Basu, S. Bhatkar, and D. C. DuVarney. Model-carrying code: A practical approach for safe execution of untrusted applications. 2003.   \n[33] Craig A Soules, Garth R Goodson, John D Strunk, and Gregory R Ganger. Metadata efficiency in a comprehensive versioning file system. Technical report, CARNEGIE-MELLON UNIV PITTSBURGH PA SCHOOL OF COMPUTER SCIENCE, 2002.   \n[34] Weiqing Sun, R. Sekar, Gaurav Poothia, and Tejas Karandikar. Practical Proactive Integrity Preservation: A Basis for Malware Defense. In IEEE S&P, 2008.   \n[35] Wai-Kit Sze and R Sekar. A portable user-level approach for system-wide integrity protection. In ACSAC, 2013.   \n[36] Wai Kit Sze and R Sekar. Provenance-based integrity protection for windows. In ACSAC, 2015.   \n[37] Yuanyuan Tian, Richard A. Hankins, and Jignesh M. Patel. Efficient aggregation for graph summarization. In ACM SIGMOD, 2008.   \n[38] Wajih Ul Hassan, Mark Lemay, Nuraini Aguse, Adam Bates, and Thomas Moyer. Towards scalable cluster auditing through grammatical inference over provenance graphs. In NDSS, 2018.   \n[39] V. N. Venkatakrishnan, Peri Ram, and R. Sekar. Empowering mobile code using expressive security policies. In New Security Paradigms Workshop, 2002.   \n[40] Yulai Xie, Dan Feng, Zhipeng Tan, Lei Chen, Kiran-Kumar Muniswamy-Reddy, Yan Li, and Darrell D.E. Long. A hybrid approach for efficient provenance storage. In CIKM, 2012.   \n[41] Wei Xu, Sandeep Bhatkar, and R. Sekar. Practical dynamic taint analysis for countering input validation attacks on web applications. Technical Report SECLAB-05-04, Department of Computer Science, Stony Brook University, May 2005.   \n[42] Zhang Xu, Zhenyu Wu, Zhichun Li, Kangkook Jee, Junghwan Rhee, Xusheng Xiao, Fengyuan Xu, Haining Wang, and Guofei Jiang. High fidelity data reduction for big data security dependency analyses. In ACM CCS, 2016.   \n[43] Ningning Zhu and Tzi-cker Chiueh. Design, implementation, and evaluation of repairable file service. In Dependable Systems and Networks, 2003.  "}