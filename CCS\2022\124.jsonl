{"text": "# Poster: <PERSON><PERSON><PERSON><PERSON><PERSON> – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software  \n\nCan Huang   \nPeking University   \nBeijing, China   \n<EMAIL>   \n<PERSON> Li   \nPeking University   \nBeijing, China   \n<EMAIL>  \n\n<PERSON><PERSON><PERSON> Peking University Beijing, China <EMAIL>  \n\n<PERSON><PERSON><PERSON>∗   \nPeking University   \nBeijing, China   \n<EMAIL>  \n\n# ABSTRACT  \n\nIn this poster, we proposed a .NET patch analysis framework named MSILDiffer based on Microsoft Intermediate Language (MSIL). First, <PERSON><PERSON><PERSON><PERSON><PERSON> directly extracts MSIL instructions from the .NET assemblies, and retrieves the hierarchy of classes as well as their internal class methods. Then, with coarse and fine granularity feature extraction and comparison, <PERSON><PERSON><PERSON><PERSON>er quickly filters out the code with substantial changes after patch. Besides, we build a dataset of patch analysis containing 24.46 million class methods based on the Microsoft Exchange mail system security patches. With the assistance of MSIL<PERSON>iff<PERSON>, we generated 32 call paths and crafted corresponding POCs for 1-day vulnerabilities in the dataset. Through the experiment evaluation, MSILDiffer is superior to JustAssembly in terms of coverage, accuracy and time consumption of patch difference analysis.  \n\n# CCS CONCEPTS  \n\n• Security and privacy $\\rightarrow$ Software security engineering.  \n\n# KEYWORDS  \n\nPatch Analysis; Vulnerability Mining; .NET Framework  \n\n# ACM Reference Format:  \n\n<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. 2022. Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software. In Proceedings of the 2022 ACM SIGSAC Conference on Computer and Communications Security (CCS ’22), November 7–11, 2022, Los Angeles, CA, USA. ACM, New York, NY, USA, 3 pages. https://doi.org/10.1145/3548606.3563518  \n\n# 1 INTRODUCTION  \n\nSecurity researchers usually use 0-day or 1-day to describe specific periods of software vulnerability exposure. A 0-day vulnerability is an exploitable bug previously unknown to the vendor, while  \n\n∗Corresponding author  \n\n1-day means the vendor already publishes patches for several days but the software has not been updated to apply the patches yet. Research [4] shows that it takes an average of 30 days to patch a vulnerability, so such vulnerability is still able to cause serious damage even after the patch is released. Due to safety concerns, software vendors only publish the impact and involved versions of the vulnerability rather than details when releasing security patches. To develop defense strategies and blocking rules in time, researchers need to analyze vulnerability functions and utilization conditions from security patches, to shorten the utilization time window of the 1-day vulnerability.  \n\nPatch analysis is a widely-used method to mine 1-day vulnerabilities. There are already researches on binary-level [1, 2] and source-level [3] patch analyses. However, these methods do not work well on large software programs developed with .NET framework that are compiled to MSIL (Microsoft Intermediate Language). Taking the Exchange mail system as an example, over the last 2 years, every security patch of Exchange would overwrite all the 646 .NET assemblies and introduce code unrelated to the vulnerabilities. As far as we know, the analysis of large .NET software is deficient, so we take it as our research object.  \n\nThe main contributions are:  \n\n(1) We proposed a MSIL patch analysis framework named MSILDiffer. MSILDiffer extracts features from MSIL instructions and control flow graphs of class methods in .NET assemblies, compares these features before and after the patch, and detects substantial changes introduced by the patch. We also implemented method call path analysis based on MSIL to generate vulnerability triggering paths.   \n(2) We built an evaluation dataset by collecting all the patches of the Exchange mail system since 2020. It contains 15 security patchs and 46 1-day vulnerabilities. We successfully generated call paths and created POCs for 32 vulnerabilities in the dataset.   \n(3) we conducted a comparative evaluation between MSILDiffer and JustAssembly [5]. The results showed that MSILDiffer could successfully locate all security vulnerabilities, and had advantages in coverage, accuracy and time consumption.  \n\n![](/tmp/output/124_20250326232064/images/667f721b4dcda3287c06b6ac0c4726993a30fbdcc55a59194a01eb973695fa94.jpg)  \nFigure 1: MSILDiffer analysis framework.  \n\n# 2 METHODOLOGY  \n\n# 2.1 Problem Definition  \n\nThe goal of patch analysis is to locate the set of patched .NET assemblies, classes and the class methods. We use $\\boldsymbol{D_{a s s e m b l y}}$ , $D_{c l a s s}$ and $D_{m e t h o d}$ to respectively represent the sets of .NET assemblies, classes and methods, whose contents have been changed in the patch. We use $A_{a s s e m b l y},A_{c l a s s}$ , and $A_{m e t h o d}$ to respectively represent the newly created or deleted sets of corresponding elements.  \n\nModifications that are related to vulnerabilities and software functionalities in the patch are obviously more important than the unrelated ones. Therefore, we subdivide $D_{m e t h o d}$ into related difference methods set $D R_{m e t h o d}$ and unrelated difference methods set $D U_{m e t h o d}$ . The security patches and corresponding statistical figures of Exchange mail system from March to May 2021 are shown in Table 1. The average number of $D_{m e t h o d}$ in a single patch is about 7104, while $D R_{m e t h o d}$ accounts for only $0.33\\%$ . The proportion indicates that the patch of large software introduces a large number of invalid differences and interferes. So we need to filter them to accurately locate the vulnerabilities.  \n\nTable 1: Exchange security patch difference statistics.   \n\n\n<html><body><table><tr><td>Patch</td><td>TotalMethods</td><td>Dmethod</td><td>DRmethod</td></tr><tr><td>KB5000871</td><td>1,063,308</td><td>7117</td><td>18</td></tr><tr><td>KB5001779</td><td>1,063,286</td><td>7105</td><td>35</td></tr><tr><td>KB5003435</td><td>1,063,308</td><td>7091</td><td>18</td></tr></table></body></html>  \n\n# 2.2 Analysis Framework  \n\nThe MSILDiffer analysis framework consists of three stages: coarsegrained comparison, fine-grained comparison and method call path analysis as shown in Figure 1.  \n\nCoarse-grained comparison: This step is to extract class information and quickly identify the differences of assemblies and classes, so it reduces the size of data to be analyzed in fine-grained comparison.  \n\n$\\textcircled{1}$ We input all .NET assemblies before and after the patch and use file name paring to filter out $A_{a s s e m b l y}$ . $\\textcircled{2}$ We extract metadata and MSIL instructions from the paired .NET assemblies and restore the all information of the classes and objects within the files, such as the namespaces, attributes and class methods. $\\textcircled{3}$ We pair the classes before and after the patch and put the unmatched classes into 𝐴𝑐𝑙𝑎𝑠𝑠 .  \n\n![](/tmp/output/124_20250326232064/images/bd024270b75969edd5b9d9aef71b73e68ac4fdb656497e9303da1aed7ef12f46.jpg)  \nFigure 2: Coarse-grained extracted feature information.  \n\n$\\textcircled{4}$ For paired classes, we collect their namespaces, names, types and various modifiers into a feature set, as shown in Figure 2. $\\textcircled{5}$ After extraction, we compute the hash value of the feature set as the fingerprint $F(c l a s s)$ , which is used to reduce the complexity of comparing a large number of features and classes. If the fingerprints of the class before patch and after the patch are inconsistent, the class is considered to be changed and put into 𝐷𝑐𝑙𝑎𝑠𝑠 .  \n\nFine-grained comparison: This step is to identify the differences of methods and further distinguish methods potentially related to vulnerabilities.  \n\n$\\textcircled{1}$ We extract features of class methods, including their names, return types, arguments, attributes and instruction sequences. These features are hashed to generate fingerprints $F(m e t h o d)$ which are paired to get $D_{m e t h o d}$ and $A_{m e t h o d}$ .  \n\n$\\textcircled{2}$ To distinguish $D R_{m e t h o d}$ , we first generate a control flow graph (CFG) and compare both structural and non-structural features of the graph. Structural features include the number of basic blocks, block edges, out-degree and in-degree, etc. Non-structural features are the number of instructions of each basic block. If two paired methods have different graph features, the method has been patched and may be related to vulnerabilities, so we put it in $D R_{m e t h o d}$ directly. Otherwise we further compare their instruction features. $\\textcircled{3}$ For instruction features, we gather the instruction distribution, method calls and object operations from the instruction sequence of each method, as shown in Figure 3. The instruction distribution is computed by counting each MSIL instruction inside the method according to instruction categories; The method call feature contains the class name, method name, parameters and return types of the call and callvirt instructions; The object operation feature contains types and constructor names of the ldsfld instruction which loads stack object members and the newobj instruction which creates object members, etc.  \n\nMethod call path analysis: To assist security researchers in writing POCs after obtaining $D R_{m e t h o d}$ and $A_{m e t h o d}$ , MISLDiffer analyzes the function calls of each method in both sets one by one to generate a call path to reach the patch sites. Not all of the generated paths are vulnerable, but they are still valuable for further analyses.  \n\n![](/tmp/output/124_20250326232064/images/55fe713026c24e5b451163653fac445107d5579cd90d0e904637edc1f579ec75.jpg)  \nFigure 3: Fine-grained extraction of instruction features.  \n\n![](/tmp/output/124_20250326232064/images/584207d18679a4e1476b4f2ed4205f131b17a702543e8c66b1060688b6723c9c.jpg)  \nFigure 4: Method call path analysis.  \n\nWe implement call analysis for different types of method calls (i.e., abstract classes, interfaces, threads and delegates) to find references to target methods, and support both inner and cross assembly calls. Figure 4 shows an example of generating the complete call path to method E across different .NET assemblies.  \n\n# 3 EXPERIMENT  \n\nDue to the lack of a common dataset of .NET software patches, we built our own dataset by collecting the security patches of the Exchange mail system (during January 2020 and March 2022). The dataset contains 15 security patches, 46 vulnerabilities, 9690 independent .NET assemblies, 3,290,914 classes and 24,462,270 methods.  \n\nWe analyzed all the 1-day vulnerabilities in our dataset with the help of MSILDiffer, and successfully crafted 32 POCs out of 46 vulnerabilities based on the generated call paths (Table 2). Among these vulnerabilities, Microsoft fixed CVE-2021-34473, CVE-2021- 34523 and CVE-2021-33766 in KB5001779 April 2021 but released the announcement until July, which proves that MSILDiffer is capable of finding fixed yet unpublished vulnerabilities. Besides, we also tested MSILDiffer on SolarWinds and Veeam Backup, and generated the POCs of CVE-2021-35218, CVE-2022-26500 and CVE-2022-26501, showing that MSILDiffer is useful.  \n\nIn order to illustrate the ability of MSILDiffer, we compared it with JustAssembly in speed and accuracy. JustAssembly is a commonly used .NET software difference analysis tool that decompiles the program and applies text alignment. In the experiment, we use all .NET assemblies before and after patch as input for both software, then output $D R_{m e t h o d}$ and $A_{m e t h o d}$ . Subsequently, we match the outputs with the ground-truth sets 𝐷𝑅𝑟𝑚𝑒𝑒𝑎𝑡𝑙ℎ 𝑜𝑑 and 𝐴𝑚𝑒𝑡ℎ $A_{m e t h o d}^{r e a l}$ . The following evaluating indicators are calculated:  \n\nTable 2: Mined 1-day vulnerabilities of Exchange.   \n\n\n<html><body><table><tr><td>CVE-2022-24463</td><td>CVE-2022-21969</td><td>CVE-2021-41349</td><td>CVE-2021-42321</td></tr><tr><td>CVE-2021-31196</td><td>CVE-2021-31206</td><td>CVE-2021-31195</td><td>CVE-2021-31198</td></tr><tr><td>CVE-2021-31207</td><td>CVE-2021-31209</td><td>CVE-2021-28480</td><td>CVE-2021-28482</td></tr><tr><td>CVE-2021-34473</td><td>CVE-2021-34523</td><td>CVE-2021-33766</td><td>CVE-2021-26412</td></tr><tr><td>CVE-2021-26854</td><td>CVE-2021-26855</td><td>CVE-2021-27065</td><td>CVE-2021-26858</td></tr><tr><td>CVE-2021-24085</td><td>CVE-2020-17117</td><td>CVE-2020-17132</td><td>CVE-2020-17141</td></tr><tr><td>CVE-2020-17143</td><td>CVE-2020-17083</td><td>CVE-2020-17084</td><td>CVE-2020-17085</td></tr><tr><td>CVE-2020-16875</td><td>CVE-2020-0903</td><td>CVE-2020-0692</td><td>CVE-2020-0688</td></tr></table></body></html>  \n\nTable 3: Performance of JustAssembly and MSILDiffer.   \n\n\n<html><body><table><tr><td></td><td>DRmethod Amethod</td><td></td><td>Rr</td><td>Ra</td><td>Rf</td><td>T</td></tr><tr><td>JustAssembly</td><td>106665</td><td>324</td><td>98%</td><td>94%</td><td>0.229%2099s</td><td></td></tr><tr><td>MSILDiffer</td><td>293</td><td>347</td><td>100%100%</td><td></td><td>91.2%</td><td>377s</td></tr></table></body></html>  \n\n$\\begin{array}{r}{R_{r}=\\frac{\\left|D R_{m e t h o d}\\cap D R_{m e t h o d}^{r e a l}\\right|}{\\left|D R_{m e t h o d}^{r e a l}\\right|}}\\end{array}$ indicates the ability to find the changed methods th\f at relate \fd to the vulnerabilities.   \n• $\\begin{array}{r}{R_{a}=\\frac{\\left|A_{m e t h o d}\\cap A_{m e t h o d}^{r e a l}\\right|}{\\left|A_{m e t h o d}^{r e a l}\\right|}}\\end{array}$ indicates the ability to find the actual created and deleted methods.   \n• $\\begin{array}{r}{R_{f}=\\frac{\\left|D R_{m e t h o d}\\cap D R_{m e t h o d}^{r e a l}\\right|}{\\left|D R_{m e t h o d}\\right|}}\\end{array}$ methodl indicates the filtering ratio of invalid differences methods and reflects the filtering ability of patch analysis method dealing with invalid differences methods. • Time consumption $T$ is the average time spent on analyzing a patch.  \n\nThe experimental results are shown in Table 3. MSILDiffer is able to locate all the actual difference methods and better at filtering invalid difference methods with less time. On the contrary, the $R_{r}$ and $R_{a}$ of JustAssembly are lower than 1, since the decompiler used by JustAssembly produces exceptions and missed some class methods when restoring, which also proves that the directly extracting MSIL instructions in .NET assemblies has more advantages. In terms of $R_{f}$ index, MSILDiffer performs much better than JustAssembly, showing its good ability of filtering out invalid methods. Besides, the MSILDiffer’s time consumption is only $18\\%$ of JustAssembly.  \n\n# 4 CONCLUSION  \n\nAiming at security patch analysis and 1-day vulnerability mining of large .NET software, we present an analysis framework based on MSIL and build an evaluation dataset based on real patches. With the assistance of MSILDiffer, we mined and reproduced 32 Exchange, 1 SolarWinds and 2 Veeam Backup 1-day vulnerabilities. Compared with the JustAssembly, MSILDiffer analyzes faster and shows higher accuracy in locating differences.  \n\n# REFERENCES  \n\n[1] Thomas Dullien and Rolf Rolles. 2005. Graph-based comparison of executable objects (english version). Sstic 5, 1 (2005), 3.   \n[2] Halvar Flake. 2004. Structural comparison of executable objects. In Detection of intrusions and malware & vulnerability assessment, GI SIG SIDAR workshop, DIMVA 2004. Gesellschaft für Informatik eV.   \n[3] Masatomo Hashimoto and Akira Mori. 2008. Diff/TS: A tool for fine-grained structural change analysis. In 2008 15th working conference on reverse engineering. IEEE, 279–288.   \n[4] Antonio Nappa, Richard Johnson, Leyla Bilge, Juan Caballero, and Tudor Dumitras. 2015. The attack of the clones: A study of the impact of shared code on vulnerability patching. In 2015 IEEE symposium on security and privacy. IEEE, 692–708.   \n[5] Telerik. 2021. Telerik JustAssembly. https://www.telerik.com/justassembly  "}