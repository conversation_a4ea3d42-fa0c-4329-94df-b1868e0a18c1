{"text": "# Observing and Preventing Leakage in MapReduce  \n\n<PERSON> Microsoft Research Microsoft Research <NAME_EMAIL> <EMAIL> <EMAIL> ˚ <PERSON><PERSON>kantsidis <PERSON>weiss <PERSON><PERSON><PERSON> Research Microsoft Research Carnegie <NAME_EMAIL> <EMAIL> <EMAIL>  \n\n# ABSTRACT  \n\nThe use of public cloud infrastructure for storing and processing large datasets raises new security concerns. Current solutions propose encrypting all data, and accessing it in plaintext only within secure hardware. Nonetheless, the distributed processing of large amounts of data still involves intensive encrypted communications between different processing and network storage units, and those communications patterns may leak sensitive information.  \n\nWe consider secure implementation of MapReduce jobs, and analyze their intermediate traffic between mappers and reducers. Using datasets that include personal and geographical data, we show how an adversary that observes the runs of typical jobs can infer precise information about their input. We give a new definition of data privacy for MapReduce, and describe two provably-secure, practical solutions. We implement our solutions on top of VC3, a secure implementation of Hadoop, and evaluate their performance.  \n\n# Categories and Subject Descriptors  \n\nK.6.5 [Management of Computing and Information Systems]: Security and Protection.  \n\n# Keywords  \n\nMap-reduce; traffic analysis; oblivious shuffle; oblivious load balancing.  \n\n# 1. INTRODUCTION  \n\nThe use of shared cloud infrastructure for storing and processing large structured datasets has gained widespread prominence. In particular, the MapReduce framework is routinely used to outsource such tasks in a simple, scalable, and cost-effective manner. As can be expected, reliance on a cloud provider for processing sensitive data entails new integrity and privacy risks.  \n\nSeveral recent works explore different trade-offs between performance, security, and (partial) trust in the cloud. Most proposals involve protecting data at rest—using some form of authenticated encryption—and protecting data in use with either advanced cryptography or secure hardware. Although homomorphic encryption [10] may address our privacy concerns, it remains impractical for general processing of large data, in particular when they involve complex, dynamic intermediate data. Conversely, limited trust assumptions on the cloud infrastructure may lead to efficient solutions, but their actual security guarantees are less clear.  \n\nAs a concrete example, VC3 [24] recently showed that, by relying on the new Intel SGX infrastructure [17] to protect local mapper and reducer processing, one can adapt the popular Hadoop framework [1] and achieve strong integrity and confidentiality for large MapReduce tasks with a small performance overhead. All data is systematically AES-GCM-encrypted, except when processed within hardware-protected, remotely-attested enclaves that include just the code for mapping and reducing data, whereas the rest of the Hadoop distributed infrastructure need not be trusted. They report an average $4\\%$ performance overhead for typical MapReduce jobs. Trusting Intel’s CPUs may be adequate for many commercial applications, and yields a much smaller TCB than the whole cloud infrastructure. Similar practical solutions may rely instead, for instance, on a hypervisor and simple virtual machines dedicated to the MapReduce job.  \n\nEven if we assume perfect encryption for all data and perfect isolation for all local processing, mappers and reducers still need to access shared resources (the memory, the data store, the network) thereby opening as many side channels. Their access patterns to memory, storage, and network— such as for instance the data volume of map and reduce jobs—are visible to the cloud provider and, to a lesser extents, to its other tenants. Revealing this information may be justified by the practical performance one gains in return. However, there are circumstances where observing the encrypted traffic of MapReduce jobs on a sensitive dataset reveals much more information than may be expected.  \n\nA first, important insight is that observing access to intermediate data structures is more informative than just observing inputs and outputs. In the case of MapReduce, for instance, observing and correlating intermediate key-value pairs exchanged between every mapper and every reducer for a series of typical jobs, each using a different field of the input records as key for mapping—say the age, the place of birth, and the place of work—enables us to label each input record with precise values for all these fields. What we learn from such a ‘job composition’ attack is much more detailed than what we would learn even by accessing the job results in plaintext. This may come as a surprise for data owners, who reason about which MapReduce job to authorize, and which results to declassify, but usually not about leakage in their distributed execution.  \n\nTo support our claim, we demonstrate information leaked from two sample datasets (900MB and 24GB, respectively) that include personal and geographical attributes. We assume an honest-but-curious adversary that observes the volume of encrypted communications between long-term storage, mappers, and reducers. (Other lower-level side channels may be available, such as precise timings, page faults, cache misses, etc, but they seem harder to exploit in general, and may not reveal much more information on the input datasets.) Our attacks suggest that, even with the use of encryption and secure hardware, stronger methods are required to avoid leakage through traffic analysis.  \n\nTo remedy this problem, we propose a new definition of data privacy for MapReduce—essentially, that observable I/O should look independent of the input dataset—and we describe two practical solutions that meet this definition, and thus prevent our attacks.  \n\nAs a trivial solution, we may pad all accesses and communications to their potential maximal lengths. Similarly, we may apply generic oblivious RAM techniques [11] and oblivious sorting on top of a MapReduce implementation. However, such solutions would incur a polylogarithmic overhead, and they would preclude any speedups enabled by the parallel nature of MapReduce. We further discuss related baseline solutions in 9.  \n\nIntuitively, many existing mechanisms already in place in MapReduce frameworks to achieve good performance should also help us for privacy. Mappers and reducers often use large I/O buffers, making it harder to track individual records within large, encrypted batches of data. Similarly, for jobs with adequate load balancing, one would expect mappers and reducers to exchange roughly the same amount of data with one another, thereby limiting that side channel. Our solutions take advantage of these mechanisms to provide strong, provable security, while respecting the simple, uniform, parallel data flow in MapReduce jobs.  \n\n# In summary, we contribute:  \n\n1. an empirical analysis of ‘MapReduce leakage’ on two sample datasets—a sample of the 1990 US Census and a log of taxi rides in New York—showing that one can reliably infer (or even extract) precise information about these datasets by simply observing the volume of encrypted communications between the mappers and reducers that perform a few typical jobs on their data;   \n2. a model of information leakage in MapReduce jobs, against an adversary that observes all encrypted intermediate traffic between Map and Reduce nodes;   \n3. two practical solutions that provably limit leakage to essentially the total volume of their I/O, relying on shuffling, sampling, and a carefully-chosen amount of padding, with different performance trade-offs.   \n4. an implementation of these solutions, embedded as auxiliary MapReduce jobs for Hadoop and VC3, and their performance evaluation on our sample datasets. Our results suggest that information security can be achieved for typical MapReduce jobs with a reasonable overhead ( $7\\%$ on average for our most secure so  \n\nlution, depending on the distribution of intermediate key-value pairs) and in some cases can outperform the baseline solution due to internal grouping of key-value pairs.  \n\nAlthough we focus on MapReduce, and sometimes on the details of the VC3 secure implementation of Hadoop, our results may be of interest for a larger class of data-intensive applications, such as SQL and semi-structured databases.  \n\n# 2. PRELIMINARIES  \n\nNotations. We use $A=\\langle a_{1},a_{2},...\\rangle$ to denote a list with records $a_{1},a_{2}$ etc, and $A\\|B$ to denote list concatenation.  \n\nIf record $a_{i}$ is a key-value pair then $a_{i}.\\mathsf{k e y}$ and $a_{i}$ .val denote the key and value of the pair, respectively. We let $A|_{k}$ denote the pairs of $A$ with the same key, $k$ , i.e., $A|_{k}=\\langle a_{i}\\mid a_{i}.{\\mathrm{ke}}\\rangle=k\\rangle$ . For a set $K$ of keys, we write $\\|_{k\\in K}$ to denote concatenation of lists indexed by $k$ , e.g., $\\|_{k\\in K}A\\vert_{k}$ denotes the list of records of ordered and groupe›d by $k\\in K$ . We also sometimes split $A$ into $M$ batches $A_{m}$ such that $A=\\parallel_{m\\in[1,M]}A_{m}$ . $\\mathsf{M}(a_{i})$ applies an operation $\\mathsf{M}$ on $a_{i}$ , while ${\\mathsf{M}}(A)$ applies the operation element-wise on $A$ .  \n\nCryptographic Primitives. Our solutions rely on semantically secure encryption, pseudo-random permutations, and pseudo-random functions. We use the usual cryptographic notions of negligibility and indistinguishability. As we treat cryptographic schemes as abstract building blocks, we avoid committing to either an asymptotic or a concrete security model. Similarly we keep cryptographic keys and their sizes implicit.  \n\nSemantically secure encryption [12] guarantees that every encryption of the same message is very likely to map to a different ciphertext. That is, given two ciphertexts the adversary cannot distinguish whether they correspond to two encryptions of the same message or encryptions of two different messages. We use $[p]$ to denote a semantically secure encryption of a plaintext $p$ . We sometimes overload this notation, using $[D]$ to denote an encrypted dataset $D$ , where each record may be encrypted separately.  \n\nThe second primitive, a pseudo-random permutation $\\pi$ [14] is an efficiently computable keyed permutation function. Its security property is expressed as indistinguishability from a truly random permutation. That is, if an adversary observes an output of $\\pi$ and a truly random permutation, he is not able to distinguish the two. We use $\\pi(i)$ to denote the location of the ith record according to $\\pi$ and, again overload notations, use $\\pi(D)$ to denote a dataset that contains the records of $D$ permuted according to $\\pi$ .  \n\nThe third primitive, a pseudo-random function $f$ [14], is a keyed cryptographic primitive that is indistinguishable from a truly random function with the same domain and range.  \n\nSecure Regions/Hardware. Our solutions rely on the ability to protect local processing inside secure regions. Secure regions are trusted containers of code and data that are isolated from other code in a system. Secure regions may be implemented as trusted physical machines, trusted virtual machines, or other forms of trusted execution environments such as SGX enclaves [17]. While our solutions apply independently of the specific implementation of secure regions, we outline an implementation based on SGX processors and used in our experiments. In this case, secure regions are implemented as ranges of virtual memory addresses that are protected by secure SGX processors using three mechanisms.  \n\nFirst, processors control memory accesses to the secure regions. Code inside the region may be invoked only through a call-gate mechanism that transfers control to an entry point inside the region. Code inside the region has full access to the data inside the region, but external read, write, and execute accesses to the memory region are blocked by the processor, even if they originate from code running at a high level of privilege. Thus, the software TCB in our solutions is just the code inside secure regions and, in particular, does not include the operating system or the hypervisor.  \n\nSecond, processors encrypt and protect the integrity of cache lines when they are evicted to system memory (RAM). This guarantees that the data in the regions is never in the clear outside the physical processor package. This removes a broad class of hardware attacks, such as cold boot attacks, and limits our hardware TCB to only the processor.  \n\nFinally, processors support remote attestation. When a region is created, the processor computes a cryptographic digest of the region and signs it with a secret key available only to the processor. This allows an external entity to verify that data originated from a specific secure region. We use this mechanism to establish secure channels between regions and remote systems.  \n\n# 3. MAPREDUCE  \n\nMapReduce. Let $D$ be a dataset that contains $_n$ records of equal size. Let $(\\mathsf{M},\\mathsf{R})$ be a pair of map and reduce functions defining a MapReduce job on $D$ . Let $X$ be a list of intermediate key-value pairs and $O$ be the output of the MapReduce job executed on $D$ , as explained below. Lower-case subscript for each of the datasets denotes a record of the dataset in the corresponding position (e.g., $d_{i}$ is the $i$ th record of $D$ ).  \n\n‚ M takes a record $d_{i}$ as input and outputs a list of keyvalue pairs $X_{i}$ . Let $X$ collect the key-value pairs produced by M on every record of D: X “ ›i 1,..., D . ‚ R takes as input records $X\\vert_{k}$ with key $k$ , and outputs a list of values. Hence, the output of MapReduce is $O=\\|_{k\\in K}\\mathsf{R}(X|_{k})$ where $K$ is the set of keys in $X$ .  \n\nIn cloud d›eployments, a user uploads $D$ to a cloud datacenter and later requests that the cloud provider executes jobs on $D$ by sending pairs $(\\mathsf{M},\\mathsf{R})$ . The MapReduce framework (e.g., Hadoop) is responsible for invoking $\\mathsf{M}$ on every record of $D$ in parallel and obtain $X$ as a result; grouping key-value pairs in $X$ by keys; and calling a reduce function $\\mathsf{R}$ on each resulting group.  \n\nMapReduce on Secure Hardware. We now describe adaptations to the MapReduce framework when executed on secure hardware. The high-level idea of such systems (e.g., VC3) is to store the data, intermediate key-value pairs, and output in encrypted form, and run the map and reduce functions within secure regions (see e.g., $\\S2$ ).  \n\nThe system is set up with the following changes. The user uploads an encrypted dataset $\\lfloor D\\rfloor$ to the cloud. Whenever she needs to request a run of a MapReduce job, she uses a secure channel with the secure regions to provide binaries for functions $\\mathsf{M}$ and R, numbers $M$ and $R$ , and keys for protecting the data as well as for evaluating a pseudo-random function $f:K\\rightarrow[1,R]$ . The key for accessing the dataset is reused across computations but crucially every MapReduce job uses a fresh pseudo-random function.  \n\n![](/tmp/output/113_20250326180971/images/f1897d243331008b05437aa0fd036a7e335396de91cc41e5a17a67f1e5b711a5.jpg)  \nFigure 1: Example of MapReduce on secure hardware (see $\\S3$ ) with three Mappers and three Reducers. Dashed lines indicate intermediate traffic observable by an adversary (see $\\S4_{.}$ ).  \n\nThe MapReduce framework then executes the job by invoking $M$ Mappers and $R$ Reducers. At a high level, mappers and reducers execute $\\mathsf{M}$ and $\\mathsf{R}$ within a secure region, respectively. We describe their functionality in more detail. The MapReduce framework splits the records of $D$ into $M$ batches; we write $D_{m}$ for the mth batch. Each batch is processed as follows: given the $m$ th encrypted batch $[D_{m}]$ , a mapper decrypts it and executes $\\mathsf{M}$ on each of its records. For every key-value pair $x_{j}$ with key $x_{j}.\\mathsf{k e y}=k$ produced by M, Mapper outputs a tuple $(r,[x_{j}])$ , where $r=f(k)$ is the index of a reducer. Every mapper evaluates the same pseudo-random function and thus assigns a record with the same key to the same value of $r$ . The input of the $r$ th Reducer is $(r,[X|_{r}])$ for $X|_{r}=\\|_{k\\in f^{-1}(r)}X|_{k}$ . The index $r$ hides the exact $x_{j}$ .key and the o›rdering of the $x_{j}$ , but still allows the MapReduce framework to group tuples by keys. The $r$ th Reducer is responsible for decrypting key-value pairs it receives, grouping them according to keys, executing R on each group, and outputting the encrypted results $\\left\\lfloor O_{r}\\right\\rfloor$ . Figure 1 shows an example of the system with mappers processing batches of three records and three reducers, where two keys happened to be mapped to the last reducer.  \n\nThe batch size an adversary can observe varies from system to system and may be deliberately chosen by its designers or may depend on the contingent nature of its performance characteristics. For instance, when running Hadoop unmodified in a secure region, an adversary can delay inputs to mappers to observe batches of a single record. On the other hand VC3 enforces a pre-configured batch size.  \n\n# 4. TRAFFIC ANALYSIS  \n\nIn the previous section, we described MapReduce on secure hardware using the storage and compute infrastructure of an untrusted cloud provider. By encrypting data stored or exchanged between mappers and reducers, and by executing mappers and reducers within secure regions, it makes an important step towards protecting the privacy of outsourced data and MapReduce computation.  \n\nHowever, there are many aspects of this system that are still observable in the environment where MapReduce is run and, as we show, lead to information leakage.  \n\n# 4.1 What’s the adversary?  \n\nWe first consider a wide range of attacks, and then narrow it down to the main channels we consider in the rest of the paper. (These channels suffice for our attacks, and they are simple enough to yield analytical results for our solutions.) Basically, our adversary observes runs of MapReduce jobs:  \n\nAt the system level, he may record the exchange of encrypted data, either between every node in the system (network traffic analysis) or between every node and storage (storage traffic analysis). The volume of data exchanged may be measured in bytes, pages, packets, or records. Some batching and padding may blur this side channel.  \n\nIn our examples, we suppose that the number of records can be accurately observed (or deduced) from network traffic. Within one job, as further explained in 4.2, the granularity of each observation may range from individual records to large batches. Conversely, we do not consider cache, timing and other low-level side channels against local runs of the map and reduce functions; devising countermeasures for them is an independent problem.  \n\nAt the application level, the adversary may have background knowledge about the job, its input, and its output. Information about the job itself may be readily available, or may be inferred from the shape of the traffic data. (In VC3, for instance, the code for M and R is encrypted; still, the adversary may use, e.g., their data-exchange profile, binary code size, and runtimes to guess the job being executed.)  \n\nStatistical information about the input and output data may also be common knowledge, e.g., the adversary may know the distribution of marital status.  \n\nIn our attacks, unless explicitly mentioned, we assume that the adversary knows the job and some statistics about the data, but not the content of its input and output—except for their total sizes. (Such aggregate information is in general hard to hide.)  \n\nIn our security definition, we model arbitrary background knowledge by letting the adversary choose two input datasets for which the background knowledge is the same while data contents may differ; the adversary is then challenged to infer which dataset was computed on. This more demanding definition lets us capture that the adversary cannot learn anything besides his background knowledge by observing traffic.  \n\nOur adversary may also actively interfere with job runs:  \n\nAt the system level, an active adversary may control resources and scheduling, e.g., feeding a Mapper one record at a time. However, as discussed in $\\S2$ , we assume he cannot directly alter encrypted traffic or break into secure regions.  \n\nAt the application level, he may partly choose his own input, or even his own jobs, to mount adaptive attacks. Our security definition reflects such capabilities by letting the adversary adaptively choose the jobs as it is trying to use traffic analysis to learn information about the dataset.  \n\nOur adversary may observe a sequence of jobs, on the same datasets, or related datasets. MapReduce implementations re-use inputs for multiple jobs, inasmuch as they co-locate the input batches and the mappers on the same nodes. They also avoid costly re-encryption of data between jobs. As a qualitative example, assume the adversary observes runs of a job before and after adding a single, target record in the dataset. If the job splits on an attribute with few keys and a known distribution, then the attribute for the new record can be precisely inferred.  \n\nSample Attacks. Our examples primarily target the passive adversary that observes traffic from mappers to reducers at the system level, as described in detail in §4.2. Then, in $\\S4.3$ , we show that combining these observations with background knowledge available at the application level leads to privacy leaks. Considering a stronger active adversary would only facilitate our attacks and increase their precision.  \n\nIn practice, the more selective the jobs are, the more precise information we can extract (assuming we know, or we can guess, what the job is). In the following, we mostly focus on observing jobs that split all input records depending on some of their attributes. More generally, MapReduce jobs may first filter out parts of the input records before splitting. Our attacks would similarly apply to those jobs (in particular to multiple jobs with the same filter), except that we would learn information specific to the smaller, filtered collection of inputs, rather than the whole dataset.  \n\nExample: Attacking VC3. In VC3, an adversary may gain full control of the Hadoop scheduler, enabling it, for example, to send the same input batch to multiple mappers, or to send a single input batch to a mapper; it may even cause mappers to fail or restart, enabling it to improve the accuracy of his network traffic measurements. On the other hand, the input batches (and their sizes) are GCMprotected, so the adversary cannot change them. VC3 reducers also incorporate integrity checks against any inputbatch replication: a reducer that receives intermediate keyvalue pairs from different mappers processing the same input batch will detect the attack and safely stop processing data. Hadoop tries to keep nodes stateless, hence they rarely delay sending data between batches. In VC3, mapper-reducer communications rely on stateful secure channels for the whole job; however, the adversary may send input batches one at a time, and measure how many bytes are communicated as a result of their processing.  \n\nOverall, we can thus conservatively model this adversary as ‘passive’, but able to collect precise network traffic at the granularity of individual input batches.  \n\nExample: Attacking Unmodified Hadoop. An adversary against unmodified Hadoop (running in secure regions, and encrypting all data, but without the VC3 countermeasures) may have finer control of its scheduling, for example by delaying packets in data streams, or exploiting failure-recovery to observe multiple runs of the same jobs with a different assignment of inputs to mappers, thereby collecting traffic information at a finer granularity, possibly for each record.  \n\n# 4.2 Observing Intermediate Traffic  \n\nWe model the data collected by an adversary observing MapReduce traffic, then we explain how he can use it to trace information from the output of a MapReduce job back to individual input records and how, as he observes runs of multiple jobs on the same input records, he can correlate this information between jobs.  \n\nData dependent intermediate traffic. We model observations of intermediate traffic using a matrix A with dimensions $M\\times R$ where $M$ is the number of mappers and $R$ is the number of reducers. $\\mathbb{A}[m,r]$ is the number of intermediate key-value pairs sent from mapper $m$ to reducer $r$ . Since an adversary observes input and output of every mapper and reducer, he can easily construct this matrix.  \n\nBefore analyzing how he can use A to learn more about the input dataset, we give an intuition with an aggregate MapReduce job in Figure 1. The matrix A for this job is shown below, with aggregate volumes of data sent by each mapper (right) and received by each reducer (bottom).  \n\n<html><body><table><tr><td>m/r</td><td>1 2</td><td>3</td><td></td></tr><tr><td>1</td><td>3 0</td><td>0</td><td>3</td></tr><tr><td>2</td><td>1 1</td><td>1</td><td>3</td></tr><tr><td>3</td><td>0 1</td><td>2</td><td>3</td></tr><tr><td></td><td>4 2</td><td>3</td><td></td></tr></table></body></html>  \n\nEvery Mapper reads three encrypted records, extracts a zip code and each Reducer counts the number of records per zip code. In the matrix, each entry $\\mathbb{A}[m,r]$ indicates how many intermediate key-value pairs produced by the mth mapper have zip code that was returned by the $r$ th reducer. In particular, the adversary sees that the first three records have the same zip code (43301) and the last three records do not have this zip code. Given background knowledge of the distribution of zip codes, the adversary can thus, in this case, label each column of A with a zip code. Abusing our notation, we refer to the cell Ar1, 1s as $\\mathbb{A}[1,\\mathopen{}\\mathclose\\bgroup\\left(43301^{\\prime}\\aftergroup\\egroup\\right]$ .  \n\nThe example illustrates that matrix A lets the adversary correlate input and output of a MapReduce job as long as (1) records read by a mapper can be correlated with intermediate key-value pairs in A, and (2) there is variation between values in each row and column. The first condition depends on how mappers read and write their input and output, while the second condition depends on the data.  \n\nNetwork traffic from two or more jobs can easily be combined and lead to a ‘job composition’ attack. The adversary observes a matrix A from each job and, as long as the same input data is used, he can label each input batch with the results of such inferences. For example, he can observe jobs on zip code, gender and data of birth. Sweeney [25] showed that combinations of such simple demographics often already identify people uniquely. In the rest of this section we show how the adversary can still correlate mapper’s inputs and outputs for less trivial input datasets.  \n\nGranularity: observing traffic on input batches. In general a mapper can process a sequence (or batch) of records (to amortize the cost of encryption, for example). If the mapper reads a batch, there are several ways in which it could control its I/O. For example, it could sequentially read a record and immediately return the corresponding keyvalue pair; it could buffer key-value pairs for several records and return all of them when the buffer is full (as in the VC3 implementation); or start reading the next sequence of records while still processing the first sequence.  \n\nDifferent I/O processing creates noise in the signal of the adversary when he tries to correlate the input and the output of a mapper. For example, this noise does not allow the adversary to precisely determine which record resulted in which key-value pair. However, the adversary can still correlate a batch of input records with key-value pairs, i.e., by using a time window for when records are read and intermediate key-value pairs are returned. Similar I/O buffering can be done on the reducer side. However, due to the functionality of the reducer, in some cases it has to read all its input records before returning the output.  \n\nIn our examples of information leakage, we assume that the mapper would try to protect the correlation between records it reads and intermediate key-value pairs it returns. In particular, we assume that the mapper puts a threshold on how many records it has to process in a batch before returning the output. He further permutes the intermediate key-value pairs to break I/O correlation. However, as we illustrate below, this is only a partial remedy.  \n\n# 4.3 Exploiting Intermediate Traffic  \n\nWe give concrete evidence of information leakage, both when records are processed one at a time and when they are processed in batches. In the latter case, although it is more difficult to extract information about individual records, we show that it remains possible when the input records are somewhat sorted, and that MapReduce traffic still leaks information about many statistics in the input data.  \n\nOur goal is not to uncover new facts about these datasets, readily available from their plaintext, but to show that, more surprisingly, those facts are also available to an adversary that merely observes encrypted traffic. Our experiments also suggest that naive techniques based on padding inputs and outputs would be of limited value for these datasets.  \n\nOur experiments are based on two datasets:  \n\n‚ U.S. 1990 Census Sample [16] (900 MB). The dataset contains 2.5 million personal records. Every record has 120 attributes, including the Age, Gender, POW (place of work), POB (place of birth), MS (marital status), etc. Some attributes have been discretized: for instance, Age ranges over 8 age groups, such as 20–29. ‚ New York 2013 Taxi Rides [26] (24 GB). This dataset contains records for all the taxi rides (yellow cabs) in New York city in 2013. It is split in 12 monthly segments, and each segment contains approximately 14 million records. The records have 14 attributes and describe trip details including the hashed license number, pickup date and time, drop off date and time, and number of passengers.  \n\nThe first dataset is representative of personal data commonly stored in the databases of medical institutions, insurance companies, and banks. The second dataset contains sensitive information and, despite some basic anonymization, is susceptible to inference attacks [21, 28]. Some of these attacks use MapReduce [21] to extract correlation between the rides (in plaintext). We show that the same kind of information can also be extracted by traffic analysis.  \n\nIn this section, the adversary is assumed to have the following subset of the capabilities described in 4.1. He observes only basic aggregate jobs, which all go as follows: M splits the records, with the attribute used for aggregation (e.g., the Age) as key; hence R receives all records with the same attribute value; it may return their count, or any other function of their contents. He is also assumed to have statistical information on the attribute values used for splitting (e.g., distribution of age and marital status in the U.S. and popular destinations in New York). This allows him to label columns of A with the corresponding attribute values.  \n\n![](/tmp/output/113_20250326180971/images/4309d06750b12acd8682fe71c07404dabe41403bde8d7e543f65ef0124349c3b.jpg)  \nFigure 2: Distribution of Census records across age groups (left), place of birth (center) and marital status (right), where U.S. count is trimmed.  \n\n# 4.3.1 Individual records  \n\nOur first attacks are based on observing mappers, as they consume one record at a time and immediately produce intermediate data. Hence, the intermediate-traffic matrixes have one row for each individual record and, at least for basic aggregate jobs, each row has exactly one non-zero entry. To illustrate the correlation of observations across jobs, we show that, after observing aggregate jobs on distinct attributes, the adversary is able to answer specific queries on any combination of these attributes, such as  \n\n1. Given the index of a record in a dataset, return the values of these attributes;   \n2. Test if the dataset contains a record that matches particular values for some of these attributes; and   \n3. Given the values of some of these attributes, infer the possible values of the others in the dataset.  \n\nCensus Data. For these attacks, we observe three aggregate jobs, one for the age group, one for the place-of-birth, and one for marital status. This yields three intermediatetraffic matrixes: $\\mathbb{A}_{\\mathtt{A g e}}$ , APOB and AMS with 2.5M rows each.  \n\nFigure 2 displays aggregate counts for the three jobs, i.e., the number of key-value pairs assigned to each attribute value. Up to a permutation of the columns, this is the same information as the sums of the columns in $\\mathbb{A}_{\\mathtt{A g e}}$ , APOB and AMS. The adversary can determine the key processed by every reducer in these matrices (i.e., label the columns of A) using auxiliary public information on the distribution of, for example, the age of the U.S. population.  \n\nLet us analyze the information in each matrix individually. $\\mathbb{A}_{\\mathtt{A g e}}$ gives a precise age group for every record, APOB gives a geographical region for a place of birth for every record, and AMS gives the marital status for every record. As long as all jobs processed the same dataset, the adversary can combine the information he learns across all jobs. That is if, $\\mathbb{A}_{\\mathtt{A g e}}[i,:1{-}12^{\\prime}]=1$ (overloading the reducer key with the label it processed) and $\\mathbb{A}_{\\mathtt{P O B}}[i,\\cdot\\mathrm{Africa}^{\\prime}]=1$ , then the ith record is in the age group “1–12” and was born in Africa.  \n\nThus, the adversary can directly answer queries such as   \n1. What is the marital status of person #1,326,457 in the census? Never married, since AMSr1326457, ‘Never Married’s “ 1.   \n2. Is there a person with $\\{A g e$ : 13-19, POB: Oceania, Marital Status: Divorced} in the dataset? Yes, since there is (exactly, in this case) one index $i=1$ , 005, 243 such that $\\mathbb{A}_{\\mathtt{A g e}}[i,\\overleftarrow{\\mathrm{13}}\\mathrm{-19^{\\prime}}]$ , $\\mathbb{A}_{\\mathtt{P O B}}[i$ , ‘Oceania’s and $\\mathbb{A}_{\\mathtt{M S}}\\big[i$ , ‘Divorced’s are all equal to 1.  \n\nTaxi Data. Our sample attack is based on observations of aggregate job on the pickup day, pickup location, and dropoff location. Suppose an adversary saw a person getting in a taxi on a corner of Linden Blvd and 221st Street in Queens on January 13, 2013. The adversary then looks at row indices in APickupD and APickupL that have non-zero entries for ‘Linden Blvd and 221st Street, Queens’ and ‘January 13’, There is exactly one such index in our dataset, 13, 484, 400. The drop-off location is the non-zero entry in ADropoffLris row, that is, ‘1053 Atlantic Ave, Brooklyn’.  \n\n# 4.3.2 Batch records  \n\nOur second series of attacks apply against mappers that securely process large batches of records at a time. We assume that each mapper reads all records in a batch assigned to him, applies M on each record, and returns permuted key-value pairs. Hence, observing an aggregate job yields an intermediate-traffic matrix A with fewer rows (only one for each batch). Since each job we consider has only a few keys (at most 50) we still assume that there is a reducer for every key. Hence, the adversary knows precisely which intermediate keys in the columns of matrix A produced a given output value. Thus, each row provides a histogram of the values of the attribute for all the records in the batch.  \n\nThough intuitively, batching makes it harder to extract precise information from the dataset, we show that it does not always suffice. In particular, if the data is ordered by some attribute, the information about a batch will provide information conditional on values of that attribute.  \n\nHere, we consider only the New York taxi dataset—see the full version of the paper for additional examples on batched Census data. The taxi dataset is split into batches, as follows: each batch contains ${\\sim}147\\mathrm{K}$ records of taxi rides (24Kb), with 100 batches per monthly segment.  \n\nAn adversary that observes an aggregate job on some arbitrary attribute (attr) recorded in the dataset can reliably perform the following tasks:  \n\n1. Given an aggregate count of the values of attr over the whole dataset, he can infer precise counts of attr values over smaller batches of data (for each day of the year for the taxi rides).   \n2. Given prior information about a specific record, such as its location in a dataset or the value of its attribute, he can infer other attributes for that record (i.e., the pick up day for a taxi ride.)  \n\nConsider a curious adversary who observes intermediate traffic for two aggregate jobs, on PickUpD (pick-up day of a taxi ride) and PassenN (number of passengers in a taxi ride).  \n\nLet us first describe the information available in APickUpD and APassenN. Since it is known that the data in every 100 batches corresponds to a month of the year, we look at the rows that correspond to January. In particular, we consider the same 31 rows of $\\mathtt{A_{P i c k U p D}}$ and APassenN (since there are 31 days in January). For each row, we consider its distribution across attribute values (i.e., the days of the year). As expected, there are only 31 non-zero counts in each row.  \n\nWe plot the number of key-value pairs assigned from each batch to 31 reducers in $\\mathtt{A_{P i c k U p D}}$ in Figure 3 (top). For example, $31\\%$ of the records for day ‘Jan 1, 2013’ came from the dark-blue batch. It is evident that the data is not uniformly distributed. For comparison, the bottom half of Figure 3 plots the attacker’s guess on batches distribution if he knew only the aggregate counts for day, i.e., the column sums  \n\n![](/tmp/output/113_20250326180971/images/8068a4dcf45d6068f548bdf888007b293261149c904c642a5bfb63442162e241.jpg)  \n\nFigure 3: Distribution of taxi records from 100 batches across reducers for “Aggregate by pickup day” job (top) vs. uniform, i.e., attacker’s guess without observing network traffic (bottom).  \n\n![](/tmp/output/113_20250326180971/images/85cdff1a10cb2d4b367535271a0a79e427dd0f519676b569eee41948e96518fa.jpg)  \nFigure 4: Passenger counts across six days and the expected count if the attacker observed only aggregates (bars for single passenger rides are trimmed).  \n\nof $\\mathtt{A_{P i c k U p D}}$ . It appears that the data is sorted according to individual days and the 100 batch split divides some of the days in several batches. Furthermore, we note that the original dataset was probably slightly shuffled, however, the day order is still preserved. In particular we see that $98\\%$ of Batch 7 (purple batch) was assigned to Jan 3.  \n\nIn Figure 4 we capture the distribution of some of the rows across all values in APassenN. Since we know that each batch contains rides of only a few days, we label the batches with the day that is represented the most in that batch. There are 6 passenger counts (there are 0 count that we ignore since it is also under represented and suggests an error in the dataset). If the adversary sees the output of the aggregate job for PassenN, he knows precisely the passenger count. If not, some of the labels are easily inferable (e.g., 4 passenger rides are the most rare while 1 person ride is most popular). Given APickupD and APassenN, the adversary can thus answer the following queries:  \n\n‚ What is the number of passengers on Friday nights?  \n\nThis information is available in Figure 4 (or the full version for all days). The adversary simply looks up the bars that corresponds to Fridays. Note that this information is more precise than what the adversary can learn from aggregate information for passenger number (i.e., the last bars of Figure 4). If the adversary is a competitive taxi company these counts could be used for better scheduling of own cars across the week. ‚ Did someone take a taxi ride from Canarsie Park in Brooklyn in January? When exactly? The adversary uses combined traffic from the two aggregate jobs. Luckily, there is only one record assigned to pick up location in Canarsie Park in the 100 batches of January. Moreover, the batch that includes the record has most of its taxi rides from January 19 (79K), January 18 (39K), January 20 (24K), and January 6 (6K), with only 11 rides left over 6 other days. Hence, it is most likely that this ride happened on January 19 (and in fact, it did).  \n\nFinally, we point out that the difference in the distribution of keys in batches is high (e.g., a batch for January 1 vs. a batch for January 30 differ by 20K rides for 5 and 6 passengers). Similarly, the number of key-value pairs processed by reducers is also different (e.g., reducers that count rides with one passenger vs. five passengers). Hence, padding the inputs to some number of keys to protect the identity of each reducer may become very expensive for long tail distributions. Even for the taxi dataset it would require mappers to pad their traffic to each reducer to 125K key-value pairs. Moreover, it is not clear how mappers can fix this level of padding without communicating with each other.  \n\n# 5. SECURITY DEFINITIONS  \n\nHow can we design systems that are resilient to such traffic analysis attacks? To be able to evaluate protection mechanisms, this section brings forward different security notions for MapReduce on secure hardware. Our definitions are defined as games between a challenger and an adversary: the adversary chooses two datasets that share some arbitrary background information available to the adversary, the challenger chooses one of them and lets the adversary adaptively request jobs on it. (For instance, arbitrary background information may include “knowledge” of the output of a job computation; this is captured by the adversary choosing two inputs that yield this output.)  \n\nDefinition 1 (MapReduce Game). At the start of the game, the adversary picks two datasets $D^{0}$ and $D^{1}$ and fixes the number M and $R$ of mappers and reducers. He is then given access to $[D]$ , where $D=D^{0}$ or $D=D^{1}$ , and can observe the run of multiple MapReduce jobs $(\\mathsf{M},\\mathsf{R})$ of his choice. He observes their full network traffic to guess whether $D=D^{0}$ or $D=D^{1}$ . The adversary’s advantage of winning the game is his probability of guessing correctly minus $\\frac{1}{2}$  \n\nThe MapReduce game models that the traffic observed for any two datasets $D^{0}$ and $D^{1}$ should appear to be the same and not reveal anything about the input data. As argued in $\\S4.1$ , the integrity checks of secure MapReduce systems such as VC3 prevent active interference of the adversary on the network; this enables us to model their adversaries as passive in that sense.  \n\nAs stated, the MapReduce game is trivial to win, e.g., if $\\vert[D^{0}]\\vert\\ne\\vert[D^{1}]\\vert$ , and, as discussed in §4.1, it is reasonable to assume that the adversary may learn the size of $D$ and a few other aggregate values. We give two variants of our definition, of increasing strength, that specify what each job is allowed to leak about the data as requirements on $D^{0}$ and $D^{1}$ , expressed on their matrices A $\\mathrm{^0}$ and A1. Recall that we associate the matrix A to a dataset $D$ and a MapReduce job $(\\mathsf{M},\\mathsf{R})$ such that each cell represents the number of intermediate key-value pairs from the $m$ th batch to key $k$ , that is, $\\mathbb{A}[m,k]=|\\mathsf{M}(D_{m})|_{k}|$ (using notations from §3).  \n\nDefinition 2. Correlation hiding requires that no efficient adversary has more than a negligible advantage in winning the MapReduce game as long as $|D^{0}|=|D^{1}|$ and, for every MapReduce job $(\\mathsf{M},\\mathsf{R})$ the adversary picked during the game, the following holds:  \n\n1. Mappers produce the same amount of output, i.e., for all $m\\in[1,M]$ , we have $\\begin{array}{r}{\\sum_{k}\\mathbb{A}^{0}[m,k]=\\sum_{k}\\mathbb{A}^{1}[m,k]}\\end{array}$ . 2. Reduce functions take the  same amount o f input, i.e., there exists a permutation $\\sigma$ on the keys such that, for all keys $k\\in K$ , we have $\\begin{array}{r}{\\sum_{m}\\mathbb{A}^{0}[m,k]=\\sum_{m}\\mathbb{A}^{1}[m,\\sigma(k)]}\\end{array}$ 3. The output size of the reduce function $\\mathsf{R}$ is constant.  \n\nThe permutation in Requirement 2 accounts for the fact that the adversary only observes encrypted keys. The definition does not leak the details of $\\mathbb{A}[m,k]$ , hence it hides which records have common keys, preventing the composition attack in §4.3. It is applicable to map functions M that project a key and a value from a record; typically to allow a reducer function $\\mathsf{R}$ to then compute some aggregate statistic about the values grouped by the key: Requirement 1 and 3 are clearly met by such functions, and Requirement 2 is a statistic, such as those in Figure 2, about the distribution of keys in $D$ that may often already be publicly known.  \n\nMapReduce jobs for which this definition does not do well are functions $(\\mathsf{M},\\mathsf{R})$ that perform filtering. A map function M that discards all people living in Oceania from processing leaks this attribute value trivially if no record was returned. Our second definition protects even against such attacks.  \n\nDefinition 3. Strong hiding requires that no efficient adversary has more than a negligible advantage in winning the MapReduce game as long as $|D^{0}|=|D^{1}|$ and, for every MapReduce job $(\\mathsf{M},\\mathsf{R})$ the adversary picked during the game, the following holds:  \n\n1. The volume of intermediate data is the same: $|X^{0}|=$ $\\vert X^{1}\\vert$ ; and the number of keys is the same: $|K^{0}|=|K^{1}|$ ;   \n2. The number of records for the most popular key is the same: $\\begin{array}{r}{\\operatorname*{max}_{k}(\\sum_{m}\\mathbb{A}^{0}[m,k])=\\operatorname*{max}_{k}(\\sum_{m}\\mathbb{A}^{1}[m,k])}\\end{array}$ .   \n3. The output size of the reduce function $\\mathsf{R}$ is constant.  \n\nThe two security definitions differ in their requirements on $D^{0}$ and $D^{1}$ , which restricts the type of data and sequence of $(\\mathsf{M},\\mathsf{R})$ computations the definition can protect. The latter definition is strictly stronger, since agreement on the cardinality of keys implies agreement for the most popular key, as well as on the sizes of $|O|$ and $|X|$ . Requirement 2 allows us to leak the number of records for the most popular key, which is a lower bound on the traffic received by the reducer that must process all these records.  \n\n# 6. SHUFFLE-IN-THE-MIDDLE SOLUTION  \n\nOur first solution prevents intermediate traffic analysis on a job by securely shuffling all the key-value pairs produced by the Mappers and consumed by the Reducers. Hence, the adversary may still observe volume of intermediate traffic for each mapper and for each reducer, but it cannot trace traffic from reducers back to individual mappers.  \n\n# 6.1 Method  \n\nWe present our solution using a data shuffle algorithm as a black box that, given $[X]$ and a pseudo-random permutation $\\pi$ on $1\\dots|X|$ , returns $\\lfloor\\pi(X)\\rfloor$ . We then describe our implementation of the Melbourne Shuffle algorithm [20] using MapReduce jobs (§6.2).  \n\nLet $X_{\\mathsf{M}}$ be the output of the mappers, and $X_{{\\mathsf{R}}}$ the output of the shuffle passed to the reducers. $X_{\\mathsf{M}}$ and $\\pi$ are given as input to a data shuffle job to permute the records. The output $X_{{\\mathsf{R}}}$ of the shuffle is then grouped and sent to the Reducers by the MapReduce framework, as before.  \n\nIn more details, each Mapper proceeds similarly to §3, except for the content of its output. Recall that a mapper in $\\S3$ returned a tuple $(r,[x_{j}])$ where $r$ is the index of the Reducer that processes records with keys $k$ such that $f(k)=$ $r$ . Instead, we modify Mapper to return $([r],[x_{j}])$ , so that $[X_{\\mathsf{M}}]$ now consists of pairs of ciphertexts.  \n\nThen, the data shuffle is invoked on $[X_{\\mathsf{M}}]$ with a small adaptation: instead of simply outputting $\\left[\\pi(X_{\\mathsf{M}})\\right]$ , the last step is modified to return the decrypted value of $r$ , while re-encrypting $[x_{j}]$ . Hence, the output of the data shuffle is a list of tuples $(r,[x_{j}])$ that is a random permutation of the intermediate key-value pairs of the original MapReduce job.  \n\nThe rest of the protocol is the same as the one for MapReduce on Secure Hardware in $\\S3$ . The MapReduce framework (e.g., Hadoop) groups key-value pairs according to their reducer index $r$ and invokes Reducer on each group.  \n\nTheorem 1. If r¨s is a semantically secure encryption scheme, $f$ is a pseudo-random function and $\\pi$ is a pseudorandom permutation, then the Shuffle-in-the-Middle solution is correlation hiding (Definition 2).  \n\nThe proof of the theorem is in the full version of the paper.  \n\n# 6.2 Data Shuffle  \n\nGiven an encrypted dataset $[D]$ as input, the shuffle yields some permutation $\\left[\\pi(D)\\right]$ as output. Since $D$ can be large, we want an efficient implementation of the shuffle within a secure MapReduce framework. Moreover, we want to ensure that the observations about the network traffic that the adversary can make (as described in §4.2) do not leak any information about the data (except its size) and the shuffle permutation $\\pi$ . Hence, an adversary that observes a job implementing either $\\pi^{0}$ and $\\pi^{1}$ , should not be able to say whether the output is an encryption of $\\pi^{0}(D)$ or $\\pi^{1}(D)$ .  \n\nSorting networks [4] provide the security guarantees above: their network traffic is independent of the data. However, since these algorithms perform sorting, they incur a logarithmic depth computational overhead (plus additional constants). Instead, for our solutions, we choose the parallel version of the Melbourne Shuffle [20], which offers the same security guarantees, and we implement it as two successive runs of the MapReduce job described below. We refer to [20] for a detailed analysis of the algorithm.  \n\nThe input $D$ is viewed as a set of disjoint input batches where records are split sequentially according to their index in $D$ . The output $\\pi(D)$ is also viewed as a set of disjoint output batches but where records are split sequentially using their permutations tags. The task of the mappers is to distribute records from input batches into correct output batches such that an observer of traffic cannot tell which output batch an input record was assigned to. To this end, a mapper sends the same number of records to each output batch (padding with dummy records if needed). A reducer then reads a padded output batch, removes the dummies and places records in their correct locations within the batch. The number of real and dummy records assigned to each output batch is the same across batches, hence, all reducers read and output the same number of records, thereby revealing nothing about $\\pi$ .  \n\nIn more details, each mapper takes as input a permutation $\\pi$ (e.g., it takes a key to a pseudo-random permutation) and a batch of $b$ records, and outputs a bin of max records (for some fixed number $\\mathsf{m a x}>b/R$ ) for each reducer, that is, $R$ bins in total. The mapper assigns each record $d_{j}$ (where $j$ is the index of the record in $D$ ) in the batch to one of the $R$ bins according to its permutation tag: record $d_{j}$ goes to bin $r=|\\pi(j)/R|$ . If a bin is assigned more than max records, the algorithm aborts. Otherwise, the mapper pads each bin to max records, by adding dummies with the same size as genuine records, then it encrypts and outputs each bin as a single intermediate value with key $r$ .  \n\nEach reducer takes a list of bins (one from each mapper), removes the dummies, sorts the remaining records by their permutation tags, removes the tags, and outputs the result.  \n\nA single run of the MapReduce job above will fail on some permutations, namely those where a mapper assigns more than max records to the same reducer. For example, if $\\pi$ is the identity function, the job will fail unless $\\mathbf{max}\\geqslant b$ . To remedy this while keeping max small, two successive MapReduce jobs are invoked: the first job is for a uniformly random permutation $\\rho$ and the second one is for the target permutation $\\pi$ . Although these invocations may still fail, this happens rarely. Moreover, the analysis of [20] shows that success and failure of the shuffles depends only on $\\rho$ , hence, it leaks no information about the actual input and output of the algorithm. Besides, max can be carefully chosen to control the probability of failure: on average, each bin should get $b/R$ records, and balls-and-bins analysis tells us how much to over-provision as we choose max.  \n\n# 7. SHUFFLE & BALANCE SOLUTION  \n\nThe Shuffle-In-The-Middle described in $\\S6$ prevents the adversary from observing the volume of data exchanged between individual Mappers and Reducers (the matrix A). However, the adversary still observes the number of records each Mapper produces and the distribution of encrypted keys.  \n\nOur second solution meets our stronger Definition 3 by evenly distributing the intermediate traffic sent from each mapper to each reducer. It preserves the data parallelism of MapReduce, and may even improve its performance by facilitating resource allocation and scheduling. But it requires a more precise load-balancing than what is typically achieved by MapReduce implementations.  \n\n# 7.1 Overview  \n\nWe are seeking solutions that fit into existing MapReduce implementations, which precludes a complete redesign of mappers with better balancing properties. Instead, we use preliminary MapReduce jobs to plan how to balance (and pad) the intermediate key-value pairs for the ‘main’ job. We split this pre-processing into offline and online jobs. The offline stage runs on the input data (once for all jobs) and randomizes the ordering of the input records. This erases any correlations between the ordering of inputs and the values of their attributes (as those exploited in §4.3.2), and ensures that all mappers produce the same distribution of key-value pairs. This stage may be implemented by a shuffle (§6.2) or, pragmatically, as the user uploads her input data to the cloud.  \n\nThe online stage is job specific; it samples the input data to collect statistics about the keys produced by mappers, in order to balance them evenly between reducers and to estimate (with high probability) an upper bound on the number of key-value pairs sent by each mapper to each reducer. Let $\\mathsf{M}$ and $\\mathsf{R}$ be the map and reduce functions of the job and $R$ its number of reducers.  \n\n1. We first run M and $\\mathsf{R}$ on a fixed sample of the randomized input. We collect statistics on its intermediate keys: a list of pairs $(k_{1},f_{1}),(k_{2},f_{2}),\\ldots,(k_{l},f_{l})$ where $k_{i}$ ranges over the sampled keys and $f_{i}$ is the fraction of key-value pairs in the sample with key $k_{i}$ . This list enables us to estimate the distribution of keys for the whole input dataset, notably its most popular key. We also determine the total number of key-value pairs returned for the sample size and the constant output size $\\ell$ of $\\mathsf{R}$ . This task is easily expressed as a small job whose traffic pattern depends only on the size of the sample. The statistics we collect are reminiscent of those maintained in databases to optimize, for instance, joins on record fields; they may similarly be cached and shared between jobs that map data on the same attributes.   \n2. We generate a key assignment for the job: a function from all (potential) intermediate keys to $1..R$ , intended to balance $\\mathbb{A}[m,r]$ by grouping keys so that every reducer gets roughly the same number of records, as detailed in $\\S7.2$ . We also estimate a safe upper bound on the fraction of traffic sent from any mapper to reducer $r$ and an upper bound on the number of different keys assigned per reducer. Our algorithms are detailed in $\\S7.2$ .  \n\nThe ‘main’ job then runs, essentially unchanged, except that (1) every mapper uses the resulting assignment to map keys to reducers, instead of the random, uniform intermediate partition in the base solution; (2) every mapper finally sends dummy key-value pairs to every reducer, up to its upper bound estimate; and (3) every reducer silently discards intermediate dummies, and pads its output with dummy values up to its upper bound estimate.  \n\nAs an optional, post-processing stage, we may use a shuffle on the reducer outputs, to discard their dummy values and erase any correlation between key frequencies and output ordering, or pragmatically leave this simple task to the user.  \n\nDefinition 3 leaks the exact values of $\\begin{array}{r}{\\operatorname*{max}_{k}(\\sum_{m}^{\\i}\\mathbb{A}[m,k])}\\end{array}$ , $|X|$ , and the number of keys in the dataset, whereas our solution leaks these values as observed in a random sample. How do our estimates relate to the exact values for the dataset? An estimate, with certain confidence, yields a range in which the exact value lies. Since our sample is chosen at random, the estimates depend on these three values and our target confidence level, but also on the actual records in the sample. To meet the definition, we formally require that statistics to be collected on the whole input dataset. We note, however, that for large shuffled datasets, even relatively small samples already provide excellent estimates.  \n\n# 7.2 Mapping vs Bin-Packing  \n\nIn this section, we explain how we use the statistics collected in the online stage to estimate upper bounds on the number of key-value pairs with the same key and the total number of keys, and to produce a secure balanced assignment. (Intuitively, our sample provides precise frequencies for the most frequent keys, but may miss many infrequent keys.)  \n\nWe first suppose that the statistics give us exact information on the key distribution, notably on the largest fraction of key-value pairs with the same key (the maximal value of the fractions $f_{i}$ above, written $\\alpha$ in the following) and we explain how to allocate sufficient bandwidth between mappers and reducers to fit any distribution with the same $\\alpha$ . (Recall that Definition 3 enables us to leak only the maximal value of $f_{i}$ , not the detailed key distribution.) We will then justify our use of estimates instead of exact values.  \n\nOur problem, at heart, is an instance of bin packing, so we first review bin packing basics before giving our algorithm.  \n\nBin packing. The (offline) bin packing instance is expressed in terms of a fixed bin capacity $c$ and a list of items, each with a weight at most $c$ . (In our case, a key is viewed as an item and its frequency as its weight.) The goal of bin packing algorithms is to minimize the number of bins $N$ needed to allocate all items without an overflow. Since the offline bin-packing problem is NP-Complete, approximation algorithms, such as First Fit Decreasing (FFD) [8], return both a number of bins and a guarantee on how far it can be from the optimum in the worst case. The FFD algorithm places items in decreasing weight order, allocating new bins on demand: it places the heaviest item in the first bin, then proceeds with the next item and tries to place it into one of the open bins (i.e., the bins that already have items) without exceeding its capacity. If there is no space left in any open bin, it places the item in a new bin.  \n\nBin Packing, Obliviously. Our problem is more general: given only some maximal item weight $\\alpha$ , we must find a bin capacity $c$ and an upper bound on the number of bins $N$ so that FFD packing will succeed on any weighted list of items with maximal weight $\\alpha$ .  \n\nIn the general case, we choose $\\begin{array}{r}{N=\\frac{2}{\\alpha}-1}\\end{array}$ and $c=\\alpha$ . (The full paper justifies these choices.) Since the weights of all items sum up to $^{1}$ , $N c-1$ is the overhead of the solution in terms of dummy key-value pairs that have to be added to fill in the bins. Hence, the values of $N$ and $c$ above yield an overhead of $\\mathrm{1}-\\alpha$ . We can reduce the overhead above in several special cases; for example:  \n\n‚ If $\\begin{array}{r}{\\alpha\\ll\\frac{1}{R}}\\end{array}$ , then we may pick $N=R$ and $\\begin{array}{r}{c=\\frac{1}{R}+\\alpha}\\end{array}$ which yields a low overhead of $\\alpha R$ . ‚ If $\\alpha\\geqslant{\\frac{1}{2}}$ , we have at least one very large reducer of capacity $\\alpha$ and everything else will fit into a second, smaller reducer of capacity $1-\\alpha$ . In this special case, there is no actual need for FFD.  \n\nThe general and special cases of fixing $N$ and $c$ ensure that, from a traffic analysis viewpoint, the number and capacities of bins (which, as described next, entirely determine the matrix A for the job we protect) depend only on $\\alpha$ .  \n\nBin Packing Keys to Reducers. Once $c$ is fixed, we are ready to bin-pack the distribution of keys we have sampled, and to generate our (secret) assignment for the main job.  \n\nTo this end, we add two dummy keys with weight 0, for the smallest and largest keys (if not already present in the sample). We partition the domain of all intermediate keys into intervals, such that the total weight of all the keys in any given interval is bounded by $\\alpha$ . Hence, there is at least one interval that contains one single key, with maximal weight $\\alpha$ . The inclusion of dummy keys ensures that assignment is a total function, even on keys that we have not sampled. We then sort these intervals by decreasing weight and run the FFD algorithm on them (assured that at most $N$ bins will be used) to get a mapping between key intervals and bins.  \n\nWe independently distribute $N$ bins between our $R$ reducers, such that each reducer gets at most $\\lceil N/R\\rceil$ bins. Hence, some reducers may get less than $\\lceil N/R\\rceil$ bins, or no bins at all if $N<R$ . Finally, we use this (public) mapping and the (secret) FFD output to produce an assignment that maps each key interval to the reducer of the bin it has been placed into.  \n\n# 7.3 Padding Traffic to Fixed Sizes  \n\nIntermediate Key-Value Pairs. We select a level of padding for the traffic sent from each mapper to each reducer based on two considerations: we must hide the actual number of key-value pairs received by each reducer—that is, the actual usage of each bin we have allocated—by filling those bins with dummies; and we must over-provision to accommodate (with high probability) for any difference in key distribution between the sample and the dataset, and between the dataset and the output of each mapper. To this end, the assignment is supplemented with padding target, to be used by every mapper to compute the apparent number of intermediate key-value pairs it must send to every reducer (as a function of the size of its input). To set those targets, we use parameter estimation for $\\alpha$ [18, Chapter 4.2]. We treat the number of key-value pairs to be sent to the rth reducer as a random variable and apply Chernoff bounds [18, Chapter 5.4] to accommodate variations in mapper inputs.  \n\nInterestingly, the resulting matrix of observable intermediate traffic A for the main job is not necessarily uniform, as mappers may process batches of different sizes and reducers may process different numbers of bins, but this matrix depends only on $\\alpha$ , $R$ , and the sizes of the mapper inputs.  \n\nReducer Output. Preventing traffic analysis on the job output is simpler. We set rkey to bound the maximum number of keys that may be allocated to a single reducer given $R$ , $|K|$ and $\\alpha$ . We count the maximum number of rare keys that may be assigned to any single bin, assuming that large keys are distributed elsewhere. In particular, we set it to $\\mathsf{r k e y}=\\vert K\\vert-\\vert1/\\alpha\\vert+1$ . Then, for every bin assigned to a reducer, the reducer output is padded up to ${\\sf r k e y}\\times\\ell$ .  \n\nTheorem 2. If r¨s is a semantically secure encryption scheme and the permutations $\\pi$ and $\\pi^{\\prime}$ used in pre- and (optional) post-processing are pseudo-random, then the Shuffle & Balance solution is strongly hiding (Definition 3).  \n\nThe proof of the theorem is in the full version of this paper. Our solution hides any distribution of keys with maximum frequency $\\alpha$ , but it does reveal $\\alpha$ . This is justified, because at least one Reducer must process all the key-value pairs for the most frequent key. However, this can be mitigated (notably when $\\begin{array}{r}{\\alpha\\ll\\frac{1}{R}}\\end{array}$ ) by increasing $\\alpha$ before computing $c$ .  \n\nTable 1: Run times for Shuffle-in-the-Middle (S).   \n\n\n<html><body><table><tr><td>DataSet/Job</td><td>Base</td><td>Run time (Shuffle)</td></tr><tr><td>Census /Ageg grouped</td><td>20</td><td>91 (25)</td></tr><tr><td>Taxi Jan/PassenN</td><td>39</td><td>122 (38)</td></tr><tr><td>Taxi Jan/PickupD</td><td>40</td><td>131 (43)</td></tr></table></body></html>  \n\nTable 2: Run times for Shuffle & Balance (S) where PassenN-1 aggregates passenger counts without the most popular key.   \n\n\n<html><body><table><tr><td rowspan=\"2\">Attrib</td><td colspan=\"3\">Taxi Jan 2.5GB</td><td colspan=\"3\">TaxiJan-Apr (10GB</td></tr><tr><td></td><td>[yl</td><td>Run time (xBase)</td><td></td><td>[K]</td><td>Run time (xBase)</td></tr><tr><td>PassenN</td><td>.71</td><td>6</td><td>45 (1.01)</td><td>.71</td><td>6</td><td>61 (0.93）</td></tr><tr><td>PickUpD</td><td>.038</td><td>31</td><td>48 (1.12)</td><td>.01</td><td>120</td><td>78 (1.21)</td></tr><tr><td>PassenN-1</td><td>.47</td><td>5</td><td>43 (1.09)</td><td>.45</td><td>5</td><td>55 (1.06)</td></tr></table></body></html>  \n\n# 8. EVALUATION  \n\nWe have evaluated our framework using a local Hadoop cluster of 8 workstations connected with a Netgear GS108 1Gbps switch. Our nodes ran under Microsoft Windows Server 2012 R2 64-Bit on workstations with a 2.9 GHz Intel Core i5-4570 (Haswell) processor, 8 GB of RAM, and a 250 GB Samsung 840 Evo SSD. We implemented our solutions in Java for experiments on plain Hadoop and in C++ for VC3 experiments, which use AES-GCM encryption implemented with AES-NI instructions and a software emulator for SGX.  \n\nWe perform experiments on the two datasets presented in 4.3: a census data sample (900 MB) and the New York taxi rides (2.5 GB per month). We perform two types of jobs: aggregate and aggregate-filter, where the latter is an aggregate over records filtered by some parameter. The baseline run times correspond to the initial job on Hadoop without protection. The reported numbers are averaged over 5 runs.  \n\nThe run times for the Shuffle-in-the-Middle solution in Java are summarized in Table 1. This experiment involves 4 MapReduce jobs: for mapping, shuffling twice, and reducing. Hence, no batching and parallelization is enabled and all jobs are treated sequentially. In contrast, in the base execution of this job, grouping of intermediate pairs by keys starts as soon as mappers produce some output. Starting the shuffle as soon as the map job outputs its first keyvalue pairs may reduce the I/O overhead (similarly starting sorting keys before the last shuffle finishes). Shuffling costs highly depend on the size of the data; for passenger count a value is simply a number vs. a date for pickup date job.  \n\nNext we measure the run times of our Java implementation for the Shuffle & Balance solution of the Taxi dataset of size 2.5 GB (Jan) and 10 GB (Jan-Apr) with $R=15$ . The results for the online phase for a randomized taxi dataset are presented in Table 2. For each job we show the frequency of the most popular key and the number of keys. Shuffle $\\&$ Balance is more efficient than our first solution, assuming one can run the jobs on shuffled data: performance overhead increases on average by $7\\%$ . In one example, our solution even outperforms the baseline. This is due to the smaller number of key-value pairs returned to the system by the mappers and, hence, lower overhead for the framework to group them together. Recall that our solution pre-groups values with the same key during bin packing, thereby using a smaller number of keys but larger values.  \n\nFinally, we implemented the Melbourne Shuffle as two runs of the MapReduce job presented in §6.2, both in Java and in C++ for VC3. For the Census and Taxi Jan input datasets, our Java implementation takes 76s and 122s, respectively, and VC3 takes 160s and 153s, respectively. Recall that this phase is run once per dataset, and not once per every job.  \n\n# 9. RELATED WORK  \n\nSeveral systems protect confidentiality of data in the cloud. CryptDB [22] and MrCrypt [27] use partial homomorphic encryption to run some computations on encrypted data; they neither protect confidentiality of code, nor guarantee the integrity of results. On the upside, they do not use trusted hardware. TrustedDB [3], Cipherbase [2], and Monomi [29] use trusted hardware to process database queries over encrypted data, but do not protect the confidentiality and integrity of all code and data. Haven [5] can run databases on a single machine.  \n\nAll systems above are vulnerable to side-channel attacks. For example, Xu et al. [31] show how side-channel attacks can be exploited in systems such as Haven where an untrusted operating system controls page faults. We also refer the reader to [31] for an overview on side-channel attacks.  \n\nSeveral security-enhanced MapReduce systems have been proposed. Airavat [23] defends against possibly malicious map function implementations using differential privacy. SecureMR [30] is an integrity enhancement for MapReduce that relies on redundant computations. Ko et al. propose a hybrid security model for MapReduce where sensitive data is handled in a private cloud while non-sensitive processing is outsourced to a public cloud provider [15]. PRISM [6] is a privacy-preserving word search scheme for MapReduce that utilizes private information retrieval methods.  \n\nNayak et al. [19] propose a programming model for secure parallel processing of data represented as a graph using oblivious sorting and garbled circuits. Goodrich and Mitzenmacher [13] describe a simulation of MapReduce that resembles a sequential version of our Shuffle-in-the-Middle solution using a sorting network instead of a shuffle to protect against traffic analysis. This method can be parallelized using a step from 6 where oblivious sorting uses a reducer number (computed as a pseudo-random function of each key) to sort key-value pairs and returns reducer keys in the clear. In independent parallel work, Dinh et al. [9] also consider securing MapReduce using a mix network to shuffle traffic between mappers and reducers. The three solutions above rely either on oblivious sort or mix network, and thus incur a logarithmic depth overhead. In comparison, our use of the Melbourne Shuffle in our first solution, Shuffle in the Middle, requires only two additional map-reduce jobs, and incurs a constant depth overhead. Besides, our second solution, Shuffle & Balance, dominates the first, even with the Melbourne Shuffle: the security guarantees are stronger (it hides key distributions, mapper output sizes, and reducer input sizes) and incurs a much smaller overhead (§8).  \n\nOblivious RAM (ORAM) [11] is a general, well-studied technique for protecting computations against memory traffic analysis. Though ORAMs are becoming more efficient, they incur a logarithmic overhead on every access and do not hide I/O volume. Moreover most ORAMs, except for the recent theoretical work by Boyle et al. [7] with polylogarithmic access overhead, are intrinsically sequential.  \n\n# 10. REFERENCES  \n\n[1] Apache Software Foundation. Hadoop. http://wiki.apache.org/hadoop/, 15/05/15.   \n[2] A. Arasu, S. Blanas, K. Eguro, R. Kaushik, D. Kossmann, R. Ramamurthy, and R. Venkatesan. Orthogonal security with Cipherbase. In Conference on Innovative Data Systems Research (CIDR), 2013.   \n[3] S. Bajaj and R. Sion. TrustedDB: A trusted hardware-based database with privacy and data confidentiality. Knowledge and Data Engineering, IEEE Transactions on, 26(3):752–765, March 2014.   \n[4] K. E. Batcher. Sorting networks and their applications. In Proc. 1968 Spring Joint Computer Conf., pages 307–314. AFIPS Press, 1968.   \n[5] A. Baumann, M. Peinado, and G. Hunt. Shielding applications from an untrusted cloud with haven. In USENIX Symposium on Operating Systems Design and Implementation (OSDI), 2014.   \n[6] E.-O. Blass, R. Di Pietro, R. Molva, and M. Onen. Prism—privacy-preserving search in MapReduce. In S. Fischer-Hubner and M. Wright, editors, Privacy Enhancing Technologies, volume 7384 of Lecture Notes in Computer Science. Springer Berlin Heidelberg, 2012.   \n[7] E. Boyle, K.-M. Chung, and R. Pass. Oblivious parallel RAM. Cryptology ePrint Archive, Report 2014/594, 2014. http://eprint.iacr.org/.   \n[8] E. Coffman Jr., J. Csirik, G. Galambosa, S. Martello, and D. Vigo. Bin packing approximation algorithms: Survey and classification. In P. M. Pardalos, D.-Z. Du, and R. L. Graham, editors, Handbook of Combinatorial Optimization, pages 455–531. Springer New York, 2013.   \n[9] A. Dinh, P. Saxena, C. Ee-chien, Z. Chunwang, and O. B. Chin. M2r: Enabling stronger privacy in mapreduce computation. In 24th USENIX Security Symposium (USENIX Security 15), Washington, D.C., Aug. 2015. USENIX Association.   \n[10] C. Gentry. Fully homomorphic encryption using ideal lattices. In Proceedings of the Forty-first Annual ACM Symposium on Theory of Computing, STOC ’09, pages 169–178, New York, NY, USA, 2009. ACM.   \n[11] O. Goldreich and R. Ostrovsky. Software protection and simulation on oblivious RAMs. J. ACM, 43(3):431–473, 1996.   \n[12] S. Goldwasser and S. Micali. Probabilistic encryption. J. Comput. Syst. Sci., 28(2):270–299, 1984.   \n[13] M. Goodrich and M. Mitzenmacher. Privacy-preserving access of outsourced data via oblivious RAM simulation. In L. Aceto, M. Henzinger, and J. Sgall, editors, International Colloquium on Automata, Languages and Programming (ICALP), volume 6756 of Lecture Notes in Computer Science, pages 576–587. Springer Berlin Heidelberg, 2011.   \n[14] J. Katz and Y. Lindell. Introduction to Modern Cryptography. Chapman and Hall/CRC Press, 2007.   \n[15] S. Y. Ko, K. Jeon, and R. Morales. The Hybrex model for confidentiality and privacy in cloud computing. In USENIX Workshop on Hot Topics in Cloud Computing (HotCloud), 2011.   \n[16] M. Lichman. UCI machine learning repository, 2013.   \n[17] F. Mckeen, I. Alexandrovich, A. Berenzon, C. Rozas, H. Shafi, V. Shanbhogue, and U. Savagaonkar. Innovative instructions and software model for isolated execution. In Workshop on Hardware and Architectural Support for Security and Privacy (HASP), 2013.   \n[18] M. Mitzenmacher and E. Upfal. Probability and Computing: Randomized Algorithms and Probabilistic Analysis. Cambridge University Press, New York, NY, USA, 2005.   \n[19] K. Nayak, X. S. Wang, S. Ioannidis, U. Weinsberg, N. Taft, and E. Shi. GraphSC: Parallel secure computation made easy. In IEEE Symposium on Security and Privacy, 2015.   \n[20] O. Ohrimenko, M. Goodrich, R. Tamassia, and E. Upfal. The melbourne shuffle: Improving oblivious storage in the cloud. In J. Esparza, P. Fraigniaud, T. Husfeldt, and E. Koutsoupias, editors, International Colloquium on Automata, Languages and Programming (ICALP), volume 8573 of Lecture Notes in Computer Science, pages 556–567. Springer Berlin Heidelberg, 2014.   \n[21] V. Pandurangan. On taxis and rainbows: Lessons from NYC’s improperly anonymized taxi logs, 2014.   \n[22] R. A. Popa, C. M. S. Redfield, N. Zeldovich, and H. Balakrishnan. CryptDB: Protecting confidentiality with encrypted query processing. In Proceedings of the Twenty-Third ACM Symposium on Operating Systems Principles, SOSP ’11, pages 85–100, New York, NY, USA, 2011. ACM.   \n[23] I. Roy, S. T. Setty, A. Kilzer, V. Shmatikov, and E. Witchel. Airavat: Security and privacy for MapReduce. In USENIX Symposium on Networked Systems Design and Implementation (NSDI), 2010.   \n[24] F. Schuster, M. Costa, C. Fournet, C. Gkantsidis, M. Peinado, G. Mainar-Ruiz, and M. Russinovich. VC3: Trustworthy data analytics in the cloud using SGX. In IEEE Symposium on Security and Privacy, 2015.   \n[25] L. Sweeney. Simple demographics often identify people uniquely. Carnegie Mellon University, Data Privacy Working Paper 3, 2000.   \n[26] NYC taxi trips. www.andresmh.com/nyctaxitrips/, 16/05/15.   \n[27] S. D. Tetali, M. Lesani, R. Majumdar, and T. Millstein. MrCrypt: Static analysis for secure cloud computations. SIGPLAN Not., 48(10):271–286, Oct. 2013.   \n[28] A. Tockar. Riding with the stars: Passenger privacy in the NYC taxicab dataset, 2014.   \n[29] S. Tu, M. F. Kaashoek, S. Madden, and N. Zeldovich. Processing analytical queries over encrypted data. Proc. VLDB Endow., 6(5):289–300, Mar. 2013.   \n[30] W. Wei, J. Du, T. Yu, and X. Gu. SecureMR: A service integrity assurance framework for mapreduce. In Proceedings of the 2009 Annual Computer Security Applications Conference, ACSAC ’09, pages 73–82, Washington, DC, USA, 2009. IEEE Computer Society.   \n[31] Y. Xu, W. Cui, and M. Peinado. Controlled-channel attacks: Deterministic side channels for untrusted operating systems. In IEEE Symposium on Security and Privacy, 2015.  "}