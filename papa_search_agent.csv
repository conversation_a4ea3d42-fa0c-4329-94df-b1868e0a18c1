序号,原始关键词（raw_keywords_zh）,原始查询词（query_keywords_en）,查询问题（query_zh）,查询问题（query_en）,语义提取(zh),语义提取(en),论文名称,作者,论文总结,顶会名称/年份,文件名称
1,在线游戏、反作弊、异常检测、远程测量、客户端模拟,"Online Games, Anti-cheating, Anomaly-based Detection, Remote Measurement, Partial Client Emulation",如何在不依赖特定游戏的情况下检测在线游戏中的作弊行为？,How can cheating in online games be detected without relying on game-specific signatures?,异常检测与客户端模拟结合可实现多种游戏和作弊方式的通用检测。,The combination of anomaly detection and client emulation enables general detection across various games and cheating methods.,Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,"<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>","{""en"":""Fides presents a remote anomaly-based cheat detection system for online games, leveraging client emulation and server collaboration to efficiently detect various cheating methods with general applicability."";""zh"":""Fides提出了一种基于异常检测的远程游戏作弊检测系统，通过客户端模拟和服务器协作，有效识别多种作弊方式，具有通用性和高效性。""}",CCS/2009,25.jsonl
2,在线游戏、反作弊、异常检测、远程测量、客户端模拟,"Online Games, Anti-cheating, Anomaly-based Detection, Remote Measurement, Partial Client Emulation",异常检测方法在游戏反作弊系统中有哪些优势？,What are the advantages of anomaly-based detection methods in game anti-cheat systems?,异常检测无需维护作弊特征库，能快速发现未知作弊，降低人工和存储成本。,"Anomaly-based detection does not require maintaining cheat signature databases, can quickly detect unknown cheats, and reduces manual and storage costs.",Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,"Edward Kaiser, Wu-chang Feng, Travis Schluessler","{""en"":""Fides presents a remote anomaly-based cheat detection system for online games, leveraging client emulation and server collaboration to efficiently detect various cheating methods with general applicability."";""zh"":""Fides提出了一种基于异常检测的远程游戏作弊检测系统，通过客户端模拟和服务器协作，有效识别多种作弊方式，具有通用性和高效性。""}",CCS/2009,25.jsonl
3,在线游戏、反作弊、异常检测、远程测量、客户端模拟,"Online Games, Anti-cheating, Anomaly-based Detection, Remote Measurement, Partial Client Emulation",远程测量和客户端模拟在反作弊系统中的作用是什么？,What roles do remote measurement and client emulation play in anti-cheat systems?,远程测量可持续监控客户端状态，客户端模拟帮助验证测量结果，提高检测准确性。,"Remote measurement enables continuous monitoring of client state, while client emulation helps validate measurements, improving detection accuracy.",Fides: Remote Anomaly-Based Cheat Detection Using Client Emulation,"Edward Kaiser, Wu-chang Feng, Travis Schluessler","{""en"":""Fides presents a remote anomaly-based cheat detection system for online games, leveraging client emulation and server collaboration to efficiently detect various cheating methods with general applicability."";""zh"":""Fides提出了一种基于异常检测的远程游戏作弊检测系统，通过客户端模拟和服务器协作，有效识别多种作弊方式，具有通用性和高效性。""}",CCS/2009,25.jsonl
4,非干扰性、信息流、反应式编程、Web浏览器、安全类型系统,"Noninterference, information flow, reactive programming, web browsers, security type system",如何通过类型系统实现反应式程序的信息流安全？,How can information-flow security for reactive programs be achieved using type systems?,类型系统可用于保证反应式程序满足非干扰性的信息流安全。,Type systems can be used to ensure that reactive programs satisfy noninterference information-flow security.,Reactive Noninterference,"Aaron Bohannon, Benjamin C. Pierce, Vilhelm Sjöberg, Stephanie Weirich, Steve Zdancewic","{""en"":""This paper proposes an information-flow security theory for reactive programs, defines several noninterference security models, and demonstrates security proofs via a type system, applicable to multi-party interactive scenarios such as web clients."";""zh"":""本文提出了面向反应式程序的信息流安全理论，定义了多种非干扰性安全模型，并通过类型系统实现了安全性证明，适用于Web客户端等多方交互场景。""}",CCS/2009,5.jsonl
5,非干扰性、信息流、反应式编程、Web浏览器、安全类型系统,"Noninterference, information flow, reactive programming, web browsers, security type system",反应式程序中的非干扰性安全模型有哪些类型？,What types of noninterference security models exist for reactive programs?,非干扰性安全模型包括终止敏感和终止不敏感模型，适用于不同的安全需求。,"Noninterference security models include termination-sensitive and termination-insensitive models, suitable for different security requirements.",Reactive Noninterference,"Aaron Bohannon, Benjamin C. Pierce, Vilhelm Sjöberg, Stephanie Weirich, Steve Zdancewic","{""en"":""This paper proposes an information-flow security theory for reactive programs, defines several noninterference security models, and demonstrates security proofs via a type system, applicable to multi-party interactive scenarios such as web clients."";""zh"":""本文提出了面向反应式程序的信息流安全理论，定义了多种非干扰性安全模型，并通过类型系统实现了安全性证明，适用于Web客户端等多方交互场景。""}",CCS/2009,5.jsonl
6,非干扰性、信息流、反应式编程、Web浏览器、安全类型系统,"Noninterference, information flow, reactive programming, web browsers, security type system",Web客户端如何防止多方交互中的信息泄露？,How can web clients prevent information leakage in multi-party interactions?,通过形式化的安全模型和类型系统，Web客户端可有效隔离不同方的数据，防止信息泄露。,Through formal security models and type systems,"web clients can effectively isolate data from different parties and prevent information leakage.""Reactive Noninterference","Aaron Bohannon, Benjamin C. Pierce, Vilhelm Sjöberg, Stephanie Weirich, Steve Zdancewic","{""en"":""This paper proposes an information-flow security theory for reactive programs, defines several noninterference security models, and demonstrates security proofs via a type system, applicable to multi-party interactive scenarios such as web clients."";""zh"":""本文提出了面向反应式程序的信息流安全理论，定义了多种非干扰性安全模型，并通过类型系统实现了安全性证明，适用于Web客户端等多方交互场景。""}",CCS/2009,5.jsonl
7,安全契约、自动信任协商、Web服务、访问控制、策略协商,"Security-by-Contract, Automated Trust Negotiation, Web Services, Access Control, Policy Negotiation",如何通过协商机制实现Web服务访问控制中的服务与凭证的灵活匹配？,How can negotiation mechanisms enable flexible matching of services and credentials in web service access control?,本文提出的框架将服务、凭证和行为约束捆绑，允许双方根据偏好协商服务与凭证的组合，实现灵活匹配。,"The proposed framework bundles services, credentials, and behavioral constraints, allowing both parties to negotiate preferred combinations for flexible matching.",Security-by-Contract for Web Services or How to Trade Credentials for Services,"Nicola Dragoni, Fabio Massacci","{""en"":""This paper proposes a security negotiation framework for web services, bundling services, required credentials, and behavioral constraints, enabling flexible negotiation of services and credentials while defending against malicious attacks, thus enhancing the flexibility and security of web service access control."";""zh"":""本文提出了一种面向Web服务的安全协商框架，将服务、所需凭证和披露行为约束捆绑，支持服务与凭证的灵活协商，兼顾合作与防御恶意攻击，提升了Web服务访问控制的灵活性与安全性。""}",CCS/2007,146.jsonl
8,安全契约、自动信任协商、Web服务、访问控制、策略协商,"Security-by-Contract, Automated Trust Negotiation, Web Services, Access Control, Policy Negotiation",安全契约在Web服务信任协商中起到什么作用？,What role does security-by-contract play in trust negotiation for web services?,安全契约将服务提供与凭证披露行为以合同方式明确，提升了协商的可控性和安全性。,"Security-by-contract formalizes service provision and credential disclosure as contracts, improving controllability and security in negotiation.",Security-by-Contract for Web Services or How to Trade Credentials for Services,"Nicola Dragoni, Fabio Massacci","{""en"":""This paper proposes a security negotiation framework for web services, bundling services, required credentials, and behavioral constraints, enabling flexible negotiation of services and credentials while defending against malicious attacks, thus enhancing the flexibility and security of web service access control."";""zh"":""本文提出了一种面向Web服务的安全协商框架，将服务、所需凭证和披露行为约束捆绑，支持服务与凭证的灵活协商，兼顾合作与防御恶意攻击，提升了Web服务访问控制的灵活性与安全性。""}",CCS/2007,146.jsonl
9,安全契约、自动信任协商、Web服务、访问控制、策略协商,"Security-by-Contract, Automated Trust Negotiation, Web Services, Access Control, Policy Negotiation",如何在Web服务协商中防御恶意参与者的策略攻击？,How can negotiation strategies defend against malicious participants in web service negotiations?,协议设计要求每次协商都需有实质性进展，防止恶意方通过反复试探获取对方全部策略偏好。,"The protocol requires substantive progress in each negotiation step, preventing malicious parties from probing and learning all policy preferences.",Security-by-Contract for Web Services or How to Trade Credentials for Services,"Nicola Dragoni, Fabio Massacci","{""en"":""This paper proposes a security negotiation framework for web services, bundling services, required credentials, and behavioral constraints, enabling flexible negotiation of services and credentials while defending against malicious attacks, thus enhancing the flexibility and security of web service access control."";""zh"":""本文提出了一种面向Web服务的安全协商框架，将服务、所需凭证和披露行为约束捆绑，支持服务与凭证的灵活协商，兼顾合作与防御恶意攻击，提升了Web服务访问控制的灵活性与安全性。""}",CCS/2007,146.jsonl
10,安全度量、软件质量、可靠性、测量方法、业务环境,"Security metrics, software quality, reliability, measurement methods, business context",制定安全度量指标时应考虑哪些关键因素？,What key factors should be considered when developing security metrics?,安全度量应兼顾技术可行性与业务实际需求，避免只测量易得但无实际意义的指标。,"Security metrics should balance technical feasibility and business needs, avoiding measurement of easily available but meaningless indicators.",Measuring Up: How to Keep Security Metrics Useful and Realistic,Shari Lawrence Pfleeger,"{""en"":""This keynote reviews lessons from software quality measurement and proposes principles for effective security metrics, emphasizing the need to balance technical and business realities to support system and network security assessment."";""zh"":""本报告回顾了软件质量度量的经验教训，提出了制定有效安全度量的原则，强调度量应兼顾技术与业务实际，助力系统和网络安全评估。""}",CCS/2007,86.jsonl
11,安全度量、软件质量、可靠性、测量方法、业务环境,"Security metrics, software quality, reliability, measurement methods, business context",如何确保安全度量指标在实际应用中具有可用性和现实性？,How can security metrics be kept useful and realistic in practice?,根据组织目标和实际环境制定度量标准，有助于确保度量结果能指导安全改进。,Tailoring metrics to organizational goals and real environments helps ensure results guide security improvements.,Measuring Up: How to Keep Security Metrics Useful and Realistic,Shari Lawrence Pfleeger,"{""en"":""This keynote reviews lessons from software quality measurement and proposes principles for effective security metrics, emphasizing the need to balance technical and business realities to support system and network security assessment."";""zh"":""本报告回顾了软件质量度量的经验教训，提出了制定有效安全度量的原则，强调度量应兼顾技术与业务实际，助力系统和网络安全评估。""}",CCS/2007,86.jsonl
12,安全度量、软件质量、可靠性、测量方法、业务环境,"Security metrics, software quality, reliability, measurement methods, business context",软件质量度量的经验对安全度量体系建设有何启示？,What lessons from software quality measurement can inform the construction of security metrics systems?,多学科视角和实际反馈有助于完善科学合理的度量体系。,Multidisciplinary perspectives and practical feedback help improve scientific and reasonable metrics systems.,Measuring Up: How to Keep Security Metrics Useful and Realistic,Shari Lawrence Pfleeger,"{""en"":""This keynote reviews lessons from software quality measurement and proposes principles for effective security metrics, emphasizing the need to balance technical and business realities to support system and network security assessment."";""zh"":""本报告回顾了软件质量度量的经验教训，提出了制定有效安全度量的原则，强调度量应兼顾技术与业务实际，助力系统和网络安全评估。""}",CCS/2007,86.jsonl
13,JavaScript、eval、分阶段元编程、静态分析、程序转换,"JavaScript, eval, staged metaprogramming, static analysis, program transformation",如何提升对JavaScript中eval语句的静态安全分析能力？,How can the static security analysis of eval statements in JavaScript be improved?,分阶段元编程结构化动态代码生成过程，提升静态分析的精确性和可实现性。,"Staged metaprogramming structures dynamic code generation, improving the precision and feasibility of static analysis.",Position Paper: The Science of Boxing - Analysing Eval using Staged Metaprogramming,Martin Lester,"{""en"":""This paper proposes transforming JavaScript's eval into staged metaprogramming to facilitate static analysis, presents a related algorithm, and discusses its potential for information flow security analysis."";""zh"":""本文提出将JavaScript中的eval转换为分阶段元编程（staged metaprogramming）以便于静态分析，介绍了相关算法，并探讨了其在信息流安全分析中的应用前景。""}",CCS/2013,225.jsonl
14,JavaScript、eval、分阶段元编程、静态分析、程序转换,"JavaScript, eval, staged metaprogramming, static analysis, program transformation",分阶段元编程在程序安全分析中有哪些优势？,What are the advantages of staged metaprogramming in program security analysis?,分阶段元编程结构化了动态代码生成过程，使静态分析更精确、更易于实现。,"Staged metaprogramming structures dynamic code generation, making static analysis more precise and feasible.",Position Paper: The Science of Boxing - Analysing Eval using Staged Metaprogramming,Martin Lester,"{""en"":""This paper proposes transforming JavaScript's eval into staged metaprogramming to facilitate static analysis, presents a related algorithm, and discusses its potential for information flow security analysis."";""zh"":""本文提出将JavaScript中的eval转换为分阶段元编程（staged metaprogramming）以便于静态分析，介绍了相关算法，并探讨了其在信息流安全分析中的应用前景。""}",CCS/2013,225.jsonl
15,JavaScript、eval、分阶段元编程、静态分析、程序转换,"JavaScript, eval, staged metaprogramming, static analysis, program transformation",eval转换算法在信息流安全分析中如何应用？,How can the eval transformation algorithm be applied in information flow security analysis?,eval转换算法可将相关代码转化为可静态分析的形式，支持信息流安全属性的验证。,"The eval transformation algorithm can convert related code into a statically analyzable form, supporting the verification of information flow security properties.",Position Paper: The Science of Boxing - Analysing Eval using Staged Metaprogramming,Martin Lester,"{""en"":""This paper proposes transforming JavaScript's eval into staged metaprogramming to facilitate static analysis, presents a related algorithm, and discusses its potential for information flow security analysis."";""zh"":""本文提出将JavaScript中的eval转换为分阶段元编程（staged metaprogramming）以便于静态分析，介绍了相关算法，并探讨了其在信息流安全分析中的应用前景。""}",CCS/2013,225.jsonl
16,跨平台、恶意软件、漏洞、代码复用、利用工具包,"Cross-Platform, malware, vulnerabilities, code reuse, exploit kits",跨平台恶意软件如何实现多操作系统的感染与传播？,How do cross-platform malware achieve infection and propagation across multiple operating systems?,利用脚本、字节码和多平台漏洞可实现多操作系统间的传播和执行。,"Scripts, bytecode, and cross-platform vulnerabilities enable propagation and execution across multiple operating systems.","POSTER: Cross-Platform Malware: Write Once, Infect Everywhere","Martina Lindorfer, Matthias Neumayr, Juan Caballero, Christian Platzer","{""en"":""This paper systematically investigates cross-platform malware and the vulnerabilities they exploit, analyzes their distribution methods, code reuse, and exploit kits, and highlights the growing trend of cross-platform threats."";""zh"":""本文系统性研究了跨平台恶意软件及其利用的跨平台漏洞，分析了其传播方式、代码复用和利用工具包的现状，并指出跨平台威胁日益增长的趋势。""}",CCS/2013,93.jsonl
17,跨平台、恶意软件、漏洞、代码复用、利用工具包,"Cross-Platform, malware, vulnerabilities, code reuse, exploit kits",跨平台漏洞在恶意软件传播中起到什么作用？,What role do cross-platform vulnerabilities play in malware propagation?,跨平台漏洞为恶意软件提供统一攻击入口，使其可通过同一漏洞感染多平台目标。,"Cross-platform vulnerabilities provide a unified attack vector, enabling malware to infect multiple platforms through the same vulnerability.","POSTER: Cross-Platform Malware: Write Once, Infect Everywhere","Martina Lindorfer, Matthias Neumayr, Juan Caballero, Christian Platzer","{""en"":""This paper systematically investigates cross-platform malware and the vulnerabilities they exploit, analyzes their distribution methods, code reuse, and exploit kits, and highlights the growing trend of cross-platform threats."";""zh"":""本文系统性研究了跨平台恶意软件及其利用的跨平台漏洞，分析了其传播方式、代码复用和利用工具包的现状，并指出跨平台威胁日益增长的趋势。""}",CCS/2013,93.jsonl
18,跨平台、恶意软件、漏洞、代码复用、利用工具包,"Cross-Platform, malware, vulnerabilities, code reuse, exploit kits",当前跨平台恶意软件的代码复用和利用工具包有哪些特点？,What are the characteristics of code reuse and exploit kits in current cross-platform malware?,统一代码或平台特定模块结合商业化利用工具包可提升传播效率。,Unified code or platform-specific modules combined with commercial exploit kits can improve propagation efficiency.,"POSTER: Cross-Platform Malware: Write Once, Infect Everywhere","Martina Lindorfer, Matthias Neumayr, Juan Caballero, Christian Platzer","{""en"":""This paper systematically investigates cross-platform malware and the vulnerabilities they exploit, analyzes their distribution methods, code reuse, and exploit kits, and highlights the growing trend of cross-platform threats."";""zh"":""本文系统性研究了跨平台恶意软件及其利用的跨平台漏洞，分析了其传播方式、代码复用和利用工具包的现状，并指出跨平台威胁日益增长的趋势。""}",CCS/2013,93.jsonl
19,控制流完整性、动态CFG、输入相关安全、软件防护、攻击防御,"Control-Flow Integrity, Dynamic CFG, Input-Dependent Security, Software Protection, Attack Mitigation",如何通过动态生成控制流图提升对控制流劫持攻击的防护能力？,How does dynamically generating control-flow graphs enhance protection against control-flow hijacking attacks?,动态生成控制流图可针对每个输入精细化限制控制流，提升安全性并减少攻击面。,"Dynamically generating control-flow graphs enables fine-grained control-flow restrictions for each input, enhancing security and reducing the attack surface.",Per-Input Control-Flow Integrity,"Ben Niu, Gang Tan","{""en"":""This paper proposes Per-Input Control-Flow Integrity (πCFI), a mechanism that dynamically generates and enforces a control-flow graph (CFG) for each concrete input, significantly improving protection against control-flow hijacking attacks while maintaining security and performance."";""zh"":""本文提出了每输入控制流完整性（Per-Input CFI, πCFI）机制，通过为每个具体输入动态生成和强制执行控制流图（CFG），显著提升了对控制流劫持攻击的防护能力，并兼顾了安全性与性能。""}",CCS/2015,112.jsonl
20,控制流完整性、动态CFG、输入相关安全、软件防护、攻击防御,"Control-Flow Integrity, Dynamic CFG, Input-Dependent Security, Software Protection, Attack Mitigation",每输入控制流完整性（πCFI）与传统CFI方法相比有何优势？,What are the advantages of Per-Input Control-Flow Integrity (πCFI) over traditional CFI methods?,针对每个输入的精细化控制流限制可避免静态CFG的过度放宽，提高安全性且性能开销小。,"Fine-grained control-flow restrictions for each input can avoid the over-approximation of static CFGs, improving security with minimal performance overhead.",Per-Input Control-Flow Integrity,"Ben Niu, Gang Tan","{""en"":""This paper proposes Per-Input Control-Flow Integrity (πCFI), a mechanism that dynamically generates and enforces a control-flow graph (CFG) for each concrete input, significantly improving protection against control-flow hijacking attacks while maintaining security and performance."";""zh"":""本文提出了每输入控制流完整性（Per-Input CFI, πCFI）机制，通过为每个具体输入动态生成和强制执行控制流图（CFG），显著提升了对控制流劫持攻击的防护能力，并兼顾了安全性与性能。""}",CCS/2015,112.jsonl
21,控制流完整性、动态CFG、输入相关安全、软件防护、攻击防御,"Control-Flow Integrity, Dynamic CFG, Input-Dependent Security, Software Protection, Attack Mitigation",在实际软件防护中，如何平衡控制流完整性的安全性与性能？,How can the balance between security and performance be achieved in practical control-flow integrity protection?,惰性边激活和高效补丁机制有助于提升安全性的同时保持较低的运行时开销。,Lazy edge activation and efficient patching mechanisms help enhance security while maintaining low runtime overhead.,Per-Input Control-Flow Integrity,"Ben Niu, Gang Tan","{""en"":""This paper proposes Per-Input Control-Flow Integrity (πCFI), a mechanism that dynamically generates and enforces a control-flow graph (CFG) for each concrete input, significantly improving protection against control-flow hijacking attacks while maintaining security and performance."";""zh"":""本文提出了每输入控制流完整性（Per-Input CFI, πCFI）机制，通过为每个具体输入动态生成和强制执行控制流图（CFG），显著提升了对控制流劫持攻击的防护能力，并兼顾了安全性与性能。""}",CCS/2015,112.jsonl
22,MapReduce、流量分析、数据隐私、云安全、泄露防护,"MapReduce, Traffic Analysis, Data Privacy, Cloud Security, Leakage Prevention",如何防止MapReduce在云平台上的中间流量泄露敏感信息？,How can intermediate traffic leakage of sensitive information in MapReduce on cloud platforms be prevented?,安全混洗与负载均衡机制可降低中间流量泄露敏感信息的风险。,Secure shuffling and load balancing mechanisms reduce the risk of sensitive information leakage through intermediate traffic.,Observing and Preventing Leakage in MapReduce,"Olga Ohrimenko, Manuel Costa, Cédric Fournet, Christos Gkantsidis, Markulf Kohlweiss, Divya Sharma","{""en"":""This paper analyzes the risk of intermediate traffic leakage in MapReduce on cloud platforms proposes two secure solutions to prevent leakageand implements and evaluates them on the VC3 secure Hadoop platform,demonstrating effective prevention of sensitive information leakage with reasonable performance overhead."";""zh"":""本文分析了MapReduce在云环境下的中间通信流量泄露风险，提出了两种防泄漏的安全方案，并在VC3安全Hadoop平台上实现与评估，证明可在合理性能开销下有效防止敏感信息泄露。""}",CCS/2015,113.jsonl
23,MapReduce、流量分析、数据隐私、云安全、泄露防护,"MapReduce, Traffic Analysis, Data Privacy, Cloud Security, Leakage Prevention",MapReduce在安全硬件环境下仍存在哪些隐私风险？,What privacy risks remain for MapReduce even in secure hardware environments?,即使数据加密和硬件隔离，通信模式和流量特征仍可能泄露输入数据的敏感属性。,"Even with encrypted data and hardware isolation, communication patterns and traffic characteristics can still leak sensitive attributes of input data.",Observing and Preventing Leakage in MapReduce,"Olga Ohrimenko, Manuel Costa, Cédric Fournet, Christos Gkantsidis, Markulf Kohlweiss, Divya Sharma","{""en"":""This paper analyzes the risk of intermediate traffic leakage in MapReduce on cloud platforms proposes two secure solutions to prevent leakageand implements and evaluates them on the VC3 secure Hadoop platform,demonstrating effective prevention of sensitive information leakage with reasonable performance overhead."";""zh"":""本文分析了MapReduce在云环境下的中间通信流量泄露风险，提出了两种防泄漏的安全方案，并在VC3安全Hadoop平台上实现与评估，证明可在合理性能开销下有效防止敏感信息泄露。""}",CCS/2015,113.jsonl
24,MapReduce、流量分析、数据隐私、云安全、泄露防护,"MapReduce, Traffic Analysis, Data Privacy, Cloud Security, Leakage Prevention",提升云端大数据处理隐私保护的有效方法有哪些？,What are effective methods to enhance privacy protection in cloud-based big data processing?,流量混淆、批量处理、伪装填充等技术可提升云端大数据处理的隐私保护能力。,Techniques such as traffic obfuscationbatch processing and dummy padding can enhance privacy protection in cloud-based big data processing.,Observing and Preventing Leakage in MapReduce,"Olga Ohrimenko, Manuel Costa, Cédric Fournet, Christos Gkantsidis, Markulf Kohlweiss, Divya Sharma","{""en"":""This paper analyzes the risk of intermediate traffic leakage in MapReduce on cloud platforms,proposes two secure solutions to prevent leakage,and implements and evaluates them on the VC3 secure Hadoop platform,demonstrating effective prevention of sensitive information leakage with reasonable performance overhead."";""zh"":""本文分析了MapReduce在云环境下的中间通信流量泄露风险，提出了两种防泄漏的安全方案，并在VC3安全Hadoop平台上实现与评估，证明可在合理性能开销下有效防止敏感信息泄露。""}",CCS/2015,113.jsonl
25,CPS安全、工业控制系统、仿真平台、网络攻防、物理层交互,"CPS Security, Industrial Control Systems, Simulation Platform, Network Attack and Defense, Physical Layer Interaction",如何利用仿真平台开展工业控制系统的安全攻防研究？,How can simulation platforms be used for security attack and defense research in industrial control systems?,网络与物理层的可扩展仿真有助于复现真实协议和攻击场景，便于安全机制开发与验证。,Extensible simulation of network and physical layers helps reproduce realistic protocols and attack scenarios facilitating the development and validation of security mechanisms.,MiniCPS: A Toolkit for Security Research on CPS Networks ,"Daniele Antonioli, Nils Ole Tippenhauer","{""en"":""MiniCPS is a simulation toolkit for security research on Industrial Control Systems (ICS) and Cyber-Physical Systems (CPS)supporting extensible simulation of network,control logic,and physical-layer interactions,facilitating security experiments and the development and validation of novel defense mechanisms."";""zh"":""MiniCPS是一个面向工业控制系统（ICS）和网络物理系统（CPS）安全研究的仿真工具包，支持网络、控制逻辑与物理层交互的可扩展仿真，便于安全攻防实验和新型防护机制的开发与验证。""}",CCS/2015,170.jsonl
26,CPS安全、工业控制系统、仿真平台、网络攻防、物理层交互,"CPS Security, Industrial Control Systems, Simulation Platform, Network Attack and Defense, Physical Layer Interaction",MiniCPS在CPS安全研究中具备哪些独特优势？,What unique advantages does MiniCPS offer for CPS security research?,网络仿真、控制逻辑与物理过程模拟的结合提升了实验的可复现性和灵活性。,"The combination of network emulation, control logic, and physical process simulation improves the reproducibility and flexibility of experiments.",MiniCPS: A Toolkit for Security Research on CPS Networks ,"Daniele Antonioli, Nils Ole Tippenhauer","{""en"":""MiniCPS is a simulation toolkit for security research on Industrial Control Systems (ICS) and Cyber-Physical Systems (CPS)supporting extensible simulation of network,control logic,and physical-layer interactions,facilitating security experiments and the development and validation of novel defense mechanisms."";""zh"":""MiniCPS是一个面向工业控制系统（ICS）和网络物理系统（CPS）安全研究的仿真工具包，支持网络、控制逻辑与物理层交互的可扩展仿真，便于安全攻防实验和新型防护机制的开发与验证。""}",CCS/2015,170.jsonl
27,CPS安全、工业控制系统、仿真平台、网络攻防、物理层交互,"CPS Security, Industrial Control Systems, Simulation Platform, Network Attack and Defense, Physical Layer Interaction",在CPS安全实验中，如何实现物理层与网络层的联动仿真？,How can joint simulation of physical and network layers be achieved in CPS security experiments?,API接口联动物理过程与网络通信可实现多层次安全实验与防护机制测试。,API interfaces linking physical processes and network communication enable multi-level security experiments and testing of defense mechanisms.,MiniCPS: A Toolkit for Security Research on CPS Networks ,"Daniele Antonioli, Nils Ole Tippenhauer","{""en"":""MiniCPS is a simulation toolkit for security research on Industrial Control Systems (ICS) and Cyber-Physical Systems (CPS)supporting extensible simulation of network,control logic,and physical-layer interactions,facilitating security experiments and the development and validation of novel defense mechanisms."";""zh"":""MiniCPS是一个面向工业控制系统（ICS）和网络物理系统（CPS）安全研究的仿真工具包，支持网络、控制逻辑与物理层交互的可扩展仿真，便于安全攻防实验和新型防护机制的开发与验证。""}",CCS/2015,170.jsonl
28,云安全、恶意仓库、BarFinder、云服务滥用、配置缺陷,"Cloud Security, Malicious Repository, BarFinder, Cloud Service Abuse, Misconfiguration",如何检测云服务中被滥用为恶意基础设施的仓库？,How can repositories abused as malicious infrastructure in cloud services be detected?,分析仓库与网站的拓扑关系和内容特征有助于自动检测恶意云仓库。,Analyzing topological relationships and content features between repositories and websites helps automatically detect malicious cloud repositories.,Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,"Xiaojing Liao, Sumayah Alrwais, Kan Yuan, Luyi Xing, XiaoFeng Wang, Shuang Hao, Raheem Beyah","{""en"":""This paper systematically studies the abuse of cloud storage services as malicious proposes the BarFinder detection systeminfrastructure reveals the critical role of malicious cloud repositories in attack infrastructures,and discovers new security risks such as misconfiguration."";""zh"":""本文系统研究了云存储服务被滥用为恶意服务的现象，提出了BarFinder检测系统，揭示了恶意云仓库在攻击基础设施中的关键作用，并发现了配置缺陷等新型安全隐患。""}",CCS/2016,110.jsonl
29,云安全、恶意仓库、BarFinder、云服务滥用、配置缺陷,"Cloud Security, Malicious Repository, BarFinder, Cloud Service Abuse, Misconfiguration",云服务仓库被滥用为攻击基础设施的主要方式有哪些？,What are the main ways cloud service repositories are abused as attack infrastructure?,恶意仓库常被用于分发恶意代码、钓鱼、广告欺诈等，云平台信誉高、难以拉黑，成为攻击链核心。,"Malicious repositories are often used for malware distribution, phishing, ad fraud, etc., and become the core of attack chains due to the high reputation and blacklist resistance of cloud platforms.",Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,"Xiaojing Liao, Sumayah Alrwais, Kan Yuan, Luyi Xing, XiaoFeng Wang, Shuang Hao, Raheem Beyah","{""en"":""This paper systematically studies the abuse of cloud storage services as malicious proposes the BarFinder detection systeminfrastructure reveals the critical role of malicious cloud repositories in attack infrastructures,and discovers new security risks such as misconfiguration."";""zh"":""本文系统研究了云存储服务被滥用为恶意服务的现象，提出了BarFinder检测系统，揭示了恶意云仓库在攻击基础设施中的关键作用，并发现了配置缺陷等新型安全隐患。""}",CCS/2016,110.jsonl
30,云安全、恶意仓库、BarFinder、云服务滥用、配置缺陷,"Cloud Security, Malicious Repository, BarFinder, Cloud Service Abuse, Misconfiguration",云服务仓库配置缺陷会带来哪些安全风险？,What security risks are introduced by misconfiguration of cloud service repositories?,配置缺陷可能导致任意用户上传、篡改或删除仓库内容，扩大攻击面和安全风险。,"Misconfiguration may allow arbitrary users to upload, modify, or delete repository content, expanding the attack surface and security risks.",Lurking Malice in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service,"Xiaojing Liao, Sumayah Alrwais, Kan Yuan, Luyi Xing, XiaoFeng Wang, Shuang Hao, Raheem Beyah","{""en"":""This paper systematically studies the abuse of cloud storage services as malicious proposes the BarFinder detection systeminfrastructure reveals the critical role of malicious cloud repositories in attack infrastructures,and discovers new security risks such as misconfiguration."";""zh"":""本文系统研究了云存储服务被滥用为恶意服务的现象，提出了BarFinder检测系统，揭示了恶意云仓库在攻击基础设施中的关键作用，并发现了配置缺陷等新型安全隐患。""}",CCS/2016,110.jsonl
31,密码安全、助记句、用户行为、个性化策略、可用性评估,"Password Security, Mnemonic Sentences, User Behavior, Personalized Strategy, Usability Evaluation",基于助记句的密码生成策略存在哪些安全隐患？,What security risks exist in mnemonic sentence-based password generation strategies?,常见句子的选择会导致密码分布集中，降低密码强度和安全性。,"Choosing common sentences leads to concentrated password distributions, reducing password strength and security.",An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,"Weining Yang, Ninghui Li, Omar Chowdhury, Aiping Xiong, Robert W. Proctor","{""en"":""This paper systematically evaluates the security and usability of mnemonic sentence-based password generation strategies through large-scale user studies finding that personalized sentences and high-quality examples significantly enhance password strength while generic instructions tend to produce weak passwords."";""zh"":""本文通过大规模用户实验，系统评估了基于助记句的密码生成策略的安全性与可用性，发现个性化句子和高质量示例能显著提升密码强度，而通用指令易导致弱密码。""}",CCS/2016,182.jsonl
32,密码安全、助记句、用户行为、个性化策略、可用性评估,"Password Security, Mnemonic Sentences, User Behavior, Personalized Strategy, Usability Evaluation",如何提升基于助记句的密码生成策略的安全性？,How can the security of mnemonic sentence-based password generation strategies be improved?,个性化句子选择和高质量示例有助于提升密码的不可预测性和强度。,Personalized sentence selection and high-quality examples help improve the unpredictability and strength of passwords.,An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,"Weining Yang, Ninghui Li, Omar Chowdhury, Aiping Xiong, Robert W. Proctor","{""en"":""This paper systematically evaluates the security and usability of mnemonic sentence-based password generation strategies through large-scale user studies finding that personalized sentences and high-quality examples significantly enhance password strength while generic instructions tend to produce weak passwords."";""zh"":""本文通过大规模用户实验，系统评估了基于助记句的密码生成策略的安全性与可用性，发现个性化句子和高质量示例能显著提升密码强度，而通用指令易导致弱密码。""}",CCS/2016,182.jsonl
33,密码安全、助记句、用户行为、个性化策略、可用性评估,"Password Security, Mnemonic Sentences, User Behavior, Personalized Strategy, Usability Evaluation",基于助记句的密码策略在可用性上表现如何？,How do mnemonic sentence-based password strategies perform in terms of usability?,该策略在记忆性和生成效率上与传统方法相当，未显著增加用户负担。,"This strategy is comparable to traditional methods in memorability and generation efficiency, without significantly increasing user burden.",An Empirical Study of Mnemonic Sentence-based Password Generation Strategies,"Weining Yang, Ninghui Li, Omar Chowdhury, Aiping Xiong, Robert W. Proctor","{""en"":""This paper systematically evaluates the security and usability of mnemonic sentence-based password generation strategies through large-scale user studies finding that personalized sentences and high-quality examples significantly enhance password strength while generic instructions tend to produce weak passwords."";""zh"":""本文通过大规模用户实验，系统评估了基于助记句的密码生成策略的安全性与可用性，发现个性化句子和高质量示例能显著提升密码强度，而通用指令易导致弱密码。""}",CCS/2016,182.jsonl
34,工业控制系统、蜜罐、虚拟化、高交互、网络安全,"Industrial Control System, Honeypot, Virtualization, High-Interaction, Cybersecurity",高交互虚拟ICS蜜罐有哪些关键设计要点？,What are the key design points of high-interaction virtual ICS honeypots?,真实感、低成本、可扩展性和多协议仿真是高交互蜜罐设计的关键要素。,"Realism, low cost, scalability, and multi-protocol simulation are key elements in the design of high-interaction honeypots.",Towards High-Interaction Virtual ICS Honeypots-in-a-Box,"Daniele Antonioli, Anand Agrawal, Nils Ole Tippenhauer","{""en"":""This paper proposes a high-interaction virtualized honeypot design and implementation for industrial control systems (ICS)balancing low cost,scalability,and realism,and validates its practicality and research value in a CTF competition."";""zh"":""本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾低成本、可扩展性和真实感，并在CTF竞赛中验证了其实用性和安全研究价值。""}",CCS/2016,196.jsonl
35,工业控制系统、蜜罐、虚拟化、高交互、网络安全,"Industrial Control System, Honeypot, Virtualization, High-Interaction, Cybersecurity",虚拟ICS蜜罐在安全研究中的应用价值体现在哪些方面？,In what aspects does the application value of virtual ICS honeypots manifest in security research?,可用于新型攻击行为捕获、防御机制测试和安全教育等多场景。,"They can be used for capturing novel attack behaviors, testing defense mechanisms, and security education in various scenarios.",Towards High-Interaction Virtual ICS Honeypots-in-a-Box,"Daniele Antonioli, Anand Agrawal, Nils Ole Tippenhauer","{""en"":""This paper proposes a high-interaction virtualized honeypot design and implementation for industrial control systems (ICS)balancing low cost,scalability,and realism,and validates its practicality and research value in a CTF competition."";""zh"":""本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾低成本、可扩展性和真实感，并在CTF竞赛中验证了其实用性和安全研究价值。""}",CCS/2016,196.jsonl
36,工业控制系统、蜜罐、虚拟化、高交互、网络安全,"Industrial Control System, Honeypot, Virtualization, High-Interaction, Cybersecurity",如何保证ICS蜜罐的可维护性和安全隔离？,How to ensure maintainability and security isolation of ICS honeypots?,虚拟化和物理隔离设计有助于降低维护成本并防止攻击者渗透真实系统。,Virtualization and physical isolation design help reduce maintenance costs and prevent attackers from penetrating real systems.,Towards High-Interaction Virtual ICS Honeypots-in-a-Box,"Daniele Antonioli, Anand Agrawal, Nils Ole Tippenhauer","{""en"":""This paper proposes a high-interaction virtualized honeypot design and implementation for industrial control systems (ICS)balancing low cost,scalability,and realism,and validates its practicality and research value in a CTF competition."";""zh"":""本文提出了一种高交互、虚拟化的工业控制系统（ICS）蜜罐设计与实现方案，兼顾低成本、可扩展性和真实感，并在CTF竞赛中验证了其实用性和安全研究价值。""}",CCS/2016,196.jsonl
37,可验证外包、交互式证明、硬件加速、数据并行、成本分析,"Verifiable Outsourcing, Interactive Proofs, Hardware Acceleration, Data Parallelism, Cost Analysis",如何在大规模数据并行计算中实现高效的可验证外包？,How can efficient verifiable outsourcing be achieved in large-scale data-parallel computations?,协议与硬件协同优化可提升大规模数据并行场景下的可验证外包效率。,Protocol and hardware co-optimization can improve the efficiency of verifiable outsourcing in large-scale data-parallel scenarios.,Full Accounting for Verifiable Outsourcing,"Riad S. Wahby, Ye Ji, Andrew J. Blumberg, abhi shelat, Justin Thaler, Michael Walfish, Thomas Wies","{""en"":""This paper presents Giraffe a system that fully accounts for prover verifier,and precomputation costs in verifiable outsourcing,optimizes hardware design and protocol efficiency,and significantly enhances verifiable outsourcing for large-scale data-parallel computations."";""zh"":""本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了硬件设计与协议效率，显著提升了大规模数据并行计算的可验证外包能力。""}",CCS/2017,180.jsonl
38,可验证外包、交互式证明、硬件加速、数据并行、成本分析,"Verifiable Outsourcing, Interactive Proofs, Hardware Acceleration, Data Parallelism, Cost Analysis",可验证外包系统中如何全面计量和优化证明者、验证者及预计算的成本？,"How can the costs of prover, verifier, and precomputation be fully accounted for and optimized in verifiable outsourcing systems?",统一分析与优化三类成本有助于提升系统的可用性和经济性。,Unified analysis and optimization of the three costs help improve system usability and cost-effectiveness.,Full Accounting for Verifiable Outsourcing,"Riad S. Wahby, Ye Ji, Andrew J. Blumberg, abhi shelat, Justin Thaler, Michael Walfish, Thomas Wies","{""en"":""This paper presents Giraffe a system that fully accounts for prover verifier,and precomputation costs in verifiable outsourcing,optimizes hardware design and protocol efficiency,and significantly enhances verifiable outsourcing for large-scale data-parallel computations."";""zh"":""本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了硬件设计与协议效率，显著提升了大规模数据并行计算的可验证外包能力。""}",CCS/2017,180.jsonl
39,可验证外包、交互式证明、硬件加速、数据并行、成本分析,"Verifiable Outsourcing, Interactive Proofs, Hardware Acceleration, Data Parallelism, Cost Analysis",硬件加速在可验证外包协议中起到什么作用？,What role does hardware acceleration play in verifiable outsourcing protocols?,自动化硬件设计模板可提升协议执行效率和可扩展性。,Automated hardware design templates can improve protocol execution efficiency and scalability.,Full Accounting for Verifiable Outsourcing,"Riad S. Wahby, Ye Ji, Andrew J. Blumberg, abhi shelat, Justin Thaler, Michael Walfish, Thomas Wies","{""en"":""This paper presents Giraffe a system that fully accounts for prover verifier,and precomputation costs in verifiable outsourcing,optimizes hardware design and protocol efficiency,and significantly enhances verifiable outsourcing for large-scale data-parallel computations."";""zh"":""本文提出了Giraffe系统，实现了对可验证外包中证明者、验证者和预计算三类成本的全面计量，优化了硬件设计与协议效率，显著提升了大规模数据并行计算的可验证外包能力。""}",CCS/2017,180.jsonl
40,代码签名、恶意软件、PKI、证书滥用、信任体系,"Code Signing, Malware, PKI, Certificate Abuse, Trust Infrastructure",Windows代码签名PKI体系中存在哪些主要安全弱点？,What are the main security weaknesses in the Windows code-signing PKI system?,客户端保护不足、发布者密钥管理失误和CA验证失效是主要安全弱点。,"Insufficient client-side protection, publisher key mismanagement, and CA verification failures are the main security weaknesses.",Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,"Doowon Kim, Bum Jun Kwon, Tudor Dumitras","{""en"":""This paper systematically analyzes the abuse of the Windows code-signing PKI proposes a threat model reveals three main weaknesses—insufficient client-side protection,publisher key mismanagement,and CA verification failures—and offers improvement suggestions."";""zh"":""本文系统性分析了Windows代码签名PKI被滥用的现象，提出威胁模型，揭示了客户端保护不足、发布者密钥管理失误和CA验证失效三类主要弱点，并提出改进建议。""}",CCS/2017,98.jsonl
41,代码签名、恶意软件、PKI、证书滥用、信任体系,"Code Signing, Malware, PKI, Certificate Abuse, Trust Infrastructure",恶意软件如何利用代码签名证书绕过安全检测？,How do malware authors exploit code-signing certificates to bypass security detection?,滥用或伪造签名证书可绕过安全检测，增加恶意软件传播风险。,"Abusing or forging code-signing certificates can bypass security detection, increasing the risk of malware propagation.",Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,"Doowon Kim, Bum Jun Kwon, Tudor Dumitras","{""en"":""This paper systematically analyzes the abuse of the Windows code-signing PKI proposes a threat model reveals three main weaknesses—insufficient client-side protection,publisher key mismanagement,and CA verification failures—and offers improvement suggestions."";""zh"":""本文系统性分析了Windows代码签名PKI被滥用的现象，提出威胁模型，揭示了客户端保护不足、发布者密钥管理失误和CA验证失效三类主要弱点，并提出改进建议。""}",CCS/2017,98.jsonl
42,代码签名、恶意软件、PKI、证书滥用、信任体系,"Code Signing, Malware, PKI, Certificate Abuse, Trust Infrastructure",如何改进代码签名PKI体系以提升软件供应链安全？,How can the code-signing PKI system be improved to enhance software supply chain security?,加强密钥保护、严格身份验证和签名透明度有助于提升供应链安全。,"Strengthening key protection, enforcing strict identity verification, and signature transparency help enhance supply chain security.",Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI,"Doowon Kim, Bum Jun Kwon, Tudor Dumitras","{""en"":""This paper systematically analyzes the abuse of the Windows code-signing PKI proposes a threat model reveals three main weaknesses—insufficient client-side protection,publisher key mismanagement,and CA verification failures—and offers improvement suggestions."";""zh"":""本文系统性分析了Windows代码签名PKI被滥用的现象，提出威胁模型，揭示了客户端保护不足、发布者密钥管理失误和CA验证失效三类主要弱点，并提出改进建议。""}",CCS/2017,98.jsonl
43,伪随机数生成器、RNG、X9.31、硬编码密钥、状态恢复攻击、VPN安全、标准化缺陷,"Pseudorandom Number Generator, RNG, X9.31, Hard-coded Key, State Recovery Attack, VPN Security, Standardization Flaw",如何评估和检测加密产品中伪随机数生成器的安全性？,How to evaluate and detect the security of pseudorandom number generators in cryptographic products?,分析RNG实现、密钥管理和认证流程有助于发现硬编码密钥等安全隐患。,"Analyzing RNG implementation, key management, and certification processes helps identify security risks such as hard-coded keys.",Practical state recovery attacks against legacy RNG implementations,"Shaanan N. Cohney, Matthew D. Green, Nadia Heninger","{""en"":""This paper systematically analyzes the security of the ANSI X9.17/X9.31 pseudorandom number generator (RNG) standard discovers hard-coded keys in multiple products enabling attackers to passively recover RNG state and decrypt sensitive communications such as VPNs,and reveals serious flaws in the standardization and certification process."";""zh"":""本文系统分析了ANSI X9.17/X9.31伪随机数生成器（RNG）标准的安全性，发现多个产品存在硬编码密钥，导致攻击者可通过被动监听恢复RNG状态并解密VPN等敏感通信，揭示了标准化和认证流程的严重缺陷。""} ",CCS/2018,54.jsonl
44,伪随机数生成器、RNG、X9.31、硬编码密钥、状态恢复攻击、VPN安全、标准化缺陷,"Pseudorandom Number Generator, RNG, X9.31, Hard-coded Key, State Recovery Attack, VPN Security, Standardization Flaw",在VPN等协议中，RNG弱点会带来哪些实际安全威胁？,What practical security threats do RNG weaknesses pose in protocols like VPN?,RNG弱点可能导致密钥恢复和加密通信被动解密，危及数据隐私。,"RNG weaknesses may lead to key recovery and passive decryption of encrypted communications, compromising data privacy.",Practical state recovery attacks against legacy RNG implementations,"Shaanan N. Cohney, Matthew D. Green, Nadia Heninger","{""en"":""This paper systematically analyzes the security of the ANSI X9.17/X9.31 pseudorandom number generator (RNG) standard discovers hard-coded keys in multiple products enabling attackers to passively recover RNG state and decrypt sensitive communications such as VPNs,and reveals serious flaws in the standardization and certification process."";""zh"":""本文系统分析了ANSI X9.17/X9.31伪随机数生成器（RNG）标准的安全性，发现多个产品存在硬编码密钥，导致攻击者可通过被动监听恢复RNG状态并解密VPN等敏感通信，揭示了标准化和认证流程的严重缺陷。""} ",CCS/2018,54.jsonl
45,伪随机数生成器、RNG、X9.31、硬编码密钥、状态恢复攻击、VPN安全、标准化缺陷,"Pseudorandom Number Generator, RNG, X9.31, Hard-coded Key, State Recovery Attack, VPN Security, Standardization Flaw",标准化流程中未明确密钥生成要求会导致哪些后果？,What are the consequences of unclear key generation requirements in standardization processes?,标准不明确易导致实现中出现硬编码密钥等系统性安全隐患。,Unclear standards can lead to systemic security risks such as hard-coded keys in implementations.,Practical state recovery attacks against legacy RNG implementations,"Shaanan N. Cohney, Matthew D. Green, Nadia Heninger","{""en"":""This paper systematically analyzes the security of the ANSI X9.17/X9.31 pseudorandom number generator (RNG) standard discovers hard-coded keys in multiple products enabling attackers to passively recover RNG state and decrypt sensitive communications such as VPNs,and reveals serious flaws in the standardization and certification process."";""zh"":""本文系统分析了ANSI X9.17/X9.31伪随机数生成器（RNG）标准的安全性，发现多个产品存在硬编码密钥，导致攻击者可通过被动监听恢复RNG状态并解密VPN等敏感通信，揭示了标准化和认证流程的严重缺陷。""} ",CCS/2018,54.jsonl
46,同态加密、矩阵计算、隐私保护、神经网络、外包计算、加密推理,"Homomorphic Encryption, Matrix Computation, Privacy-preserving, Neural Network, Outsourced Computation, Encrypted Inference",如何在不可信环境下实现对加密数据的高效神经网络推理？,How to efficiently perform neural network inference on encrypted data in untrusted environments?,同态加密矩阵计算方法可在云端实现加密数据和模型的高效推理，保护数据隐私。,"Homomorphic encryption-based matrix computation enables efficient inference on encrypted data and models in the cloud, preserving data privacy.",Secure Outsourced Matrix Computation and Application to Neural Networks,"Xiaoqian Jiang, Miran Kim, Kristin Lauter, Yongsoo Song","{""en"":""This paper proposes an efficient homomorphic encryption-based matrix computation method enabling neural network inference on encrypted data and models in untrusted environments significantly improving the performance of privacy-preserving machine learning applications,and demonstrating its practicality on the MNIST dataset."";""zh"":""本文提出了一种高效的同态加密矩阵计算方法，支持在不可信环境下对加密数据和模型进行神经网络推理，显著提升了隐私保护下的机器学习应用性能，并在MNIST数据集上验证了其实用性。""} ",CCS/2018,96.jsonl
47,同态加密、矩阵计算、隐私保护、神经网络、外包计算、加密推理,"Homomorphic Encryption, Matrix Computation, Privacy-preserving, Neural Network, Outsourced Computation, Encrypted Inference",同态加密在机器学习中的主要应用场景有哪些？,What are the main application scenarios of homomorphic encryption in machine learning?,隐私保护模型推理、加密数据分析和安全外包计算是主要应用场景。,"Privacy-preserving model inference, encrypted data analysis, and secure outsourced computation are the main application scenarios.",Secure Outsourced Matrix Computation and Application to Neural Networks,"Xiaoqian Jiang, Miran Kim, Kristin Lauter, Yongsoo Song","{""en"":""This paper proposes an efficient homomorphic encryption-based matrix computation method enabling neural network inference on encrypted data and models in untrusted environments significantly improving the performance of privacy-preserving machine learning applications,and demonstrating its practicality on the MNIST dataset."";""zh"":""本文提出了一种高效的同态加密矩阵计算方法，支持在不可信环境下对加密数据和模型进行神经网络推理，显著提升了隐私保护下的机器学习应用性能，并在MNIST数据集上验证了其实用性。""} ",CCS/2018,96.jsonl
48,同态加密、矩阵计算、隐私保护、神经网络、外包计算、加密推理,"Homomorphic Encryption, Matrix Computation, Privacy-preserving, Neural Network, Outsourced Computation, Encrypted Inference",矩阵计算效率对隐私保护机器学习有何影响？,How does matrix computation efficiency affect privacy-preserving machine learning?,高效矩阵计算可降低同态加密下的计算延迟，提高实际应用的可行性和性能。,"Efficient matrix computation can reduce computation latency under homomorphic encryption, improving the feasibility and performance of real-world applications.",Secure Outsourced Matrix Computation and Application to Neural Networks,"Xiaoqian Jiang, Miran Kim, Kristin Lauter, Yongsoo Song","{""en"":""This paper proposes an efficient homomorphic encryption-based matrix computation method enabling neural network inference on encrypted data and models in untrusted environments significantly improving the performance of privacy-preserving machine learning applications,and demonstrating its practicality on the MNIST dataset."";""zh"":""本文提出了一种高效的同态加密矩阵计算方法，支持在不可信环境下对加密数据和模型进行神经网络推理，显著提升了隐私保护下的机器学习应用性能，并在MNIST数据集上验证了其实用性。""} ",CCS/2018,96.jsonl
49,编程语言安全、程序分析、信息流安全、加密API、Web安全,"Programming Languages Security, Program Analysis, Information Flow Security, Cryptographic APIs, Web Security",如何利用程序分析技术发现软件系统中的安全漏洞？,How can program analysis techniques be used to discover security vulnerabilities in software systems?,静态与动态分析在漏洞发现和安全机制验证中具有重要作用。,Static and dynamic analysis play important roles in vulnerability discovery and security mechanism verification.,PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,"Piotr Mardziel, Niki Vazou","{""en"":""PLAS 2019 focuses on the application of programming languages and program analysis to software security covering topics such as information flow theory cryptographic APIs,JavaScript and firewall analysis,and PDF security,promoting research and collaboration at the intersection of security and programming languages."";""zh"":""PLAS 2019聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等前沿议题，推动了安全与程序语言交叉领域的研究与交流。""}",CCS/2019,133.jsonl
50,编程语言安全、程序分析、信息流安全、加密API、Web安全,"Programming Languages Security, Program Analysis, Information Flow Security, Cryptographic APIs, Web Security",编程语言设计如何提升Web应用的安全性？,How can programming language design enhance the security of web applications?,Web编程语言的安全特性有助于防护新型威胁。,Security features of web programming languages help defend against emerging threats.,PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,"Piotr Mardziel, Niki Vazou","{""en"":""PLAS 2019 focuses on the application of programming languages and program analysis to software security covering topics such as information flow theory cryptographic APIs,JavaScript and firewall analysis,and PDF security,promoting research and collaboration at the intersection of security and programming languages."";""zh"":""PLAS 2019聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等前沿议题，推动了安全与程序语言交叉领域的研究与交流。""}",CCS/2019,133.jsonl
51,编程语言安全、程序分析、信息流安全、加密API、Web安全,"Programming Languages Security, Program Analysis, Information Flow Security, Cryptographic APIs, Web Security",信息流安全理论在实际软件系统中有哪些应用？,What are the applications of information flow security theory in real-world software systems?,信息流安全理论可用于加密协议验证、访问控制和数据泄露防护等。,"Information flow security theory can be used for cryptographic protocol verification, access control, and data leakage prevention.",PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security,"Piotr Mardziel, Niki Vazou","{""en"":""PLAS 2019 focuses on the application of programming languages and program analysis to software security covering topics such as information flow theory cryptographic APIs,JavaScript and firewall analysis,and PDF security,promoting research and collaboration at the intersection of security and programming languages."";""zh"":""PLAS 2019聚焦于编程语言与程序分析在软件安全中的应用，涵盖信息流理论、加密API、JavaScript与防火墙分析、PDF安全等前沿议题，推动了安全与程序语言交叉领域的研究与交流。""}",CCS/2019,133.jsonl
52,安全多方计算、五方协议、公平性、输出保证、加密电路,"Secure Multiparty Computation, Five-Party Protocol, Fairness, Guaranteed Output Delivery, Garbled Circuits",如何在五方安全多方计算中实现公平性和输出保证？,How can fairness and guaranteed output delivery be achieved in five-party secure multiparty computation?,广播信道和新型协议设计有助于高效实现多种安全目标。,Broadcast channels and novel protocol designs help efficiently achieve multiple security goals.,Fast Actively Secure Five-Party Computation with Security Beyond Abort,"Megha Byali, Carmit Hazay, Arpita Patra, Swati Singla","{""en"":""This paper presents efficient five-party secure multiparty computation protocols supporting fairness guaranteed output delivery and other security goals,significantly improving the practicality and security of small-scale MPC in high-latency networks."";""zh"":""本文提出了高效的五方安全多方计算协议，支持公平性、输出保证等多种安全目标，显著提升了小规模多方计算在高延迟网络下的实用性和安全性。""}",CCS/2019,51.jsonl
53,安全多方计算、五方协议、公平性、输出保证、加密电路,"Secure Multiparty Computation, Five-Party Protocol, Fairness, Guaranteed Output Delivery, Garbled Circuits",小规模多方计算协议在高延迟网络下如何优化通信与性能？,How can small-scale MPC protocols optimize communication and performance in high-latency networks?,常数轮次协议设计适应高延迟环境，通信开销低。,Constant-round protocol design adapts to high-latency environments with low communication overhead.,Fast Actively Secure Five-Party Computation with Security Beyond Abort,"Megha Byali, Carmit Hazay, Arpita Patra, Swati Singla","{""en"":""This paper presents efficient five-party secure multiparty computation protocols supporting fairness guaranteed output delivery and other security goals,significantly improving the practicality and security of small-scale MPC in high-latency networks."";""zh"":""本文提出了高效的五方安全多方计算协议，支持公平性、输出保证等多种安全目标，显著提升了小规模多方计算在高延迟网络下的实用性和安全性。""}",CCS/2019,51.jsonl
54,安全多方计算、五方协议、公平性、输出保证、加密电路,"Secure Multiparty Computation, Five-Party Protocol, Fairness, Guaranteed Output Delivery, Garbled Circuits",加密电路在安全多方计算中的作用是什么？,What is the role of garbled circuits in secure multiparty computation?,加密电路可实现高效的输入隐私保护和功能计算。,Garbled circuits enable efficient input privacy protection and function computation.,Fast Actively Secure Five-Party Computation with Security Beyond Abort,"Megha Byali, Carmit Hazay, Arpita Patra, Swati Singla","{""en"":""This paper presents efficient five-party secure multiparty computation protocols supporting fairness guaranteed output delivery and other security goals,significantly improving the practicality and security of small-scale MPC in high-latency networks."";""zh"":""本文提出了高效的五方安全多方计算协议，支持公平性、输出保证等多种安全目标，显著提升了小规模多方计算在高延迟网络下的实用性和安全性。""}",CCS/2019,51.jsonl
55,设备指纹、磁信号、CPU、智能设备认证、电磁辐射,"Device Fingerprinting, Magnetic Signals, CPU, Smart Device Authentication, Electromagnetic Radiation",如何利用硬件磁信号实现智能设备的唯一性识别？,How can hardware magnetic signals be used for unique identification of smart devices?,分析CPU模块磁信号可实现设备级别的唯一性指纹识别。,Analyzing CPU module magnetic signals enables device-level unique fingerprinting.,DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,"Yushi Cheng, Xiaoyu Ji, Juchuan Zhang, Wenyuan Xu, Yi-Chao Chen","{""en"":""This paper proposes DeMiCPUa device fingerprinting method based on CPU magnetic signals which can accurately distinguish a large number of smart devices,is resistant to impersonation and replay attacks,and is applicable to mobile device authentication."";""zh"":""本文提出了一种基于CPU磁信号的设备指纹识别方法DeMiCPU，能够高精度区分大量智能设备，具有抗仿冒和抗重放攻击能力，适用于移动设备认证等场景。""}",CCS/2019,66.jsonl
56,设备指纹、磁信号、CPU、智能设备认证、电磁辐射,"Device Fingerprinting, Magnetic Signals, CPU, Smart Device Authentication, Electromagnetic Radiation",DeMiCPU方法在抗仿冒和抗重放攻击方面有哪些优势？,What are the advantages of DeMiCPU in resisting impersonation and replay attacks?,硬件差异和磁信号难以复制，有助于提升抗仿冒和抗重放能力。,Hardware discrepancies and the difficulty of replicating magnetic signals help enhance resistance to impersonation and replay attacks.,DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,"Yushi Cheng, Xiaoyu Ji, Juchuan Zhang, Wenyuan Xu, Yi-Chao Chen","{""en"":""This paper proposes DeMiCPUa device fingerprinting method based on CPU magnetic signals which can accurately distinguish a large number of smart devices,is resistant to impersonation and replay attacks,and is applicable to mobile device authentication."";""zh"":""本文提出了一种基于CPU磁信号的设备指纹识别方法DeMiCPU，能够高精度区分大量智能设备，具有抗仿冒和抗重放攻击能力，适用于移动设备认证等场景。""}",CCS/2019,66.jsonl
57,设备指纹、磁信号、CPU、智能设备认证、电磁辐射,"Device Fingerprinting, Magnetic Signals, CPU, Smart Device Authentication, Electromagnetic Radiation",该方法对移动设备认证的实际应用价值如何？,What is the practical value of this method for mobile device authentication?,适用于大规模移动设备认证，支持低采样率和多操作系统环境。,"Suitable for large-scale mobile device authentication, supporting low sampling rates and multiple operating systems.",DeMiCPU: Device Fingerprinting with Magnetic Signals Radiated by CPU,"Yushi Cheng, Xiaoyu Ji, Juchuan Zhang, Wenyuan Xu, Yi-Chao Chen","{""en"":""This paper proposes DeMiCPUa device fingerprinting method based on CPU magnetic signals which can accurately distinguish a large number of smart devices,is resistant to impersonation and replay attacks,and is applicable to mobile device authentication."";""zh"":""本文提出了一种基于CPU磁信号的设备指纹识别方法DeMiCPU，能够高精度区分大量智能设备，具有抗仿冒和抗重放攻击能力，适用于移动设备认证等场景。""}",CCS/2019,66.jsonl
58,配对密码、自动化验证、PPE测试、密码学设计、AutoPPE工具,"Pairing-based Cryptography, Automated Verification, PPE Testability, Cryptographic Design, AutoPPE Tool",如何自动化验证配对密码方案中的元素正确性？,How can the correctness of elements in pairing-based cryptographic schemes be automatically verified?,系统搜索PPE方程可实现配对元素的自动化验证。,Systematic search of PPE equations enables automated verification of pairing elements.,Are These Pairing Elements Correct? Automated Verification and Applications,"Susan Hohenberger, Satyanarayana Vusirikala","{""en"":""This paper proposes an automated algorithm and tool AutoPPE for verifying the correctness of pairing elements,systematically analyzes pairing product equation (PPE) testability and its applications in cryptographic design,improving the efficiency and security of pairing-based cryptographic schemes."";""zh"":""本文提出了自动化验证配对元素正确性的算法和工具AutoPPE，系统性分析了配对产品方程（PPE）可验证性及其在密码学设计中的应用，提升了配对密码方案的设计效率与安全性。""}",CCS/2019 ,99.jsonl
59,配对密码、自动化验证、PPE测试、密码学设计、AutoPPE工具,"Pairing-based Cryptography, Automated Verification, PPE Testability, Cryptographic Design, AutoPPE Tool",PPE可验证性在密码学设计中有哪些实际应用？,What are the practical applications of PPE testability in cryptographic design?,PPE可验证性广泛应用于签名、可验证随机函数、可追责加密等密码学构造。,"PPE testability is widely used in signatures, verifiable random functions, accountable encryption, and other cryptographic constructions.",Are These Pairing Elements Correct? Automated Verification and Applications,"Susan Hohenberger, Satyanarayana Vusirikala","{""en"":""This paper proposes an automated algorithm and tool AutoPPE for verifying the correctness of pairing elements,systematically analyzes pairing product equation (PPE) testability and its applications in cryptographic design,improving the efficiency and security of pairing-based cryptographic schemes."";""zh"":""本文提出了自动化验证配对元素正确性的算法和工具AutoPPE，系统性分析了配对产品方程（PPE）可验证性及其在密码学设计中的应用，提升了配对密码方案的设计效率与安全性。""}",CCS/2019 ,99.jsonl
60,配对密码、自动化验证、PPE测试、密码学设计、AutoPPE工具,"Pairing-based Cryptography, Automated Verification, PPE Testability, Cryptographic Design, AutoPPE Tool",AutoPPE工具如何提升配对密码方案的设计效率？,How does the AutoPPE tool improve the design efficiency of pairing-based cryptographic schemes?,自动化生成验证方程可减少人工推导，提高设计准确性和效率。,Automated generation of verification equations reduces manual derivation and improves design accuracy and efficiency.,Are These Pairing Elements Correct? Automated Verification and Applications,"Susan Hohenberger, Satyanarayana Vusirikala","{""en"":""This paper proposes an automated algorithm and tool AutoPPE for verifying the correctness of pairing elements,systematically analyzes pairing product equation (PPE) testability and its applications in cryptographic design,improving the efficiency and security of pairing-based cryptographic schemes."";""zh"":""本文提出了自动化验证配对元素正确性的算法和工具AutoPPE，系统性分析了配对产品方程（PPE）可验证性及其在密码学设计中的应用，提升了配对密码方案的设计效率与安全性。""}",CCS/2019 ,99.jsonl
61,多方安全计算、差分隐私、可信执行环境、人工智能、商业智能、数据协作,"Secure Multi-Party Computation, Differential Privacy, Trusted Execution Environment, Artificial Intelligence, Business Intelligence, Data Collaboration",如何在多组织间实现数据协作的同时保护用户隐私？,How can user privacy be protected while enabling data collaboration across organizations?,多种隐私保护技术结合可保障多方数据协作过程中的隐私与合规性。,The combination of multiple privacy-preserving technologies can ensure privacy and compliance in multi-party data collaboration.,Introduction to Secure Collaborative Intelligence (SCI) Lab,Pu Duan,"{""en"":""The SCI Lab is dedicated to applying privacy-preserving technologies in AI and BI combining secure multi-party computation differential privacy,and trusted execution environments to enable cross-organization data collaboration and risk control,with real-world deployments in finance and other sectors."";""zh"":""SCI实验室致力于多方隐私保护技术在AI和BI领域的应用，结合安全多方计算、差分隐私和可信执行环境，推动跨组织数据协作与风险控制，已在金融等场景落地。""}",CCS/2020 ,163.jsonl
62,多方安全计算、差分隐私、可信执行环境、人工智能、商业智能、数据协作,"Secure Multi-Party Computation, Differential Privacy, Trusted Execution Environment, Artificial Intelligence, Business Intelligence, Data Collaboration",哪些前沿隐私保护技术被应用于企业级AI/BI协作平台？,Which cutting-edge privacy-preserving technologies are applied in enterprise-level AI/BI collaboration platforms?,安全多方计算、差分隐私和可信执行环境是企业级协作平台常用的隐私保护技术。,"Secure multi-party computation, differential privacy, and trusted execution environments are commonly used privacy-preserving technologies in enterprise-level collaboration platforms.",Introduction to Secure Collaborative Intelligence (SCI) Lab,Pu Duan,"{""en"":""The SCI Lab is dedicated to applying privacy-preserving technologies in AI and BI combining secure multi-party computation differential privacy,and trusted execution environments to enable cross-organization data collaboration and risk control,with real-world deployments in finance and other sectors."";""zh"":""SCI实验室致力于多方隐私保护技术在AI和BI领域的应用，结合安全多方计算、差分隐私和可信执行环境，推动跨组织数据协作与风险控制，已在金融等场景落地。""}",CCS/2020 ,163.jsonl
63,多方安全计算、差分隐私、可信执行环境、人工智能、商业智能、数据协作,"Secure Multi-Party Computation, Differential Privacy, Trusted Execution Environment, Artificial Intelligence, Business Intelligence, Data Collaboration",SCI实验室的隐私保护平台已在哪些实际业务场景落地？,In which real-world business scenarios has the SCI Lab's privacy-preserving platform been deployed?,平台已应用于联合风控、协同数据分析、联合支付反欺诈等多种金融场景。,"The platform has been deployed in joint risk control, collaborative data analysis, joint payment fraud detection, and other financial scenarios.",Introduction to Secure Collaborative Intelligence (SCI) Lab,Pu Duan,"{""en"":""The SCI Lab is dedicated to applying privacy-preserving technologies in AI and BI combining secure multi-party computation differential privacy,and trusted execution environments to enable cross-organization data collaboration and risk control,with real-world deployments in finance and other sectors."";""zh"":""SCI实验室致力于多方隐私保护技术在AI和BI领域的应用，结合安全多方计算、差分隐私和可信执行环境，推动跨组织数据协作与风险控制，已在金融等场景落地。""}",CCS/2020 ,163.jsonl
64,属性加密、属性无感翻译、隐私保护数据共享、多组织协作、访问控制,"Attribute-Based Encryption, Oblivious Attribute Translation, Privacy-Preserving Data Sharing, Interorganizational Collaboration, Access Control",如何在多组织间实现基于多词汇的隐私保护数据共享？,How can privacy-preserving data sharing be achieved across organizations with different vocabularies?,OTABE机制支持多组织自定义属性词汇，实现安全的数据共享与访问控制。,"The OTABE mechanism supports organizations in defining their own attribute vocabularies, enabling secure data sharing and access control.","PRShare: A Framework for Privacy-Preserving, Interorganizational Data Sharing","Lihi Idan, Joan Feigenbaum","{""en"":""PRShare proposes a privacy-preserving framework for interorganizational data sharing introducing Attribute-Based Encryption with Oblivious Attribute Translation (OTABE) to support multi-vocabulary attribute privacy,and flexible access control,applicable to law enforcement,finance,and more."";""zh"":""PRShare提出了一种支持多组织间数据共享的隐私保护框架，创新性地引入了带有属性无感翻译的属性加密（OTABE），实现了多词汇、属性隐私和灵活的访问控制，适用于执法、金融等多场景。""}",CCS/2020 ,182.jsonl
65,属性加密、属性无感翻译、隐私保护数据共享、多组织协作、访问控制,"Attribute-Based Encryption, Oblivious Attribute Translation, Privacy-Preserving Data Sharing, Interorganizational Collaboration, Access Control",OTABE机制在数据共享安全中起到什么作用？,What role does OTABE play in secure data sharing?,OTABE机制实现属性无感翻译和隐藏访问策略，保护组织间的属性隐私。,"The OTABE mechanism enables oblivious attribute translation and hidden access policies, protecting attribute privacy across organizations.","PRShare: A Framework for Privacy-Preserving, Interorganizational Data Sharing","Lihi Idan, Joan Feigenbaum","{""en"":""PRShare proposes a privacy-preserving framework for interorganizational data sharing introducing Attribute-Based Encryption with Oblivious Attribute Translation (OTABE) to support multi-vocabulary attribute privacy,and flexible access control,applicable to law enforcement,finance,and more."";""zh"":""PRShare提出了一种支持多组织间数据共享的隐私保护框架，创新性地引入了带有属性无感翻译的属性加密（OTABE），实现了多词汇、属性隐私和灵活的访问控制，适用于执法、金融等多场景。""}",CCS/2020 ,182.jsonl
66,属性加密、属性无感翻译、隐私保护数据共享、多组织协作、访问控制,"Attribute-Based Encryption, Oblivious Attribute Translation, Privacy-Preserving Data Sharing, Interorganizational Collaboration, Access Control",PRShare框架适用于哪些实际应用场景？,What real-world application scenarios is the PRShare framework suitable for?,适用于执法数据共享、金融征信、医疗等需保护隐私的跨组织数据协作场景。,"Suitable for law enforcement data sharing, financial credit, healthcare, and other privacy-sensitive interorganizational collaboration scenarios.","PRShare: A Framework for Privacy-Preserving, Interorganizational Data Sharing","Lihi Idan, Joan Feigenbaum","{""en"":""PRShare proposes a privacy-preserving framework for interorganizational data sharing introducing Attribute-Based Encryption with Oblivious Attribute Translation (OTABE) to support multi-vocabulary attribute privacy,and flexible access control,applicable to law enforcement,finance,and more."";""zh"":""PRShare提出了一种支持多组织间数据共享的隐私保护框架，创新性地引入了带有属性无感翻译的属性加密（OTABE），实现了多词汇、属性隐私和灵活的访问控制，适用于执法、金融等多场景。""}",CCS/2020 ,182.jsonl
67,调度器侧信道、实时系统、不可区分性、差分隐私、ε-Scheduler,"Scheduler Side-Channels, Real-Time Systems, Indistinguishability, Differential Privacy, ε-Scheduler",如何防止实时系统中因调度确定性导致的侧信道信息泄露？,How can side-channel information leakage caused by scheduling determinism in real-time systems be prevented?,调度不可区分性和受控噪声机制可打破任务执行的确定性，防止侧信道泄露。,Schedule indistinguishability and controlled noise mechanisms can break deterministic task execution and prevent side-channel leakage.,Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"Chien-Ying Chen, Debopam Sanyal, Sibin Mohan","{""en"":""This paper introduces the concept of 'schedule indistinguishability' by injecting controlled noise into real-time system scheduling breaking deterministic task execution to prevent scheduler side-channel leakage. The implemented ε-Scheduler in Linux real-time systems effectively enhances security while maintaining performance and quality of service."";""zh"":""本文提出了""调度不可区分性""概念，通过在实时系统调度中引入受控噪声，打破任务执行的确定性，防止调度器侧信道泄露。实现的ε-Scheduler在Linux实时系统中有效提升了安全性，同时兼顾性能和服务质量。""}",CCS/2021,45.jsonl
68,调度器侧信道、实时系统、不可区分性、差分隐私、ε-Scheduler,"Scheduler Side-Channels, Real-Time Systems, Indistinguishability, Differential Privacy, ε-Scheduler",ε-Scheduler在提升实时系统安全性方面有哪些优势？,What are the advantages of ε-Scheduler in enhancing the security of real-time systems?,ε-Scheduler可提升系统对调度侧信道攻击的防护能力，并提供可量化的安全保障。,ε-Scheduler can enhance protection against scheduler side-channel attacks and provide quantifiable security guarantees.,Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"Chien-Ying Chen, Debopam Sanyal, Sibin Mohan","{""en"":""This paper introduces the concept of 'schedule indistinguishability' by injecting controlled noise into real-time system scheduling breaking deterministic task execution to prevent scheduler side-channel leakage. The implemented ε-Scheduler in Linux real-time systems effectively enhances security while maintaining performance and quality of service."";""zh"":""本文提出了""调度不可区分性""概念，通过在实时系统调度中引入受控噪声，打破任务执行的确定性，防止调度器侧信道泄露。实现的ε-Scheduler在Linux实时系统中有效提升了安全性，同时兼顾性能和服务质量。""}",CCS/2021,45.jsonl
69,调度器侧信道、实时系统、不可区分性、差分隐私、ε-Scheduler,"Scheduler Side-Channels, Real-Time Systems, Indistinguishability, Differential Privacy, ε-Scheduler",差分隐私思想如何应用于实时系统调度安全？,How is the concept of differential privacy applied to scheduling security in real-time systems?,差分隐私中的噪声机制可为调度引入随机性，实现任务间的不可区分性，提升系统安全性。,"Noise mechanisms from differential privacy can introduce randomness into scheduling, achieving indistinguishability among tasks and enhancing system security.",Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems,"Chien-Ying Chen, Debopam Sanyal, Sibin Mohan","{""en"":""This paper introduces the concept of 'schedule indistinguishability' by injecting controlled noise into real-time system scheduling breaking deterministic task execution to prevent scheduler side-channel leakage. The implemented ε-Scheduler in Linux real-time systems effectively enhances security while maintaining performance and quality of service."";""zh"":""本文提出了""调度不可区分性""概念，通过在实时系统调度中引入受控噪声，打破任务执行的确定性，防止调度器侧信道泄露。实现的ε-Scheduler在Linux实时系统中有效提升了安全性，同时兼顾性能和服务质量。""}",CCS/2021,45.jsonl
70,补丁分析、漏洞挖掘、.NET框架、MSIL、自动化安全工具,"Patch Analysis, Vulnerability Mining, .NET Framework, MSIL, Automated Security Tool",如何高效定位大型.NET软件补丁中与安全漏洞相关的关键方法？,How can key methods related to security vulnerabilities be efficiently located in large .NET software patches?,MSIL指令和控制流图特征对比有助于快速筛选补丁中与漏洞相关的关键方法。,Comparing MSIL instructions and control flow graph features helps quickly filter out key methods related to vulnerabilities in patches.,Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,"Can Huang, Cheng Li, Jiashuo Liang, Xinhui Han","{""en"":""This paper presents MSILDiffer a security patch analysis framework for large .NET software based on Microsoft Intermediate Language (MSIL) which efficiently and accurately locates key methods related to patches and assists in 1-day vulnerability mining. Experiments show that MSILDiffer outperforms JustAssembly in coverage,accuracy,and analysis speed,and has successfully discovered multiple real-world vulnerabilities."";""zh"":""本文提出了基于MSIL的.NET大型软件安全补丁分析框架MSILDiffer，能够高效、准确地定位补丁相关的关键方法，辅助1-day漏洞挖掘。实验表明，MSILDiffer在覆盖率、准确率和分析速度上均优于JustAssembly，并已成功挖掘多个真实漏洞。""}",CCS/2022,124.jsonl
71,补丁分析、漏洞挖掘、.NET框架、MSIL、自动化安全工具,"Patch Analysis, Vulnerability Mining, .NET Framework, MSIL, Automated Security Tool",MSILDiffer在补丁分析中相较于传统工具有哪些优势？,What are the advantages of MSILDiffer over traditional tools in patch analysis?,高覆盖率、准确率和分析速度有助于更好地过滤无效差异并定位真实漏洞点。,"High coverage, accuracy, and analysis speed help better filter invalid differences and locate real vulnerability points.",Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,"Can Huang, Cheng Li, Jiashuo Liang, Xinhui Han","{""en"":""This paper presents MSILDiffer a security patch analysis framework for large .NET software based on Microsoft Intermediate Language (MSIL) which efficiently and accurately locates key methods related to patches and assists in 1-day vulnerability mining. Experiments show that MSILDiffer outperforms JustAssembly in coverage,accuracy,and analysis speed,and has successfully discovered multiple real-world vulnerabilities."";""zh"":""本文提出了基于MSIL的.NET大型软件安全补丁分析框架MSILDiffer，能够高效、准确地定位补丁相关的关键方法，辅助1-day漏洞挖掘。实验表明，MSILDiffer在覆盖率、准确率和分析速度上均优于JustAssembly，并已成功挖掘多个真实漏洞。""}",CCS/2022,124.jsonl
72,补丁分析、漏洞挖掘、.NET框架、MSIL、自动化安全工具,"Patch Analysis, Vulnerability Mining, .NET Framework, MSIL, Automated Security Tool",MSILDiffer如何辅助1-day漏洞的自动化挖掘与验证？,How does MSILDiffer assist in the automated mining and verification of 1-day vulnerabilities?,补丁差异分析与方法调用路径生成有助于自动化生成POC并验证1-day漏洞。,Patch difference analysis and method call path generation help automate POC generation and 1-day vulnerability verification.,Poster: MSILDiffer – A Security Patch Analysis Framework Based on Microsoft Intermediate Language for Large Software,"Can Huang, Cheng Li, Jiashuo Liang, Xinhui Han","{""en"":""This paper presents MSILDiffer a security patch analysis framework for large .NET software based on Microsoft Intermediate Language (MSIL) which efficiently and accurately locates key methods related to patches and assists in 1-day vulnerability mining. Experiments show that MSILDiffer outperforms JustAssembly in coverage,accuracy,and analysis speed,and has successfully discovered multiple real-world vulnerabilities."";""zh"":""本文提出了基于MSIL的.NET大型软件安全补丁分析框架MSILDiffer，能够高效、准确地定位补丁相关的关键方法，辅助1-day漏洞挖掘。实验表明，MSILDiffer在覆盖率、准确率和分析速度上均优于JustAssembly，并已成功挖掘多个真实漏洞。""}",CCS/2022,124.jsonl
73,MPEG安全、选择性加密、视频加密、I帧、压缩效率,"MPEG Security, Selective Encryption, Video Encryption, I-frame, Compression Efficiency",如何在保证视频压缩效率的同时提升MPEG视频传输的安全性？,How can the security of MPEG video transmission be enhanced while maintaining compression efficiency?,选择性加密可减少加密计算负担，但需权衡安全性与压缩率，合理调整加密粒度和I帧频率。,Selective encryption reduces computational overhead but requires balancing security and compression by adjusting encryption granularity and I-frame frequency.,An Empirical Study of Secure MPEG Video Transmissions,"Iskender Agi, Li Gong","{""en"":""This paper empirically studies selective encryption schemes for MPEG video revealing their limitations for sensitive applications and discussing trade-offs between security computational cost,and compression efficiency."";""zh"":""本文实证分析了MPEG视频的选择性加密方案，指出其在敏感应用中的局限性，并探讨了安全性、计算开销与压缩效率之间的权衡。""}",NDSS/1996,1.jsonl
74,MPEG加密、I块、运动补偿、视频隐私、信息泄露,"MPEG Encryption, I-block, Motion Compensation, Video Privacy, Information Leakage",MPEG视频中未加密的I块和运动补偿机制会带来哪些信息泄露风险？,What information leakage risks are posed by unencrypted I-blocks and motion compensation in MPEG videos?,未加密I块和帧间相关性可能导致加密视频仍可被部分还原，存在隐私泄露隐患。,Unencrypted I-blocks and inter-frame correlation may allow partial recovery of encrypted video posing privacy risks.,An Empirical Study of Secure MPEG Video Transmissions,"Iskender Agi, Li Gong","{""en"":""This paper empirically studies selective encryption schemes for MPEG video revealing their limitations for sensitive applications and discussing trade-offs between security computational cost,and compression efficiency."";""zh"":""本文实证分析了MPEG视频的选择性加密方案，指出其在敏感应用中的局限性，并探讨了安全性、计算开销与压缩效率之间的权衡。""}",NDSS/1996,1.jsonl
75,MPEG标准、加密机制、实时应用、带宽消耗、安全参数优化,"MPEG Standard, Encryption Mechanism, Real-time Application, Bandwidth Consumption, Security Parameter Optimization",在实时多媒体应用中，如何优化MPEG加密参数以兼顾安全性和带宽消耗？,How can MPEG encryption parameters be optimized in real-time multimedia applications to balance security and bandwidth consumption?,加密参数如I帧频率和加密范围需根据应用场景动态调整，以实现安全性与带宽利用率的平衡。,Encryption parameters such as I-frame frequency and encryption scope should be dynamically adjusted according to application scenarios to balance security and bandwidth utilization.,An Empirical Study of Secure MPEG Video Transmissions,"Iskender Agi, Li Gong","{""en"":""This paper empirically studies selective encryption schemes for MPEG video revealing their limitations for sensitive applications and discussing trade-offs between security computational cost,and compression efficiency."";""zh"":""本文实证分析了MPEG视频的选择性加密方案，指出其在敏感应用中的局限性，并探讨了安全性、计算开销与压缩效率之间的权衡。""}",NDSS/1996,1.jsonl
76,Java安全、分布式策略管理、SPKI证书、授权、访问控制,"Java Security, Distributed Policy Management, SPKI Certificate, Authorization, Access Control",如何在大规模分布式Java环境中实现灵活的安全策略管理？,How can flexible security policy management be achieved in large-scale distributed Java environments?,SPKI授权证书可实现分布式、动态的权限分配，提升Java环境的安全策略灵活性。,SPKI authorization certificates enable distributed and dynamic permission assignment enhancing the flexibility of security policies in Java environments.,Distributed Policy Management for Java 2,Jonna Partanen,"{""en"":""This paper explores the use of SPKI authorization certificates to improve distributed policy enabling scalable,secure,and dynamic access control."";""zh"":""本文探讨了利用SPKI授权证书提升Java 2分布式策略管理，实现可扩展、安全和动态的访问控制。""}",NDSS/1999,14.jsonl
77,Java 2、访问控制、保护域、临时密钥、委托授权,"Java 2, Access Control, Protection Domain, Temporary Key, Delegation Authorization",Java 2安全模型中，如何通过保护域和临时密钥实现权限委托？,How can protection domains and temporary keys be used for permission delegation in the Java 2 security model?,保护域结合临时密钥机制可实现权限的安全委托，支持分布式环境下的动态授权。,Protection domains combined with temporary key mechanisms enable secure delegation of permissions supporting dynamic authorization in distributed environments.,Distributed Policy Management for Java 2,Jonna Partanen,"{""en"":""This paper explores the use of SPKI authorization certificates to improve distributed policy enabling scalable,secure,and dynamic access control."";""zh"":""本文探讨了利用SPKI授权证书提升Java 2分布式策略管理，实现可扩展、安全和动态的访问控制。""}",NDSS/1999,14.jsonl
78,SPKI、授权证书、Java权限对象、动态访问控制、策略可扩展性,"SPKI, Authorization Certificate, Java Permission Object, Dynamic Access Control, Policy Scalability",SPKI授权证书如何提升Java应用的动态访问控制与策略可扩展性？,How do SPKI authorization certificates enhance dynamic access control and policy scalability in Java applications?,SPKI证书可直接绑定权限到密钥，实现匿名授权和灵活的权限管理，提升系统可扩展性。,SPKI certificates can directly bind permissions to keys enabling anonymous authorization and flexible permission management thus improving system scalability.,Distributed Policy Management for Java 2,Jonna Partanen,"{""en"":""This paper explores the use of SPKI authorization certificates to improve distributed policy enabling scalable,secure,and dynamic access control."";""zh"":""本文探讨了利用SPKI授权证书提升Java 2分布式策略管理，实现可扩展、安全和动态的访问控制。""}",NDSS/1999,14.jsonl
79,多安全域网络、联盟网络、访问控制、加密架构、虚拟专用网,"Multi-Security Domain Network, Alliance Network, Access Control, Encryption Architecture, Virtual Private Network",如何在多安全域环境下实现灵活的联盟网络访问控制？,How can flexible alliance network access control be achieved in multi-security domain environments?,结合对称与非对称加密技术可实现多安全域下的分级访问与动态联盟管理。,Combining symmetric and asymmetric encryption enables hierarchical access and dynamic alliance management across multiple security domains.,An Architecture for Flexible Multi-Security Domain Networks,Tim Gibson,"{""en"":""This paper proposes an architecture for secure multi-security domain networksaddressing interoperability and access control challenges in military and commercial alliances through layered encryption and flexible policy management."";""zh"":""本文提出了一种多安全域网络架构，通过分层加密与灵活策略管理，解决军事及商业联盟中的互操作性与访问控制难题。""}",NDSS/2001,8.jsonl
80,联盟网络、社区隔离、仲裁服务器、数字证书、动态成员管理,"Alliance Network, Community Isolation, Arbitration Server, Digital Certificate, Dynamic Membership Management",联盟网络架构如何支持不同社区的隔离与动态成员管理？,How does alliance network architecture support community isolation and dynamic membership management?,仲裁服务器与数字证书机制可实现社区隔离与成员权限的灵活调整。,Arbitration servers and digital certificate mechanisms enable community isolation and flexible adjustment of member privileges.,An Architecture for Flexible Multi-Security Domain Networks,Tim Gibson,"{""en"":""This paper proposes an architecture for secure multi-security domain networksaddressing interoperability and access control challenges in military and commercial alliances through layered encryption and flexible policy management."";""zh"":""本文提出了一种多安全域网络架构，通过分层加密与灵活策略管理，解决军事及商业联盟中的互操作性与访问控制难题。""}",NDSS/2001,8.jsonl
81,军事网络、加密硬件、访问撤销、互操作性、商业应用,"Military Network, Encryption Hardware, Access Revocation, Interoperability, Commercial Application",如何在联盟网络中实现高效的访问撤销与权限恢复？,How can efficient access revocation and privilege restoration be achieved in alliance networks?,基于硬件加密与仲裁服务器的权限管理机制可实现快速撤销与恢复成员访问。,Hardware-based encryption and arbitration server privilege management enable rapid revocation and restoration of member access.,An Architecture for Flexible Multi-Security Domain Networks,Tim Gibson,"{""en"":""This paper proposes an architecture for secure multi-security domain networksaddressing interoperability and access control challenges in military and commercial alliances through layered encryption and flexible policy management."";""zh"":""本文提出了一种多安全域网络架构，通过分层加密与灵活策略管理，解决军事及商业联盟中的互操作性与访问控制难题。""}",NDSS/2001,8.jsonl
82,自动信任协商、访问控制策略、数字凭证、策略泄露保护、渐进信任建立,"Automated Trust Negotiation, Access Control Policy, Digital Credential, Policy Disclosure Protection, Gradual Trust Establishment",如何在自动信任协商中保护访问控制策略的敏感信息？,How can sensitive information in access control policies be protected during automated trust negotiation?,通过分阶段披露策略与凭证，可在建立信任的同时保护策略敏感信息。,Gradual disclosure of policies and credentials enables trust establishment while protecting sensitive policy information.,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,"Kent E. Seamons, Marianne Winslett, Ting Yu","{""en"":""This paper analyzes strategies for protecting sensitive access control policies during automated trust negotiation proposing gradual trust establishment and selective disclosure to balance security and usability."";""zh"":""本文分析了自动信任协商中保护敏感访问控制策略的策略，提出渐进式信任建立与选择性披露以平衡安全性与可用性。""}",NDSS/2001,16.jsonl
83,数字凭证、策略图、访问控制、策略语言、授权路径,"Digital Credential, Policy Graph, Access Control, Policy Language, Authorization Path",策略图在自动信任协商中的作用是什么？,What is the role of policy graphs in automated trust negotiation?,策略图可形式化描述多层访问控制需求，支持灵活的授权路径选择。,Policy graphs formalize multi-layer access control requirements and support flexible authorization path selection.,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,"Kent E. Seamons, Marianne Winslett, Ting Yu","{""en"":""This paper analyzes strategies for protecting sensitive access control policies during automated trust negotiation proposing gradual trust establishment and selective disclosure to balance security and usability."";""zh"":""本文分析了自动信任协商中保护敏感访问控制策略的策略，提出渐进式信任建立与选择性披露以平衡安全性与可用性。""}",NDSS/2001,16.jsonl
84,信任协商策略、凭证披露、通信效率、策略安全性、最小披露集,"Trust Negotiation Strategy, Credential Disclosure, Communication Efficiency, Policy Security, Minimal Disclosure Set",如何在保证策略安全性的前提下优化信任协商中的凭证披露与通信效率？,How can credential disclosure and communication efficiency be optimized in trust negotiation while ensuring policy security?,通过相关凭证集与全部相关策略两种协商策略，可在安全性与通信效率间灵活权衡。,Relevant credential set and all relevant policies strategies enable flexible trade-offs between security and communication efficiency.,Limiting the Disclosure of Access Control Policies During Automated Trust Negotiation,"Kent E. Seamons, Marianne Winslett, Ting Yu","{""en"":""This paper analyzes strategies for protecting sensitive access control policies during automated trust negotiation proposing gradual trust establishment and selective disclosure to balance security and usability."";""zh"":""本文分析了自动信任协商中保护敏感访问控制策略的策略，提出渐进式信任建立与选择性披露以平衡安全性与可用性。""}",NDSS/2001,16.jsonl
85,跨站脚本、动态污点分析、静态分析、浏览器安全、信息流追踪,"Cross-Site Scripting, Dynamic Taint Analysis, Static Analysis, Browser Security, Information Flow Tracking",如何通过动态污点分析与静态分析结合提升跨站脚本攻击防护能力？,How can the combination of dynamic taint analysis and static analysis enhance protection against cross-site scripting attacks?,动态污点分析结合静态分析可全面追踪敏感信息流，有效阻断XSS攻击路径。,Combining dynamic taint analysis with static analysis enables comprehensive tracking of sensitive information flows effectively blocking XSS attack vectors.,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,"Philipp Vogt, Florian Nentwich, Nenad Jovanovic, Engin Kirda, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper proposes a client-side XSS prevention approach by integrating dynamic taint analysis and static analysis into the Firefox browser achieving reliable protection with low false positives."";""zh"":""本文提出了一种基于浏览器端的XSS防护方法，将动态污点分析与静态分析集成到Firefox浏览器中，实现了高可靠性和低误报率的防护效果。""}",NDSS/2007,15.jsonl
86,Web浏览器安全、敏感信息保护、用户隐私、跨域数据流、警告机制,"Web Browser Security, Sensitive Information Protection, User Privacy, Cross-Domain Data Flow, Warning Mechanism",Web浏览器如何有效防止敏感信息被跨域泄露？,How can web browsers effectively prevent cross-domain leakage of sensitive information?,通过信息流追踪与用户交互警告机制，可阻止敏感数据被恶意脚本跨域传输。,Information flow tracking and user-interactive warning mechanisms can prevent sensitive data from being exfiltrated across domains by malicious scripts.,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,"Philipp Vogt, Florian Nentwich, Nenad Jovanovic, Engin Kirda, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper proposes a client-side XSS prevention approach by integrating dynamic taint analysis and static analysis into the Firefox browser achieving reliable protection with low false positives."";""zh"":""本文提出了一种基于浏览器端的XSS防护方法，将动态污点分析与静态分析集成到Firefox浏览器中，实现了高可靠性和低误报率的防护效果。""}",NDSS/2007,15.jsonl
87,JavaScript安全、DOM、控制依赖、静态污点分析、自动化评测,"JavaScript Security, DOM, Control Dependency, Static Taint Analysis, Automated Evaluation",静态污点分析在JavaScript安全防护中有哪些应用优势？,What are the advantages of static taint analysis in JavaScript security protection?,静态污点分析可覆盖动态分析难以检测的间接控制依赖，提升整体安全性。,Static taint analysis can cover indirect control dependencies that are hard to detect dynamically enhancing overall security.,Cross-Site Scripting Prevention with Dynamic Data Tainting and Static Analysis,"Philipp Vogt, Florian Nentwich, Nenad Jovanovic, Engin Kirda, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper proposes a client-side XSS prevention approach by integrating dynamic taint analysis and static analysis into the Firefox browser achieving reliable protection with low false positives."";""zh"":""本文提出了一种基于浏览器端的XSS防护方法，将动态污点分析与静态分析集成到Firefox浏览器中，实现了高可靠性和低误报率的防护效果。""}",NDSS/2007,15.jsonl
88,对象能力、最小权限原则、Java子集、静态类型安全、安全编程,"Object Capability, Principle of Least Privilege, Java Subset, Static Type Safety, Secure Programming",如何通过对象能力模型和最小权限原则提升Java程序的安全性？,How can the object capability model and principle of least privilege enhance the security of Java programs?,对象能力模型结合最小权限原则可细粒度控制权限分配，降低安全风险并提升系统可审计性。,The object capability model combined with the principle of least privilege enables fine-grained privilege allocation reducing security risks and improving system auditability.,Joe-E: A Security-Oriented Subset of Java,"Adrian Mettler, David Wagner, Tyler Close","{""en"":""This paper presents Joe-E a security-oriented subset of Java that enforces object-capability discipline and static type safety,enabling secure,auditable,and extensible software systems."";""zh"":""本文提出了Joe-E，一种面向安全的Java子集，通过对象能力范式和静态类型安全，支持可审计、可扩展的安全软件系统开发。""}",NDSS/2010,10.jsonl
89,不可变性、能力安全、参考监控、模块化安全分析、恶意代码隔离,"Immutability, Capability Security, Reference Monitor, Modular Security Analysis, Malicious Code Isolation",不可变性和参考监控机制在能力安全语言中有何作用？,What roles do immutability and reference monitor mechanisms play in capability-secure languages?,不可变性保证对象状态不被篡改，参考监控机制便于实现细粒度安全策略和模块化分析。,Immutability ensures object state cannot be tampered with and reference monitors facilitate fine-grained security policies and modular analysis.,Joe-E: A Security-Oriented Subset of Java,"Adrian Mettler, David Wagner, Tyler Close","{""en"":""This paper presents Joe-E a security-oriented subset of Java that enforces object-capability discipline and static type safety,enabling secure,auditable,and extensible software systems."";""zh"":""本文提出了Joe-E，一种面向安全的Java子集，通过对象能力范式和静态类型安全，支持可审计、可扩展的安全软件系统开发。""}",NDSS/2010,10.jsonl
90,安全可扩展性、恶意插件隔离、最小信任基、安全审计、持久化一致性,"Secure Extensibility, Malicious Plugin Isolation, Minimal Trusted Computing Base, Security Audit, Persistence Consistency",如何在支持可扩展性的同时保障系统对恶意插件的安全隔离？,How can systems ensure secure isolation of malicious plugins while supporting extensibility?,能力安全语言通过限制默认权限和接口设计，实现插件级别的安全隔离与最小信任基。,Capability-secure languages achieve plugin-level isolation and minimal trusted computing base by restricting default privileges and interface design.,Joe-E: A Security-Oriented Subset of Java,"Adrian Mettler, David Wagner, Tyler Close","{""en"":""This paper presents Joe-E a security-oriented subset of Java that enforces object-capability discipline and static type safety,enabling secure,auditable,and extensible software systems."";""zh"":""本文提出了Joe-E，一种面向安全的Java子集，通过对象能力范式和静态类型安全，支持可审计、可扩展的安全软件系统开发。""}",NDSS/2010,10.jsonl
91,Sybil攻击、社交网络安全、潜在社区模型、生成模型、贝叶斯推断,"Sybil Attack, Social Network Security, Latent Community Model, Generative Model, Bayesian Inference",如何利用潜在社区模型提升社交网络中Sybil攻击的检测能力？,How can latent community models enhance the detection of Sybil attacks in social networks?,潜在社区模型通过学习网络结构的生成机制，有效识别异常社区和Sybil节点，提升检测准确率。,Latent community models learn the generative mechanisms of network structure effectively identifying anomalous communities and Sybil nodes to improve detection accuracy.,The Latent Community Model for Detecting Sybil Attacks,"Zhuhua Cai, Christopher Jermaine","{""en"":""This paper proposes a latent community (LC) model for Sybil attack detection leveraging generative modeling and Bayesian inference to identify Sybil nodes in social networks."";""zh"":""本文提出了一种基于生成模型和贝叶斯推断的潜在社区（LC）模型，用于社交网络中的Sybil攻击检测。""}",NDSS/2012,8.jsonl
92,社交网络、生成模型、异常检测、Gibbs采样、网络结构分析,"Social Network, Generative Model, Anomaly Detection, Gibbs Sampling, Network Structure Analysis",生成模型与Gibbs采样在社交网络异常检测中有何应用？,What are the applications of generative models and Gibbs sampling in anomaly detection for social networks?,生成模型结合Gibbs采样可对节点属性和社区结构进行概率建模，实现高效的异常检测。,Generative models combined with Gibbs sampling enable probabilistic modeling of node attributes and community structure for efficient anomaly detection.,The Latent Community Model for Detecting Sybil Attacks,"Zhuhua Cai, Christopher Jermaine","{""en"":""This paper proposes a latent community (LC) model for Sybil attack detection leveraging generative modeling and Bayesian inference to identify Sybil nodes in social networks."";""zh"":""本文提出了一种基于生成模型和贝叶斯推断的潜在社区（LC）模型，用于社交网络中的Sybil攻击检测。""}",NDSS/2012,8.jsonl
93,贝叶斯网络、社群划分、Sybil节点识别、算法复杂度、应用局限性,"Bayesian Network, Community Partitioning, Sybil Node Identification, Algorithm Complexity, Application Limitation",基于贝叶斯网络的Sybil检测方法在实际应用中存在哪些局限？,What are the limitations of Bayesian network-based Sybil detection methods in practice?,该方法对树状或稀疏攻击、非社交网络等场景适用性有限，且算法分布式能力不足。,This method has limited applicability to tree-structured or sparse attacks and non-social network scenarios and lacks distributed algorithm capability.,The Latent Community Model for Detecting Sybil Attacks,"Zhuhua Cai, Christopher Jermaine","{""en"":""This paper proposes a latent community (LC) model for Sybil attack detection leveraging generative modeling and Bayesian inference to identify Sybil nodes in social networks."";""zh"":""本文提出了一种基于生成模型和贝叶斯推断的潜在社区（LC）模型，用于社交网络中的Sybil攻击检测。""}",NDSS/2012,8.jsonl
94,社交网络安全、Facebook防护、实时威胁检测、创新安全机制、自动化响应,"Social Network Security, Facebook Defense, Real-time Threat Detection, Innovative Security Mechanism, Automated Response",如何在大规模社交网络中实现实时威胁检测与自动化安全响应？,How can real-time threat detection and automated security response be achieved in large-scale social networks?,结合机器学习与自动化机制可实现对社交网络威胁的实时监测与快速响应，提升整体安全性。,Combining machine learning and automation enables real-time monitoring and rapid response to threats in social networks enhancing overall security.   ,Innovating to Protect the Graph,Facebook Security Team,"{""en"":""This paper introduces Facebook's innovative security strategies including real-time threat detection,automated response,and user education,to address evolving threats in large-scale social networks."";""zh"":""本文介绍了Facebook在大规模社交网络中采用的创新安全策略，包括实时威胁检测、自动化响应和用户教育，以应对不断演化的安全威胁。""}",NDSS/2013,46.jsonl
95,用户教育、社交工程防护、上下文消息、社会化举报、内容筛查,"User Education, Social Engineering Defense, Contextual Messaging, Social Reporting, Content Screening",用户教育和社会化举报机制在社交网络安全防护中有何作用？,What roles do user education and social reporting mechanisms play in social network security defense?,用户教育与社会化举报可提升用户安全意识，及时发现并阻断社交工程攻击和有害内容。,User education and social reporting enhance user security awareness and enable timely detection and blocking of social engineering attacks and harmful content.,Innovating to Protect the Graph,Facebook Security Team,"{""en"":""This paper introduces Facebook's innovative security strategies including real-time threat detection,automated response,and user education,to address evolving threats in large-scale social networks."";""zh"":""本文介绍了Facebook在大规模社交网络中采用的创新安全策略，包括实时威胁检测、自动化响应和用户教育，以应对不断演化的安全威胁。""}",NDSS/2013,46.jsonl
96,机器学习安全、异常检测、自动化防御、恶意行为识别、数据驱动安全,"Machine Learning Security, Anomaly Detection, Automated Defense, Malicious Behavior Identification, Data-driven Security",机器学习在社交网络恶意行为识别与自动化防御中有哪些应用？,What are the applications of machine learning in malicious behavior identification and automated defense in social networks?,机器学习算法可对用户行为和内容进行分类，实现恶意账号、垃圾信息等威胁的自动识别与防御。,Machine learning algorithms classify user behaviors and content enabling automatic identification and defense against threats such as malicious accounts and spam.,Innovating to Protect the Graph,Facebook Security Team,"{""en"":""This paper introduces Facebook's innovative security strategies including real-time threat detection,automated response,and user education,to address evolving threats in large-scale social networks."";""zh"":""本文介绍了Facebook在大规模社交网络中采用的创新安全策略，包括实时威胁检测、自动化响应和用户教育，以应对不断演化的安全威胁。""}",NDSS/2013,46.jsonl
97,虚拟机内省、语义鸿沟、二进制代码复用、动态污点分析、性能优化,"Virtual Machine Introspection, Semantic Gap, Binary Code Reuse, Dynamic Taint Analysis, Performance Optimization",如何通过二进制代码复用和动态污点分析提升虚拟机内省的效率与准确性？,How can binary code reuse and dynamic taint analysis improve the efficiency and accuracy of virtual machine introspection?,结合二进制代码复用与动态污点分析可自动化桥接虚拟机内省中的语义鸿沟，提升实时监控能力。,Combining binary code reuse and dynamic taint analysis can automatically bridge the semantic gap in VMI enhancing real-time monitoring capabilities.,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,"Alireza Saberi, Yangchun Fu, Zhiqiang Lin","{""en"":""This paper presents HYBRID-BRIDGE a system that efficiently bridges the semantic gap in virtual machine introspection by decoupling taint analysis and leveraging training memoization,achieving significant performance improvements for real-time VM monitoring."";""zh"":""本文提出了HYBRID-BRIDGE系统，通过解耦污点分析与训练记忆化，有效桥接虚拟机内省中的语义鸿沟，实现了实时监控下的性能大幅提升。""}",NDSS/2014,40.jsonl
98,内存重定向、KVM、QEMU、动态补丁、云安全,"Memory Redirection, KVM, QEMU, Dynamic Patching, Cloud Security",在云环境下，如何利用内存重定向与动态补丁机制提升虚拟机安全监控的可扩展性？,How can memory redirection and dynamic patching mechanisms enhance the scalability of VM security monitoring in cloud environments?,通过KVM与QEMU结合的内存重定向和动态补丁机制，可实现高效、可扩展的虚拟机安全监控。,Memory redirection and dynamic patching mechanisms combining KVM and QEMU enable efficient and scalable VM security monitoring.,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,"Alireza Saberi, Yangchun Fu, Zhiqiang Lin","{""en"":""This paper presents HYBRID-BRIDGE a system that efficiently bridges the semantic gap in virtual machine introspection by decoupling taint analysis and leveraging training memoization,achieving significant performance improvements for real-time VM monitoring."";""zh"":""本文提出了HYBRID-BRIDGE系统，通过解耦污点分析与训练记忆化，有效桥接虚拟机内省中的语义鸿沟，实现了实时监控下的性能大幅提升。""}",NDSS/2014,40.jsonl
99,虚拟机监控、性能优化、元数据记忆化、动态回退、云服务安全,"VM Monitoring, Performance Optimization, Metadata Memoization, Dynamic Fallback, Cloud Service Security",如何通过元数据记忆化与动态回退机制提升大规模云服务的虚拟机监控性能？,How can metadata memoization and dynamic fallback mechanisms improve VM monitoring performance in large-scale cloud services?,元数据记忆化与动态回退机制可减少重复分析开销，提升大规模云服务下的虚拟机监控效率。,Metadata memoization and dynamic fallback mechanisms reduce redundant analysis overhead improving VM monitoring efficiency in large-scale cloud services.,HYBRID-BRIDGE: Efficiently Bridging the Semantic Gap in Virtual Machine Introspection via Decoupled Execution and Training Memoization,"Alireza Saberi, Yangchun Fu, Zhiqiang Lin","{""en"":""This paper presents HYBRID-BRIDGE a system that efficiently bridges the semantic gap in virtual machine introspection by decoupling taint analysis and leveraging training memoization,achieving significant performance improvements for real-time VM monitoring."";""zh"":""本文提出了HYBRID-BRIDGE系统，通过解耦污点分析与训练记忆化，有效桥接虚拟机内省中的语义鸿沟，实现了实时监控下的性能大幅提升。""}",NDSS/2014,40.jsonl
100,Android安全、SSL/TLS、中间人攻击、自动化分析、证书验证,"Android Security, SSL/TLS, Man-in-the-Middle Attack, Automated Analysis, Certificate Validation",如何自动化检测Android应用中的SSL/TLS中间人攻击漏洞？,How can MITM vulnerabilities in Android apps be automatically detected?,结合静态与动态分析可大规模自动化识别Android应用中的SSL/TLS中间人攻击风险。,Combining static and dynamic analysis enables large-scale automated identification of SSL/TLS MITM risks in Android apps.,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,"David Sounthiraraj, Justin Sahs, Garret Greenwood, Zhiqiang Lin, Latifur Khan","{""en"":""This paper presents SMV-HUNTER a system that combines static and dynamic analysis to automatically detect SSL/TLS MITM vulnerabilities in Android apps at scale."";""zh"":""本文提出了SMV-HUNTER系统，结合静态与动态分析，实现了Android应用中SSL/TLS中间人攻击漏洞的自动化大规模检测。""}",NDSS/2014,43.jsonl
101,证书验证、X509TrustManager、HostNameVerifier、UI自动化、漏洞检测,"Certificate Validation, X509TrustManager, HostNameVerifier, UI Automation, Vulnerability Detection",Android应用中自定义证书验证实现存在哪些常见安全隐患？,What common security risks exist in custom certificate validation implementations in Android apps?,自定义X509TrustManager和HostNameVerifier常因实现不当导致证书验证绕过，增加中间人攻击风险。,Custom X509TrustManager and HostNameVerifier implementations often bypass certificate validation increasing MITM attack risks.,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,"David Sounthiraraj, Justin Sahs, Garret Greenwood, Zhiqiang Lin, Latifur Khan","{""en"":""This paper presents SMV-HUNTER a system that combines static and dynamic analysis to automatically detect SSL/TLS MITM vulnerabilities in Android apps at scale."";""zh"":""本文提出了SMV-HUNTER系统，结合静态与动态分析，实现了Android应用中SSL/TLS中间人攻击漏洞的自动化大规模检测。""}",NDSS/2014,43.jsonl
102,静态分析、动态分析、UI自动化、移动应用安全、漏洞修复率,"Static Analysis, Dynamic Analysis, UI Automation, Mobile App Security, Vulnerability Remediation Rate",在大规模移动应用安全检测中，如何提升漏洞检测的准确性与修复率？,How can the accuracy and remediation rate of vulnerability detection be improved in large-scale mobile app security testing?,静态与动态分析结合UI自动化可提升检测准确性，定期复测有助于跟踪漏洞修复进展。,Combining static and dynamic analysis with UI automation improves detection accuracy and periodic retesting helps track vulnerability remediation progress.,SMV-HUNTER: Large Scale Automated Detection of SSL/TLS Man-in-the-Middle Vulnerabilities in Android Apps,"David Sounthiraraj, Justin Sahs, Garret Greenwood, Zhiqiang Lin, Latifur Khan","{""en"":""This paper presents SMV-HUNTER a system that combines static and dynamic analysis to automatically detect SSL/TLS MITM vulnerabilities in Android apps at scale."";""zh"":""本文提出了SMV-HUNTER系统，结合静态与动态分析，实现了Android应用中SSL/TLS中间人攻击漏洞的自动化大规模检测。""}",NDSS/2014,43.jsonl
103,ARM安全、特权分离、虚拟地址调整、内核隔离、AArch64,"ARM Security, Privilege Separation, Virtual Address Adjustment, Kernel Isolation, AArch64",如何在ARM AArch64架构下实现高效的内核级特权分离？,How can efficient kernel-level privilege separation be achieved on ARM AArch64 architecture?,利用TxSZ硬件特性动态调整虚拟地址范围，可实现同级别内核模块的高效隔离与安全防护。,Dynamic adjustment of virtual address range using TxSZ hardware enables efficient isolation and security protection of kernel modules at the same privilege level.,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,"Yeongpil Cho, Donghyun Kown, Hayoon Yi, Yunheung Paek","{""en"":""This paper proposes Hilps a technique leveraging ARM AArch64's TxSZ feature to achieve intra-level privilege separation enabling secure isolation of kernel and hypervisor components with minimal performance overhead."";""zh"":""本文提出了Hilps技术，利用ARM AArch64的TxSZ特性，实现了同级别内核和虚拟化组件的高效隔离，安全性强且性能开销极低。""}",NDSS/2017,6.jsonl
104,内存保护、域切换、系统安全、硬件支持、ARM TrustZone,"Memory Protection, Domain Switching, System Security, Hardware Support, ARM TrustZone",ARM TrustZone等多级特权软件如何实现高效的内存隔离与安全切换？,How can efficient memory isolation and secure switching be achieved among multi-level privileged software like ARM TrustZone?,通过动态虚拟地址调整和硬件支持，可在不同特权级别间实现安全高效的内存隔离与切换。,Dynamic virtual address adjustment and hardware support enable secure and efficient memory isolation and switching among different privilege levels.,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,"Yeongpil Cho, Donghyun Kown, Hayoon Yi, Yunheung Paek","{""en"":""This paper proposes Hilps a technique leveraging ARM AArch64's TxSZ feature to achieve intra-level privilege separation enabling secure isolation of kernel and hypervisor components with minimal performance overhead."";""zh"":""本文提出了Hilps技术，利用ARM AArch64的TxSZ特性，实现了同级别内核和虚拟化组件的高效隔离，安全性强且性能开销极低。""}",NDSS/2017,6.jsonl
105,内核安全、硬件隔离、性能评估、系统可扩展性、攻击防护,"Kernel Security, Hardware Isolation, Performance Evaluation, System Scalability, Attack Mitigation",在不影响系统性能的前提下，如何提升ARM平台内核安全与可扩展性？,How can kernel security and scalability be improved on ARM platforms without impacting system performance?,硬件隔离机制结合轻量级域切换可提升内核安全性和系统可扩展性，且性能损耗极低。,Hardware isolation and lightweight domain switching enhance kernel security and scalability with minimal performance overhead.,Dynamic Virtual Address Range Adjustment for Intra-Level Privilege Separation on ARM,"Yeongpil Cho, Donghyun Kown, Hayoon Yi, Yunheung Paek","{""en"":""This paper proposes Hilps a technique leveraging ARM AArch64's TxSZ feature to achieve intra-level privilege separation enabling secure isolation of kernel and hypervisor components with minimal performance overhead."";""zh"":""本文提出了Hilps技术，利用ARM AArch64的TxSZ特性，实现了同级别内核和虚拟化组件的高效隔离，安全性强且性能开销极低。""}",NDSS/2017,6.jsonl
106,比特币匿名性、P2P混合、交易不可关联、DiceMix协议、区块链隐私,"Bitcoin Anonymity, P2P Mixing, Transaction Unlinkability, DiceMix Protocol, Blockchain Privacy",如何通过P2P混合协议提升比特币等加密货币的交易匿名性？,How can P2P mixing protocols enhance transaction anonymity in cryptocurrencies like Bitcoin?,DiceMix协议通过高效的P2P混合机制，实现了比特币交易的不可关联性和强匿名性。,The DiceMix protocol achieves unlinkability and strong anonymity for Bitcoin transactions through efficient P2P mixing.,P2P Mixing and Unlinkable Bitcoin Transactions,"Tim Ruffing, Pedro Moreno-Sanchez, Aniket Kate","{""en"":""This paper presents DiceMix a P2P mixing protocol that enables unlinkable Bitcoin transactions with strong anonymity and practical efficiency outperforming previous solutions."";""zh"":""本文提出了DiceMix协议，实现了高效的P2P混合和比特币交易不可关联性，匿名性强，性能优于以往方案。""}",NDSS/2017,52.jsonl
107,去中心化混币、抗攻击性、协议轮次优化、恶意节点防护、匿名通信,"Decentralized Coin Mixing, Attack Resistance, Protocol Round Optimization, Malicious Node Defense, Anonymous Communication",去中心化混币协议如何在存在恶意节点时保障匿名性和协议高效性？,How do decentralized coin mixing protocols ensure anonymity and efficiency in the presence of malicious nodes?,DiceMix协议通过最优通信轮次和恶意节点识别机制，提升了混币协议的安全性与效率。,DiceMix optimizes communication rounds and identifies malicious nodes enhancing the security and efficiency of coin mixing protocols.,P2P Mixing and Unlinkable Bitcoin Transactions,"Tim Ruffing, Pedro Moreno-Sanchez, Aniket Kate","{""en"":""This paper presents DiceMix a P2P mixing protocol that enables unlinkable Bitcoin transactions with strong anonymity and practical efficiency outperforming previous solutions."";""zh"":""本文提出了DiceMix协议，实现了高效的P2P混合和比特币交易不可关联性，匿名性强，性能优于以往方案。""}",NDSS/2017,52.jsonl
108,区块链隐私保护、去中心化协议、交易混淆、匿名性评估、比特币扩展性,"Blockchain Privacy Protection, Decentralized Protocol, Transaction Obfuscation, Anonymity Evaluation, Bitcoin Scalability",在区块链系统中，如何评估和提升去中心化协议的隐私保护能力与可扩展性？,How can the privacy protection and scalability of decentralized protocols be evaluated and improved in blockchain systems?,结合协议性能评估与匿名性分析，可系统提升区块链去中心化协议的隐私保护与扩展能力。,Combining protocol performance evaluation and anonymity analysis systematically improves privacy protection and scalability of decentralized blockchain protocols.,P2P Mixing and Unlinkable Bitcoin Transactions,"Tim Ruffing, Pedro Moreno-Sanchez, Aniket Kate","{""en"":""This paper presents DiceMix a P2P mixing protocol that enables unlinkable Bitcoin transactions with strong anonymity and practical efficiency outperforming previous solutions."";""zh"":""本文提出了DiceMix协议，实现了高效的P2P混合和比特币交易不可关联性，匿名性强，性能优于以往方案。""}",NDSS/2017,52.jsonl
111,云安全、域名验证、SSL证书、DNS陈旧记录、IP地址重用攻击,"Cloud Security, Domain Validation, SSL Certificate, Stale DNS Record, IP Address Reuse Attack",云服务环境下，域名验证型SSL证书存在哪些安全隐患？,What security risks exist for domain-validated SSL certificates in cloud service environments?,云端弹性资源与自动化证书颁发机制结合，易因DNS陈旧记录导致IP地址重用攻击，威胁域名安全。,The combination of elastic cloud resources and automated certificate issuance mechanisms can lead to IP address reuse attacks due to stale DNS records threatening domain security.,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposes a new authentication method and best practices to mitigate domain takeover attacks."";""zh"":""本文揭示了云IP地址重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践以缓解域名接管攻击。""}",NDSS/2018,13.jsonl
112,IP地址重用、DNS安全、云服务迁移、证书滥用、域名接管,"IP Address Reuse, DNS Security, Cloud Service Migration, Certificate Abuse, Domain Takeover",云服务迁移过程中，如何防范因IP地址重用导致的域名接管与证书滥用？,How can domain takeover and certificate abuse caused by IP address reuse be prevented during cloud service migration?,及时清理DNS记录、延迟释放IP及采用改进的证书验证机制可有效防止域名接管与证书滥用。,Timely DNS record cleanup delayed IP release and improved certificate validation mechanisms can effectively prevent domain takeover and certificate abuse.,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposes a new authentication method and best practices to mitigate domain takeover attacks."";""zh"":""本文揭示了云IP地址重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践以缓解域名接管攻击。""}",NDSS/2018,13.jsonl
113,自动化证书管理、ACME协议、云DNS运维、攻击窗口、最佳实践,"Automated Certificate Management, ACME Protocol, Cloud DNS Operation, Attack Window, Best Practices",自动化证书管理环境下，如何缩短云服务域名被攻击的窗口期？,How can the attack window for cloud service domain names be minimized in automated certificate management environments?,采用证书透明性、预签名验证及合理DNS运维可缩短攻击窗口，提升云服务安全性。,Certificate transparency pre-signature validation and proper DNS operation can minimize the attack window and enhance cloud service security.,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposes a new authentication method and best practices to mitigate domain takeover attacks."";""zh"":""本文揭示了云IP地址重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践以缓解域名接管攻击。""}",NDSS/2018,13.jsonl
114,数据完整性证明、协作式嵌入式系统、控制流证明、无人机安全、模块化认证,"Data Integrity Attestation, Collaborative Embedded Systems, Control-Flow Attestation, Drone Security, Modular Attestation",如何在无人机等协作式嵌入式系统中实现高效的数据完整性证明？,How can efficient data integrity attestation be achieved in collaborative embedded systems like drones?,模块化控制流证明可显著降低嵌入式系统协作中的安全开销，实现端到端数据完整性保护。,Modular control-flow attestation significantly reduces security overhead in collaborative embedded systems enabling end-to-end data integrity protection.,DIAT: Data Integrity Attestation for Resilient Collaboration of Autonomous Systems,"Tigist Abera, Raad Bahmani, Ferdinand Brasser, Ahmad Ibrahim, Ahmad-Reza Sadeghi, Matthias Schunter","{""en"":""This paper proposes DIAT a modular control-flow attestation scheme for collaborative autonomous systems,demonstrating its effectiveness on drones and its scalability for large networks."";""zh"":""本文提出了DIAT，一种面向协作式自主系统的模块化控制流证明方案，并在无人机等场景下验证了其高效性与可扩展性。""}",NDSS/2019,3.jsonl
115,远程证明、物联网安全、运行时攻击防护、可信计算基、分布式认证,"Remote Attestation, IoT Security, Runtime Attack Defense, Trusted Computing Base, Distributed Attestation",物联网环境下如何实现分布式的运行时安全证明与攻击防护？,How can distributed runtime attestation and attack defense be achieved in IoT environments?,分布式证明结合轻量级硬件安全架构，可有效检测并隔离运行时攻击，提升物联网整体安全性。,Distributed attestation combined with lightweight hardware security architectures can effectively detect and isolate runtime attacks enhancing overall IoT security.,DIAT: Data Integrity Attestation for Resilient Collaboration of Autonomous Systems,"Tigist Abera, Raad Bahmani, Ferdinand Brasser, Ahmad Ibrahim, Ahmad-Reza Sadeghi, Matthias Schunter","{""en"":""This paper proposes DIAT a modular control-flow attestation scheme for collaborative autonomous systems,demonstrating its effectiveness on drones and its scalability for large networks."";""zh"":""本文提出了DIAT，一种面向协作式自主系统的模块化控制流证明方案，并在无人机等场景下验证了其高效性与可扩展性。""}",NDSS/2019,3.jsonl
116,控制流证明、数据流监控、嵌入式安全、无人机协作、可扩展性评估,"Control-Flow Attestation, Data-Flow Monitoring, Embedded Security, Drone Collaboration, Scalability Evaluation",在大规模协作无人机网络中，如何平衡安全性与系统性能？,How to balance security and system performance in large-scale collaborative drone networks?,通过模块化证明与数据流监控，可在保证安全的同时大幅降低系统性能开销，适用于大规模无人机协作。,Modular attestation and data-flow monitoring enable strong security with minimal performance overhead suitable for large-scale drone collaboration.,DIAT: Data Integrity Attestation for Resilient Collaboration of Autonomous Systems,"Tigist Abera, Raad Bahmani, Ferdinand Brasser, Ahmad Ibrahim, Ahmad-Reza Sadeghi, Matthias Schunter","{""en"":""This paper proposes DIAT a modular control-flow attestation scheme for collaborative autonomous systems,demonstrating its effectiveness on drones and its scalability for large networks."";""zh"":""本文提出了DIAT，一种面向协作式自主系统的模块化控制流证明方案，并在无人机等场景下验证了其高效性与可扩展性。""}",NDSS/2019,3.jsonl
117,自动化补丁、开源软件安全、二进制修复、变体分析、移动应用防护,"Automated Patching, Open-Source Software Security, Binary Repair, Variability Analysis, Mobile App Protection",如何实现对移动应用中开源组件的自动化二进制级安全补丁？,How can automated binary-level security patching be achieved for open-source components in mobile applications?,通过源代码与二进制匹配及变体分析，可自动生成并注入补丁，提升移动应用安全性。,Source-to-binary matching and variability analysis enable automated patch generation and injection enhancing mobile app security.,Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""This paper presents OSSPATCHER a system for automated binary patching of vulnerable open-source software in mobile apps,demonstrating high precision and negligible overhead."";""zh"":""本文提出了OSSPATCHER系统，实现了移动应用中开源组件的自动化二进制补丁，具有高精度和低开销。""}",NDSS/2019,26.jsonl
118,变体感知分析、函数级修复、Android安全、配置推断、补丁注入,"Variability-Aware Analysis, Function-Level Repair, Android Security, Configuration Inference, Patch Injection",在Android应用安全加固中，如何应对开源组件多变体和无符号二进制的补丁难题？,How to address patching challenges for multi-variant and stripped binaries of open-source components in Android security?,变体感知分析与配置推断可定位并修复无符号二进制中的漏洞函数，实现高效安全加固。,Variability-aware analysis and configuration inference enable efficient patching of stripped binaries improving Android app security.,Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""This paper presents OSSPATCHER a system for automated binary patching of vulnerable open-source software in mobile apps,demonstrating high precision and negligible overhead."";""zh"":""本文提出了OSSPATCHER系统，实现了移动应用中开源组件的自动化二进制补丁，具有高精度和低开销。""}",NDSS/2019,26.jsonl
119,二进制补丁评估、开源漏洞检测、性能与正确性验证、移动安全、自动回滚机制,"Binary Patch Evaluation, Open-Source Vulnerability Detection, Performance and Correctness Verification, Mobile Security, Auto-Rollback Mechanism",如何评估自动化二进制补丁系统在移动应用中的安全性与性能影响？,How to evaluate the security and performance impact of automated binary patching systems in mobile applications?,通过大规模实测与自动回滚机制，验证补丁系统的安全性、正确性及低性能开销。,Large-scale testing and auto-rollback mechanisms verify the security correctness and low overhead of patching systems.,Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""This paper presents OSSPATCHER a system for automated binary patching of vulnerable open-source software in mobile apps,demonstrating high precision and negligible overhead."";""zh"":""本文提出了OSSPATCHER系统，实现了移动应用中开源组件的自动化二进制补丁，具有高精度和低开销。""}",NDSS/2019,26.jsonl
120,区块链可扩展性、匿名多跳锁、支付通道网络、跨链互操作、ECDSA脚本锁,"Blockchain Scalability, Anonymous Multi-Hop Lock, Payment Channel Network, Cross-Chain Interoperability, ECDSA Scriptless Lock",如何通过匿名多跳锁机制提升区块链支付通道网络的可扩展性与隐私性？,How can anonymous multi-hop locks enhance scalability and privacy in blockchain payment channel networks?,匿名多跳锁机制可实现高效、隐私保护的多跳支付，支持跨链原子交换与互操作，提升区块链网络性能。,Anonymous multi-hop locks enable efficient privacy-preserving multi-hop payments supporting atomic swaps and interoperability across blockchains.,Anonymous Multi-Hop Locks for Blockchain Scalability and Interoperability,"Giulio Malavolta, Pedro Moreno-Sanchez, Clara Schneidewind, Aniket Kate, Matteo Maffei","{""en"":""This paper proposes AMHLs,a cryptographic primitive for secure,private,and interoperable payment channel networks,with practical ECDSA-based constructions."";""zh"":""本文提出了AMHLs，一种用于安全、隐私和互操作支付通道网络的密码学原语，并给出实用的ECDSA实现。""}",NDSS/2019 ,50.jsonl
121,支付通道安全、蠕虫洞攻击、链下交易、原子交换、隐私保护协议,"Payment Channel Security, Wormhole Attack, Off-Chain Transaction, Atomic Swap, Privacy-Preserving Protocol",区块链支付通道网络中存在哪些安全隐患及如何防御新型攻击？,What security risks exist in blockchain payment channel networks and how to defend against novel attacks?,提出蠕虫洞攻击模型并给出防御机制，提升支付通道网络的安全性与健壮性。,The wormhole attack model and defense mechanisms improve the security and robustness of payment channel networks.,Anonymous Multi-Hop Locks for Blockchain Scalability and Interoperability,"Giulio Malavolta, Pedro Moreno-Sanchez, Clara Schneidewind, Aniket Kate, Matteo Maffei","{""en"":""This paper proposes AMHLs,a cryptographic primitive for secure,private,and interoperable payment channel networks,with practical ECDSA-based constructions."";""zh"":""本文提出了AMHLs，一种用于安全、隐私和互操作支付通道网络的密码学原语，并给出实用的ECDSA实现。""}",NDSS/2019 ,50.jsonl
122,跨链支付、脚本无关加密、区块链互操作、原子性保障、性能评估,"Cross-Chain Payment, Scriptless Encryption, Blockchain Interoperability, Atomicity Guarantee, Performance Evaluation",如何实现不同区块链间的高效原子支付与互操作？,How to achieve efficient atomic payments and interoperability across different blockchains?,基于ECDSA的脚本无关加密方案支持多链原子支付，兼顾安全性、隐私性与高性能。,ECDSA-based scriptless encryption enables secure private and high-performance atomic payments across blockchains.,Anonymous Multi-Hop Locks for Blockchain Scalability and Interoperability,"Giulio Malavolta, Pedro Moreno-Sanchez, Clara Schneidewind, Aniket Kate, Matteo Maffei","{""en"":""This paper proposes AMHLs,a cryptographic primitive for secure,private,and interoperable payment channel networks,with practical ECDSA-based constructions."";""zh"":""本文提出了AMHLs，一种用于安全、隐私和互操作支付通道网络的密码学原语，并给出实用的ECDSA实现。""}",NDSS/2019 ,50.jsonl
123,缩略图加密、图像隐私保护、格式保持加密、云存储安全、可用性平衡,"Thumbnail-Preserving Encryption, Image Privacy Protection, Format-Preserving Encryption, Cloud Storage Security, Usability Balance",如何在云存储环境下实现兼顾隐私与可用性的图像加密？,How can image encryption balance privacy and usability in cloud storage environments?,缩略图保持加密方案可在不泄露原图细节的前提下，支持云端图片浏览与管理。,Thumbnail-preserving encryption enables cloud-side image browsing and management without leaking original image details.,Balancing Image Privacy and Usability with Thumbnail-Preserving Encryption,"Kimia Tajik, Akshith Gunasekaran, Rhea Dutta, Brandon Ellis, Rakesh B. Bobba, Mike Rosulek, Charles V. Wright, Wu-chi Feng","{""en"":""This paper proposes ideal thumbnail-preserving encryption (TPE) for images balancing privacy and usability in cloud storage and demonstrates its security and practicality."";""zh"":""本文提出了理想缩略图保持加密（TPE）方案，实现了云存储场景下图像隐私与可用性的平衡，并验证了其安全性与实用性。""}",NDSS/2019 ,72.jsonl
124,图像可用性、用户体验评估、加密缩略图、云端照片管理、隐私-可用性权衡,"Image Usability, User Experience Evaluation, Encrypted Thumbnails, Cloud Photo Management, Privacy-Usability Tradeoff",加密缩略图如何影响用户在云端管理和识别照片的体验？,How do encrypted thumbnails affect user experience in cloud photo management and identification?,用户实验表明，低分辨率缩略图可在保护隐私的同时，保证用户对照片的有效管理与识别。,User studies show that low-resolution encrypted thumbnails protect privacy while ensuring effective photo management and identification.,Balancing Image Privacy and Usability with Thumbnail-Preserving Encryption,"Kimia Tajik, Akshith Gunasekaran, Rhea Dutta, Brandon Ellis, Rakesh B. Bobba, Mike Rosulek, Charles V. Wright, Wu-chi Feng","{""en"":""This paper proposes ideal thumbnail-preserving encryption (TPE) for images balancing privacy and usability in cloud storage and demonstrates its security and practicality."";""zh"":""本文提出了理想缩略图保持加密（TPE）方案，实现了云存储场景下图像隐私与可用性的平衡，并验证了其安全性与实用性。""}",NDSS/2019 ,72.jsonl
125,格式保持加密、图像安全、云服务兼容性、性能分析、隐私泄露评估,"Format-Preserving Encryption, Image Security, Cloud Service Compatibility, Performance Analysis, Privacy Leakage Evaluation",格式保持加密在实际云服务中应用时存在哪些安全与性能挑战？,What are the security and performance challenges of format-preserving encryption in practical cloud services?,格式保持加密需兼顾安全性、兼容性与性能，需评估缩略图泄露带来的隐私风险。,Format-preserving encryption must balance security compatibility  and performance requiring evaluation of privacy risks from thumbnail leakage.,Balancing Image Privacy and Usability with Thumbnail-Preserving Encryption,"Kimia Tajik, Akshith Gunasekaran, Rhea Dutta, Brandon Ellis, Rakesh B. Bobba, Mike Rosulek, Charles V. Wright, Wu-chi Feng","{""en"":""This paper proposes ideal thumbnail-preserving encryption (TPE) for images balancing privacy and usability in cloud storage and demonstrates its security and practicality."";""zh"":""本文提出了理想缩略图保持加密（TPE）方案，实现了云存储场景下图像隐私与可用性的平衡，并验证了其安全性与实用性。""}",NDSS/2019 ,72.jsonl
126,算法复杂性、拒绝服务、模糊测试、Java安全、遗传算法、自动化漏洞检测,"Algorithmic Complexity, Denial-of-Service, Fuzzing, Java Security, Genetic Algorithm, Automated Vulnerability Detection",在实际软件开发中，如何评估第三方库的算法复杂性安全风险？,How can developers assess the algorithmic complexity security risks of third-party libraries in real-world software development?,第三方库、算法复杂性、风险评估,Third-party libraries algorithmic complexity risk assessment,HotFuzz: Discovering Algorithmic Denial-of-Service Vulnerabilities in Java Libraries,"Petros Maniatis, David Wagner, et al.","{""en"":""This paper presents HotFuzz,an automated framework based on micro-fuzzing and genetic algorithms to detect algorithmic complexity denial-of-service (DoS) vulnerabilities in Java libraries. The approach can automatically generate high-complexity inputs and discover new DoS security risks in mainstream Java runtimes and third-party libraries,providing an effective tool for software security assessment and protection."";""zh"":""本文提出了一种基于微型模糊测试和遗传算法的自动化框架HotFuzz，用于检测Java库中的算法复杂性拒绝服务漏洞。该方法能够自动生成高复杂度输入，发现主流Java运行环境和第三方库中的新型DoS安全隐患，为软件安全性评估和防护提供了有效工具。""}",NDSS/2020,17.jsonl
127,算法复杂性、拒绝服务、模糊测试、Java安全、遗传算法、自动化漏洞检测,"Algorithmic Complexity, Denial-of-Service, Fuzzing, Java Security, Genetic Algorithm, Automated Vulnerability Detection",遗传算法在安全漏洞自动化检测中有哪些优势？,What are the advantages of using genetic algorithms in automated security vulnerability detection?,遗传算法、自动化检测、漏洞挖掘,Genetic algorithm automated detection vulnerability mining,HotFuzz: Discoavering Algorithmic Denial-of-Service Vulnerabilities in Java Libraries,"Petros Maniatis, David Wagner, et al.","{""en"":""This paper presents HotFuzz,an automated framework based on micro-fuzzing and genetic algorithms to detect algorithmic complexity denial-of-service (DoS) vulnerabilities in Java libraries. The approach can automatically generate high-complexity inputs and discover new DoS security risks in mainstream Java runtimes and third-party libraries,providing an effective tool for software security assessment and protection."";""zh"":""本文提出了一种基于微型模糊测试和遗传算法的自动化框架HotFuzz，用于检测Java库中的算法复杂性拒绝服务漏洞。该方法能够自动生成高复杂度输入，发现主流Java运行环境和第三方库中的新型DoS安全隐患，为软件安全性评估和防护提供了有效工具。""}",NDSS/2020,17.jsonl
128,算法复杂性、拒绝服务、模糊测试、Java安全、遗传算法、自动化漏洞检测,"Algorithmic Complexity, Denial-of-Service, Fuzzing, Java Security, Genetic Algorithm, Automated Vulnerability Detection",算法复杂性DoS攻击对云服务平台有何潜在影响？,What potential impacts do algorithmic complexity DoS attacks have on cloud service platforms?,云服务、DoS攻击、性能安全,Cloud service DoS attack performance security,HotFuzz: Discoavering Algorithmic Denial-of-Service Vulnerabilities in Java Libraries,"Petros Maniatis, David Wagner, et al.","{""en"":""This paper presents HotFuzz,an automated framework based on micro-fuzzing and genetic algorithms to detect algorithmic complexity denial-of-service (DoS) vulnerabilities in Java libraries. The approach can automatically generate high-complexity inputs and discover new DoS security risks in mainstream Java runtimes and third-party libraries,providing an effective tool for software security assessment and protection."";""zh"":""本文提出了一种基于微型模糊测试和遗传算法的自动化框架HotFuzz，用于检测Java库中的算法复杂性拒绝服务漏洞。该方法能够自动生成高复杂度输入，发现主流Java运行环境和第三方库中的新型DoS安全隐患，为软件安全性评估和防护提供了有效工具。""}",NDSS/2020,17.jsonl
129,浏览器扩展、指纹识别、隐私威胁、行为分析、去匿名攻击、信息泄露,"Browser Extension, Fingerprinting, Privacy Threat, Behavioral Analysis, Deanonymization Attack, Information Leakage",浏览器扩展指纹化技术如何影响用户的在线隐私保护？,How does browser extension fingerprinting technology affect users' online privacy protection?,扩展指纹、隐私保护、在线安全,Extension fingerprint privacy protection online security,Carnus: Exploring the Privacy Threats of Browser Extension Fingerprinting,"Soroush Karami, Panagiotis Ilia, Konstantinos Solomos, Jason Polakis","{""en"":""This paper introduces Carnus,the first system to automatically generate and detect behavioral fingerprints of browser extensions. By analyzing extensions' behaviors,communication patterns,and resource access,Carnus reveals serious privacy threats such as deanonymization attacks and sensitive information inference,highlighting the urgent need for more effective privacy protection measures."";""zh"":""本文提出了Carnus系统，首次实现了浏览器扩展的自动化行为指纹生成与检测。通过多维度分析扩展的行为、通信模式和资源访问，Carnus揭示了扩展指纹对用户隐私的严重威胁，包括去匿名攻击和敏感信息推断，强调了亟需更有效的隐私保护对策。""}",NDSS/2020,39.jsonl
130,浏览器扩展、指纹识别、隐私威胁、行为分析、去匿名攻击、信息泄露,"Browser Extension, Fingerprinting, Privacy Threat, Behavioral Analysis, Deanonymization Attack, Information Leakage",行为指纹与传统资源指纹在扩展检测中的区别是什么？,What is the difference between behavioral fingerprints and traditional resource fingerprints in extension detection?,行为指纹、资源指纹、检测方法,Behavioral fingerprint resource fingerprint detection method,Carnus: Exploring the Privacy Threats of Browser Extension Fingerprinting,"Soroush Karami, Panagiotis Ilia, Konstantinos Solomos, Jason Polakis","{""en"":""This paper introduces Carnus,the first system to automatically generate and detect behavioral fingerprints of browser extensions. By analyzing extensions' behaviors,communication patterns,and resource access,Carnus reveals serious privacy threats such as deanonymization attacks and sensitive information inference,highlighting the urgent need for more effective privacy protection measures."";""zh"":""本文提出了Carnus系统，首次实现了浏览器扩展的自动化行为指纹生成与检测。通过多维度分析扩展的行为、通信模式和资源访问，Carnus揭示了扩展指纹对用户隐私的严重威胁，包括去匿名攻击和敏感信息推断，强调了亟需更有效的隐私保护对策。""}",NDSS/2020,39.jsonl
131,浏览器扩展、指纹识别、隐私威胁、行为分析、去匿名攻击、信息泄露,"Browser Extension, Fingerprinting, Privacy Threat, Behavioral Analysis, Deanonymization Attack, Information Leakage",如何评估浏览器扩展生态系统中的隐私泄露风险？,How can privacy leakage risks in the browser extension ecosystem be assessed?,扩展生态、隐私泄露、风险评估,Extension ecosystem privacy leakage risk assessment,Carnus: Exploring the Privacy Threats of Browser Extension Fingerprinting,"Soroush Karami, Panagiotis Ilia, Konstantinos Solomos, Jason Polakis","{""en"":""This paper introduces Carnus,the first system to automatically generate and detect behavioral fingerprints of browser extensions. By analyzing extensions' behaviors,communication patterns,and resource access,Carnus reveals serious privacy threats such as deanonymization attacks and sensitive information inference,highlighting the urgent need for more effective privacy protection measures."";""zh"":""本文提出了Carnus系统，首次实现了浏览器扩展的自动化行为指纹生成与检测。通过多维度分析扩展的行为、通信模式和资源访问，Carnus揭示了扩展指纹对用户隐私的严重威胁，包括去匿名攻击和敏感信息推断，强调了亟需更有效的隐私保护对策。""}",NDSS/2020,39.jsonl
132,反机器人电话、用户行为、界面设计、风险感知、电话安全、警示效果,"Anti-Robocall, User Behavior, Interface Design, Risk Perception, Call Security, Warning Effectiveness",界面设计中的哪些元素有助于提升用户对骚扰电话的风险感知？,Which elements in interface design help enhance users' risk perception of spam calls?,界面设计、风险感知、骚扰电话,Interface design risk perception spam call,Are You Going to Answer That? Measuring User Responses to Anti-Robocall Application Indicators,"Imani N. Sherman, Jasmine D. Bowers, Keith McNamara Jr., Juan E. Gilbert, Jaime Ruiz, Patrick Traynor","{""en"":""This paper systematically analyzes the interface design of mainstream anti-robocall apps and their impact on user call response behavior. Through focus groups and user experiments,it is found that warning designs (such as color,icons,and authentication labels) can significantly reduce the probability of users answering spam calls,but attackers can bypass some protections through number spoofing. The study provides empirical evidence for improving secure call interface design."";""zh"":""本文系统分析了主流反机器人电话App的界面设计及其对用户应答行为的影响。通过焦点小组和用户实验，发现警示设计（如颜色、图标、认证标识）能显著降低用户接听骚扰电话的概率，但攻击者可通过号码伪造等手段绕过部分防护。研究为提升电话安全界面设计提供了实证依据。""}",NDSS/2020,62.jsonl
133,反机器人电话、用户行为、界面设计、风险感知、电话安全、警示效果,"Anti-Robocall, User Behavior, Interface Design, Risk Perception, Call Security, Warning Effectiveness",反机器人电话App如何平衡误报与用户体验？,How do anti-robocall apps balance false positives and user experience?,误报、用户体验、电话安全,False positive user experience call security,Are You Going to Answer That? Measuring User Responses to Anti-Robocall Application Indicators,"Imani N. Sherman, Jasmine D. Bowers, Keith McNamara Jr., Juan E. Gilbert, Jaime Ruiz, Patrick Traynor","{""en"":""This paper systematically analyzes the interface design of mainstream anti-robocall apps and their impact on user call response behavior. Through focus groups and user experiments,it is found that warning designs (such as color,icons,and authentication labels) can significantly reduce the probability of users answering spam calls,but attackers can bypass some protections through number spoofing. The study provides empirical evidence for improving secure call interface design."";""zh"":""本文系统分析了主流反机器人电话App的界面设计及其对用户应答行为的影响。通过焦点小组和用户实验，发现警示设计（如颜色、图标、认证标识）能显著降低用户接听骚扰电话的概率，但攻击者可通过号码伪造等手段绕过部分防护。研究为提升电话安全界面设计提供了实证依据。""}",NDSS/2020,62.jsonl
134,反机器人电话、用户行为、界面设计、风险感知、电话安全、警示效果,"Anti-Robocall, User Behavior, Interface Design, Risk Perception, Call Security, Warning Effectiveness",攻击者如何绕过反骚扰电话应用的安全防护？,How can attackers bypass the security protections of anti-spam call applications?,攻击手段、号码伪造、安全绕过,Attack method number spoofing security bypass,Are You Going to Answer That? Measuring User Responses to Anti-Robocall Application Indicators,"Imani N. Sherman, Jasmine D. Bowers, Keith McNamara Jr., Juan E. Gilbert, Jaime Ruiz, Patrick Traynor","{""en"":""This paper systematically analyzes the interface design of mainstream anti-robocall apps and their impact on user call response behavior. Through focus groups and user experiments,it is found that warning designs (such as color,icons,and authentication labels) can significantly reduce the probability of users answering spam calls,but attackers can bypass some protections through number spoofing. The study provides empirical evidence for improving secure call interface design."";""zh"":""本文系统分析了主流反机器人电话App的界面设计及其对用户应答行为的影响。通过焦点小组和用户实验，发现警示设计（如颜色、图标、认证标识）能显著降低用户接听骚扰电话的概率，但攻击者可通过号码伪造等手段绕过部分防护。研究为提升电话安全界面设计提供了实证依据。""}",NDSS/2020,62.jsonl
135,Iago攻击、遗留代码、可信执行环境、系统调用、漏洞检测、Emilia工具,"Iago Attack, Legacy Code, Trusted Execution Environment, System Call, Vulnerability Detection, Emilia Tool",在将遗留应用迁移到可信执行环境（TEE）时，如何系统性评估其Iago攻击风险？,How can the risk of Iago attacks be systematically assessed when porting legacy applications to Trusted Execution Environments (TEEs)?,Iago攻击、TEE迁移、风险评估,Iago attack TEE migration risk assessment,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia,a tool that automatically detects Iago vulnerabilities in legacy applications running in TEEs by fuzzing system call return values. The study finds Iago vulnerabilities are widespread in mainstream applications,most of which can be mitigated by simple stateless checks,while a few require application-level fixes. This work provides a systematic approach for TEE migration security assessment."";""zh"":""本文提出了Emilia工具，通过系统调用返回值模糊测试，自动检测遗留应用在TEE环境下的Iago漏洞。研究发现Iago漏洞在主流应用中普遍存在，大部分可通过简单的无状态检查缓解，少数需结合应用修补。该工作为TEE迁移安全评估提供了系统化方法。""}",NDSS/2021,8.jsonl
136,Iago攻击、遗留代码、可信执行环境、系统调用、漏洞检测、Emilia工具,"Iago Attack, Legacy Code, Trusted Execution Environment, System Call, Vulnerability Detection, Emilia Tool",安全专家在评估TEE环境下遗留代码安全时应关注哪些系统调用接口？,Which system call interfaces should security experts focus on when assessing legacy code security in TEEs?,系统调用接口、返回值校验、攻击面,System call interface return value validation attack surface,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia,a tool that automatically detects Iago vulnerabilities in legacy applications running in TEEs by fuzzing system call return values. The study finds Iago vulnerabilities are widespread in mainstream applications,most of which can be mitigated by simple stateless checks,while a few require application-level fixes. This work provides a systematic approach for TEE migration security assessment."";""zh"":""本文提出了Emilia工具，通过系统调用返回值模糊测试，自动检测遗留应用在TEE环境下的Iago漏洞。研究发现Iago漏洞在主流应用中普遍存在，大部分可通过简单的无状态检查缓解，少数需结合应用修补。该工作为TEE迁移安全评估提供了系统化方法。""}",NDSS/2021,8.jsonl
137,Iago攻击、遗留代码、可信执行环境、系统调用、漏洞检测、Emilia工具,"Iago Attack, Legacy Code, Trusted Execution Environment, System Call, Vulnerability Detection, Emilia Tool",安全专业学生如何理解Iago漏洞的成因及其在实际系统中的表现？,How can security students understand the causes and manifestations of Iago vulnerabilities in real systems?,Iago漏洞成因、系统调用信任、实际案例,Causes of Iago vulnerability syscall trust real-world cases,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia,a tool that automatically detects Iago vulnerabilities in legacy applications running in TEEs by fuzzing system call return values. The study finds Iago vulnerabilities are widespread in mainstream applications,most of which can be mitigated by simple stateless checks,while a few require application-level fixes. This work provides a systematic approach for TEE migration security assessment."";""zh"":""本文提出了Emilia工具，通过系统调用返回值模糊测试，自动检测遗留应用在TEE环境下的Iago漏洞。研究发现Iago漏洞在主流应用中普遍存在，大部分可通过简单的无状态检查缓解，少数需结合应用修补。该工作为TEE迁移安全评估提供了系统化方法。""}",NDSS/2021,8.jsonl
138,Iago攻击、遗留代码、可信执行环境、系统调用、漏洞检测、Emilia工具,"Iago Attack, Legacy Code, Trusted Execution Environment, System Call, Vulnerability Detection, Emilia Tool",智能助手如何辅助开发者在TEE迁移过程中自动检测和修复Iago漏洞？,How can intelligent assistants help developers automatically detect and fix Iago vulnerabilities during TEE migration?,自动检测、辅助修复、智能工具,Automatic detection intelligent tool,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia,a tool that automatically detects Iago vulnerabilities in legacy applications running in TEEs by fuzzing system call return values. The study finds Iago vulnerabilities are widespread in mainstream applications,most of which can be mitigated by simple stateless checks,while a few require application-level fixes. This work provides a systematic approach for TEE migration security assessment."";""zh"":""本文提出了Emilia工具，通过系统调用返回值模糊测试，自动检测遗留应用在TEE环境下的Iago漏洞。研究发现Iago漏洞在主流应用中普遍存在，大部分可通过简单的无状态检查缓解，少数需结合应用修补。该工作为TEE迁移安全评估提供了系统化方法。""}",NDSS/2021,8.jsonl
139,5G安全测距、无线定位、V-Range、距离操控攻击、OFDM、物理层安全,"5G Secure Ranging, Wireless Positioning, V-Range, Distance Manipulation Attack, OFDM, Physical Layer Security",5G无线网络在定位与测距安全方面面临哪些主要威胁？,What are the main security threats to positioning and ranging in 5G wireless networks?,5G定位安全、距离操控、物理层攻击,5G positioning security distance manipulation physical layer attack,V-Range: Enabling Secure Ranging in 5G Wireless Networks,"Mridula Singh, Marc Roeschlin, Aanjhan Ranganathan, Srdjan Capkun","{""en"":""This paper proposes V-Range,a 5G-compatible secure ranging system that effectively defends against distance reduction and enlargement attacks. V-Range shortens OFDM symbol duration and applies multi-level integrity checks,significantly improving the security and accuracy of 5G positioning."";""zh"":""本文提出了V-Range系统，实现了与5G标准兼容的安全测距，能有效防御距离缩短与放大等操控攻击。V-Range通过缩短OFDM符号时长和多层完整性校验，显著提升了5G定位的安全性和精度。""}",NDSS/2022,57.jsonl
140,5G安全测距、无线定位、V-Range、距离操控攻击、OFDM、物理层安全,"5G Secure Ranging, Wireless Positioning, V-Range, Distance Manipulation Attack, OFDM, Physical Layer Security",安全专家在设计5G定位系统时应重点防范哪些物理层攻击？,Which physical layer attacks should security experts focus on when designing 5G positioning systems?,物理层攻击、信号遮蔽、频偏操控,Physical layer attack signal overshadowing frequency offset manipulation,V-Range: Enabling Secure Ranging in 5G Wireless Networks,"Mridula Singh, Marc Roeschlin, Aanjhan Ranganathan, Srdjan Capkun","{""en"":""This paper proposes V-Range,a 5G-compatible secure ranging system that effectively defends against distance reduction and enlargement attacks. V-Range shortens OFDM symbol duration and applies multi-level integrity checks,significantly improving the security and accuracy of 5G positioning."";""zh"":""本文提出了V-Range系统，实现了与5G标准兼容的安全测距，能有效防御距离缩短与放大等操控攻击。V-Range通过缩短OFDM符号时长和多层完整性校验，显著提升了5G定位的安全性和精度。""}",NDSS/2022,57.jsonl
141,5G安全测距、无线定位、V-Range、距离操控攻击、OFDM、物理层安全,"5G Secure Ranging, Wireless Positioning, V-Range, Distance Manipulation Attack, OFDM, Physical Layer Security",安全专业学生如何理解5G测距系统中的距离操控攻击及其防御机制？,How can security students understand distance manipulation attacks and their defenses in 5G ranging systems?,距离操控原理、防御机制、系统设计,Distance manipulation principle defense mechanism system design,V-Range: Enabling Secure Ranging in 5G Wireless Networks,"Mridula Singh, Marc Roeschlin, Aanjhan Ranganathan, Srdjan Capkun","{""en"":""This paper proposes V-Range,a 5G-compatible secure ranging system that effectively defends against distance reduction and enlargement attacks. V-Range shortens OFDM symbol duration and applies multi-level integrity checks,significantly improving the security and accuracy of 5G positioning."";""zh"":""本文提出了V-Range系统，实现了与5G标准兼容的安全测距，能有效防御距离缩短与放大等操控攻击。V-Range通过缩短OFDM符号时长和多层完整性校验，显著提升了5G定位的安全性和精度。""}",NDSS/2022,57.jsonl
142,5G安全测距、无线定位、V-Range、距离操控攻击、OFDM、物理层安全,"5G Secure Ranging, Wireless Positioning, V-Range, Distance Manipulation Attack, OFDM, Physical Layer Security",智能助手如何辅助开发者在5G定位系统中实现自动化安全检测与防护？,How can intelligent assistants help developers automate security detection and protection in 5G positioning systems?,自动化检测、物理层防护、智能工具,Automated detection physical layer protection intelligent tool,V-Range: Enabling Secure Ranging in 5G Wireless Networks,"Mridula Singh, Marc Roeschlin, Aanjhan Ranganathan, Srdjan Capkun","{""en"":""This paper proposes V-Range,a 5G-compatible secure ranging system that effectively defends against distance reduction and enlargement attacks. V-Range shortens OFDM symbol duration and applies multi-level integrity checks,significantly improving the security and accuracy of 5G positioning."";""zh"":""本文提出了V-Range系统，实现了与5G标准兼容的安全测距，能有效防御距离缩短与放大等操控攻击。V-Range通过缩短OFDM符号时长和多层完整性校验，显著提升了5G定位的安全性和精度。""}",NDSS/2022,57.jsonl
143,加密货币、P2P网络、匿名性、Dandelion、Lightning Network、贝叶斯推断,"Cryptocurrency, P2P Network, Anonymity, Dandelion, Lightning Network, Bayesian Inference",在区块链P2P网络中，现有匿名性协议面临哪些主要去匿名攻击风险？,What are the main deanonymization risks faced by current anonymity protocols in blockchain P2P networks?,P2P匿名性、去匿名攻击、协议评估,P2P anonymity deanonymization attack protocol evaluation,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,"Piyush Kumar Sharma, Devashish Gosain, Claudia Diaz","{""en"":""This paper models and evaluates the real-world anonymity of major anonymity protocols (e.g.,Dandelion,Lightning Network) in cryptocurrency P2P networks,proposing a Bayesian inference-based framework. The study reveals that even a small fraction of compromised nodes can significantly reduce user anonymity,highlighting serious deanonymization risks in current solutions."";""zh"":""本文系统建模并评估了加密货币P2P网络中主流匿名性协议（如Dandelion、Lightning Network）的实际匿名性，提出基于贝叶斯推断的分析框架，揭示即使少量节点被攻陷也能大幅降低用户匿名性，指出现有方案在大规模网络下仍存在严重去匿名风险。""}",NDSS/2023,78.jsonl
144,加密货币、P2P网络、匿名性、Dandelion、Lightning Network、贝叶斯推断,"Cryptocurrency, P2P Network, Anonymity, Dandelion, Lightning Network, Bayesian Inference",如何系统性评估P2P网络匿名性协议的抗攻击能力？,How can the resilience of P2P network anonymity protocols against attacks be systematically evaluated?,匿名性评估、贝叶斯分析、协议安全,Anonymity evaluation Bayesian analysis protocol security,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,"Piyush Kumar Sharma, Devashish Gosain, Claudia Diaz","{""en"":""This paper models and evaluates the real-world anonymity of major anonymity protocols (e.g.,Dandelion,Lightning Network) in cryptocurrency P2P networks,proposing a Bayesian inference-based framework. The study reveals that even a small fraction of compromised nodes can significantly reduce user anonymity,highlighting serious deanonymization risks in current solutions."";""zh"":""本文系统建模并评估了加密货币P2P网络中主流匿名性协议（如Dandelion、Lightning Network）的实际匿名性，提出基于贝叶斯推断的分析框架，揭示即使少量节点被攻陷也能大幅降低用户匿名性，指出现有方案在大规模网络下仍存在严重去匿名风险。""}",NDSS/2023,78.jsonl
145,加密货币、P2P网络、匿名性、Dandelion、Lightning Network、贝叶斯推断,"Cryptocurrency, P2P Network, Anonymity, Dandelion, Lightning Network, Bayesian Inference",网络规模扩大是否能提升P2P匿名协议的安全性？,Does increasing network size improve the security of P2P anonymity protocols?,网络规模、匿名性、协议局限,Network size anonymity protocol limitation,On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies,"Piyush Kumar Sharma, Devashish Gosain, Claudia Diaz","{""en"":""This paper models and evaluates the real-world anonymity of major anonymity protocols (e.g.,Dandelion,Lightning Network) in cryptocurrency P2P networks,proposing a Bayesian inference-based framework. The study reveals that even a small fraction of compromised nodes can significantly reduce user anonymity,highlighting serious deanonymization risks in current solutions."";""zh"":""本文系统建模并评估了加密货币P2P网络中主流匿名性协议（如Dandelion、Lightning Network）的实际匿名性，提出基于贝叶斯推断的分析框架，揭示即使少量节点被攻陷也能大幅降低用户匿名性，指出现有方案在大规模网络下仍存在严重去匿名风险。""}",NDSS/2023,78.jsonl
146,域名安全、链上检测、图学习、DOITRUST、信任传播、恶意网站,"Domain Security, On-chain Detection, Graph Learning, DOITRUST, Trust Propagation, Malicious Website",如何利用图学习方法提升大规模域名链上安全检测的准确性与效率？,How can graph learning methods improve the accuracy and efficiency of large-scale on-chain domain security detection?,图神经网络、信任传播、恶意检测,Graph neural network trust propagation malicious detection,DOITRUST: Dissecting On-chain Compromised Internet Domains via Graph Learning,"Shuo Wang, Mahathir Almashor, Alsharif Abuadbba, et al.","{""en"":""This paper presents DOITRUST,a system combining graph neural networks and trust propagation to efficiently detect and trace stealthy malicious nodes in large-scale on-chain internet domains,significantly improving the accuracy and scalability of malicious domain identification.,"";""zh"":""本文提出DOITRUST系统，结合图神经网络与信任传播，实现对大规模互联网域名链上隐蔽恶意节点的高效检测与溯源，显著提升了恶意域名识别的准确率与可扩展性。""}",NDSS/2023,82.jsonl
147,域名安全、链上检测、图学习、DOITRUST、信任传播、恶意网站,"Domain Security, On-chain Detection, Graph Learning, DOITRUST, Trust Propagation, Malicious Website",在实际应用中，如何解决恶意域名标签稀疏带来的检测难题？,How to address the challenge of sparse malicious domain labels in practical detection scenarios?,半监督学习、标签传播、可扩展性,Semi-supervised learning label propagation scalability,DOITRUST: Dissecting On-chain Compromised Internet Domains via Graph Learning,"Shuo Wang, Mahathir Almashor, Alsharif Abuadbba, et al.","{""en"":""This paper presents DOITRUST,a system combining graph neural networks and trust propagation to efficiently detect and trace stealthy malicious nodes in large-scale on-chain internet domains,significantly improving the accuracy and scalability of malicious domain identification.,"";""zh"":""本文提出DOITRUST系统，结合图神经网络与信任传播，实现对大规模互联网域名链上隐蔽恶意节点的高效检测与溯源，显著提升了恶意域名识别的准确率与可扩展性。""}",NDSS/2023,82.jsonl
148,域名安全、链上检测、图学习、DOITRUST、信任传播、恶意网站,"Domain Security, On-chain Detection, Graph Learning, DOITRUST, Trust Propagation, Malicious Website",链上隐蔽恶意节点的检测对行业部署有何实际意义？,What is the practical significance of detecting stealthy on-chain malicious nodes for industry deployment?,安全防护、威胁溯源、行业应用,Security protection threat tracing industry application,DOITRUST: Dissecting On-chain Compromised Internet Domains via Graph Learning,"Shuo Wang, Mahathir Almashor, Alsharif Abuadbba, et al.","{""en"":""This paper presents DOITRUST,a system combining graph neural networks and trust propagation to efficiently detect and trace stealthy malicious nodes in large-scale on-chain internet domains,significantly improving the accuracy and scalability of malicious domain identification.,"";""zh"":""本文提出DOITRUST系统，结合图神经网络与信任传播，实现对大规模互联网域名链上隐蔽恶意节点的高效检测与溯源，显著提升了恶意域名识别的准确率与可扩展性。""}",NDSS/2023,82.jsonl
149,机器学习、遗忘、隐私保护、特征删除、标签删除、影响函数,"Machine Learning, Unlearning, Privacy Protection, Feature Removal, Label Removal, Influence Function",在机器学习模型中，如何高效实现对敏感特征和标签的遗忘？,How to efficiently achieve unlearning of sensitive features and labels in machine learning models?,模型更新、隐私合规、影响函数,Model update privacy compliance influence function,Machine Unlearning of Features and Labels,"Alexander Warnecke, Lukas Pirch, Christian Wressnegger, Konrad Rieck","{""en"":""This paper proposes a feature and label unlearning method based on influence functions,enabling efficient correction of sensitive information in models,supporting privacy compliance and the ""right to be forgotten"",with both theoretical guarantees and practical efficiency."";""zh"":""本文提出了一种基于影响函数的特征与标签遗忘方法，可高效修正模型中的敏感信息，支持隐私合规与""被遗忘权""要求，兼顾理论可证性与实际效率。""}",NDSS/2023,84.jsonl
150,机器学习、遗忘、隐私保护、特征删除、标签删除、影响函数,"Machine Learning, Unlearning, Privacy Protection, Feature Removal, Label Removal, Influence Function",现有机器学习遗忘方法在大规模数据场景下存在哪些局限？,What are the limitations of current machine unlearning methods in large-scale data scenarios?,可扩展性、效率、理论保证,Scalability efficiency theoretical guarantee,Machine Unlearning of Features and Labels,"Alexander Warnecke, Lukas Pirch, Christian Wressnegger, Konrad Rieck","{""en"":""This paper proposes a feature and label unlearning method based on influence functions,enabling efficient correction of sensitive information in models,supporting privacy compliance and the ""right to be forgotten"",with both theoretical guarantees and practical efficiency."";""zh"":""本文提出了一种基于影响函数的特征与标签遗忘方法，可高效修正模型中的敏感信息，支持隐私合规与""被遗忘权""要求，兼顾理论可证性与实际效率。""}",NDSS/2023,84.jsonl
151,机器学习、遗忘、隐私保护、特征删除、标签删除、影响函数,"Machine Learning, Unlearning, Privacy Protection, Feature Removal, Label Removal, Influence Function",如何在不影响模型性能的前提下实现高效遗忘？,How to achieve efficient unlearning without degrading model performance?,模型保真、理论分析、经验评估,Model fidelity theoretical analysis empirical evaluation,Machine Unlearning of Features and Labels,"Alexander Warnecke, Lukas Pirch, Christian Wressnegger, Konrad Rieck","{""en"":""This paper proposes a feature and label unlearning method based on influence functions,enabling efficient correction of sensitive information in models,supporting privacy compliance and the ""right to be forgotten"",with both theoretical guarantees and practical efficiency."";""zh"":""本文提出了一种基于影响函数的特征与标签遗忘方法，可高效修正模型中的敏感信息，支持隐私合规与""被遗忘权""要求，兼顾理论可证性与实际效率。""}",NDSS/2023,84.jsonl
152,入侵容忍、文件碎片化、分布式存储、密钥管理、阈值方案、数据安全,"Intrusion tolerance, File fragmentation, Distributed storage, Key management, Threshold scheme, Data security",分布式系统中，文件碎片化与分散存储如何提升入侵容忍能力？,How does file fragmentation and distributed storage enhance intrusion tolerance in distributed systems?,碎片化机制、分布式安全、入侵防护,Fragmentation mechanism distributed security intrusion defense,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,"Jean-Michel Fray, Yves Deswarte, David Powell","{""en"":""This paper proposes an intrusion-tolerant method based on fine-grained file fragmentation and distributed storage. By splitting sensitive files into multiple fragments and distributing them across different storage nodes,even if some nodes are compromised,attackers find it difficult to reconstruct the complete data. Combined with threshold key schemes and distributed management,the approach significantly enhances system security and reliability."";""zh"":""本文提出了一种基于细粒度文件碎片化与分散存储的入侵容忍方法。通过将敏感文件分割为多个碎片并分布在不同存储节点，即使部分节点被攻陷，攻击者也难以重构完整数据。结合密钥阈值方案和分布式管理，有效提升了系统的安全性与可靠性。""}",SP/1986,6.jsonl
153,入侵检测、通信信道安全、匿名碎片、分布式容错、数据完整性、系统可用性,"Intrusion detection, Communication channel security, Anonymous fragments, Distributed fault tolerance, Data integrity, System availability",在分布式环境下，如何防止通信信道中的数据碎片被窃听或关联？,How can data fragments in communication channels be protected from eavesdropping or correlation in distributed environments?,通信安全、匿名性、碎片调度,Communication security anonymity fragment scheduling,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,"Jean-Michel Fray, Yves Deswarte, David Powell","{""en"":""This paper proposes an intrusion-tolerant method based on fine-grained file fragmentation and distributed storage. By splitting sensitive files into multiple fragments and distributing them across different storage nodes,even if some nodes are compromised,attackers find it difficult to reconstruct the complete data. Combined with threshold key schemes and distributed management,the approach significantly enhances system security and reliability."";""zh"":""本文提出了一种基于细粒度文件碎片化与分散存储的入侵容忍方法。通过将敏感文件分割为多个碎片并分布在不同存储节点，即使部分节点被攻陷，攻击者也难以重构完整数据。结合密钥阈值方案和分布式管理，有效提升了系统的安全性与可靠性。""}",SP/1986,6.jsonl
154,碎片化算法、密钥阈值、容错机制、分布式架构、数据认证、系统设计,"Fragmentation algorithm, Key threshold, Fault tolerance, Distributed architecture, Data authentication, System design",细粒度碎片化与密钥阈值方案在实际系统设计中面临哪些挑战？,What are the main challenges of implementing fine-grained fragmentation and key threshold schemes in real-world system design?,算法效率、密钥管理、认证机制,Algorithm efficiency key management authentication mechanism,INTRUSION-TOLERANCE USING FINE-GRAIN FRAGMENTATION-SCATTERING,"Jean-Michel Fray, Yves Deswarte, David Powell","{""en"":""This paper proposes an intrusion-tolerant method based on fine-grained file fragmentation and distributed storage. By splitting sensitive files into multiple fragments and distributing them across different storage nodes,even if some nodes are compromised,attackers find it difficult to reconstruct the complete data. Combined with threshold key schemes and distributed management,the approach significantly enhances system security and reliability."";""zh"":""本文提出了一种基于细粒度文件碎片化与分散存储的入侵容忍方法。通过将敏感文件分割为多个碎片并分布在不同存储节点，即使部分节点被攻陷，攻击者也难以重构完整数据。结合密钥阈值方案和分布式管理，有效提升了系统的安全性与可靠性。""}",SP/1986,6.jsonl
155,计算机病毒、容错技术、程序流监控、N版本编程、恶意逻辑检测、系统安全,"Computer virus, Fault tolerance, Program flow monitoring, N-version programming, Malicious logic detection, System security",容错技术如何用于提升计算机病毒检测与防护能力？,How can fault tolerance techniques enhance the detection and defense against computer viruses?,容错机制、病毒检测、系统鲁棒性,Fault tolerance mechanism virus detection system robustness,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,"{""en"":""This paper proposes applying fault tolerance techniques (such as program flow monitoring and N-version programming) to computer virus detection and defense. By fine-grained monitoring of program control flow and multi-version consensus,the approach effectively identifies and contains virus propagation,enhancing system security and robustness."";""zh"":""本文提出将容错技术（如程序流监控与N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本共识，有效识别并遏制病毒传播，提升系统安全性与鲁棒性。""}",SP/1988,9.jsonl
156,程序流监控、签名机制、加密CFG、原子I/O、病毒防护、误差恢复,"Program flow monitoring, Signature mechanism, Encrypted CFG, Atomic I/O, Virus prevention, Error recovery",细粒度程序流监控在病毒检测中具备哪些优势？,What are the advantages of fine-grained program flow monitoring in virus detection?,签名比对、CFG加密和原子I/O可实现运行时病毒行为精准检测与阻断。,Signature comparison CFG encryption and atomic I/O enable precise runtime detection and blocking of virus behaviors.,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,"{""en"":""This paper proposes applying fault tolerance techniques (such as program flow monitoring and N-version programming) to computer virus detection and defense. By fine-grained monitoring of program control flow and multi-version consensus,the approach effectively identifies and contains virus propagation,enhancing system security and robustness."";""zh"":""本文提出将容错技术（如程序流监控与N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本共识，有效识别并遏制病毒传播，提升系统安全性与鲁棒性。""}",SP/1988,9.jsonl
157,N版本编程、设计容错、共识机制、开发工具安全、特洛伊木马防护、系统完整性,"N-version programming, Design fault tolerance, Consensus mechanism, Development tool security, Trojan horse defense, System integrity",N版本编程如何提升开发工具和系统的安全性与完整性？,How does N-version programming enhance the security and integrity of development tools and systems?,多版本共识可屏蔽设计缺陷和恶意逻辑，提升系统整体安全性。,Multi-version consensus can mask design flaws and malicious logic improving overall system security.,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,A FAULT TOLERANCE APPROACH TO COMPUTER VIRUSES,"{""en"":""This paper proposes applying fault tolerance techniques (such as program flow monitoring and N-version programming) to computer virus detection and defense. By fine-grained monitoring of program control flow and multi-version consensus,the approach effectively identifies and contains virus propagation,enhancing system security and robustness."";""zh"":""本文提出将容错技术（如程序流监控与N版本编程）应用于计算机病毒检测与防护，通过细粒度监控程序控制流和多版本共识，有效识别并遏制病毒传播，提升系统安全性与鲁棒性。""}",SP/1988,9.jsonl
158,访问控制模型、安全性分析、ESPM、HRU模型、权限传播、表达能力,"Access control model, Safety analysis, ESPM, HRU model, Rights propagation, Expressive power",如何在保证表达能力的同时提升访问控制模型的安全性分析可行性？,How can access control models achieve feasible safety analysis while maintaining expressive power?,安全性分析、表达能力、模型设计,Safety analysis expressive power model design,Safety Analysis For The Extended Schematic Protection Model,Safety Analysis For The Extended Schematic Protection Model,"{""en"":""This paper proposes the Extended Schematic Protection Model (ESPM),which achieves efficient safety analysis for practical security policies while maintaining expressive power equivalent to the HRU model,providing a theoretical foundation for rights management in multi-user systems."";""zh"":""本文提出了扩展示意保护模型（ESPM），在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，为多用户系统的权限管理提供了理论基础。""}",SP/1991,1.jsonl
159,权限传播、联合创建、类型系统、可判定性、单调性、保护机制,"Rights propagation, Joint creation, Type system, Decidability, Monotonicity, Protection mechanism",ESPM模型中的联合创建操作对安全性分析有何影响？,How does the joint creation operation in the ESPM model affect safety analysis?,联合创建操作提升了模型表达能力，但需限制循环结构以保证安全性分析的可判定性。,Joint creation enhances model expressiveness but cyclic structures must be restricted to ensure decidable safety analysis.,Safety Analysis For The Extended Schematic Protection Model,Safety Analysis For The Extended Schematic Protection Model,"{""en"":""This paper proposes the Extended Schematic Protection Model (ESPM),which achieves efficient safety analysis for practical security policies while maintaining expressive power equivalent to the HRU model,providing a theoretical foundation for rights management in multi-user systems."";""zh"":""本文提出了扩展示意保护模型（ESPM），在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，为多用户系统的权限管理提供了理论基础。""}",SP/1991,1.jsonl
160,访问控制、表达能力、单调HRU、保护模型、实际应用、复杂性分析,"Access control, Expressive power, Monotonic HRU, Protection model, Practical application, Complexity analysis",ESPM模型在实际多用户系统权限管理中具备哪些优势？,What are the advantages of the ESPM model in practical multi-user rights management?,ESPM兼具理论表达能力和实际安全性分析效率，适用于复杂权限管理场景。,ESPM combines theoretical expressiveness with efficient safety analysis making it suitable for complex rights management scenarios.,Safety Analysis For The Extended Schematic Protection Model,Safety Analysis For The Extended Schematic Protection Model,"{""en"":""This paper proposes the Extended Schematic Protection Model (ESPM),which achieves efficient safety analysis for practical security policies while maintaining expressive power equivalent to the HRU model,providing a theoretical foundation for rights management in multi-user systems."";""zh"":""本文提出了扩展示意保护模型（ESPM），在保持与HRU模型等价表达能力的同时，实现了对实际安全策略的高效安全性分析，为多用户系统的权限管理提供了理论基础。""}",SP/1991,1.jsonl
161,硬件环境、安全协议、资源约束、协议简化、单用户设备、分布式系统,"Hardware environment, Security protocol, Resource constraints, Protocol simplification, Single-user device, Distributed system",硬件环境的变化如何影响安全协议的设计与实现？,How do changes in the hardware environment affect the design and implementation of security protocols?,资源约束、协议复杂性、系统演化,Resource constraints protocol complexity system evolution,The Hardware Environment,R.M. Needham,"{""en"":""This paper discusses the profound impact of the hardware environment on security protocol design,noting that with abundant computing and storage resources,protocols can focus more on simplicity and verifiability,and the rise of single-user devices and distributed systems will reshape security requirements."";""zh"":""本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的丰富，协议可更注重简洁性和可验证性，单用户设备和分布式系统的兴起也将重塑安全需求。""}",SP/1999,18.jsonl
162,协议简化、错误防范、消息冗余、加密机制、无密钥方案、未来趋势,"Protocol simplification, Error prevention, Message redundancy, Encryption mechanism, Keyless scheme, Future trend",为何协议简化和冗余设计在现代安全协议中变得更加重要？,Why are protocol simplification and redundancy design becoming more important in modern security protocols?,简化设计有助于减少实现错误，冗余机制提升协议健壮性和安全性。,Simplified design helps reduce implementation errors and redundancy mechanisms enhance protocol robustness and security.,The Hardware Environment,R.M. Needham,"{""en"":""This paper discusses the profound impact of the hardware environment on security protocol design,noting that with abundant computing and storage resources,protocols can focus more on simplicity and verifiability,and the rise of single-user devices and distributed systems will reshape security requirements."";""zh"":""本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的丰富，协议可更注重简洁性和可验证性，单用户设备和分布式系统的兴起也将重塑安全需求。""}",SP/1999,18.jsonl
163,单用户设备、服务专用服务器、分布式通信、访问管理、身份验证、审计机制,"Single-user device, Service-dedicated server, Distributed communication, Access management, Identity verification, Audit mechanism",单用户设备和分布式系统的普及对安全研究带来哪些新挑战？,What new challenges do the prevalence of single-user devices and distributed systems bring to security research?,访问管理、身份验证和审计将成为未来安全研究的重点。,Access management identity verification and audit will become the focus of future security research.,The Hardware Environment,R.M. Needham,"{""en"":""This paper discusses the profound impact of the hardware environment on security protocol design,noting that with abundant computing and storage resources,protocols can focus more on simplicity and verifiability,and the rise of single-user devices and distributed systems will reshape security requirements."";""zh"":""本文探讨了硬件环境对安全协议设计的深远影响，指出随着计算与存储资源的丰富，协议可更注重简洁性和可验证性，单用户设备和分布式系统的兴起也将重塑安全需求。""}",SP/1999,18.jsonl
164,广播加密、智能卡、用户撤销、密钥管理、秘密共享、抗盗版,"Broadcast encryption, Smart card, User revocation, Key management, Secret sharing, Anti-piracy",在大规模广播加密系统中，如何高效实现用户撤销与密钥更新？,How can efficient user revocation and key update be achieved in large-scale broadcast encryption systems?,智能卡、密钥更新、系统扩展性,Smart card key update system scalability,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,"Noam Kogan, Yuval Shavitt, Avishai Wool","{""en"":""This paper proposes an efficient user revocation scheme for broadcast encryption based on smart cards,utilizing distributed computation and multi-round state management to significantly improve system scalability and anti-piracy capability."";""zh"":""本文提出了一种基于智能卡的高效广播加密用户撤销方案，通过分布式计算和多轮状态管理，显著提升了系统的可扩展性和抗盗版能力。""}",SP/2003,7.jsonl
165,秘密共享、状态管理、通信开销、存储优化、树型方案对比、仿真评估,"Secret sharing, State management, Communication overhead, Storage optimization, Tree-based scheme comparison, Simulation evaluation",秘密共享与多轮状态管理在广播加密撤销方案中有何优势？,What are the advantages of secret sharing and multi-round state management in broadcast encryption revocation schemes?,通信与存储开销低，支持大规模用户撤销，便于系统长期运行。,Low communication and storage overhead supports large-scale user revocation and facilitates long-term system operation.,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,"Noam Kogan, Yuval Shavitt, Avishai Wool","{""en"":""This paper proposes an efficient user revocation scheme for broadcast encryption based on smart cards,utilizing distributed computation and multi-round state management to significantly improve system scalability and anti-piracy capability."";""zh"":""本文提出了一种基于智能卡的高效广播加密用户撤销方案，通过分布式计算和多轮状态管理，显著提升了系统的可扩展性和抗盗版能力。""}",SP/2003,7.jsonl
166,抗盗版策略、智能卡瓶颈、系统寿命、参数优化、触发机制、仿真分析,"Anti-piracy strategy, Smart card bottleneck, System lifetime, Parameter optimization, Trigger mechanism, Simulation analysis",如何通过参数优化和触发机制提升广播加密系统的抗盗版能力与寿命？,How can parameter optimization and trigger mechanisms enhance the anti-piracy capability and lifetime of broadcast encryption systems?,参数选择与灵活触发机制可延长系统寿命并降低盗版风险。,Parameter selection and flexible trigger mechanisms can extend system lifetime and reduce piracy risk.,A Practical Revocation Scheme for Broadcast Encryption Using Smart Cards,"Noam Kogan, Yuval Shavitt, Avishai Wool","{""en"":""This paper proposes an efficient user revocation scheme for broadcast encryption based on smart cards,utilizing distributed computation and multi-round state management to significantly improve system scalability and anti-piracy capability."";""zh"":""本文提出了一种基于智能卡的高效广播加密用户撤销方案，通过分布式计算和多轮状态管理，显著提升了系统的可扩展性和抗盗版能力。""}",SP/2003,7.jsonl
167,传感器网络、数据注入攻击、跳点认证、分布式安全、能耗优化、密钥管理,"Sensor network, Data injection attack, Hop-by-hop authentication, Distributed security, Energy optimization, Key management",在大规模传感器网络中，如何有效防御由节点被攻陷引发的虚假数据注入攻击？,How can large-scale sensor networks effectively defend against false data injection attacks caused by node compromise?,分布式认证、节点冗余、能耗权衡,Distributed authentication node redundancy energy trade-off,An Interleaved Hop-by-Hop Authentication Scheme for Filtering of Injected False Data in Sensor Networks,"Sencun Zhu, Sanjeev Setia, Sushil Jajodia, Peng Ning","{""en"":""This paper proposes an interleaved hop-by-hop authentication mechanism that effectively filters false data injected by compromised nodes in sensor networks,ensuring base station detection capability and optimizing the balance between energy consumption and security."";""zh"":""本文提出了一种交错式跳点认证机制，能在传感器网络中有效过滤由被攻陷节点注入的虚假数据，保证基站检测能力并优化能耗与安全性平衡。""}",SP/2004,18.jsonl
168,跳点认证、密钥协商、节点关联、能耗安全权衡、攻击模型、容错机制,"Hop-by-hop authentication, Key agreement, Node association, Energy-security trade-off, Attack model, Fault tolerance mechanism",跳点认证机制在提升传感器网络安全性方面有哪些优势与局限？,What are the advantages and limitations of hop-by-hop authentication mechanisms in enhancing sensor network security?,跳点认证可在多跳路径中逐步过滤虚假数据，但需权衡能耗与认证强度，且对节点关联信息依赖较高。,Hop-by-hop authentication can filter false data step by step along multi-hop paths but requires balancing energy consumption and authentication strength and relies heavily on node association information.,An Interleaved Hop-by-Hop Authentication Scheme for Filtering of Injected False Data in Sensor Networks,"Sencun Zhu, Sanjeev Setia, Sushil Jajodia, Peng Ning","{""en"":""This paper proposes an interleaved hop-by-hop authentication mechanism that effectively filters false data injected by compromised nodes in sensor networks,ensuring base station detection capability and optimizing the balance between energy consumption and security."";""zh"":""本文提出了一种交错式跳点认证机制，能在传感器网络中有效过滤由被攻陷节点注入的虚假数据，保证基站检测能力并优化能耗与安全性平衡。""}",SP/2004,18.jsonl
169,分布式认证、能耗优化、攻击容忍、节点冗余、密钥管理、协议扩展性,"Distributed authentication, Energy optimization, Attack tolerance, Node redundancy, Key management, Protocol scalability",在传感器网络协议设计中，如何平衡安全性、能耗与协议扩展性？,How to balance security energy consumption and protocol scalability in sensor network protocol design?,通过分布式认证与节点冗余机制，可提升安全性和容错性，同时需优化密钥管理和能耗分配以支持协议扩展。,Distributed authentication and node redundancy mechanisms can improve security and fault tolerance while key management and energy allocation optimization are needed to support protocol scalability.,An Interleaved Hop-by-Hop Authentication Scheme for Filtering of Injected False Data in Sensor Networks,"Sencun Zhu, Sanjeev Setia, Sushil Jajodia, Peng Ning","{""en"":""This paper proposes an interleaved hop-by-hop authentication mechanism that effectively filters false data injected by compromised nodes in sensor networks,ensuring base station detection capability and optimizing the balance between energy consumption and security."";""zh"":""本文提出了一种交错式跳点认证机制，能在传感器网络中有效过滤由被攻陷节点注入的虚假数据，保证基站检测能力并优化能耗与安全性平衡。""}",SP/2004,18.jsonl
170,Web安全、浏览器隔离、虚拟机沙箱、应用管理、网络策略、用户可控性,"Web security, Browser isolation, Virtual machine sandbox, Application management, Network policy, User controllability",现代Web浏览器为何需要操作系统级的安全隔离机制？,Why do modern web browsers require OS-level security isolation mechanisms?,浏览器安全、虚拟化、隔离防护,Browser securityvirtualization isolation protection,A Safety-Oriented Platform for Web Applications,"Richard S. Cox, Jacob Gorm Hansen, Steven D. Gribble, Henry M. Levy","{""en"":""This paper proposes the Tahoma architecture,which assigns each web application an independent VM sandbox,achieving strong isolation between browsers and web services,and significantly improving security and controllability for users and services."";""zh"":""本文提出了Tahoma架构，通过为每个Web应用分配独立虚拟机沙箱，实现浏览器与Web服务的强隔离，显著提升了用户和服务的安全性与可控性。""}",SP/2006,11.jsonl
171,浏览器操作系统、沙箱机制、网络访问控制、用户界面安全、可信计算基,"Browser OS, Sandbox mechanism, Network access control, UI security, Trusted computing base",Tahoma架构如何提升Web应用的安全性与用户体验？,How does the Tahoma architecture enhance the security and user experience of web applications?,沙箱机制与网络策略限制提升了安全性，用户可见的应用管理增强了可控性和信任。,Sandbox mechanisms and network policy restrictions improve security while user-visible application management enhances controllability and trust.,A Safety-Oriented Platform for Web Applications,"Richard S. Cox, Jacob Gorm Hansen, Steven D. Gribble, Henry M. Levy","{""en"":""This paper proposes the Tahoma architecture,which assigns each web application an independent VM sandbox,achieving strong isolation between browsers and web services,and significantly improving security and controllability for users and services."";""zh"":""本文提出了Tahoma架构，通过为每个Web应用分配独立虚拟机沙箱，实现浏览器与Web服务的强隔离，显著提升了用户和服务的安全性与可控性。""}",SP/2006,11.jsonl
172,虚拟化性能、安全隔离、浏览器漏洞防护、可扩展性、用户操作延迟,"Virtualization performance, Security isolation, Browser vulnerability mitigation, Scalability, User operation latency",在实际部署中，虚拟机沙箱对Web浏览体验和系统性能有何影响？,What is the impact of VM sandboxing on web browsing experience and system performance in practice?,实验表明虚拟化带来的延迟和带宽开销较小，能有效防护大部分浏览器漏洞，兼顾安全与性能。,Experiments show that virtualization introduces minimal latency and bandwidth overhead effectively mitigates most browser vulnerabilities and balances security with performance.,A Safety-Oriented Platform for Web Applications,"Richard S. Cox, Jacob Gorm Hansen, Steven D. Gribble, Henry M. Levy","{""en"":""This paper proposes the Tahoma architecture,which assigns each web application an independent VM sandbox,achieving strong isolation between browsers and web services,and significantly improving security and controllability for users and services."";""zh"":""本文提出了Tahoma架构，通过为每个Web应用分配独立虚拟机沙箱，实现浏览器与Web服务的强隔离，显著提升了用户和服务的安全性与可控性。""}",SP/2006,11.jsonl
173,可重构硬件、FPGA安全、隔离原语、物理隔离、接口追踪、内存保护,"Reconfigurable hardware, FPGA security, isolation primitive, physical isolation, interface tracing, memory protection",在多供应商IP核混合的FPGA系统中，如何实现不同信任级别模块的安全隔离？,How can secure isolation of modules with different trust levels be achieved in FPGA systems integrating IP cores from multiple vendors?,物理隔离、接口追踪、信任分区,Physical isolation interface tracing trust partitioning,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"Ted Huffmire, Brett Brotherton, Gang Wang, Timothy Sherwood, Ryan Kastner, Timothy Levin, Thuy Nguyen, Cynthia Irvine","{""en"":""This paper proposes the ""Moats and Drawbridges"" isolation primitive,which achieves secure isolation and controlled communication among multiple modules in FPGA systems through physical isolation,interface tracing,and configuration scrubbing,enhancing the security and verifiability of reconfigurable hardware."";""zh"":本文提出了""护城河与吊桥""隔离原语，通过物理隔离、接口追踪和配置擦除等机制，实现了FPGA系统中多模块的安全隔离与受控通信，提升了可重构硬件的安全性和可验证性。""}",SP/2007,13.jsonl
174,FPGA、物理隔离、信任分区、配置擦除、动态重构、系统安全,"FPGA, physical isolation, trust partitioning, configuration scrubbing, dynamic reconfiguration, system security",FPGA系统在动态重构和模块复用过程中如何防止敏感信息泄露？,How can sensitive information leakage be prevented during dynamic reconfiguration and module reuse in FPGA systems?,配置擦除、动态隔离、信息清理,Configuration scrubbing dynamic isolation information sanitization,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"Ted Huffmire, Brett Brotherton, Gang Wang, Timothy Sherwood, Ryan Kastner, Timothy Levin, Thuy Nguyen, Cynthia Irvine","{""en"":""This paper proposes the ""Moats and Drawbridges"" isolation primitive,which achieves secure isolation and controlled communication among multiple modules in FPGA systems through physical isolation,interface tracing,and configuration scrubbing,enhancing the security and verifiability of reconfigurable hardware."";""zh"":本文提出了""护城河与吊桥""隔离原语，通过物理隔离、接口追踪和配置擦除等机制，实现了FPGA系统中多模块的安全隔离与受控通信，提升了可重构硬件的安全性和可验证性。""}",SP/2007,13.jsonl
175,接口追踪、总线仲裁、模块通信、信息流控制、硬件安全、可验证性,"Interface tracing, bus arbitration, module communication, information flow control, hardware security, verifiability",在FPGA多模块系统中，如何实现安全的模块间通信与信息流控制？,How can secure inter-module communication and information flow control be achieved in multi-module FPGA systems?,接口追踪、总线仲裁、信息流监控,Interface tracing bus arbitration information flow monitoring,Moats and Drawbridges: An Isolation Primitive for Reconfigurable Hardware Based Systems,"Ted Huffmire, Brett Brotherton, Gang Wang, Timothy Sherwood, Ryan Kastner, Timothy Levin, Thuy Nguyen, Cynthia Irvine","{""en"":""This paper proposes the ""Moats and Drawbridges"" isolation primitive,which achieves secure isolation and controlled communication among multiple modules in FPGA systems through physical isolation,interface tracing,and configuration scrubbing,enhancing the security and verifiability of reconfigurable hardware."";""zh"":本文提出了""护城河与吊桥""隔离原语，通过物理隔离、接口追踪和配置擦除等机制，实现了FPGA系统中多模块的安全隔离与受控通信，提升了可重构硬件的安全性和可验证性。""}",SP/2007,13.jsonl
176,植入式医疗设备、心脏起搏器、无线安全、软件无线电攻击、零功耗防护、隐私保护,"Implantable medical device, pacemaker, wireless security, software radio attack, zero-power defense, privacy protection",植入式心脏设备在无线通信安全方面面临哪些主要威胁？,What are the main wireless security threats faced by implantable cardiac devices?,无线窃听、重放攻击、未授权访问,Wireless eavesdropping replay attack unauthorized access,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,"Daniel Halperin, Thomas S. Heydt-Benjamin, Benjamin Ransford, Shane S. Clark, Benessa Defend, Will Morgan, Kevin Fu, Tadayoshi Kohno, William H. Maisel","{""en"":""This paper systematically analyzes the wireless security and privacy risks of implantable cardiac devices (such as pacemakers and ICDs),demonstrates protocol reverse engineering and attacks using software radio,and proposes zero-power defense mechanisms to enhance patient safety and privacy protection."";""zh"":""本文系统分析了植入式心脏设备（如起搏器、ICD）的无线通信安全与隐私风险，首次通过软件无线电实现协议逆向与攻击，并提出零功耗防护机制，有效提升患者安全与隐私保护。""}",SP/2008,14.jsonl
177,医疗设备安全、零功耗认证、患者感知、能量收集、物理层防护、应急可用性,"Medical device security, zero-power authentication, patient awareness, energy harvesting, physical layer defense, emergency availability",如何在不影响应急可用性的前提下提升植入式医疗设备的安全性？,How can the security of implantable medical devices be enhanced without compromising emergency availability?,零功耗认证、患者感知、物理层防护,Zero-power authentication patient awareness physical layer defense,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,"Daniel Halperin, Thomas S. Heydt-Benjamin, Benjamin Ransford, Shane S. Clark, Benessa Defend, Will Morgan, Kevin Fu, Tadayoshi Kohno, William H. Maisel","{""en"":""This paper systematically analyzes the wireless security and privacy risks of implantable cardiac devices (such as pacemakers and ICDs),demonstrates protocol reverse engineering and attacks using software radio,and proposes zero-power defense mechanisms to enhance patient safety and privacy protection."";""zh"":""本文系统分析了植入式心脏设备（如起搏器、ICD）的无线通信安全与隐私风险，首次通过软件无线电实现协议逆向与攻击，并提出零功耗防护机制，有效提升患者安全与隐私保护。""}",SP/2008,14.jsonl
178,无线协议逆向、主动攻击、隐私泄露、能量消耗攻击、声学密钥交换、未来展望,"Wireless protocol reverse engineering, active attack, privacy leakage, energy depletion attack, acoustic key exchange, future directions",未来植入式医疗设备在安全设计上应关注哪些关键问题？,What key issues should be considered in the security design of future implantable medical devices?,协议加密、能耗防护、患者知情、密钥管理,Protocol encryption energy consumption defense patient awareness key management,Pacemakers and Implantable Cardiac Defibrillators: Software Radio Attacks and Zero-Power Defenses,"Daniel Halperin, Thomas S. Heydt-Benjamin, Benjamin Ransford, Shane S. Clark, Benessa Defend, Will Morgan, Kevin Fu, Tadayoshi Kohno, William H. Maisel","{""en"":""This paper systematically analyzes the wireless security and privacy risks of implantable cardiac devices (such as pacemakers and ICDs),demonstrates protocol reverse engineering and attacks using software radio,and proposes zero-power defense mechanisms to enhance patient safety and privacy protection."";""zh"":""本文系统分析了植入式心脏设备（如起搏器、ICD）的无线通信安全与隐私风险，首次通过软件无线电实现协议逆向与攻击，并提出零功耗防护机制，有效提升患者安全与隐私保护。""}",SP/2008,14.jsonl
179,JavaScript安全、符号执行、字符串约束、代码注入、自动化分析、Kudzu工具,"JavaScript security, symbolic execution, string constraint, code injection, automated analysis, Kudzu tool",自动化符号执行框架如何提升JavaScript客户端代码的安全性分析能力？,How does an automated symbolic execution framework enhance the security analysis of client-side JavaScript code?,符号执行、输入空间探索、漏洞检测,Symbolic execution input space exploration vulnerability detection,A Symbolic Execution Framework for JavaScript,"Prateek Saxena, Devdatta Akhawe, Steve Hanna, Feng Mao, Stephen McCamant, Dawn Song","{""en"":""This paper presents Kudzu,the first tool to enable automated symbolic execution and input space exploration for client-side JavaScript code. By integrating a string constraint solver,Kudzu effectively discovers various code injection vulnerabilities and significantly improves the automation and coverage of web application security analysis."";""zh"":""本文提出了Kudzu工具，首次实现了对JavaScript客户端代码的自动化符号执行与输入空间探索，结合字符串约束求解器，有效发现了多种代码注入漏洞，显著提升了Web应用安全性分析的自动化与覆盖率。""}",SP/2010,26.jsonl
180,字符串约束求解、正则表达式、输入验证、动态分析、Web应用安全、Kaluza,"String constraint solving, regular expression, input validation, dynamic analysis, web application security, Kaluza",在Web应用安全分析中，字符串约束求解器的表达能力为何至关重要？,Why is the expressiveness of string constraint solvers critical in web application security analysis?,正则表达式、字符串操作、约束表达力,Regular expression string operation constraint expressiveness,A Symbolic Execution Framework for JavaScript,"Prateek Saxena, Devdatta Akhawe, Steve Hanna, Feng Mao, Stephen McCamant, Dawn Song","{""en"":""This paper presents Kudzu,the first tool to enable automated symbolic execution and input space exploration for client-side JavaScript code. By integrating a string constraint solver,Kudzu effectively discovers various code injection vulnerabilities and significantly improves the automation and coverage of web application security analysis."";""zh"":""本文提出了Kudzu工具，首次实现了对JavaScript客户端代码的自动化符号执行与输入空间探索，结合字符串约束求解器，有效发现了多种代码注入漏洞，显著提升了Web应用安全性分析的自动化与覆盖率。""}",SP/2010,26.jsonl
181,客户端代码注入、事件空间、动态符号执行、GUI自动化、覆盖率提升、XSS防护,"Client-side code injection, event space, dynamic symbolic execution, GUI automation, coverage improvement, XSS prevention",动态符号执行与GUI事件空间探索结合对Web安全测试有何优势？,What are the advantages of combining dynamic symbolic execution with GUI event space exploration for web security testing?,事件驱动、路径覆盖、自动化测试,Event-driven path coverage automated testing,A Symbolic Execution Framework for JavaScript,"Prateek Saxena, Devdatta Akhawe, Steve Hanna, Feng Mao, Stephen McCamant, Dawn Song","{""en"":""This paper presents Kudzu,the first tool to enable automated symbolic execution and input space exploration for client-side JavaScript code. By integrating a string constraint solver,Kudzu effectively discovers various code injection vulnerabilities and significantly improves the automation and coverage of web application security analysis."";""zh"":""本文提出了Kudzu工具，首次实现了对JavaScript客户端代码的自动化符号执行与输入空间探索，结合字符串约束求解器，有效发现了多种代码注入漏洞，显著提升了Web应用安全性分析的自动化与覆盖率。""}",SP/2010,26.jsonl
182,安全两方计算、半诚实模型、双重执行、信息泄漏、加密电路、协议优化,"Secure two-party computation, semi-honest model, dual execution, information leakage, garbled circuit, protocol optimization",如何在提升安全性的同时兼顾两方计算协议的效率？,How can two-party computation protocols balance enhanced security and efficiency?,双重执行协议、信息泄漏权衡、协议优化,Dual execution protocol information leakage trade-off protocol optimization,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,"Yan Huang, Jonathan Katz, David Evans","{""en"":""This paper proposes the Quid-Pro-Quo-tocols protocol,which uses dual execution and efficient output validation to upgrade the semi-honest model to a stronger security level against limited malicious behavior with minimal overhead,making it suitable for large-scale privacy-preserving computation."";""zh"":""本文提出了Quid-Pro-Quo-tocols协议，通过双重执行和高效的输出验证机制，在仅增加极小计算和通信开销的前提下，将半诚实模型提升为可抵御有限恶意行为的安全级别，适用于大规模隐私计算场景。""}",SP/2012,18.jsonl
183,信息泄漏、输出验证、恶意对手、协议安全性、应用场景、性能评估,"Information leakage, output validation, malicious adversary, protocol security, application scenario, performance evaluation",在实际应用中，单比特信息泄漏对安全两方计算有何影响？,What is the impact of single-bit information leakage on secure two-party computation in practice?,信息泄漏影响有限，适用于对效率要求高、可容忍极小泄漏的隐私计算应用,Limited impact suitable for privacy-preserving applications with high efficiency requirements and minimal leakage tolerance,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,"Yan Huang, Jonathan Katz, David Evans","{""en"":""This paper proposes the Quid-Pro-Quo-tocols protocol,which uses dual execution and efficient output validation to upgrade the semi-honest model to a stronger security level against limited malicious behavior with minimal overhead,making it suitable for large-scale privacy-preserving computation."";""zh"":""本文提出了Quid-Pro-Quo-tocols协议，通过双重执行和高效的输出验证机制，在仅增加极小计算和通信开销的前提下，将半诚实模型提升为可抵御有限恶意行为的安全级别，适用于大规模隐私计算场景。""}",SP/2012,18.jsonl
184,加密电路、输出公平性、渐进泄漏、协议增强、隐私保护、应用扩展,"Garbled circuit, output fairness, progressive leakage, protocol enhancement, privacy protection, application extension",如何通过协议增强进一步降低信息泄漏并提升输出公平性？,How can protocol enhancements further reduce information leakage and improve output fairness?,输出延迟、分步揭示、增强验证,Output delay stepwise revelation enhanced validation,Quid-Pro-Quo-tocols: Strengthening Semi-Honest Protocols with Dual Execution,"Yan Huang, Jonathan Katz, David Evans","{""en"":""This paper proposes the Quid-Pro-Quo-tocols protocol,which uses dual execution and efficient output validation to upgrade the semi-honest model to a stronger security level against limited malicious behavior with minimal overhead,making it suitable for large-scale privacy-preserving computation."";""zh"":""本文提出了Quid-Pro-Quo-tocols协议，通过双重执行和高效的输出验证机制，在仅增加极小计算和通信开销的前提下，将半诚实模型提升为可抵御有限恶意行为的安全级别，适用于大规模隐私计算场景。""}",SP/2012,18.jsonl
185,智能卡安全、随机数生成、克隆攻击、接触式支付、门禁系统、密钥管理,"Smart card security, random number generation, cloning attack, contactless payment, access control system, key management",现实智能卡系统中，随机数生成器的弱点会带来哪些安全隐患？,What security risks are posed by weak random number generators in real-world smart card systems?,随机数生成、密钥安全、克隆风险,Random number generation key security cloning risk,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,"Nicolas T. Courtois, Daniel Hulme, Kumail Hussain, Jerzy A. Gawinecki, Marek Grajek","{""en"":""This paper systematically analyzes the random number generation and key management issues in mainstream contactless and contact smart cards (e.g.,MiFare Classic,HID iClass) used in access control and payment scenarios,revealing the risks of cloning attacks and key leakage caused by poor randomness,and highlighting the urgent need for security upgrades and user education in the industry."";""zh"":""本文系统分析了主流接触式与非接触式智能卡（如MiFare Classic、HID iClass等）在门禁、支付等场景下的随机数生成与密钥管理问题，揭示了随机数质量不足导致的克隆攻击与密钥泄露风险，强调了行业对安全升级和用户教育的迫切需求。""}",SP/2013,45.jsonl
186,智能卡克隆、门禁安全、密钥多样性、攻击复杂度、用户教育,"Smart card cloning, access control security, key diversity, attack complexity, user awareness",如何通过提升密钥多样性和用户教育降低智能卡克隆攻击的实际风险？,How can increasing key diversity and user education reduce the practical risk of smart card cloning attacks?,密钥多样性、用户培训、防护升级,Key diversity user training defense upgrade,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,"Nicolas T. Courtois, Daniel Hulme, Kumail Hussain, Jerzy A. Gawinecki, Marek Grajek","{""en"":""This paper systematically analyzes the random number generation and key management issues in mainstream contactless and contact smart cards (e.g.,MiFare Classic,HID iClass) used in access control and payment scenarios,revealing the risks of cloning attacks and key leakage caused by poor randomness,and highlighting the urgent need for security upgrades and user education in the industry."";""zh"":""本文系统分析了主流接触式与非接触式智能卡（如MiFare Classic、HID iClass等）在门禁、支付等场景下的随机数生成与密钥管理问题，揭示了随机数质量不足导致的克隆攻击与密钥泄露风险，强调了行业对安全升级和用户教育的迫切需求。""}",SP/2013,45.jsonl
187,门禁系统、序列号安全、低熵风险、物理访问控制、行业升级,"Access control system, serial number security, low entropy risk, physical access control, industry upgrade",门禁系统中序列号和设施码的低熵问题会带来哪些实际安全挑战？,What practical security challenges are caused by low-entropy serial numbers and facility codes in access control systems?,序列号碰撞、物理渗透、取证困难,Serial number collision physical penetration forensic difficulty,On Bad Randomness and Cloning of Contactless Payment and Building Smart Cards,"Nicolas T. Courtois, Daniel Hulme, Kumail Hussain, Jerzy A. Gawinecki, Marek Grajek","{""en"":""This paper systematically analyzes the random number generation and key management issues in mainstream contactless and contact smart cards (e.g.,MiFare Classic,HID iClass) used in access control and payment scenarios,revealing the risks of cloning attacks and key leakage caused by poor randomness,and highlighting the urgent need for security upgrades and user education in the industry."";""zh"":""本文系统分析了主流接触式与非接触式智能卡（如MiFare Classic、HID iClass等）在门禁、支付等场景下的随机数生成与密钥管理问题，揭示了随机数质量不足导致的克隆攻击与密钥泄露风险，强调了行业对安全升级和用户教育的迫切需求。""}",SP/2013,45.jsonl
188,垃圾邮件、僵尸网络、邮件投递、地下经济、Cutwail、反垃圾策略,"Spam, botnet, email delivery, underground economy, Cutwail, anti-spam strategy",垃圾邮件活动中，哪些因素决定了一次垃圾邮件攻击的成功与否？,What factors determine the success of a spam campaign?,邮件地址质量、重试机制、资源分配,Email address quality retry mechanism resource allocation,The Tricks of the Trade: What Makes Spam Campaigns Successful?,"Jane Iedemska, Gianluca Stringhini, Richard Kemmerer, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper systematically analyzes the operation of spam botnets (e.g.,Cutwail),revealing that factors such as email address quality,retry mechanism,and resource allocation are decisive for the success of spam campaigns,overturning traditional misconceptions about botnet size and geographic distribution."";""zh"":""本文系统分析了垃圾邮件僵尸网络（如Cutwail）运营机制，揭示了邮件地址质量、重试机制和资源分配等因素对垃圾邮件活动成败的决定性影响，推翻了传统对僵尸网络规模和地理分布的误解。""}",SP/2014,57.jsonl
189,邮件地址管理、重试策略、反垃圾防护、机器学习检测、地下市场,"Email address management, retry strategy, anti-spam defense, machine learning detection, underground market",垃圾邮件僵尸网络如何通过重试机制提升邮件投递成功率？,How do spam botnets improve email delivery success rates through retry mechanisms?,重试机制、网络容错、投递优化,Retry mechanism network fault tolerance delivery optimization,The Tricks of the Trade: What Makes Spam Campaigns Successful?,"Jane Iedemska, Gianluca Stringhini, Richard Kemmerer, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper systematically analyzes the operation of spam botnets (e.g.,Cutwail),revealing that factors such as email address quality,retry mechanism,and resource allocation are decisive for the success of spam campaigns,overturning traditional misconceptions about botnet size and geographic distribution."";""zh"":""本文系统分析了垃圾邮件僵尸网络（如Cutwail）运营机制，揭示了邮件地址质量、重试机制和资源分配等因素对垃圾邮件活动成败的决定性影响，推翻了传统对僵尸网络规模和地理分布的误解。""}",SP/2014,57.jsonl
190,地理分布、僵尸网络租赁、成本效益、反垃圾误区、经验调优,"Geographic distribution, botnet rental, cost-effectiveness, anti-spam misconception, experience tuning",垃圾邮件僵尸网络中，僵尸主机的地理分布对攻击效果有何实际影响？,What is the actual impact of bot geographic distribution on the effectiveness of spam botnets?,地理分布影响有限、经验调优更关键,Limited impact of geographic distribution experience-based tuning is more critical,The Tricks of the Trade: What Makes Spam Campaigns Successful?,"Jane Iedemska, Gianluca Stringhini, Richard Kemmerer, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper systematically analyzes the operation of spam botnets (e.g.,Cutwail),revealing that factors such as email address quality,retry mechanism,and resource allocation are decisive for the success of spam campaigns,overturning traditional misconceptions about botnet size and geographic distribution."";""zh"":""本文系统分析了垃圾邮件僵尸网络（如Cutwail）运营机制，揭示了邮件地址质量、重试机制和资源分配等因素对垃圾邮件活动成败的决定性影响，推翻了传统对僵尸网络规模和地理分布的误解。""}",SP/2014,57.jsonl
191,隐私影响评估、社会影响评估、物联网、隐私设计、合规方法,"Privacy Impact Assessment, Social Impact Assessment, Internet of Things, Privacy by Design, Compliance Methodology",物联网系统中如何将隐私影响评估扩展为社会影响评估？,How can privacy impact assessment be extended to social impact assessment in IoT systems?,社会影响评估不仅关注数据隐私，还涵盖伦理、可持续性和用户信任等更广泛社会因素，推动物联网系统的全面合规与责任创新。,"Social impact assessment covers not only data privacy but also broader social factors such as ethics, sustainability, and user trust, promoting comprehensive compliance and responsible innovation in IoT systems.",From Privacy Impact Assessment to Social Impact Assessment,"Lilian Edwards, Derek McAuley, Laurence Diver","{""en"":""This paper proposes extending privacy impact assessment (PIA) to social impact assessment (SIA) for IoT, addressing not only privacy but also ethics, transparency, and responsible innovation."";""zh"":""本文提出将隐私影响评估（PIA）扩展为社会影响评估（SIA），不仅关注隐私，还涵盖伦理、透明度与责任创新。""}",SP/2016,76.jsonl
192,隐私设计、合规工具、GDPR、数据最小化、责任创新,"Privacy by Design, Compliance Tools, GDPR, Data Minimization, Responsible Innovation",GDPR下隐私设计原则如何指导物联网产品的合规开发？,How does the privacy by design principle under GDPR guide compliant development of IoT products?,GDPR要求在产品全生命周期内嵌入隐私保护和数据最小化原则，推动企业采用标准化合规工具和责任创新方法。,"GDPR requires embedding privacy protection and data minimization throughout the product lifecycle, driving enterprises to adopt standardized compliance tools and responsible innovation approaches.",From Privacy Impact Assessment to Social Impact Assessment,"Lilian Edwards, Derek McAuley, Laurence Diver","{""en"":""The paper analyzes how GDPR's privacy by design principle mandates compliance and innovation in IoT, emphasizing data minimization and standardized tools."";""zh"":""论文分析了GDPR下隐私设计原则如何推动物联网合规与创新，强调数据最小化和标准化工具。""}",SP/2016,76.jsonl
193,物联网、数据共享、供应链、透明度、伦理影响,"Internet of Things, Data Sharing, Supply Chain, Transparency, Ethical Impact",物联网供应链中数据共享和再利用存在哪些伦理与合规挑战？,What are the ethical and compliance challenges of data sharing and reuse in IoT supply chains?,物联网供应链中的数据共享和再利用涉及多方责任、透明度和伦理风险，需通过社会影响评估和合规机制加以规范。,"Data sharing and reuse in IoT supply chains involve multi-party responsibility, transparency, and ethical risks, which should be regulated through social impact assessment and compliance mechanisms.",From Privacy Impact Assessment to Social Impact Assessment,"Lilian Edwards, Derek McAuley, Laurence Diver","{""en"":""The paper discusses ethical and compliance challenges in IoT supply chain data sharing, highlighting the need for transparency and social impact assessment."";""zh"":""论文探讨了物联网供应链数据共享的伦理与合规挑战，强调透明度和社会影响评估的重要性。""}",SP/2016,76.jsonl
194,能力系统、软件隔离、最小权限原则、硬件安全、内存保护,"Capability System, Software Compartmentalization, Principle of Least Privilege, Hardware Security, Memory Protection",如何通过能力系统和最小权限原则提升C语言软件的安全隔离能力？,How can capability systems and the principle of least privilege enhance security compartmentalization in C-language software?,能力系统结合最小权限原则可实现细粒度的软件隔离，降低攻击面并提升系统安全性。,Capability systems combined with the principle of least privilege enable fine-grained software compartmentalization reducing the attack surface and enhancing system security.,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,"Robert N. M. Watson, Jonathan Woodruff, Peter G. Neumann, Simon W. Moore, Jonathan Anderson, David Chisnall, Nirav Dave, Brooks Davis, Khilan Gudka, Ben Laurie, Steven J. Murdoch, Robert Norton, Michael Roe, Stacey Son, Munraj Vadera","{""en"":""This paper presents CHERI, a hybrid capability-system architecture that extends RISC ISA, compiler, and OS to support fine-grained, capability-based memory protection and scalable software compartmentalization, demonstrating significant improvements in security, programmability, and performance."";""zh"":""本文提出了CHERI，一种混合能力系统架构，通过扩展RISC指令集、编译器和操作系统，实现了细粒度的基于能力的内存保护和可扩展的软件隔离，显著提升了安全性、可编程性和性能。""}",SP/2015,46.jsonl
195,硬件-软件协同、对象能力模型、操作系统安全、兼容性、性能优化,"Hardware-Software Co-design, Object Capability Model, Operating System Security, Compatibility, Performance Optimization",硬件-软件协同的对象能力模型如何提升操作系统的安全性与兼容性？,How does a hardware-software co-designed object capability model improve OS security and compatibility?,对象能力模型通过硬件与软件协同实现高效隔离和安全通信，兼容现有系统并提升性能。,The object capability model implemented through hardware-software co-design enables efficient isolation and secure communication while maintaining compatibility with existing systems and improving performance.,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,"Robert N. M. Watson, Jonathan Woodruff, Peter G. Neumann, Simon W. Moore, Jonathan Anderson, David Chisnall, Nirav Dave, Brooks Davis, Khilan Gudka, Ben Laurie, Steven J. Murdoch, Robert Norton, Michael Roe, Stacey Son, Munraj Vadera","{""en"":""This paper presents CHERI, a hybrid capability-system architecture that extends RISC ISA, compiler, and OS to support fine-grained, capability-based memory protection and scalable software compartmentalization, demonstrating significant improvements in security, programmability, and performance."";""zh"":""本文提出了CHERI，一种混合能力系统架构，通过扩展RISC指令集、编译器和操作系统，实现了细粒度的基于能力的内存保护和可扩展的软件隔离，显著提升了安全性、可编程性和性能。""}",SP/2015,46.jsonl
196,内存安全、编译器支持、应用隔离、兼容C语言、性能评估,"Memory Safety, Compiler Support, Application Isolation, C Language Compatibility, Performance Evaluation",如何在不牺牲性能和兼容性的前提下实现C语言应用的内存安全与隔离？,How can memory safety and isolation be achieved for C-language applications without sacrificing performance and compatibility?,通过编译器和硬件支持的能力机制，可在保持高性能和兼容性的同时，实现C语言应用的内存安全与隔离。,With compiler and hardware-supported capability mechanisms memory safety and isolation for C-language applications can be achieved while maintaining high performance and compatibility.,CHERI: A Hybrid Capability-System Architecture for Scalable Software Compartmentalization,"Robert N. M. Watson, Jonathan Woodruff, Peter G. Neumann, Simon W. Moore, Jonathan Anderson, David Chisnall, Nirav Dave, Brooks Davis, Khilan Gudka, Ben Laurie, Steven J. Murdoch, Robert Norton, Michael Roe, Stacey Son, Munraj Vadera","{""en"":""This paper presents CHERI, a hybrid capability-system architecture that extends RISC ISA, compiler, and OS to support fine-grained, capability-based memory protection and scalable software compartmentalization, demonstrating significant improvements in security, programmability, and performance."";""zh"":""本文提出了CHERI，一种混合能力系统架构，通过扩展RISC指令集、编译器和操作系统，实现了细粒度的基于能力的内存保护和可扩展的软件隔离，显著提升了安全性、可编程性和性能。""}",SP/2015,46.jsonl
197,伪装者检测、图划分、文件系统访问事件、异常检测、Markov聚类,"Masquerader Detection, Graph Partitioning, File System Access Events, Anomaly Detection, Markov Clustering",如何利用图划分方法提升文件系统访问事件中的伪装者检测能力？,How can graph partitioning methods enhance masquerader detection in file system access events?,基于图划分的异常检测方法可有效区分正常用户与伪装者的行为模式，提升检测准确率。,Graph partitioning-based anomaly detection methods can effectively distinguish between normal users and masqueraders improving detection accuracy.,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,"Flavio Toffalini, Ivan Homoliak, Athul Harilal, Alexander Binder, Martin Ochoa","{""en"":""This paper proposes a one-class anomaly detection approach using graph partitioning on file system access events to identify masqueraders, demonstrating high accuracy on benchmark datasets."";""zh"":""本文提出了一种基于图划分的单类异常检测方法，通过分析文件系统访问事件识别伪装者，在基准数据集上取得了高准确率。""}",SP/2018,99.jsonl
198,异常检测、用户行为建模、相似性度量、文件系统日志、隐私保护,"Anomaly Detection, User Behavior Modeling, Similarity Measurement, File System Logs, Privacy Protection",如何在不暴露文件系统结构的前提下实现用户行为的异常检测？,How can anomaly detection of user behavior be achieved without exposing file system structure?,通过事件序列匿名化和图聚类，可在保护隐私的同时实现高效的异常检测。,By anonymizing event sequences and using graph clustering efficient anomaly detection can be achieved while preserving privacy.,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,"Flavio Toffalini, Ivan Homoliak, Athul Harilal, Alexander Binder, Martin Ochoa","{""en"":""This paper proposes a one-class anomaly detection approach using graph partitioning on file system access events to identify masqueraders, demonstrating high accuracy on benchmark datasets."";""zh"":""本文提出了一种基于图划分的单类异常检测方法，通过分析文件系统访问事件识别伪装者，在基准数据集上取得了高准确率。""}",SP/2018,99.jsonl
199,Markov聚类、图相似性、时间窗口、攻击检测、性能评估,"Markov Clustering, Graph Similarity, Time Window, Attack Detection, Performance Evaluation",Markov聚类算法在伪装者检测中的优势体现在哪些方面？,What are the advantages of Markov clustering algorithms in masquerader detection?,Markov聚类可自动发现行为模式，结合时间窗口分析提升检测的实时性与准确性。,Markov clustering can automatically discover behavioral patterns and combined with time window analysis improves the real-time and accuracy of detection.,Detection of Masqueraders Based on Graph Partitioning of File System Access Events,"Flavio Toffalini, Ivan Homoliak, Athul Harilal, Alexander Binder, Martin Ochoa","{""en"":""This paper proposes a one-class anomaly detection approach using graph partitioning on file system access events to identify masqueraders, demonstrating high accuracy on benchmark datasets."";""zh"":""本文提出了一种基于图划分的单类异常检测方法，通过分析文件系统访问事件识别伪装者，在基准数据集上取得了高准确率。""}",SP/2018,99.jsonl
200,隐私风险评估、数据主体、威胁建模、GDPR、风险分解,"Privacy Risk Assessment, Data Subject, Threat Modeling, GDPR, Risk Decomposition",如何在威胁建模中实现以数据主体为中心的隐私风险评估？,How can data subject-centric privacy risk assessment be achieved in threat modeling?,以数据主体为中心的风险分解方法可提升隐私威胁建模的合规性和精细度。,Data subject-centric risk decomposition enhances compliance and granularity in privacy threat modeling.,Privacy Risk Assessment for Data Subject-aware Threat Modeling,"Laurens Sion, Dimitri Van Landuyt, Kim Wuyts, Wouter Joosen","{""en"":""This paper proposes a privacy risk assessment model focused on data subjects, integrating with threat modeling to improve compliance with GDPR and enable fine-grained risk analysis."";""zh"":""本文提出了一种以数据主体为中心的隐私风险评估模型，结合威胁建模提升GDPR合规性，实现细粒度风险分析。""}",SP/2019,109.jsonl
201,数据流图、蒙特卡洛模拟、隐私工程、风险参数化、eHealth应用,"Data Flow Diagram, Monte Carlo Simulation, Privacy Engineering, Risk Parameterization, eHealth Application",如何通过数据流图和蒙特卡洛模拟提升隐私风险评估的自动化与精度？,How can data flow diagrams and Monte Carlo simulation improve automation and accuracy in privacy risk assessment?,结合DFD与蒙特卡洛模拟可实现风险输入自动化，提升评估精度与可追溯性。,Combining DFDs and Monte Carlo simulation enables automated risk input and improves assessment accuracy and traceability.,Privacy Risk Assessment for Data Subject-aware Threat Modeling,"Laurens Sion, Dimitri Van Landuyt, Kim Wuyts, Wouter Joosen","{""en"":""This paper proposes a privacy risk assessment model focused on data subjects, integrating with threat modeling to improve compliance with GDPR and enable fine-grained risk analysis."";""zh"":""本文提出了一种以数据主体为中心的隐私风险评估模型，结合威胁建模提升GDPR合规性，实现细粒度风险分析。""}",SP/2019,109.jsonl
202,GDPR合规、隐私影响评估、风险分解、数据保护设计、可追溯性,"GDPR Compliance, Privacy Impact Assessment, Risk Decomposition, Data Protection by Design, Traceability",GDPR下如何实现可追溯的隐私风险管理与合规性验证？,How can traceable privacy risk management and compliance verification be achieved under GDPR?,风险分解与参数化支持合规性验证，提升隐私工程的可追溯性与管理效率。,Risk decomposition and parameterization support compliance verification enhancing traceability and management efficiency in privacy engineering.,Privacy Risk Assessment for Data Subject-aware Threat Modeling,"Laurens Sion, Dimitri Van Landuyt, Kim Wuyts, Wouter Joosen","{""en"":""This paper proposes a privacy risk assessment model focused on data subjects, integrating with threat modeling to improve compliance with GDPR and enable fine-grained risk analysis."";""zh"":""本文提出了一种以数据主体为中心的隐私风险评估模型，结合威胁建模提升GDPR合规性，实现细粒度风险分析。""}",SP/2019,109.jsonl
203,神经网络、后门攻击、触发器检测、模型修复、深度学习安全,"Neural Network, Backdoor Attack, Trigger Detection, Model Repair, Deep Learning Security",如何检测和缓解深度神经网络中的后门攻击？,How can backdoor attacks in deep neural networks be detected and mitigated?,通过触发器逆向工程与神经元修剪等方法可有效检测并修复后门攻击。,Backdoor attacks can be effectively detected and mitigated via trigger reverse engineering and neuron pruning.,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,"Bolun Wang, Yuanshun Yao, Shawn Shan, Huiying Li, Bimal Viswanath, Haitao Zheng, Ben Y. Zhao","{""en"":""This paper proposes Neural Cleanse, a robust and generalizable system for detecting and mitigating backdoor attacks in deep neural networks, validated on multiple tasks and attack variants."";""zh"":""本文提出了Neural Cleanse系统，实现了对深度神经网络后门攻击的检测与缓解，并在多种任务和攻击变体上验证了其有效性。""}",SP/2019,70.jsonl
204,触发器逆向、异常检测、神经元激活、模型安全、自动化防御,"Trigger Reverse Engineering, Anomaly Detection, Neuron Activation, Model Security, Automated Defense",触发器逆向与神经元激活分析在神经网络安全中的作用是什么？,What roles do trigger reverse engineering and neuron activation analysis play in neural network security?,触发器逆向与神经元激活分析可辅助自动化检测后门并提升模型安全性。,Trigger reverse engineering and neuron activation analysis assist in automated backdoor detection and enhance model security.,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,"Bolun Wang, Yuanshun Yao, Shawn Shan, Huiying Li, Bimal Viswanath, Haitao Zheng, Ben Y. Zhao","{""en"":""This paper proposes Neural Cleanse, a robust and generalizable system for detecting and mitigating backdoor attacks in deep neural networks, validated on multiple tasks and attack variants."";""zh"":""本文提出了Neural Cleanse系统，实现了对深度神经网络后门攻击的检测与缓解，并在多种任务和攻击变体上验证了其有效性。""}",SP/2019,70.jsonl
205,深度学习、模型透明性、自动化防御、对抗样本、模型修补,"Deep Learning, Model Transparency, Automated Defense, Adversarial Example, Model Patching",如何提升深度学习模型的透明性与自动化防御能力？,How can the transparency and automated defense capability of deep learning models be improved?,通过自动化检测与修补机制可提升模型对后门和对抗样本的防御能力。,Automated detection and patching mechanisms can enhance model defense against backdoors and adversarial examples.,Neural Cleanse: Identifying and Mitigating Backdoor Attacks in Neural Networks,"Bolun Wang, Yuanshun Yao, Shawn Shan, Huiying Li, Bimal Viswanath, Haitao Zheng, Ben Y. Zhao","{""en"":""This paper proposes Neural Cleanse, a robust and generalizable system for detecting and mitigating backdoor attacks in deep neural networks, validated on multiple tasks and attack variants."";""zh"":""本文提出了Neural Cleanse系统，实现了对深度神经网络后门攻击的检测与缓解，并在多种任务和攻击变体上验证了其有效性。""}",SP/2019,70.jsonl
206,零知识证明、透明多项式委托、分层算术电路、后量子安全、交互式证明,"Zero-Knowledge Proof, Transparent Polynomial Delegation, Layered Arithmetic Circuits, Post-Quantum Security, Interactive Proof",如何通过透明多项式委托提升零知识证明系统的可扩展性与安全性？,How can transparent polynomial delegation enhance the scalability and security of zero-knowledge proof systems?,透明多项式委托机制无需可信设置，结合分层算术电路可实现高效、可扩展且后量子安全的零知识证明。,Transparent polynomial delegation eliminates the need for trusted setup and combined with layered arithmetic circuits enables efficient scalable and post-quantum secure zero-knowledge proofs.,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,"Jiaheng Zhang, Tiancheng Xie, Yupeng Zhang, Dawn Song","{""en"":""This paper presents a new transparent zero-knowledge argument scheme for layered arithmetic circuits, eliminating trusted setup, achieving efficient prover and verifier time, and demonstrating post-quantum security. The implementation, Virgo, outperforms existing systems in speed and scalability."";""zh"":""本文提出了一种面向分层算术电路的透明零知识论证方案，无需可信设置，证明者和验证者效率高，具备后量子安全性。实现系统Virgo在速度和可扩展性上优于现有方案。""}",SP/2020,100.jsonl
207,零知识证明、交互式证明、哈希函数、后量子安全、分层电路,"Zero-Knowledge Proof, Interactive Proof, Hash Function, Post-Quantum Security, Layered Circuits",零知识证明系统在后量子安全和高效性方面面临哪些挑战？,What are the challenges for zero-knowledge proof systems in terms of post-quantum security and efficiency?,传统零知识证明多依赖可信设置或复杂运算，难以兼顾高效性与后量子安全，需创新协议设计。,Traditional zero-knowledge proofs often rely on trusted setup or complex operations making it difficult to balance efficiency and post-quantum security requiring innovative protocol designs.,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,"Jiaheng Zhang, Tiancheng Xie, Yupeng Zhang, Dawn Song","{""en"":""This paper presents a new transparent zero-knowledge argument scheme for layered arithmetic circuits, eliminating trusted setup, achieving efficient prover and verifier time, and demonstrating post-quantum security. The implementation, Virgo, outperforms existing systems in speed and scalability."";""zh"":""本文提出了一种面向分层算术电路的透明零知识论证方案，无需可信设置，证明者和验证者效率高，具备后量子安全性。实现系统Virgo在速度和可扩展性上优于现有方案。""}",SP/2020,100.jsonl
208,透明零知识协议、可验证多项式委托、分层电路、系统实现、性能评估,"Transparent Zero-Knowledge Protocol, Verifiable Polynomial Delegation, Layered Circuits, System Implementation, Performance Evaluation",透明零知识协议在实际系统中的性能表现如何？,How does the transparent zero-knowledge protocol perform in real-world systems?,Virgo系统在大规模电路下实现了高效证明生成与验证，证明大小和验证时间均优于同类方案。,The Virgo system achieves efficient proof generation and verification for large-scale circuits with proof size and verification time outperforming similar schemes.,Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof,"Jiaheng Zhang, Tiancheng Xie, Yupeng Zhang, Dawn Song","{""en"":""This paper presents a new transparent zero-knowledge argument scheme for layered arithmetic circuits, eliminating trusted setup, achieving efficient prover and verifier time, and demonstrating post-quantum security. The implementation, Virgo, outperforms existing systems in speed and scalability."";""zh"":""本文提出了一种面向分层算术电路的透明零知识论证方案，无需可信设置，证明者和验证者效率高，具备后量子安全性。实现系统Virgo在速度和可扩展性上优于现有方案。""}",SP/2020,100.jsonl
209,隐私风险、通用语言模型、文本嵌入、敏感信息推断、对抗防御,"Privacy Risk, General-Purpose Language Model, Text Embedding, Sensitive Information Inference, Adversarial Defense",通用语言模型的文本嵌入存在哪些隐私风险？,What privacy risks exist in text embeddings generated by general-purpose language models?,文本嵌入可能泄露原文中的敏感信息，攻击者可通过推断攻击还原隐私内容。,Text embeddings may leak sensitive information from the original text allowing attackers to infer private content through inference attacks.,Privacy Risks of General-Purpose Language Models,"Xudong Pan, Mi Zhang, Shouling Ji, Min Yang","{""en"":""This paper systematically studies the privacy risks of general-purpose language models, demonstrates that embeddings can leak sensitive information, and evaluates four defense strategies for mitigation."";""zh"":""本文系统研究了通用语言模型的隐私风险，实验证明嵌入向量可泄露敏感信息，并评估了四种防御策略。""}",SP/2020,65.jsonl
210,敏感信息推断、关键词攻击、嵌入隐私保护、对抗训练、差分隐私,"Sensitive Information Inference, Keyword Attack, Embedding Privacy Protection, Adversarial Training, Differential Privacy",如何防御针对文本嵌入的敏感信息推断攻击？,How can sensitive information inference attacks on text embeddings be defended against?,可采用对抗训练、差分隐私、子空间投影等方法提升嵌入隐私保护能力。,Adversarial training differential privacy and subspace projection can enhance the privacy protection of embeddings.,Privacy Risks of General-Purpose Language Models,"Xudong Pan, Mi Zhang, Shouling Ji, Min Yang","{""en"":""This paper systematically studies the privacy risks of general-purpose language models, demonstrates that embeddings can leak sensitive information, and evaluates four defense strategies for mitigation."";""zh"":""本文系统研究了通用语言模型的隐私风险，实验证明嵌入向量可泄露敏感信息，并评估了四种防御策略。""}",SP/2020,65.jsonl
211,通用语言模型、嵌入攻击、隐私保护、实证评估、防御机制,"General-Purpose Language Model, Embedding Attack, Privacy Protection, Empirical Evaluation, Defense Mechanism",通用语言模型隐私保护的有效防御机制有哪些？,What are effective defense mechanisms for privacy protection in general-purpose language models?,子空间投影、对抗训练等机制可在一定程度上平衡嵌入的可用性与隐私性。,Subspace projection and adversarial training can balance the usability and privacy of embeddings to some extent.,Privacy Risks of General-Purpose Language Models,"Xudong Pan, Mi Zhang, Shouling Ji, Min Yang","{""en"":""This paper systematically studies the privacy risks of general-purpose language models, demonstrates that embeddings can leak sensitive information, and evaluates four defense strategies for mitigation."";""zh"":""本文系统研究了通用语言模型的隐私风险，实验证明嵌入向量可泄露敏感信息，并评估了四种防御策略。""}",SP/2020,65.jsonl
212,说话人识别、对抗攻击、黑盒测试、语音生物识别、系统鲁棒性,"Speaker Recognition, Adversarial Attack, Black-box Testing, Voice Biometrics, System Robustness",如何提升说话人识别系统在黑盒对抗攻击下的安全鲁棒性？,How can the security robustness of speaker recognition systems be enhanced against black-box adversarial attacks?,结合对抗样本生成与阈值估计算法，可提升说话人识别系统在实际黑盒攻击下的防护能力。,Combining adversarial sample generation and threshold estimation algorithms can enhance the defense capability of speaker recognition systems against practical black-box attacks.,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,"Guangke Chen, Sen Chen, Lingling Fan, Xiaoning Du, Zhe Zhao, Fu Song, Yang Liu","{""en"":""This paper presents the first comprehensive study of adversarial attacks on speaker recognition systems (SRSs) in the black-box setting, proposing the FAKEBOB attack, which achieves high attack success rates on both open-source and commercial systems, and discusses the need for more robust defenses."";""zh"":""本文首次系统性研究了黑盒环境下说话人识别系统的对抗攻击，提出了FAKEBOB攻击方法，在开源与商用系统上均实现了高攻击成功率，并呼吁加强防御机制。""}",SP/2021,18.jsonl
213,语音生物识别、迁移攻击、物理世界攻击、系统评估、人因研究,"Voice Biometrics, Transferability Attack, Physical-world Attack, System Evaluation, Human Study",对抗样本在物理世界下对语音生物识别系统的威胁有哪些？,What are the threats posed by adversarial samples to voice biometrics systems in the physical world?,对抗样本可在物理播放下欺骗语音识别系统，且难以被人类分辨，需结合人因研究与系统评估提升防护。,Adversarial samples can deceive voice recognition systems when played in the physical world and are hard for humans to distinguish requiring human study and system evaluation to improve defense.,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,"Guangke Chen, Sen Chen, Lingling Fan, Xiaoning Du, Zhe Zhao, Fu Song, Yang Liu","{""en"":""This paper presents the first comprehensive study of adversarial attacks on speaker recognition systems (SRSs) in the black-box setting, proposing the FAKEBOB attack, which achieves high attack success rates on both open-source and commercial systems, and discusses the need for more robust defenses."";""zh"":""本文首次系统性研究了黑盒环境下说话人识别系统的对抗攻击，提出了FAKEBOB攻击方法，在开源与商用系统上均实现了高攻击成功率，并呼吁加强防御机制。""}",SP/2021,18.jsonl
214,说话人识别、系统防御、对抗样本检测、鲁棒性评估、领域对抗,"Speaker Recognition, System Defense, Adversarial Sample Detection, Robustness Evaluation, Domain Adversarial",如何评估和提升说话人识别系统对对抗样本的检测与防御能力？,How to evaluate and improve the detection and defense capabilities of speaker recognition systems against adversarial samples?,通过多维鲁棒性评估与领域对抗训练，可提升系统对对抗样本的检测与防御能力。,Multi-dimensional robustness evaluation and domain adversarial training can improve the detection and defense capabilities of speaker recognition systems against adversarial samples.,Who is Real Bob? Adversarial Attacks on Speaker Recognition Systems,"Guangke Chen, Sen Chen, Lingling Fan, Xiaoning Du, Zhe Zhao, Fu Song, Yang Liu","{""en"":""This paper presents the first comprehensive study of adversarial attacks on speaker recognition systems (SRSs) in the black-box setting, proposing the FAKEBOB attack, which achieves high attack success rates on both open-source and commercial systems, and discusses the need for more robust defenses."";""zh"":""本文首次系统性研究了黑盒环境下说话人识别系统的对抗攻击，提出了FAKEBOB攻击方法，在开源与商用系统上均实现了高攻击成功率，并呼吁加强防御机制。""}",SP/2021,18.jsonl
215,电磁辐射、隐蔽信道、LoRa扩频、数据渗漏、物理隔离突破,"Electromagnetic Radiation, Covert Channel, LoRa Spread Spectrum, Data Exfiltration, Air-Gap Bypass",如何利用LoRa扩频技术突破物理隔离实现数据渗漏？,How can LoRa spread spectrum technology be used to bypass physical isolation for data exfiltration?,LoRa扩频可增强电磁隐蔽信道的抗衰减能力，实现远距离或穿透屏蔽的数据渗漏，突破传统物理隔离。,LoRa spread spectrum enhances the attenuation resistance of electromagnetic covert channels enabling long-range or shield-penetrating data exfiltration and bypassing traditional physical isolation.,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,"Cheng Shen, Tian Liu, Jun Huang, Rui Tan","{""en"":""This paper presents EMLoRa, a super resilient electromagnetic covert channel using LoRa-like spread spectrum, which enables long-range and shield-penetrating data exfiltration from air-gapped devices, and discusses the challenges and countermeasures."";""zh"":""本文提出了EMLoRa，一种利用LoRa扩频实现超强抗衰减能力的电磁隐蔽信道，可实现远距离及穿透屏蔽的数据渗漏，并探讨了相关挑战与对策。""}",SP/2021,87.jsonl
216,电磁隐蔽信道、抗衰减、物理安全、数据外泄检测、对抗防御,"Electromagnetic Covert Channel, Attenuation Resistance, Physical Security, Data Leakage Detection, Adversarial Defense",如何检测和防御基于扩频的电磁隐蔽信道攻击？,How to detect and defend against spread spectrum-based electromagnetic covert channel attacks?,结合能量检测与深度学习方法可提升对扩频电磁隐蔽信道的检测能力，但在高衰减环境下仍需新型防御手段。,Combining energy detection and deep learning methods can improve the detection of spread spectrum electromagnetic covert channels but novel defenses are needed in high-attenuation environments.,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,"Cheng Shen, Tian Liu, Jun Huang, Rui Tan","{""en"":""This paper presents EMLoRa, a super resilient electromagnetic covert channel using LoRa-like spread spectrum, which enables long-range and shield-penetrating data exfiltration from air-gapped devices, and discusses the challenges and countermeasures."";""zh"":""本文提出了EMLoRa，一种利用LoRa扩频实现超强抗衰减能力的电磁隐蔽信道，可实现远距离及穿透屏蔽的数据渗漏，并探讨了相关挑战与对策。""}",SP/2021,87.jsonl
217,电磁信号、设备定位、物理隔离、攻击场景、传感器部署,"Electromagnetic Signal, Device Localization, Physical Isolation, Attack Scenario, Sensor Deployment",如何通过电磁信号实现物理隔离设备的定位与攻击？,How can electromagnetic signals be used to localize and attack physically isolated devices?,利用电磁信号作为定位信标，可在大范围内实现对物理隔离设备的定位与攻击，需加强物理安全防护。,Using electromagnetic signals as beacon transmitters enables wide-area localization and attacks on physically isolated devices highlighting the need for enhanced physical security.,When LoRa Meets EMR: Electromagnetic Covert Channels Can Be Super Resilient,"Cheng Shen, Tian Liu, Jun Huang, Rui Tan","{""en"":""This paper presents EMLoRa, a super resilient electromagnetic covert channel using LoRa-like spread spectrum, which enables long-range and shield-penetrating data exfiltration from air-gapped devices, and discusses the challenges and countermeasures."";""zh"":""本文提出了EMLoRa，一种利用LoRa扩频实现超强抗衰减能力的电磁隐蔽信道，可实现远距离及穿透屏蔽的数据渗漏，并探讨了相关挑战与对策。""}",SP/2021,87.jsonl
218,AI代码生成、代码安全、Copilot、漏洞检测、CWE,"AI Code Generation, Code Security, Copilot, Vulnerability Detection, CWE",如何评估AI代码生成工具（如Copilot）在安全性方面的风险？,How can the security risks of AI code generation tools (such as Copilot) be assessed?,系统性分析AI代码生成工具在不同场景下生成代码的安全性，结合自动化工具与人工评估发现潜在漏洞。,Systematic analysis of AI code generation tools' security in various scenarios combining automated tools and manual review to identify potential vulnerabilities.,Asleep at the Keyboard? Assessing the Security of GitHub Copilot’s Code Contributions,"Hammond Pearce, Baleegh Ahmad, Benjamin Tan, Brendan Dolan-Gavitt, Ramesh Karri","{""en"":""This paper systematically investigates the security of code generated by GitHub Copilot revealing that a significant portion of its suggestions are vulnerable and highlighting the need for security-aware usage and evaluation."";""zh"":""本文系统性评估了GitHub Copilot生成代码的安全性，发现其建议中存在大量易受攻击的代码，强调了安全意识和评估的重要性。""}",SP/2022,95.jsonl
219,AI辅助编程、代码弱点、静态分析、自动化检测、软件开发安全,"AI-assisted Programming, Code Weakness, Static Analysis, Automated Detection, Software Development Security",AI辅助编程工具在生成代码时容易引入哪些常见安全弱点？,What common security weaknesses are likely to be introduced by AI-assisted programming tools when generating code?,AI辅助编程工具可能引入如输入验证不足、硬编码凭证、权限控制不当等常见弱点，需结合静态分析工具检测。,AI-assisted programming tools may introduce common weaknesses such as insufficient input validation hard-coded credentials and improper access control which require detection via static analysis tools.,Asleep at the Keyboard? Assessing the Security of GitHub Copilot’s Code Contributions,"Hammond Pearce, Baleegh Ahmad, Benjamin Tan, Brendan Dolan-Gavitt, Ramesh Karri","{""en"":""This paper systematically investigates the security of code generated by GitHub Copilot revealing that a significant portion of its suggestions are vulnerable and highlighting the need for security-aware usage and evaluation."";""zh"":""本文系统性评估了GitHub Copilot生成代码的安全性，发现其建议中存在大量易受攻击的代码，强调了安全意识和评估的重要性。""}",SP/2022,95.jsonl
220,AI代码生成、CWE榜单、安全实践、开发者教育、自动化工具,"AI Code Generation, CWE Top 25, Security Practice, Developer Education, Automated Tools",如何结合CWE榜单和自动化工具提升AI代码生成的安全性？,How can the security of AI code generation be improved by combining the CWE Top 25 list and automated tools?,结合CWE榜单和自动化检测工具可帮助开发者识别和规避高风险安全弱点，提升AI代码生成的整体安全性。,Combining the CWE Top 25 list and automated detection tools helps developers identify and avoid high-risk security weaknesses improving the overall security of AI-generated code.,Asleep at the Keyboard? Assessing the Security of GitHub Copilot’s Code Contributions,"Hammond Pearce, Baleegh Ahmad, Benjamin Tan, Brendan Dolan-Gavitt, Ramesh Karri","{""en"":""This paper systematically investigates the security of code generated by GitHub Copilot revealing that a significant portion of its suggestions are vulnerable and highlighting the need for security-aware usage and evaluation."";""zh"":""本文系统性评估了GitHub Copilot生成代码的安全性，发现其建议中存在大量易受攻击的代码，强调了安全意识和评估的重要性。""}",SP/2022,95.jsonl
221,分布式信任、参考监控、虚拟机安全、强制访问控制、可信计算,"Distributed Trust, Reference Monitor, Virtual Machine Security, Mandatory Access Control, Trusted Computing",如何在大规模分布式系统中实现可验证的信任基础与安全隔离？,How can a verifiable trust foundation and security compartmentalization be achieved in large-scale distributed systems?,基于可信硬件和参考监控的分布式信任机制可实现跨物理主机的安全隔离与动态授权。,Distributed trust mechanisms based on trusted hardware and reference monitors enable secure compartmentalization and dynamic authorization across physical hosts.,Shame on Trust in Distributed Systems,"Trent Jaeger, Patrick McDaniel, Luke St. Clair, Ramon Caceres, Reiner Sailer","{""en"":""This paper proposes the Shamon architecture, a distributed reference monitor infrastructure leveraging trusted hardware and virtual machines to provide scalable, verifiable trust and security goals enforcement in large-scale systems."";""zh"":""本文提出了Shamon架构，通过可信硬件与虚拟机参考监控实现大规模分布式系统的可验证信任与安全目标强制。""}",USENIX_Security/2006,28.jsonl
222,Shamon架构、硬件认证、远程证明、MAC策略、动态联盟,"Shamon Architecture, Hardware Attestation, Remote Attestation, MAC Policy, Dynamic Coalition",Shamon架构如何通过硬件认证与远程证明提升分布式系统的安全性？,How does the Shamon architecture enhance distributed system security through hardware attestation and remote proof?,Shamon架构利用TPM等硬件认证与远程证明机制，确保每台主机的安全属性可被验证，支持动态联盟与安全策略一致性。,The Shamon architecture uses TPM-based hardware attestation and remote proof to ensure verifiable security properties for each host supporting dynamic coalitions and consistent security policies.,Shame on Trust in Distributed Systems,"Trent Jaeger, Patrick McDaniel, Luke St. Clair, Ramon Caceres, Reiner Sailer","{""en"":""This paper proposes the Shamon architecture, a distributed reference monitor infrastructure leveraging trusted hardware and virtual machines to provide scalable, verifiable trust and security goals enforcement in large-scale systems."";""zh"":""本文提出了Shamon架构，通过可信硬件与虚拟机参考监控实现大规模分布式系统的可验证信任与安全目标强制。""}",USENIX_Security/2006,28.jsonl
223,分布式授权、MAC策略验证、虚拟机隔离、信任逻辑、系统可扩展性,"Distributed Authorization, MAC Policy Verification, VM Isolation, Trust Logic, System Scalability",在动态变化的分布式环境中，如何保证授权策略的正确性与系统可扩展性？,How can the correctness of authorization policies and system scalability be ensured in dynamic distributed environments?,通过逻辑验证与虚拟机隔离，结合分布式MAC策略分析，可在动态环境下实现安全授权与系统扩展。,Logic-based verification and VM isolation combined with distributed MAC policy analysis enable secure authorization and system scalability in dynamic environments.,Shame on Trust in Distributed Systems,"Trent Jaeger, Patrick McDaniel, Luke St. Clair, Ramon Caceres, Reiner Sailer","{""en"":""This paper proposes the Shamon architecture, a distributed reference monitor infrastructure leveraging trusted hardware and virtual machines to provide scalable, verifiable trust and security goals enforcement in large-scale systems."";""zh"":""本文提出了Shamon架构，通过可信硬件与虚拟机参考监控实现大规模分布式系统的可验证信任与安全目标强制。""}",USENIX_Security/2006,28.jsonl
223,投票机完整性、软件认证、Pioneer框架、自认证引导加载器、人可验证、攻击模型、引导链、硬件信任、时间测量、操作系统安全,"Voting Machine Integrity, Software Attestation, Pioneer Framework, Self-Attesting Bootloader, Human-Verifiable, Threat Model, Boot Chain, Hardware Trust, Timing Measurement, Operating System Security",如何通过软件认证机制提升电子投票机的完整性保障？,How can software attestation mechanisms enhance the integrity assurance of electronic voting machines?,软件认证可用于提升投票机等关键系统的完整性，但现有方法在实际部署中面临技术和操作难题。Pioneer等框架尝试通过时间测量和自认证机制实现人可验证的安全保障，但受限于硬件发展和攻击模型，难以满足实际需求。,"Software attestation can enhance the integrity of critical systems like voting machines, but current methods face technical and operational challenges in real-world deployment. Frameworks like Pioneer attempt to achieve human-verifiable security through timing measurements and self-attestation, but are limited by hardware evolution and threat models, making them impractical for actual needs.",On the Difficulty of Validating Voting Machine Software with Software,"Ryan Gardner, Sujata Garera, Aviel D. Rubin","{""en"":""This paper analyzes the challenges of validating the integrity of voting machine software using software-based attestation, focusing on the Pioneer framework. The authors design and implement a self-attesting voting machine, modify the Pioneer code for human-verifiable attestation, and demonstrate that current software attestation techniques are impractical for real-world voting machine integrity due to technological and operational limitations."";""zh"":""本文分析了利用基于软件的认证（以Pioneer框架为代表）验证投票机软件完整性的难点。作者设计并实现了自认证投票机，对Pioneer代码进行人可验证的改进，并通过实验表明，现有的软件认证技术因技术和操作局限性，难以在实际投票机完整性保障中应用。""}",USENIX_Security/2007,46.jsonl
224,投票机完整性、软件认证、Pioneer框架、自认证引导加载器、人可验证、攻击模型、引导链、硬件信任、时间测量、操作系统安全,"Voting Machine Integrity, Software Attestation, Pioneer Framework, Self-Attesting Bootloader, Human-Verifiable, Threat Model, Boot Chain, Hardware Trust, Timing Measurement, Operating System Security",Pioneer框架在人可验证的投票机软件认证中有哪些技术挑战？,What technical challenges does the Pioneer framework face in human-verifiable software attestation for voting machines?,软件认证可用于提升投票机等关键系统的完整性，但现有方法在实际部署中面临技术和操作难题。Pioneer等框架尝试通过时间测量和自认证机制实现人可验证的安全保障，但受限于硬件发展和攻击模型，难以满足实际需求。,"Software attestation can enhance the integrity of critical systems like voting machines, but current methods face technical and operational challenges in real-world deployment. Frameworks like Pioneer attempt to achieve human-verifiable security through timing measurements and self-attestation, but are limited by hardware evolution and threat models, making them impractical for actual needs.",On the Difficulty of Validating Voting Machine Software with Software,"Ryan Gardner, Sujata Garera, Aviel D. Rubin","{""en"":""This paper analyzes the challenges of validating the integrity of voting machine software using software-based attestation, focusing on the Pioneer framework. The authors design and implement a self-attesting voting machine, modify the Pioneer code for human-verifiable attestation, and demonstrate that current software attestation techniques are impractical for real-world voting machine integrity due to technological and operational limitations."";""zh"":""本文分析了利用基于软件的认证（以Pioneer框架为代表）验证投票机软件完整性的难点。作者设计并实现了自认证投票机，对Pioneer代码进行人可验证的改进，并通过实验表明，现有的软件认证技术因技术和操作局限性，难以在实际投票机完整性保障中应用。""}",USENIX_Security/2007,46.jsonl
225,投票机完整性、软件认证、Pioneer框架、自认证引导加载器、人可验证、攻击模型、引导链、硬件信任、时间测量、操作系统安全,"Voting Machine Integrity, Software Attestation, Pioneer Framework, Self-Attesting Bootloader, Human-Verifiable, Threat Model, Boot Chain, Hardware Trust, Timing Measurement, Operating System Security",当前软件认证技术在操作系统安全和引导链完整性方面存在哪些局限？,What are the limitations of current software attestation techniques in operating system security and boot chain integrity?,软件认证可用于提升投票机等关键系统的完整性，但现有方法在实际部署中面临技术和操作难题。Pioneer等框架尝试通过时间测量和自认证机制实现人可验证的安全保障，但受限于硬件发展和攻击模型，难以满足实际需求。,"Software attestation can enhance the integrity of critical systems like voting machines, but current methods face technical and operational challenges in real-world deployment. Frameworks like Pioneer attempt to achieve human-verifiable security through timing measurements and self-attestation, but are limited by hardware evolution and threat models, making them impractical for actual needs.",On the Difficulty of Validating Voting Machine Software with Software,"Ryan Gardner, Sujata Garera, Aviel D. Rubin","{""en"":""This paper analyzes the challenges of validating the integrity of voting machine software using software-based attestation, focusing on the Pioneer framework. The authors design and implement a self-attesting voting machine, modify the Pioneer code for human-verifiable attestation, and demonstrate that current software attestation techniques are impractical for real-world voting machine integrity due to technological and operational limitations."";""zh"":""本文分析了利用基于软件的认证（以Pioneer框架为代表）验证投票机软件完整性的难点。作者设计并实现了自认证投票机，对Pioneer代码进行人可验证的改进，并通过实验表明，现有的软件认证技术因技术和操作局限性，难以在实际投票机完整性保障中应用。""}",USENIX_Security/2007,46.jsonl
225,僵尸网络检测、流量分类、时序校准、RTT校准,"Botnet Detection, Flow Classification, Timing Calibration, RTT Calibration",如何通过流量分类技术检测僵尸网络的命令与控制通信？,How can flow classification techniques be used to detect botnet command and control communications?,基于流量统计特征的机器学习分类器可用于识别僵尸网络C&C通信模式，时序校准可提升检测准确性。,"Machine learning classifiers based on flow statistics can identify botnet C&C communication patterns, with timing calibration improving detection accuracy.",Salting Public Traces with Attack Traffic to Test Flow Classifiers,"Z. Berkay Celik, Jayaram Raghuram, George Kesidis, David J. Miller","{""en"":""This paper presents a method for calibrating timing-based features of simulated botnet C&C traffic before salting into background traffic traces. The study evaluates the impact of timing calibration on supervised flow classification performance using multiple machine learning algorithms, revealing that timing artifacts can significantly affect classifier results and detection accuracy."",""zh"":""本文提出了一种在将模拟僵尸网络C&C流量混入背景流量之前校准其时序特征的方法。研究评估了时序校准对监督流量分类性能的影响，使用多种机器学习算法，揭示了时序伪影可能显著影响分类器结果和检测准确性。""}",USENIX_Security/2011,85.jsonl
226,时序校准、RTT校准、流量特征、假阳性率,"Timing Calibration, RTT Calibration, Flow Features, False Positive Rate",时序特征校准对僵尸网络流量分类器的性能有何影响？,How does timing feature calibration affect the performance of botnet flow classifiers?,时序特征校准可减少假阳性率，提升分类器在实际网络环境中的检测准确性。,Timing feature calibration can reduce false positive rates and improve classifier detection accuracy in real network environments.,Salting Public Traces with Attack Traffic to Test Flow Classifiers,"Z. Berkay Celik, Jayaram Raghuram, George Kesidis, David J. Miller","{""en"":""This paper presents a method for calibrating timing-based features of simulated botnet C&C traffic before salting into background traffic traces. The study evaluates the impact of timing calibration on supervised flow classification performance using multiple machine learning algorithms, revealing that timing artifacts can significantly affect classifier results and detection accuracy."",""zh"":""本文提出了一种在将模拟僵尸网络C&C流量混入背景流量之前校准其时序特征的方法。研究评估了时序校准对监督流量分类性能的影响，使用多种机器学习算法，揭示了时序伪影可能显著影响分类器结果和检测准确性。""}",USENIX_Security/2011,85.jsonl
227,机器学习分类器、监督学习、精确率、召回率,"Machine Learning Classifiers, Supervised Learning, Precision, Recall",不同机器学习算法在僵尸网络流量检测中的性能表现如何？,How do different machine learning algorithms perform in botnet traffic detection?,不同机器学习算法在精确率、召回率和整体准确性方面表现各异，需要根据具体应用场景选择合适算法。,"Different machine learning algorithms vary in precision, recall, and overall accuracy, requiring selection based on specific application scenarios.",Salting Public Traces with Attack Traffic to Test Flow Classifiers,"Z. Berkay Celik, Jayaram Raghuram, George Kesidis, David J. Miller","{""en"":""This paper presents a method for calibrating timing-based features of simulated botnet C&C traffic before salting into background traffic traces. The study evaluates the impact of timing calibration on supervised flow classification performance using multiple machine learning algorithms, revealing that timing artifacts can significantly affect classifier results and detection accuracy."",""zh"":""本文提出了一种在将模拟僵尸网络C&C流量混入背景流量之前校准其时序特征的方法。研究评估了时序校准对监督流量分类性能的影响，使用多种机器学习算法，揭示了时序伪影可能显著影响分类器结果和检测准确性。""}",USENIX_Security/2011,85.jsonl
228,能量比例计算、功耗分析、信息泄露、隐私保护、恶意软件检测,Energy-Proportional Computing、Power Analysis、Information Leakage、Privacy Protection、Malware Detection,能量比例计算技术如何影响计算机系统的信息泄露风险？,How does energy-proportional computing technology affect information leakage risks in computer systems?,能量比例计算趋势使计算机系统更容易通过功耗分析泄露信息。降低空闲功耗降低了有效噪声底限，使活动模式在功耗追踪中更加突出。这种信息泄露既有害于隐私，也可能有益于恶意软件检测，特别是对嵌入式设备如医疗设备。,"The trend toward energy-proportional computing makes computers increasingly vulnerable to information leakage via power analysis. Reducing idle power consumption lowers the effective noise floor, making activity patterns more prominent in power traces. This information leakage can be harmful to privacy but may be beneficial for malware detection, especially for embedded devices such as medical devices.",Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,"Shane S. Clark, Benjamin Ransford, Kevin Fu","{""en"": ""This paper investigates the security and privacy implications of energy-proportional computing, demonstrating that power analysis can both compromise privacy through webpage identification and benefit malware detection in embedded devices. The research shows that modern computers leak increasing amounts of information through power consumption, creating both risks and opportunities for security applications."", ""zh"": ""本文研究了能量比例计算的安全与隐私影响，证明功耗分析既能通过网页识别损害隐私，也能在嵌入式设备中受益于恶意软件检测。研究表明现代计算机通过功耗消耗泄露越来越多的信息，为安全应用创造了风险和机遇。""}",USENIX_Security/2012,55.jsonl
229,恶意软件检测、嵌入式设备、医疗设备、功耗监控、侧信道攻击,Malware Detection、Embedded Devices、Medical Devices、Power Monitoring、Side-Channel Attacks,功耗分析在恶意软件检测中有哪些实际应用价值？,What practical applications does power analysis have in malware detection?,能量比例计算趋势使计算机系统更容易通过功耗分析泄露信息。降低空闲功耗降低了有效噪声底限，使活动模式在功耗追踪中更加突出。这种信息泄露既有害于隐私，也可能有益于恶意软件检测，特别是对嵌入式设备如医疗设备。,"The trend toward energy-proportional computing makes computers increasingly vulnerable to information leakage via power analysis. Reducing idle power consumption lowers the effective noise floor, making activity patterns more prominent in power traces. This information leakage can be harmful to privacy but may be beneficial for malware detection, especially for embedded devices such as medical devices.",Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,"Shane S. Clark, Benjamin Ransford, Kevin Fu","{""en"": ""This paper investigates the security and privacy implications of energy-proportional computing, demonstrating that power analysis can both compromise privacy through webpage identification and benefit malware detection in embedded devices. The research shows that modern computers leak increasing amounts of information through power consumption, creating both risks and opportunities for security applications."", ""zh"": ""本文研究了能量比例计算的安全与隐私影响，证明功耗分析既能通过网页识别损害隐私，也能在嵌入式设备中受益于恶意软件检测。研究表明现代计算机通过功耗消耗泄露越来越多的信息，为安全应用创造了风险和机遇。""}",USENIX_Security/2012,55.jsonl
230,嵌入式设备、医疗设备、功耗监控、侧信道攻击、能源效率,Embedded Devices、Medical Devices、Power Monitoring、Side-Channel Attacks、Energy Efficiency,嵌入式医疗设备如何通过功耗监控实现安全防护？,How can embedded medical devices achieve security protection through power monitoring?,能量比例计算趋势使计算机系统更容易通过功耗分析泄露信息。降低空闲功耗降低了有效噪声底限，使活动模式在功耗追踪中更加突出。这种信息泄露既有害于隐私，也可能有益于恶意软件检测，特别是对嵌入式设备如医疗设备。,"The trend toward energy-proportional computing makes computers increasingly vulnerable to information leakage via power analysis. Reducing idle power consumption lowers the effective noise floor, making activity patterns more prominent in power traces. This information leakage can be harmful to privacy but may be beneficial for malware detection, especially for embedded devices such as medical devices.",Potentia est Scientia: Security and Privacy Implications of Energy-Proportional Computing,"Shane S. Clark, Benjamin Ransford, Kevin Fu","{""en"": ""This paper investigates the security and privacy implications of energy-proportional computing, demonstrating that power analysis can both compromise privacy through webpage identification and benefit malware detection in embedded devices. The research shows that modern computers leak increasing amounts of information through power consumption, creating both risks and opportunities for security applications."", ""zh"": ""本文研究了能量比例计算的安全与隐私影响，证明功耗分析既能通过网页识别损害隐私，也能在嵌入式设备中受益于恶意软件检测。研究表明现代计算机通过功耗消耗泄露越来越多的信息，为安全应用创造了风险和机遇。""}",USENIX_Security/2012,55.jsonl
233,IPMI安全、BMC、固件漏洞、远程管理、服务器防护,"IPMI Security, BMC, Firmware Vulnerability, Remote Management, Server Defense",如何评估和防护服务器IPMI接口带来的安全风险？,How to assess and defend against security risks introduced by server IPMI interfaces?,IPMI作为服务器远程管理接口，存在固件漏洞、默认口令、认证绕过等风险，需隔离管理网络并及时更新固件。,IPMI as a remote management interface for servers poses risks such as firmware vulnerabilities default credentials and authentication bypass. Isolating management networks and timely firmware updates are essential.,Illuminating the Security Issues Surrounding Lights-Out Server Management,"Anthony J. Bonkoski, Russ Bielawski, J. Alex Halderman,Illuminating the Security"," {""en"":""This paper analyzes the security risks of IPMI and BMC in server management, reveals widespread vulnerabilities, and provides defense recommendations."",zh:""本文分析了服务器远程管理IPMI与BMC的安全风险，揭示了普遍存在的漏洞，并提出了防护建议。""}",USENIX_Security/2013,47.jsonl
234,BMC固件、默认口令、远程攻击、管理网络隔离,"BMC Firmware, Default Credentials, Remote Attack, Management Network Isolation",BMC固件安全加固的关键措施有哪些？,What are the key measures for strengthening BMC firmware security?,加强固件更新、禁用默认口令、隔离管理网络是提升BMC安全的核心措施。,Strengthening firmware updates disabling default credentials and isolating management networks are core measures to enhance BMC security.,Illuminating the Security Issues Surrounding Lights-Out Server Management,"Anthony J. Bonkoski, Russ Bielawski, J. Alex Halderman,Illuminating the Security"," {""en"":""This paper analyzes the security risks of IPMI and BMC in server management, reveals widespread vulnerabilities, and provides defense recommendations."",zh:""本文分析了服务器远程管理IPMI与BMC的安全风险，揭示了普遍存在的漏洞，并提出了防护建议。""}",USENIX_Security/2013,47.jsonl
235,服务器远程管理、认证绕过、固件更新、物理隔离、攻击面缩减,"Server Remote Management, Authentication Bypass, Firmware Update, Physical Isolation, Attack Surface Reduction",如何通过物理隔离和认证机制减少服务器远程管理的攻击面？,How can physical isolation and authentication mechanisms reduce the attack surface of server remote management?,物理隔离管理网络、强化认证机制和定期固件更新可有效缩小攻击面。,Physically isolating management networks strengthening authentication and regular firmware updates can effectively reduce the attack surface.,Illuminating the Security Issues Surrounding Lights-Out Server Management,"Anthony J. Bonkoski, Russ Bielawski, J. Alex Halderman,Illuminating the Security"," {""en"":""This paper analyzes the security risks of IPMI and BMC in server management, reveals widespread vulnerabilities, and provides defense recommendations."",zh:""本文分析了服务器远程管理IPMI与BMC的安全风险，揭示了普遍存在的漏洞，并提出了防护建议。""}",USENIX_Security/2013,47.jsonl
236,SRTT算法、BIND、域名服务器、随机化、NS选择安全,"SRTT Algorithm, BIND, Name Server, Randomization, NS Selection Security",BIND的SRTT算法在NS选择安全性上存在哪些隐患？,What are the security risks of BIND's SRTT algorithm in NS selection?,SRTT算法可被利用进行NS选择去随机化，降低域名解析安全性。,The SRTT algorithm can be exploited to derandomize NS selection reducing DNS resolution security.,Subverting BIND’s SRTT Algorithm Derandomizing NS Selection,"Matthew Thomas, Matthew Van Gundy, Eric Cronin, Kevin Butler, Fabian Monrose","{""en"":""This paper reveals how BIND's SRTT algorithm can be subverted to derandomize NS selection discusses the security implications, and proposes mitigation strategies."",zh:""本文揭示了BIND的SRTT算法可被利用进行NS选择去随机化，分析了安全影响，并提出了缓解措施。""}",USENIX_Security/2013,49.jsonl
237,域名解析、去随机化攻击、DNS安全、协议设计、攻击防御,"DNS Resolution, Derandomization Attack, DNS Security, Protocol Design, Attack Defense",如何防御基于SRTT去随机化的域名解析攻击？,How to defend against SRTT-based derandomization attacks in DNS resolution?,采用更强的随机化机制和协议改进可提升DNS解析安全性。,Stronger randomization mechanisms and protocol improvements can enhance DNS resolution security.,Subverting BIND’s SRTT Algorithm Derandomizing NS Selection,"Matthew Thomas, Matthew Van Gundy, Eric Cronin, Kevin Butler, Fabian Monrose","{""en"":""This paper reveals how BIND's SRTT algorithm can be subverted to derandomize NS selection discusses the security implications, and proposes mitigation strategies."",zh:""本文揭示了BIND的SRTT算法可被利用进行NS选择去随机化，分析了安全影响，并提出了缓解措施。""}",USENIX_Security/2013,49.jsonl
238,NS选择、协议漏洞、攻击检测、DNS基础设施、系统加固,"NS Selection, Protocol Vulnerability, Attack Detection, DNS Infrastructure, System Hardening",安全专家如何检测和加固DNS基础设施中的NS选择相关漏洞？,How can security experts detect and harden NS selection-related vulnerabilities in DNS infrastructure?,通过协议分析、日志监控和系统加固可发现并修复NS选择漏洞。,Protocol analysis log monitoring and system hardening can help discover and fix NS selection vulnerabilities.,Subverting BIND’s SRTT Algorithm Derandomizing NS Selection,"Matthew Thomas, Matthew Van Gundy, Eric Cronin, Kevin Butler, Fabian Monrose","{""en"":""This paper reveals how BIND's SRTT algorithm can be subverted to derandomize NS selection discusses the security implications, and proposes mitigation strategies."",zh:""本文揭示了BIND的SRTT算法可被利用进行NS选择去随机化，分析了安全影响，并提出了缓解措施。""}",USENIX_Security/2013,49.jsonl
239,健康信息隐私、社交网络、信息共享、隐私保护、用户控制,"Health Information Privacy, Social Network, Information Sharing, Privacy Protection, User Control",社交网络中健康信息共享存在哪些隐私保护挑战？,What privacy protection challenges exist in health information sharing on social networks?,健康信息敏感性高，社交平台需加强隐私控制和用户授权机制。,Health information is highly sensitive and social platforms need to strengthen privacy controls and user authorization mechanisms.,Privacy Aspects of Health Related Information Sharing in Online Social Networks,"Rui Wang, Shuo Chen, Thomas Ristenpart, Vitaly Shmatikov","{""en"":""This paper analyzes privacy risks in health information sharing on social networks and proposes user-centric privacy protection strategies.""zh:""本文分析了社交网络中健康信息共享的隐私风险，并提出了以用户为中心的隐私保护策略。""}",USENIX_Security/2013,66.jsonl
240,健康数据、隐私风险评估、访问控制、信息泄露防护、平台合规性,"Health Data, Privacy Risk Assessment, Access Control, Information Leakage Prevention, Platform Compliance",如何评估和防护社交网络平台中的健康数据隐私风险？,How to assess and protect health data privacy risks on social network platforms?,通过隐私风险评估、访问控制和合规性建设可有效防护健康数据泄露。,Privacy risk assessment access control and compliance construction can effectively protect against health data leakage.,Privacy Aspects of Health Related Information Sharing in Online Social Networks,"Rui Wang, Shuo Chen, Thomas Ristenpart, Vitaly Shmatikov","{""en"":""This paper analyzes privacy risks in health information sharing on social networks and proposes user-centric privacy protection strategies.""zh:""本文分析了社交网络中健康信息共享的隐私风险，并提出了以用户为中心的隐私保护策略。""}",USENIX_Security/2013,66.jsonl
241,用户授权、隐私偏好、健康信息管理、数据共享策略、平台责任,"User Authorization, Privacy Preference, Health Information Management, Data Sharing Strategy, Platform Responsibility",智能助手如何帮助用户管理社交网络中的健康信息隐私？,How can intelligent assistants help users manage health information privacy in social networks?,智能助手可辅助用户设置隐私偏好、授权管理和数据共享策略，提升健康信息保护水平。,Intelligent assistants can help users set privacy preferences manage authorizations and data sharing strategies improving health information protection.,Privacy Aspects of Health Related Information Sharing in Online Social Networks,"Rui Wang, Shuo Chen, Thomas Ristenpart, Vitaly Shmatikov","{""en"":""This paper analyzes privacy risks in health information sharing on social networks and proposes user-centric privacy protection strategies.""zh:""本文分析了社交网络中健康信息共享的隐私风险，并提出了以用户为中心的隐私保护策略。""}",USENIX_Security/2013,66.jsonl
242,k-匿名性、对称披露、社交网络隐私、匿名通信、群组划分,"k-anonymity, symmetric disclosure, social network privacy, anonymous communication, group partitioning",如何通过对称披露机制提升社交网络中的k-匿名性保护？,How can symmetric disclosure enhance k-anonymity protection in social networks?,对称披露机制通过群组划分和双向消息检索，有效提升社交网络中的匿名性和隐私保护。,"Symmetric disclosure, via group partitioning and bidirectional message retrieval, effectively enhances anonymity and privacy in social networks.",Symmetric Disclosure: a Fresh Look at $k$ -Anonymity,EJ Infeld,"{""en"":""This paper proposes symmetric disclosure as a new communication primitive to improve k-anonymity in online social networks analyzing group size trade-offs and resource overhead and providing practical design insights."";""zh"":""本文提出了对称披露作为提升社交网络k-匿名性的通信机制，分析了群组规模与资源开销的权衡，并给出实际设计建议。""}",USENIX_Security/2014,87.jsonl
243,社交网络、隐私保护、k-匿名性、消息检索、群组通信,"social network, privacy protection, k-anonymity, message retrieval, group communication",社交网络中如何平衡隐私保护与通信效率？,How to balance privacy protection and communication efficiency in social networks?,通过群组通信和对称披露机制，可以在提升隐私保护的同时，兼顾通信效率和系统可扩展性。,Group communication and symmetric disclosure mechanisms can enhance privacy protection while balancing communication efficiency and system scalability.,Symmetric Disclosure: a Fresh Look at $k$ -Anonymity,EJ Infeld,"{""en"":""This paper proposes symmetric disclosure as a new communication primitive to improve k-anonymity in online social networks analyzing group size trade-offs and resource overhead and providing practical design insights."";""zh"":""本文提出了对称披露作为提升社交网络k-匿名性的通信机制，分析了群组规模与资源开销的权衡，并给出实际设计建议。""}",USENIX_Security/2014,87.jsonl
244,匿名通信、社交网络安全、隐私机制、系统设计、资源开销,"anonymous communication, social network security, privacy mechanism, system design, resource overhead",匿名通信机制在社交网络安全中有哪些实际应用？,What are the practical applications of anonymous communication mechanisms in social network security?,匿名通信机制可用于保护用户身份隐私、抵御流量分析攻击，并为系统设计提供安全保障。,"Anonymous communication mechanisms can protect user identity privacy, resist traffic analysis attacks, and provide security guarantees for system design.",Symmetric Disclosure: a Fresh Look at $k$ -Anonymity,EJ Infeld,"{""en"":""This paper proposes symmetric disclosure as a new communication primitive to improve k-anonymity in online social networks analyzing group size trade-offs and resource overhead and providing practical design insights."";""zh"":""本文提出了对称披露作为提升社交网络k-匿名性的通信机制，分析了群组规模与资源开销的权衡，并给出实际设计建议。""}",USENIX_Security/2014,87.jsonl
245,Android安全、恶意软件检测、动态分析、行为特征、自动化测试,"Android security, malware detection, dynamic analysis, behavioral features, automated testing",如何通过动态行为分析提升Android恶意软件检测的准确性？,How can dynamic behavior analysis improve the accuracy of Android malware detection?,动态分析结合自动化测试可全面捕获恶意软件行为特征，提升检测准确性和覆盖率。,"Dynamic analysis combined with automated testing can comprehensively capture malware behavioral features, improving detection accuracy and coverage.",DroidScope: Seamlessly Reconstructing the OS and Dalvik Semantic Views for Dynamic Android Malware Analysis,"Xuxian Jiang, Yajin Zhou, Zhi Wang, Zhenyu Wu, Peng Ning","{""en"":""This paper presents DroidScopea dynamic analysis platform for Android malware enabling comprehensive behavioral feature extraction and accurate detection."";""zh"":""本文提出了DroidScope动态分析平台，实现了对Android恶意软件的全面行为特征提取与准确检测。""}",USENIX_Security/2018,
246,Android平台、系统视图重建、恶意行为识别、Dalvik虚拟机、动态监控,"Android platform, system view reconstruction, malicious behavior identification, Dalvik VM, dynamic monitoring",Android平台如何实现系统级与应用级的动态监控？,How does the Android platform achieve dynamic monitoring at both system and application levels?,通过系统视图与Dalvik虚拟机语义重建，可实现对恶意行为的多层次动态监控和识别。,System view and Dalvik VM semantic reconstruction enable multi-level dynamic monitoring and identification of malicious behaviors.,DroidScope: Seamlessly Reconstructing the OS and Dalvik Semantic Views for Dynamic Android Malware Analysis,"Xuxian Jiang, Yajin Zhou, Zhi Wang, Zhenyu Wu, Peng Ning","{""en"":""This paper presents DroidScopea dynamic analysis platform for Android malware enabling comprehensive behavioral feature extraction and accurate detection."";""zh"":""本文提出了DroidScope动态分析平台，实现了对Android恶意软件的全面行为特征提取与准确检测。""}",USENIX_Security/2018,
247,恶意软件分析、自动化工具、行为提取、Android安全、系统安全性评估,"malware analysis, automated tool, behavior extraction, Android security, system security assessment",自动化恶意软件分析工具在Android安全性评估中有哪些作用？,What roles do automated malware analysis tools play in Android security assessment?,自动化分析工具可提升恶意软件检测效率，支持大规模系统安全性评估和威胁情报收集。,"Automated analysis tools can improve malware detection efficiency, support large-scale system security assessment, and threat intelligence collection.",DroidScope: Seamlessly Reconstructing the OS and Dalvik Semantic Views for Dynamic Android Malware Analysis,"Xuxian Jiang, Yajin Zhou, Zhi Wang, Zhenyu Wu, Peng Ning","{""en"":""This paper presents DroidScopea dynamic analysis platform for Android malware enabling comprehensive behavioral feature extraction and accurate detection."";""zh"":""本文提出了DroidScope动态分析平台，实现了对Android恶意软件的全面行为特征提取与准确检测。""}",USENIX_Security/2018,
247,取证分析、数据压缩、依赖关系保持、日志精简、可扩展性,"forensic analysis, data compaction, dependence preservation, log reduction, scalability",如何通过依赖关系保持的数据压缩提升大规模取证分析的效率？,How can dependence-preserving data compaction improve the efficiency of large-scale forensic analysis?,依赖关系保持的数据压缩技术可大幅减少日志体积，提升取证分析的速度和可扩展性。,Dependence-preserving data compaction techniques can significantly reduce log size and improve the speed and scalability of forensic analysis.,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,"Md Nahid Hossain, Junao Wang, R. Sekar, Scott D. Stoller","{""en"":""This paper proposes dependence-preserving data compaction techniques for scalable forensic analysis achieving significant log reduction while preserving analysis accuracy."";""zh"":""本文提出了依赖关系保持的数据压缩技术，实现了大规模取证分析中日志的高效精简，并保证分析结果的准确性。""}",USENIX_Security/2018,41.jsonl
248,系统审计日志、事件归约、依赖图、攻击溯源、影响分析,"system audit log, event reduction, dependence graph, attack backtracking, impact analysis",系统审计日志的事件归约如何影响攻击溯源与影响分析的准确性？,How does event reduction in system audit logs affect the accuracy of attack backtracking and impact analysis?,事件归约需在保证依赖关系的前提下进行，才能确保攻击溯源和影响分析的准确性。,Event reduction must preserve dependencies to ensure the accuracy of attack backtracking and impact analysis.,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,"Md Nahid Hossain, Junao Wang, R. Sekar, Scott D. Stoller","{""en"":""This paper proposes dependence-preserving data compaction techniques for scalable forensic analysis achieving significant log reduction while preserving analysis accuracy."";""zh"":""本文提出了依赖关系保持的数据压缩技术，实现了大规模取证分析中日志的高效精简，并保证分析结果的准确性。""}",USENIX_Security/2018,41.jsonl
249,依赖图优化、内存表示、图数据库、性能提升、数据可视化,"dependence graph optimization, memory representation, graph database, performance improvement, data visualization",依赖图优化与紧凑内存表示在取证分析系统中有哪些优势？,What are the advantages of dependence graph optimization and compact memory representation in forensic analysis systems?,依赖图优化和紧凑内存表示可提升大规模数据分析的性能，降低存储和查询开销。,Dependence graph optimization and compact memory representation can improve the performance of large-scale data analysis and reduce storage and query overhead.,Dependence-Preserving Data Compaction for Scalable Forensic Analysis,"Md Nahid Hossain, Junao Wang, R. Sekar, Scott D. Stoller","{""en"":""This paper proposes dependence-preserving data compaction techniques for scalable forensic analysis achieving significant log reduction while preserving analysis accuracy."";""zh"":""本文提出了依赖关系保持的数据压缩技术，实现了大规模取证分析中日志的高效精简，并保证分析结果的准确性。""}",USENIX_Security/2018,41.jsonl
250,网络枢纽、渗透测试竞赛、攻防对抗,"network pivoting,penetration testing competition,attack-defense",什么是网络枢纽在渗透测试竞赛中的作用？,What is the role of network pivoting in penetration testing competitions?,网络枢纽（pivoting）是渗透测试中通过已攻陷主机进一步渗透其他主机的关键技能，真实环境和高水平竞赛中常见,"Network pivoting is a key skill in penetration testing, allowing attackers to move laterally from a compromised host to others, commonly seen in real environments and advanced competitions",King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,"Kevin Bock, George Hughey, Dave Levin","{""en"": ""This paper introduces King of the Hill (KotH), a cybersecurity competition designed to teach penetration testing by emphasizing network pivoting, custom implant development, and advanced preparation. The competition simulates real-world scenarios, requiring both offensive and defensive skills, and provides students with hands-on experience in managing and securing complex network environments."";""zh"": ""本文提出了King of the Hill（KotH）网络安全竞赛，通过强调网络枢纽、定制植入开发和提前准备，帮助学生学习渗透测试。该竞赛模拟真实场景，要求参赛者具备攻防兼备的能力，并为学生提供了管理和防护复杂网络环境的实践经验。""}",USENIX_Security/2018,141.jsonl
251,枢纽攻击、植入开发、网络拓扑、攻防策略、服务防护,"pivoting attack, implant development, network topology, attack-defense strategy, service protection",枢纽攻击与植入开发在网络安全竞赛中的作用是什么？,What roles do pivoting attacks and implant development play in cybersecurity competitions?,枢纽攻击和植入开发训练学生多维度攻防思维，提升复杂网络环境下的攻防能力。,Pivoting attacks and implant development train students' multidimensional attack-defense thinking and enhance their capabilities in complex network environments.,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,"Kevin Bock, George Hughey, Dave Levin","{""en"": ""This paper introduces King of the Hill (KotH), a cybersecurity competition designed to teach penetration testing by emphasizing network pivoting, custom implant development, and advanced preparation. The competition simulates real-world scenarios, requiring both offensive and defensive skills, and provides students with hands-on experience in managing and securing complex network environments."";""zh"": ""本文提出了King of the Hill（KotH）网络安全竞赛，通过强调网络枢纽、定制植入开发和提前准备，帮助学生学习渗透测试。该竞赛模拟真实场景，要求参赛者具备攻防兼备的能力，并为学生提供了管理和防护复杂网络环境的实践经验。""}",USENIX_Security/2018,141.jsonl
252,攻防演练、网络服务、漏洞利用、团队协作、实战训练,"attack-defense exercise, network service, vulnerability exploitation, teamwork, practical training",攻防演练型竞赛如何促进学生团队协作与漏洞利用能力提升？,How do attack-defense exercises in competitions promote teamwork and vulnerability exploitation skills among students?,攻防演练型竞赛通过团队协作和漏洞利用训练，提升学生的实战能力和安全意识。,Attack-defense exercises in competitions improve students' practical skills and security awareness through teamwork and vulnerability exploitation training.,King of the Hill: A Novel Cybersecurity Competition for Teaching Penetration Testing,"Kevin Bock, George Hughey, Dave Levin","{""en"": ""This paper introduces King of the Hill (KotH), a cybersecurity competition designed to teach penetration testing by emphasizing network pivoting, custom implant development, and advanced preparation. The competition simulates real-world scenarios, requiring both offensive and defensive skills, and provides students with hands-on experience in managing and securing complex network environments."";""zh"": ""本文提出了King of the Hill（KotH）网络安全竞赛，通过强调网络枢纽、定制植入开发和提前准备，帮助学生学习渗透测试。该竞赛模拟真实场景，要求参赛者具备攻防兼备的能力，并为学生提供了管理和防护复杂网络环境的实践经验。""}",USENIX_Security/2018,141.jsonl
253,传感器访问控制、委托图、协同程序、Android安全、权限管理,"sensor access control, delegation graph, cooperating programs, Android security, permission management",如何通过委托图机制提升Android系统中协同程序的传感器访问安全？,How can delegation graph mechanisms enhance sensor access security for cooperating programs in Android systems?,委托图机制可追踪输入事件与程序间的协作关系，实现细粒度的传感器访问授权，提升系统安全性。,"Delegation graph mechanisms can track input events and cooperation among programs, enabling fine-grained sensor access authorization and enhancing system security.",EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,"Giuseppe Petracca, Yuqiong Sun, Ahmad-Atamli Reineh, Patrick McDaniel, Jens Grossklags, Trent Jaeger","{""en"":""This paper proposes EnTrust an authorization system using delegation graphs to regulate sensor access by cooperating programs in Android achieving higher security and usability."";""zh"":""本文提出了EnTrust授权系统，通过委托图机制调控Android协同程序的传感器访问，实现了更高的安全性与可用性。""}",USENIX_Security/2019,70.jsonl
254,权限委托、输入事件追踪、用户授权、系统服务、攻击防护,"permission delegation, input event tracking, user authorization, system service, attack prevention",Android系统中，如何防止恶意程序通过权限委托滥用系统服务访问敏感传感器？,How can Android systems prevent malicious programs from abusing system services to access sensitive sensors via permission delegation?,通过追踪输入事件与权限委托路径，系统可实现基于上下文的用户授权，有效防止权限滥用和攻击。,"By tracking input events and permission delegation paths, the system can enable context-based user authorization, effectively preventing permission abuse and attacks.",EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,"Giuseppe Petracca, Yuqiong Sun, Ahmad-Atamli Reineh, Patrick McDaniel, Jens Grossklags, Trent Jaeger","{""en"":""This paper proposes EnTrust an authorization system using delegation graphs to regulate sensor access by cooperating programs in Android achieving higher security and usability."";""zh"":""本文提出了EnTrust授权系统，通过委托图机制调控Android协同程序的传感器访问，实现了更高的安全性与可用性。""}",USENIX_Security/2019,70.jsonl
255,协同程序安全、传感器操作授权、用户决策、系统兼容性、性能评估,"cooperating program security, sensor operation authorization, user decision, system compatibility, performance evaluation",EnTrust系统在提升Android传感器安全的同时，如何兼顾用户体验与系统性能？,How does the EnTrust system balance user experience and system performance while enhancing sensor security in Android?,EnTrust通过缓存授权路径和高效事件调度，减少用户授权负担，兼顾安全性、兼容性与低性能开销。,"EnTrust reduces user authorization burden by caching delegation paths and efficient event scheduling, balancing security, compatibility, and low performance overhead.",EnTrust: Regulating Sensor Access by Cooperating Programs via Delegation Graphs,"Giuseppe Petracca, Yuqiong Sun, Ahmad-Atamli Reineh, Patrick McDaniel, Jens Grossklags, Trent Jaeger","{""en"":""This paper proposes EnTrust an authorization system using delegation graphs to regulate sensor access by cooperating programs in Android achieving higher security and usability."";""zh"":""本文提出了EnTrust授权系统，通过委托图机制调控Android协同程序的传感器访问，实现了更高的安全性与可用性。""}",USENIX_Security/2019,70.jsonl
256,模型提取攻击、神经网络安全、准确性与保真度、黑盒攻击、权重恢复,"model extraction attack, neural network security, accuracy and fidelity, black-box attack, weight recovery",如何防御针对神经网络的高准确性与高保真度模型提取攻击？,How to defend against high-accuracy and high-fidelity model extraction attacks on neural networks?,模型提取攻击可通过黑盒查询恢复神经网络结构与权重，威胁模型知识产权与安全。,"Model extraction attacks can recover neural network structure and weights via black-box queries, threatening model intellectual property and security.",High Accuracy and High Fidelity Extraction of Neural Networks,"Matthew Jagielski, Nicholas Carlini, David Berthelot, Alex Kurakin, Nicolas Papernot","{""en"":""This paper systematically studies model extraction attacks on neural networks proposing new attacks that achieve high accuracy and fidelity and analyzing their limitations and defenses."";""zh"":""本文系统研究了神经网络的模型提取攻击，提出了高准确性与高保真度的新型攻击，并分析了其局限性与防御方法。""}",USENIX_Security/2020,65.jsonl
257,知识蒸馏、半监督学习、主动学习、迁移攻击、查询效率,"knowledge distillation, semi-supervised learning, active learning, transfer attack, query efficiency",半监督与主动学习如何提升模型提取攻击的效率与效果？,How do semi-supervised and active learning improve the efficiency and effectiveness of model extraction attacks?,结合知识蒸馏与半监督学习可大幅提升模型提取攻击的查询效率和迁移攻击能力。,Combining knowledge distillation and semi-supervised learning can greatly improve the query efficiency and transferability of model extraction attacks.,High Accuracy and High Fidelity Extraction of Neural Networks,"Matthew Jagielski, Nicholas Carlini, David Berthelot, Alex Kurakin, Nicolas Papernot","{""en"":""This paper systematically studies model extraction attacks on neural networks proposing new attacks that achieve high accuracy and fidelity and analyzing their limitations and defenses."";""zh"":""本文系统研究了神经网络的模型提取攻击，提出了高准确性与高保真度的新型攻击，并分析了其局限性与防御方法。""}",USENIX_Security/2020,65.jsonl
258,功能等价提取、ReLU网络、权重恢复、攻击复杂度、神经网络结构,"functionally equivalent extraction, ReLU network, weight recovery, attack complexity, neural network structure",功能等价提取在神经网络安全中有哪些挑战与意义？,What are the challenges and significance of functionally equivalent extraction in neural network security?,功能等价提取可实现对神经网络的完全复制，但在深层网络和复杂结构下存在理论与实践难题。,"Functionally equivalent extraction enables complete replication of neural networks, but faces theoretical and practical challenges in deep and complex structures.",High Accuracy and High Fidelity Extraction of Neural Networks,"Matthew Jagielski, Nicholas Carlini, David Berthelot, Alex Kurakin, Nicolas Papernot","{""en"":""This paper systematically studies model extraction attacks on neural networks proposing new attacks that achieve high accuracy and fidelity and analyzing their limitations and defenses."";""zh"":""本文系统研究了神经网络的模型提取攻击，提出了高准确性与高保真度的新型攻击，并分析了其局限性与防御方法。""}",USENIX_Security/2020,65.jsonl
259,自动驾驶安全、物理不变量、传感器攻击、异常检测、鲁棒性,"autonomous driving security, physical invariants, sensor attack, anomaly detection, robustness",如何利用物理不变量提升自动驾驶系统对传感器攻击的防御能力？,How can physical invariants enhance the defense of autonomous driving systems against sensor attacks?,基于物理不变量的检测机制可有效识别并防御针对自动驾驶传感器的多种攻击。,Detection mechanisms based on physical invariants can effectively identify and defend against various sensor attacks on autonomous driving systems.,SAVIOR: Securing Autonomous Vehicles with Robust Physical Invariants,"Raul Quinonez, Jairo Giraldo, Luis Salazar, Erick Bauman, Alvaro Cardenas, Zhiqiang Lin","{""en"":""This paper proposes SAVIOR a framework using robust physical invariants to secure autonomous vehicles against sensor attacks validated on real platforms."";""zh"":""本文提出了SAVIOR框架，利用物理不变量提升自动驾驶系统对传感器攻击的防御能力，并在真实平台上验证。""}",USENIX_Security/2020,111.jsonl
260,GPS欺骗、跨模态攻击、物理层安全、无人系统、鲁棒控制,"GPS spoofing, cross-modal attack, physical-layer security, unmanned system, robust control",GPS欺骗与跨模态攻击对无人系统安全有哪些威胁及防护措施？,What are the threats and countermeasures of GPS spoofing and cross-modal attacks to unmanned system security?,GPS欺骗和跨模态攻击可导致无人系统偏离预定轨迹，需结合物理层安全与鲁棒控制提升防护。,"GPS spoofing and cross-modal attacks can cause unmanned systems to deviate from intended paths, requiring physical-layer security and robust control for defense.",SAVIOR: Securing Autonomous Vehicles with Robust Physical Invariants,"Raul Quinonez, Jairo Giraldo, Luis Salazar, Erick Bauman, Alvaro Cardenas, Zhiqiang Lin","{""en"":""This paper proposes SAVIOR a framework using robust physical invariants to secure autonomous vehicles against sensor attacks validated on real platforms."";""zh"":""本文提出了SAVIOR框架，利用物理不变量提升自动驾驶系统对传感器攻击的防御能力，并在真实平台上验证。""}",USENIX_Security/2020,111.jsonl
261,异常检测算法、非线性系统、物理建模、鲁棒性评估、自动驾驶平台,"anomaly detection algorithm, nonlinear system, physical modeling, robustness evaluation, autonomous driving platform",非线性物理建模与异常检测算法如何提升自动驾驶平台的安全性？,How do nonlinear physical modeling and anomaly detection algorithms improve the security of autonomous driving platforms?,非线性物理建模结合CUSUM等异常检测算法，可提升自动驾驶平台对复杂攻击的鲁棒性和检测能力。,Nonlinear physical modeling combined with anomaly detection algorithms such as CUSUM can enhance the robustness and detection capability of autonomous driving platforms against complex attacks.,SAVIOR: Securing Autonomous Vehicles with Robust Physical Invariants,"Raul Quinonez, Jairo Giraldo, Luis Salazar, Erick Bauman, Alvaro Cardenas, Zhiqiang Lin","{""en"":""This paper proposes SAVIOR a framework using robust physical invariants to secure autonomous vehicles against sensor attacks validated on real platforms."";""zh"":""本文提出了SAVIOR框架，利用物理不变量提升自动驾驶系统对传感器攻击的防御能力，并在真实平台上验证。""}",USENIX_Security/2020,111.jsonl
263,Iago攻击、遗留代码、可信执行环境、系统调用、漏洞检测,"Iago attack, legacy code, trusted execution environment, system call, vulnerability detection",如何在可信执行环境中系统性检测和防御Iago攻击？,How can Iago attacks be systematically detected and defended in trusted execution environments?,Iago攻击源于遗留代码对系统调用返回值的过度信任，可信执行环境需重点关注接口最小化与返回值校验。,Iago attacks stem from legacy code's over-trust in system call return values; trusted execution environments should focus on interface minimization and return value validation.,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia a tool for systematically detecting Iago vulnerabilities in legacy code running in TEEs revealing widespread risks and providing practical mitigation strategies."";""zh"":""本文提出了Emilia工具，系统性检测TEE环境下遗留代码的Iago漏洞，揭示了广泛风险并给出实际缓解建议。""}",USENIX_Security/2021,144.jsonl
264,Iago攻击、系统调用接口、攻击面、返回值校验、接口收缩,"Iago attack, syscall interface, attack surface, return value validation, interface minimization",安全专家在TEE迁移时应如何评估和收缩系统调用攻击面？,How should security experts assess and minimize the syscall attack surface during TEE migration?,常见系统调用如read、epoll_wait等易受Iago攻击，建议重点关注接口收缩与返回值校验策略。,Common syscalls like read and epoll_wait are prone to Iago attacks; focus should be on interface minimization and return value validation strategies.,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia a tool for systematically detecting Iago vulnerabilities in legacy code running in TEEs revealing widespread risks and providing practical mitigation strategies."";""zh"":""本文提出了Emilia工具，系统性检测TEE环境下遗留代码的Iago漏洞，揭示了广泛风险并给出实际缓解建议。""}",USENIX_Security/2021,144.jsonl
265,Iago漏洞成因、系统调用信任、实际案例、漏洞防护、学生理解,"Causes of Iago vulnerability, syscall trust, real-world cases, vulnerability mitigation, student understanding",安全专业学生如何理解Iago漏洞的成因及其在实际系统中的表现？,How can security students understand the causes and manifestations of Iago vulnerabilities in real systems?,Iago漏洞源于对操作系统返回值的过度信任，实际案例有助于理解成因与防护要点。,Iago vulnerabilities arise from over-trust in OS return values; real-world cases help understand causes and mitigation.,Emilia: Catching Iago in Legacy Code,"Rongzhen Cui, Lianying Zhao, David Lie","{""en"":""This paper presents Emilia a tool for systematically detecting Iago vulnerabilities in legacy code running in TEEs revealing widespread risks and providing practical mitigation strategies."";""zh"":""本文提出了Emilia工具，系统性检测TEE环境下遗留代码的Iago漏洞，揭示了广泛风险并给出实际缓解建议。""}",USENIX_Security/2021,144.jsonl
266,云安全、域名验证、SSL证书、DNS陈旧记录、IP地址重用攻击,"Cloud security, domain validation, SSL certificate, stale DNS record, IP address reuse attack",云服务环境下如何防范因IP重用和DNS陈旧记录导致的域名接管风险？,How can domain takeover risks caused by IP reuse and stale DNS records be prevented in cloud service environments?,云端弹性资源与自动化证书机制易引发IP重用攻击，需及时清理DNS记录并改进认证机制。,Elastic cloud resources and automated certificate mechanisms can lead to IP reuse attacks; timely DNS cleanup and improved authentication are needed.,Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposing new authentication methods and best practices."";""zh"":""本文揭示了云IP重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践。""}",USENIX_Security/2021,183.jsonl
267,IP重用、DNS安全、云服务迁移、证书滥用、域名接管,"IP address reuse, DNS security, cloud service migration, certificate abuse, domain takeover",在云服务迁移过程中，如何防止因IP重用导致的证书滥用与域名接管？,How can certificate abuse and domain takeover caused by IP address reuse be prevented during cloud service migration?,及时清理DNS记录、延迟IP释放及改进证书验证机制可有效防止域名接管与证书滥用。,"Timely DNS cleanup, delayed IP release, and improved certificate validation can prevent domain takeover and certificate abuse.",Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposing new authentication methods and best practices."";""zh"":""本文揭示了云IP重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践。""}",USENIX_Security/2021,183.jsonl
268,自动化证书管理、ACME协议、DNS运维、攻击窗口、最佳实践,"Automated certificate management, ACME protocol, cloud DNS operation, attack window, best practices",自动化证书管理环境下，如何缩短云服务域名被攻击的窗口期？,How can the attack window for cloud service domain names be minimized in automated certificate management environments?,采用证书透明性、预签名验证和合理DNS运维可缩短攻击窗口，提升云服务安全性。,"Certificate transparency, pre-signature validation, and proper DNS operation can minimize the attack window and enhance cloud service security.",Cloud Strife: Mitigating the Security Risks of Domain-Validated Certificates,"Kevin Borgolte, Tobias Fiebig, Shuang Hao, Christopher Kruegel, Giovanni Vigna","{""en"":""This paper reveals large-scale security risks in domain-validated SSL certificates caused by cloud IP address reuse and stale DNS records proposing new authentication methods and best practices."";""zh"":""本文揭示了云IP重用与DNS陈旧记录导致的域名验证型SSL证书大规模安全风险，提出了新的认证方法和最佳实践。""}",USENIX_Security/2021,183.jsonl
269,自动化补丁、开源软件安全、二进制修复、变体分析、移动应用防护,"Automated patching, open-source software security, binary repair, variability analysis, mobile app protection",如何实现对移动应用中开源组件的自动化二进制级安全补丁？,How can automated binary-level security patching be achieved for open-source components in mobile applications?,源代码与二进制匹配及变体分析可自动生成并注入补丁，提升移动应用安全性。,"Source-to-binary matching and variability analysis enable automated patch generation and injection, enhancing mobile app security.",Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""This paper presents OSSPATCHER a system for automated binary patching of vulnerable open-source software in mobile apps demonstrating high precision and negligible overhead."";""zh"":""本文提出了OSSPATCHER系统，实现了移动应用中开源组件的自动化二进制补丁，具有高精度和低开销。""}",USENIX_Security/2021,196.jsonl
270,变体感知分析、函数级修复、Android安全、配置推断、补丁注入,"Variability-aware analysis, function-level repair, Android security, configuration inference, patch injection",在Android应用安全加固中，如何应对开源组件多变体和无符号二进制的补丁难题？,How to address patching challenges for multi-variant and stripped binaries of open-source components in Android security?,变体感知分析与配置推断可定位并修复无符号二进制中的漏洞函数，实现高效安全加固。,"Variability-aware analysis and configuration inference enable efficient patching of stripped binaries, improving Android app security.",Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""The paper details variability-aware function matching and patch injection for Android appssolving key challenges in binary security patching."";""zh"":""论文详细介绍了面向Android应用的变体感知函数匹配与补丁注入，解决了二进制安全补丁的关键难题。""}",USENIX_Security/2021,196.jsonl
271,二进制补丁评估、开源漏洞检测、性能与正确性验证、移动安全、自动回滚机制,"Binary patch evaluation, open-source vulnerability detection, performance and correctness verification, mobile security, auto-rollback mechanism",如何评估自动化二进制补丁系统在移动应用中的安全性与性能影响？,How to evaluate the security and performance impact of automated binary patching systems in mobile applications?,大规模测试与自动回滚机制可验证补丁系统的安全性、正确性及低开销。,"Large-scale testing and auto-rollback mechanisms verify the security, correctness, and low overhead of patching systems.",Automating Patching of Vulnerable Open-Source Software Versions in Application Binaries,"Ruian Duan, Ashish Bijlani, Yang Ji, Omar Alrawi, Yiyuan Xiong, Moses Ike, Brendan Saltaformaggio, Wenke Lee","{""en"":""The paper evaluates OSSPATCHER on 1000 apps showing effective vulnerability mitigation with negligible overhead and robust rollback support."";""zh"":""论文在1000个应用上评估了OSSPATCHER，显示其能有效缓解漏洞，且开销极低并支持自动回滚。""}",USENIX_Security/2021,196.jsonl
272,模糊标签私有集合求交、隐私保护生物识别搜索、子样本加密、同态加密、阈值匹配协议,"Fuzzy labeled private set intersection, privacy-preserving biometric search, subsample encryption, homomorphic encryption, threshold matching protocol",如何实现高效的隐私保护生物识别实时搜索？,How can efficient privacy-preserving real-time biometric search be achieved?,模糊标签私有集合求交协议结合子样本加密与同态加密，实现高效且可扩展的生物识别隐私搜索。,FLPSI protocol combines subsample encryption and homomorphic encryption to enable efficient and scalable privacy-preserving biometric search.,Fuzzy Labeled Private Set Intersection with Applications to Private Real-Time Biometric Search,"Erkam Uzun, Simon P. Chung, Vladimir Kolesnikov, Alexandra Boldyreva, Wenke Lee","{""en"":""This paper proposes FLPSI a protocol for privacy-preserving biometric search achieving sublinear communication and high accuracy in large-scale real-time scenarios."";""zh"":""本文提出了FLPSI协议，实现了大规模实时场景下高效、低通信量的隐私保护生物识别搜索。""}",USENIX_Security/2021,211.jsonl
273,生物识别隐私、阈值匹配、同态加密、子样本安全、协议可扩展性,"Biometric privacy, threshold matching, homomorphic encryption, subsample security, protocol scalability",在大规模数据库中，如何保障生物识别隐私搜索的可扩展性与安全性？,How to ensure scalability and security of biometric privacy search in large-scale databases?,阈值匹配与同态加密结合可实现大规模数据库下的高效隐私搜索，兼顾安全性与可扩展性。,Threshold matching and homomorphic encryption enable efficient and secure privacy search in large-scale databases.,Fuzzy Labeled Private Set Intersection with Applications to Private Real-Time Biometric Search,"Erkam Uzun, Simon P. Chung, Vladimir Kolesnikov, Alexandra Boldyreva, Wenke Lee","{""en"":""This paper proposes FLPSI a protocol for privacy-preserving biometric search achieving sublinear communication and high accuracy in large-scale real-time scenarios."";""zh"":""本文提出了FLPSI协议，实现了大规模实时场景下高效、低通信量的隐私保护生物识别搜索。""}",USENIX_Security/2021,211.jsonl
274,隐私保护协议、模糊匹配、同态加密优化、生物特征安全、实际应用评估,"Privacy-preserving protocol, fuzzy matching, homomorphic encryption optimization, biometric security, practical evaluation",如何评估模糊标签私有集合求交协议在实际生物识别应用中的效果？,How to evaluate the effectiveness of FLPSI protocol in practical biometric applications?,通过实际数据集和对比实验，FLPSI协议在通信量、准确率和扩展性方面均优于现有方案。,"FLPSI outperforms existing solutions in communication, accuracy, and scalability on real datasets and comparative experiments.",Fuzzy Labeled Private Set Intersection with Applications to Private Real-Time Biometric Search,"Erkam Uzun, Simon P. Chung, Vladimir Kolesnikov, Alexandra Boldyreva, Wenke Lee","{""en"":""This paper proposes FLPSI a protocol for privacy-preserving biometric search achieving sublinear communication and high accuracy in large-scale real-time scenarios."";""zh"":""本文提出了FLPSI协议，实现了大规模实时场景下高效、低通信量的隐私保护生物识别搜索。""}",USENIX_Security/2021,211.jsonl
275,动态可搜索加密、最优检索、删除操作、前向与后向隐私、加密数据库,"dynamic searchable encryption, optimal search, deletion operation, forward and backward privacy, encrypted database",如何在支持删除操作的动态可搜索加密系统中实现最优检索效率？,How can optimal search efficiency be achieved in dynamic searchable encryption systems supporting deletions?,动态可搜索加密系统需在支持高效删除的同时，实现检索效率与隐私保护的平衡。,Dynamic searchable encryption systems must balance efficient deletions with search efficiency and privacy protection.,Dynamic Searchable Encryption with Optimal Search in the Presence of Deletions,"Javad Ghareh Chamani, Dimitrios Papadopoulos, Mohammadamin Karbasforushan, Ioannis Demertzis","{""en"":""This paper proposes two dynamic searchable encryption schemes (OSSE and LLSE) that achieve optimal or near-optimal search efficiency even with deletions while ensuring forward and backward privacy. Experimental results show significant performance improvements over previous works."";""zh"":""本文提出了两种动态可搜索加密方案（OSSE和LLSE），在支持删除操作的同时实现了最优或近最优的检索效率，并保证前向与后向隐私。实验结果显示其性能显著优于现有方案。""}",USENIX_Security/2022,44.jsonl
276,可扩展性、加密索引、检索延迟、存储优化、实验评估,"scalability, encrypted index, retrieval latency, storage optimization, experimental evaluation",在大规模加密数据库中，如何优化索引结构以提升检索性能和可扩展性？,How can index structures be optimized to improve retrieval performance and scalability in large-scale encrypted databases?,加密索引结构的设计直接影响系统的检索延迟与存储效率，需兼顾安全性与可扩展性。,"The design of encrypted index structures directly affects retrieval latency and storage efficiency, requiring a balance between security and scalability.",Dynamic Searchable Encryption with Optimal Search in the Presence of Deletions,"Javad Ghareh Chamani, Dimitrios Papadopoulos, Mohammadamin Karbasforushan, Ioannis Demertzis","{""en"":""This paper proposes two dynamic searchable encryption schemes (OSSE and LLSE) that achieve optimal or near-optimal search efficiency even with deletions while ensuring forward and backward privacy. Experimental results show significant performance improvements over previous works."";""zh"":""本文提出了两种动态可搜索加密方案（OSSE和LLSE），在支持删除操作的同时实现了最优或近最优的检索效率，并保证前向与后向隐私。实验结果显示其性能显著优于现有方案。""}",USENIX_Security/2022,44.jsonl
277,前向隐私、后向隐私、信息泄露、安全性分析、协议设计,"forward privacy, backward privacy, information leakage, security analysis, protocol design",动态可搜索加密协议如何实现前向与后向隐私并降低信息泄露风险？,How do dynamic searchable encryption protocols achieve forward and backward privacy while reducing information leakage risks?,前向与后向隐私机制是提升加密检索系统安全性的关键，需结合协议设计与安全性分析。,"Forward and backward privacy mechanisms are key to enhancing the security of encrypted search systems, requiring combined protocol design and security analysis.",Dynamic Searchable Encryption with Optimal Search in the Presence of Deletions,"Javad Ghareh Chamani, Dimitrios Papadopoulos, Mohammadamin Karbasforushan, Ioannis Demertzis","{""en"":""This paper proposes two dynamic searchable encryption schemes (OSSE and LLSE) that achieve optimal or near-optimal search efficiency even with deletions while ensuring forward and backward privacy. Experimental results show significant performance improvements over previous works."";""zh"":""本文提出了两种动态可搜索加密方案（OSSE和LLSE），在支持删除操作的同时实现了最优或近最优的检索效率，并保证前向与后向隐私。实验结果显示其性能显著优于现有方案。""}",USENIX_Security/2022,44.jsonl
278,PHP应用去臃肿、静态分析、调用图、功能裁剪、Web安全,"PHP application debloating, static analysis, call graph, feature pruning, web security",如何通过静态分析和调用图实现PHP Web应用的自动化去臃肿？,How can static analysis and call graphs enable automated debloating of PHP web applications?,静态分析结合调用图可识别并裁剪Web应用中未被实际使用的功能模块，提升安全性和性能。,"Static analysis combined with call graphs can identify and prune unused functional modules in web applications, improving security and performance.",Minimalist: Semi-automated Debloating of PHP Web Applications through Static Analysis,"Rasoul Jahanshahi, Babak Amin Azad, Nick Nikiforakis, Manuel Egele","{""en"":""This paper presents Minimalist a semi-automated static analysis tool for debloating PHP web applications reducing code size and vulnerabilities by pruning unused features based on call-graph analysis."";""zh"":""本文提出了Minimalist工具，通过静态分析和调用图实现PHP Web应用的半自动化去臃肿，显著减少代码体积和安全漏洞。""}",USENIX_Security/2023,163.jsonl
279,Web应用臃肿、第三方库依赖、功能可达性、漏洞减少、自动化工具,"web application bloat, third-party library dependency, feature reachability, vulnerability reduction, automation tool",Web应用中如何自动识别和移除因第三方库引入的冗余代码？,How can redundant code introduced by third-party libraries in web applications be automatically identified and removed?,通过分析功能可达性和依赖关系，可自动识别并移除第三方库带来的冗余代码，降低攻击面。,"By analyzing feature reachability and dependencies, redundant code from third-party libraries can be automatically identified and removed, reducing the attack surface.",Minimalist: Semi-automated Debloating of PHP Web Applications through Static Analysis,"Rasoul Jahanshahi, Babak Amin Azad, Nick Nikiforakis, Manuel Egele","{""en"":""Minimalist leverages call-graph and dependency analysis to remove redundant code from third-party libraries in PHP web applications reducing attack surface and improving maintainability."";""zh"":""Minimalist通过调用图和依赖分析，自动移除PHP Web应用中第三方库引入的冗余代码，降低攻击面并提升可维护性。""}",USENIX_Security/2023,163.jsonl
280,静态去臃肿、功能可用性、误删风险、Web应用安全、自动化分析,"static debloating, feature availability, false positive risk, web application security, automated analysis",如何在Web应用去臃肿过程中平衡功能可用性与误删风险？,How to balance feature availability and false positive risk during web application debloating?,自动化去臃肿需兼顾功能完整性与安全性，合理建模可用性以降低误删风险。,"Automated debloating must balance feature completeness and security, modeling availability to reduce false positives.",Minimalist: Semi-automated Debloating of PHP Web Applications through Static Analysis,"Rasoul Jahanshahi, Babak Amin Azad, Nick Nikiforakis, Manuel Egele","{""en"":""The paper discusses how Minimalist balances feature availability and false positive risk in automated web application debloating ensuring security without sacrificing essential functionality."";""zh"":""论文探讨了Minimalist如何在自动化Web应用去臃肿中平衡功能可用性与误删风险，实现安全性与功能性的兼顾。""}",USENIX_Security/2023,163.jsonl
281,物联网安全、上下文攻击检测、深度学习、异常检测、智能家居,"IoT security, contextual attack detection, deep learning, anomaly detection, smart home",如何利用上下文建模和深度学习提升物联网环境下的异常检测能力？,How can context modeling and deep learning enhance anomaly detection in IoT environments?,结合上下文建模与深度学习可有效识别物联网环境中的异常行为和隐蔽攻击。,Combining context modeling and deep learning can effectively identify abnormal behaviors and stealthy attacks in IoT environments.,ARGUS: Context-Based Detection of Stealthy IoT Infiltration Attacks,"Phillip Rieger, Marco Chilese, Reham Mohamed, Markus Miettinen, Hossein Fereidooni, Ahmad-Reza Sadeghi","{""en"":""This paper presents ARGUS a context-based intrusion detection system for IoT leveraging deep learning to detect stealthy contextual attacks in smart environments."";""zh"":""本文提出了ARGUS，一种基于上下文的物联网入侵检测系统，利用深度学习识别智能环境中的隐蔽攻击。""}""",USENIX_Security/2023,275.jsonl
282,IoT入侵检测、自学习系统、异常阈值、智能家居安全、无监督学习,"IoT intrusion detection, self-learning system, anomaly threshold, smart home security, unsupervised learning",自学习型IoT入侵检测系统如何实现高精度与低误报？,How can self-learning IoT intrusion detection systems achieve high accuracy and low false positives?,无监督学习与动态阈值调整可提升IoT入侵检测系统的准确性并降低误报率。,Unsupervised learning and dynamic threshold adjustment can improve the accuracy and reduce false positives in IoT intrusion detection systems.,ARGUS: Context-Based Detection of Stealthy IoT Infiltration Attacks,"Phillip Rieger, Marco Chilese, Reham Mohamed, Markus Miettinen, Hossein Fereidooni, Ahmad-Reza Sadeghi","{""en"":""ARGUS employs unsupervised deep learning and dynamic thresholding to achieve high-precision low-false-positive IoT intrusion detection in real-world smart home scenarios."";""zh"":""ARGUS采用无监督深度学习与动态阈值，实现了真实智能家居场景下高精度、低误报的IoT入侵检测。""}""",USENIX_Security/2023,275.jsonl
283,上下文完整性、IoT控制面、攻击面缩减、智能环境、自动化防御,"contextual integrity, IoT control plane, attack surface reduction, smart environment, automated defense",如何通过上下文完整性建模减少IoT系统的攻击面并提升自动化防御能力？,How can contextual integrity modeling reduce the attack surface and enhance automated defense in IoT systems?,上下文完整性建模有助于识别IoT控制面滥用，缩减攻击面并提升系统自动化防御能力。,"Contextual integrity modeling helps identify IoT control plane abuse, reducing the attack surface and enhancing automated defense capabilities.",ARGUS: Context-Based Detection of Stealthy IoT Infiltration Attacks,"Phillip Rieger, Marco Chilese, Reham Mohamed, Markus Miettinen, Hossein Fereidooni, Ahmad-Reza Sadeghi","{""en"":""The paper demonstrates how contextual integrity modeling in ARGUS reduces the attack surface and enhances automated defense in IoT systems."";""zh"":""论文展示了ARGUS如何通过上下文完整性建模缩减IoT系统攻击面并提升自动化防御能力。""}",USENIX_Security/2023,275.jsonl
284,区块链安全、智能合约漏洞、自动化检测、去中心化应用、攻击防御,"blockchain security, smart contract vulnerability, automated detection, decentralized application, attack defense",如何提升区块链智能合约的自动化漏洞检测与防御能力？,How to enhance automated vulnerability detection and defense for blockchain smart contracts?,自动化检测与防御机制有助于发现并缓解智能合约中的安全漏洞，提升区块链应用的整体安全性。,"Automated detection and defense mechanisms help discover and mitigate security vulnerabilities in smart contracts, improving the overall security of blockchain applications.",CipherH: Automated Detection of Ciphertext Side-channel Vulnerabilities in Cryptographic Implementations ,Author(s) of 104.jsonl,"{""en"":""This paper presents an automated approach for detecting and defending against vulnerabilities in blockchain smart contracts improving the security of decentralized applications."";""zh"":""本文提出了一种区块链智能合约漏洞的自动化检测与防御方法，提升了去中心化应用的安全性。""}",USENIX_Security/2023,104.jsonl
285,智能合约分析、形式化验证、漏洞利用、区块链生态、自动化工具,"smart contract analysis, formal verification, vulnerability exploitation, blockchain ecosystem, automation tool",如何通过形式化验证提升区块链智能合约的安全性？,How can formal verification improve the security of blockchain smart contracts?,形式化验证工具可系统性发现智能合约中的潜在漏洞，降低被攻击风险。,"Formal verification tools can systematically discover potential vulnerabilities in smart contracts, reducing the risk of exploitation.",CipherH: Automated Detection of Ciphertext Side-channel Vulnerabilities in Cryptographic Implementations,Author(s) of 104.jsonl,"{""en"":""The paper demonstrates how formal verification tools can systematically identify vulnerabilities in blockchain smart contracts reducing exploitation risks."";""zh"":""论文展示了形式化验证工具如何系统性发现区块链智能合约漏洞，降低被攻击风险。""}",USENIX_Security/2023,104.jsonl
286,去中心化应用、攻击检测、合约安全性评估、区块链治理、威胁建模,"decentralized application, attack detection, contract security assessment, blockchain governance, threat modeling",如何评估去中心化应用中的合约安全性与攻击面？,How to assess contract security and attack surface in decentralized applications?,合约安全性评估与威胁建模有助于区块链治理和攻击检测能力提升。,Contract security assessment and threat modeling help improve blockchain governance and attack detection capabilities.,CipherH: Automated Detection of Ciphertext Side-channel Vulnerabilities in Cryptographic Implementations ,Author(s) of 104.jsonl,"{""en"":""The paper demonstrates how formal verification tools can systematically identify vulnerabilities in blockchain smart contracts reducing exploitation risks."";""zh"":""论文展示了形式化验证工具如何系统性发现区块链智能合约漏洞，降低被攻击风险。""}",USENIX_Security/2023,104.jsonl
287,恶意软件检测、二进制分析、自动化逆向、威胁情报、系统安全,"malware detection, binary analysis, automated reverse engineering, threat intelligence, system security",如何利用自动化二进制分析提升恶意软件检测能力？,How can automated binary analysis enhance malware detection capabilities?,自动化二进制分析技术可提升恶意软件检测效率和威胁情报收集能力。,Automated binary analysis techniques can improve malware detection efficiency and threat intelligence collection.,No Single Silver Bullet: Measuring the Accuracy of Password Strength Meters,Author(s) of 11.jsonl,"{""en"":""This paper presents automated binary analysis methods for malware detection improving system security and threat intelligence."";""zh"":""本文提出了自动化二进制分析方法用于恶意软件检测，提升了系统安全性与威胁情报能力。""}",USENIX_Security/2023,11.jsonl
288,逆向工程、恶意代码识别、自动化工具、样本聚类、攻击溯源,"reverse engineering, malicious code identification, automation tool, sample clustering, attack attribution",如何通过自动化逆向工程工具实现恶意代码识别与聚类？,How can automated reverse engineering tools enable malicious code identification and clustering?,自动化逆向与聚类技术有助于快速识别和归类恶意代码样本，支持攻击溯源。,"Automated reverse engineering and clustering techniques help quickly identify and classify malicious code samples, supporting attack attribution.",No Single Silver Bullet: Measuring the Accuracy of Password Strength Meters,Author(s) of 11.jsonl,"{""en"":""The paper discusses automated reverse engineering and clustering for efficient identification and attribution of malicious code."";""zh"":""论文探讨了自动化逆向与聚类技术在恶意代码识别与溯源中的应用。""}""",USENIX_Security/2023,11.jsonl
289,系统安全评估、威胁检测、自动化分析、恶意样本溯源、攻防对抗,"system security assessment, threat detection, automated analysis, malware sample attribution, attack-defense confrontation",如何实现大规模系统安全评估与恶意样本溯源？,How to achieve large-scale system security assessment and malware sample attribution?,自动化分析与溯源机制可提升系统安全评估与威胁检测能力。,Automated analysis and attribution mechanisms can enhance system security assessment and threat detection capabilities.,No Single Silver Bullet: Measuring the Accuracy of Password Strength Meters,Author(s) of 11.jsonl,"{""en"":""This work explores automated analysis and attribution for large-scale system security assessment and threat detection."";""zh"":""本文探讨了自动化分析与溯源机制在大规模系统安全评估与威胁检测中的作用。""}""",USENIX_Security/2023,11.jsonl
290,网络流量分析、加密通信检测、隐蔽通道识别、机器学习、网络安全,"network traffic analysis, encrypted communication detection, covert channel identification, machine learning, network security",如何利用机器学习提升加密通信与隐蔽通道的检测能力？,How can machine learning enhance detection of encrypted communications and covert channels?,机器学习结合流量分析可提升加密通信与隐蔽通道的检测准确率。,Machine learning combined with traffic analysis can improve the accuracy of detecting encrypted communications and covert channels.,Security and Privacy Failures in Popular 2FA Apps,Author(s) of 135.jsonl,"{""en"":""This paper presents machine learning-based methods for detecting encrypted communications and covert channels in network traffic."";""zh"":""本文提出了基于机器学习的网络流量加密通信与隐蔽通道检测方法。""}""",USENIX_Security/2023,135.jsonl
291,隐蔽通信、异常检测、网络威胁识别、数据包特征、自动化分析,"covert communication, anomaly detection, network threat identification, packet features, automated analysis",如何通过数据包特征与异常检测识别网络中的隐蔽通信？,How can packet features and anomaly detection identify covert communications in networks?,结合数据包特征与异常检测可有效识别网络中的隐蔽通信与威胁。,Combining packet features and anomaly detection can effectively identify covert communications and threats in networks.,Security and Privacy Failures in Popular 2FA Apps,Author(s) of 135.jsonl,"{""en"":""The paper discusses how packet feature analysis and anomaly detection can identify covert communications and threats in network traffic."";""zh"":""论文探讨了数据包特征与异常检测在网络隐蔽通信与威胁识别中的应用。""}""",USENIX_Security/2023,135.jsonl
292,网络安全监测、加密流量分析、威胁情报、自动化检测、深度学习,"network security monitoring, encrypted traffic analysis, threat intelligence, automated detection, deep learning",如何通过深度学习与自动化检测提升网络安全监测能力？,How can deep learning and automated detection enhance network security monitoring?,深度学习与自动化检测技术可提升加密流量分析与威胁情报能力。,Deep learning and automated detection techniques can enhance encrypted traffic analysis and threat intelligence capabilities.,Security and Privacy Failures in Popular 2FA Apps,Author(s) of 135.jsonl,"{""en"":""This work explores deep learning and automated detection for improved network security monitoring and threat intelligence."";""zh"":""本文探讨了深度学习与自动化检测在网络安全监测与威胁情报中的应用。""}""",USENIX_Security/2023,135.jsonl
