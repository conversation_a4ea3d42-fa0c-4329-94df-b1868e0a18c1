{"text": "# Intel Software Guard Extensions - Introduction and Open Research Challenges  \n\nMatthias Schunter Intel Labs, Portland, OR, USA Intel Collaborative Research Institute for Secure Computing, Darmstadt, Germany <EMAIL>  \n\n# ABSTRACT  \n\nHardware-enhanced security is an important pillar of secure systems in general and software protection in particular. This presentation will survey the recently announced Intel $^\\mathrm{\\textregistered}$ Software Guard Extensions (Intel $^\\mathrm{\\textregistered}$ SGX) as well as innovative usages for building secure systems using securityenhanced hardware.  \n\nIntel SGX is an Intel technology for application developers who are seeking to protect select code and data from disclosure or modification. Intel SGX makes such protections possible through the use of enclaves, which are protected areas of execution. Security critical application code can be put into an enclave by special instructions and is then hardware protected from attacks by other potentially malicious software. An enclave can therefore be shielded against attacks by untrusted application parts, by other applications, and also against attacks by a compromised operating system.  \n\n# Keywords  \n\nhardware-enhanced security, isolation, confidentiality, integrity, privacy, enclaves  \n\n# <PERSON><PERSON> (Dr.- Ing, MBA) is the Chief Technologist of the Intel Collaborative Research Institute for Secure Computing and a Principal Engineer at Intel Labs. His current research focuses on scalable security for IoT infrastructures. He has conducted research in diverse areas such as virtual systems  \n\n![](/tmp/output/243_20250326121703/images/ac2bae23d93610a5f58de046da60e0fd85f6c2999295e308ce9da4b13fca0eac.jpg)  \n\nsecurity, trusted computing, enterprise privacy management, security protocols, and cryptography. Prior to joining Intel, he joined IBM Research - Zurich in 2001 and has lead their research on cloud security and was technical leader of the EU Project TClouds. He holds an MBA from Warwick University, a Doctorate from Saarland University, and a Diploma in Computer Science from Hildesheim University. Dr. Schunter is author or co-author of more than sixty technical papers and twenty patent filings on security and privacy. A full CV can be found at http://www.schunter.org/.  "}