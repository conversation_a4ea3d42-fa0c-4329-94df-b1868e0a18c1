{"text": "# PLAS 2019 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security  \n\nPiotr <PERSON>@cmu.edu Carnegie Mellon University  \n\nNiki <PERSON> <EMAIL> IMDEA Software Institute  \n\n# ABSTRACT  \n\nThe 14th ACM SIGSAC Workshop on Programming Languages and Analysis for Security (PLAS 2019) is co-located with the 25th ACM Conference on Computer and Communications Security (ACM CCS 2019). PLAS provides a unique venue for researchers and practitioners to exchange ideas in programming language and program analysis techniques with the goal of improving the security of software systems.  \n\n# CCS CONCEPTS  \n\n• Security and privacy $\\longrightarrow$ Software and application security.  \n\n# KEYWORDS  \n\nprogramming languages; security  \n\n# 1 INTRODUCTION  \n\nPLAS provides a forum for exploring and evaluating the use of programming language and program analysis techniques for promoting security in the complete range of software systems, from compilers to machine learnt models. The workshop encourages proposals of new, speculative ideas, evaluations of new or known techniques in practical settings, and discussions of emerging threats and problems. We also host position papers that are radical, forward-looking, and lead to lively and discussions influential to the future research at the intersection of programming languages and security.  \n\n# 2 SCOPE  \n\nThe scope of PLAS includes, but is not limited to:  \n\n• Programming language techniques and verification applied to security in other domains (e.g. adversarial learning)   \n• Compiler-based security mechanisms (e.g. security type systems) or runtime-based security mechanisms (e.g. inline reference monitors)   \n• Program analysis techniques for discovering security vulnerabilities   \n• Automated introduction and/or verification of security enforcement mechanisms   \n• Language-based verification of security properties in software, including verification of cryptographic protocols   \n• Specifying and enforcing security policies for information flow and access control   \n• Model-driven approaches to security   \n• Security concerns for Web programming languages   \n• Language design for security in new domains such as cloud computing and IoT   \n• Applications, case studies, and implementations of these techniques  \n\n# 3 WORKSHOP FORMAT  \n\nPLAS 2019 featured five papers spanning theory of information flow; cryptographic APIs; analysis of javascript and firewalls; and security analysis for PDF documents. It also featured an invited talk by Véronique Cortier (LORIA Laboratory): Electronic voting: a journey to verifiability and vote privacy.  \n\n# 4 PROGRAM COMMITTEE  \n\n• Eleanor Birrell (Pomona College, USA)   \n• Fraser Brown (Stanford University, USA)   \n• Stephen Chong (Harvard University, USA)   \n• Nate Foster (Cornell University, USA)   \n• Klaus von Gleissenthall (University of California, San Diego, USA)   \n• Leonidas Lampropoulos (University of Maryland, College Park, USA)   \n• Piotr Mardziel (Carnegie Mellon University, USA, Co-Chair)   \n• Annabelle McIver (Macquarie University, Australia)   \n• Corina Pasareanu (NASA Ames Research Center, USA)   \n• Aseem Rastogi (Microsoft Research, India)   \n• Marco Vassena (CISPA Helmholtz Center for Information Security, Germany)   \n• Niki Vazou (IMDEA Software Institute, Spain, Co-Chair)  \n\n# 5 ACKNOWLEDGMENTS  \n\nWe would like to thank all the authors who has submitted contributions to PLAS, the program committee members and the external reviewer Avinash Sudhodanan (IMDEA Software Institute, Spain), the invited speaker Véronique Cortier, and our sponsor, Carnegie Mellon University’s CyLab Security and Privacy Institute.  "}