{"text": "# Are These Pairing Elements Correct? Automated Verification and Applications  \n\n<PERSON>∗ <PERSON><PERSON><PERSON><PERSON><PERSON>† December 2, 2019  \n\n# Abstract  \n\nUsing a set of pairing product equations (PPEs) to verify the correctness of an untrusted set of pairing elements with respect to another set of trusted elements has numerous cryptographic applications. These include the design of basic and structure-preserving signature schemes, building oblivious transfer schemes from “blind” IBE, finding new verifiable random functions and keeping the IBE/ABE authority “accountable” to the user.  \n\nA natural question to ask is: are all trusted-untrusted pairing element groups in the literature PPE testable? We provide original observations demonstrating that the answer is no, and moreover, it can be non-trivial to determine whether or not there exists a set of PPEs that can verify some pairing elements with respect to others. Many IBE schemes have PPE-testable private keys (with respect to the public parameters), while others, such as those based on dual-system encryption, provably do not.  \n\nTo aid those wishing to use PPE-based element verification in their cryptosystems, we devised rules to systematically search for a set of PPEs that can verify untrusted elements with respect to a set of trusted elements. We prove the correctness of each rule and combine them into a main searching algorithm for which we also prove correctness. We implemented this algorithm in a new software tool, called AutoPPE. Tested on over two dozen case studies, AutoPPE found a set of PPEs (on schemes where they exist) usually in just a matter of seconds. This work represents an important step towards the larger goal of improving the speed and accuracy of pairing-based cryptographic design via computer automation.  \n\n# 1 Introduction  \n\nComputer automation is showing great potential to improve the speed and accuracy of the cryptographic design process. Over the past several years, a host of new software tools, e.g., [7, 6, 5, 15, 18, 16, 12, 13, 11, 17], were made public for handling a variety of cryptographic tasks, including design, proof generation, and proof verification. Automation is particularly compelling for these tasks, which are often both complex and tedious, and where a single error can compromise the entire system.  \n\nMany of these tools focus on the pairing-based algebraic setting, since it is popular both for its efficiency and functionality. In this work, we focus on automating a novel cryptographic design task in this setting, which we call pairing-product equation $(P P E)$ testability. Let $\\mathbb{G}_{1},\\mathbb{G}_{2}$ and $\\mathbb{G}_{T}$ be groups of prime order $p$ . Recall that a pairing is an efficient map $e~:\\mathbb{G}_{1}\\times\\mathbb{G}_{2}\\to\\mathbb{G}_{T}$ , such that for all $g\\in G_{1}$ , $h\\in\\mathbb{G}_{2}$ and $a,b\\in\\mathbb{Z}_{p}$ , it holds that $e(g^{a},h^{b})=e(g,h)^{a b}$ . Following [33], a pairing product equation (PPE) over variables $Z,\\{X_{i}\\}_{i=1}^{m},\\{Y_{i}\\}_{i=1}^{n}$ is an equation of the form  \n\n$$\nZ\\cdot\\prod_{i=1}^{n}e(A_{i},Y_{i})\\cdot\\prod_{i=1}^{m}e(X_{i},B_{i})\\cdot\\prod_{i=1}^{m}\\prod_{j=1}^{n}e(X_{i},Y_{j})^{\\gamma_{i j}}=1,\n$$  \n\nwhere $A_{i},X_{i}\\in\\mathbb{G}_{1},B_{i},Y_{i}\\in\\mathbb{G}_{2},Z\\in\\mathbb{G}_{T},\\gamma_{i j}\\in\\mathbb{Z}_{p}.$ .  \n\nInformally, PPE testability captures the commonplace task of figuring out a method of verifying one set of group elements with respect to another set using the pairing map. This is extremely useful when designing new pairing-based constructions. For instance, the need to verify a signature with respect to a public key and message; or to verify a verifiable random function output and proof with respect to a public key. Let’s see more examples after formalizing this concept more crisply.  \n\nIn a nutshell, our research discovered examples illustrating that deciding whether a given cryptographic scheme supports PPE testability is highly non-trivial. There are natural examples where the answer is yes, provably no and even unknown. Our contributions in this work are: (1) formalizing the concept of PPE testability, (2) developing novel techniques to search for a PPE-based verification procedure (which we will call a PPE testing set), (3) proving the correctness of this searching algorithm, (4) implementing this algorithm as an open source software tool called $A u t o P P E$ , (5) reporting on the performance and accuracy of AutoPPE on over two dozen case studies and (6) documenting provably non-PPE-testable instances.  \n\n# 1.1 Deciding PPE Testability is Non-Trivial  \n\nLet’s explore our objective in more detail. Let a PPE problem instance be a set of pairing parameters, a set of multivariate polynomials $\\mathbf{f}=(f_{1},\\dots,f_{m})$ over variables $u=(u_{1},\\ldots,u_{n})$ in $\\mathbb{Z}_{p}$ , a sequence of pairing group identifiers $\\pmb{\\alpha}=(\\alpha_{1},\\dots,\\alpha_{m})$ , a set Fixed $\\subseteq[1,n]$ and a set Trusted $\\in[1,m]$ . This instance corresponds to a set of group elements of the form $\\mathbf{F}=(g_{\\alpha_{1}}^{f_{1}(\\mathbf{u})},\\dots,g_{\\alpha_{m}}^{f_{m}(\\mathbf{u})})$ , where the variables $u_{i}\\in\\mathbf{u}$ for $i\\in$ Fixed are chosen by the “trusted” source and the polynomials $f_{i}\\in\\mathbf{F}$ for $i\\in$ Trusted are only over these fixed variables.  \n\nThe goal of our work is that given a PPE problem instance, is there an efficient algorithm for deciding the existence of (and if yes, producing) a set of PPEs that will verify that $F_{j}=g_{\\alpha_{j}}^{f_{j}(\\mathbf{u})}$ gfαjj(u), for all j ̸∈ Trusted, for any setting of $\\mathbf{u}$ . Intuitively, if we assume the trusted elements are correctly formed, can we verify that the untrusted ones are correctly formed too, using PPEs? (We will not concern ourselves with the case that the trusted elements are not well formed.) If the answer is yes, then we say that this PPE problem instance is $P P E$ testable. In Section 2, we will provide formal definitions for a PPE problem instance and PPE testability.  \n\nLet’s explore these notions with some examples set in the Type I pairing setting, where $\\mathbb{G}_{1}=\\mathbb{G}_{2}$ . Suppose we have public parameters $(g,g^{a},g^{b})$ and want to verify if a value $T$ is $g^{a b}$ or not. Can we do this with a PPE? Easy: check that $e(g^{a},g^{b})=e(T,g)$ . Here, we’d have $\\mathbf{f}=(f_{1}=1,f_{2}=u_{1},f_{3}=u_{2},f_{4}=u_{1}u_{2})$ ) with $\\alpha=(\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1})$ , $\\mathbf{u}~=~\\left(u_{1},u_{2}\\right)$ , $\\mathsf{F i x e d}=\\{1,2\\}$ and Trusted $=\\{1,2,3\\}$ . Here’s another PPE testable example with some variables not in Fixed. Suppose the public parameters are $(g,g^{a})$ and we want to test the elements $(T_{1},T_{2})=(g^{r},g^{a r})$ . Here, we’d have $\\mathbf{f}=(f_{1}=1,f_{2}=u_{1},f_{3}=u_{2},f_{4}=u_{1}u_{2}$ ) with $\\pmb{\\alpha}=(\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1})$ , ${\\bf u}=(u_{1},u_{2})$ , $\\mathsf{F i x e d}=\\{1\\}$ and Trusted $=\\{1,2\\}$ and the PPE as $e(g^{a},T_{1})=e(T_{2},g)$ .  \n\nNext, let’s see a simple example that is not PPE testable. Suppose we have public parameters $(g,g^{a},g^{b},g^{c})$ and want to verify if a value $T$ is $g^{a b c}$ or not. Here, we’d have $\\mathbf{f}=(f_{1}=1,f_{2}=u_{1},f_{3}=u_{2}$ , $f_{4}=u_{3},f_{5}=$ $u_{1}u_{2}u_{3},$ ) with $\\pmb{\\alpha}=(\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1},\\mathbb{G}_{1})$ , ${\\bf u}=(u_{1},u_{2},u_{3})$ , $\\mathsf{F i x e d}=\\{1,2,3\\}$ and Trusted $=\\{1,2,3,4\\}$ . However, this problem is the Decisional Bilinear Diffie-Hellman (DBDH) problem, so this would not be a PPE testable instance in any group where the DBDH assumption holds.  \n\nThere are many encryption systems where an authority distributes private keys to users, and the user would like to verify that their key was correctly formed (i.e., per the key generation procedure). This comes up in IBE to signature design [22], realizing “blind IBE” to build oblivious transfer [32], and keeping the IBE authority “accountable” to the user [30, 31]. We discuss these applications in more detail soon, but think a moment about how the reader would approach PPE testing for the Waters dual system IBE [46]. Set in a Type I group, the Setup algorithm chooses random generators $g,v,v_{1},v_{2},w,u,h$ and exponents $a_{1},a_{2},b,\\alpha\\in\\mathbb{Z}_{p}$ , sets $\\tau_{1}=v v_{1}^{a_{1}}$ and $\\tau_{2}=v v_{2}^{a_{2}}$ , and then publishes the public parameters as ${\\sf p p}=\\left(g^{b},g^{a_{1}},g^{a_{2}},g^{b a_{1}},g^{b a_{2}},\\tau_{1},\\tau_{2},\\tau_{1}^{b},\\tau_{2}^{b},w,u,h,e(g,g)^{\\alpha a_{1}b}\\right)$ . How would one then verify a private key for identity id of the form $(D_{1},\\dots,D_{7},K,t)$ , where $r_{1},r_{2},z_{1},z_{2},t\\in\\mathbb{Z}_{p}$ , $r=r_{1}+r_{2}$ and  \n\n$$\n\\begin{array}{r l}{D_{1}=g^{\\alpha a_{1}}v^{r}\\quad D_{2}=g^{-\\alpha}v_{1}^{r}g^{z_{1}}\\quad D_{3}=(g^{b})^{-z_{1}}}\\ {D_{4}=v_{2}g^{z_{2}}\\quad D_{5}=(g^{b})^{-z_{2}}\\quad D_{6}=g^{r_{2}b}}\\ {D_{7}=g^{r_{1}}\\quad K=(u^{\\mathrm{id}}w^{t}h)^{r_{1}}}\\end{array}\n$$  \n\nThis is just the IBE scheme and not the HIBE! And lest the reader shrug this off as too complicated to actually be of interest for PPE testability, we counter that there are documented examples, e.g., Abe et al. [1], verifying derivatives of these private keys with PPEs to use as the base of a structure-preserving signature scheme. Indeed, when looking at IBEs of this complexity and even more advanced attribute-based encryption schemes, etc., we hope to persuade the reader to appreciate the value of having a software tool do this work rather than a human.  \n\nSo, ultimately, why did Abe et al. [1] settle for a (less secure) derivative scheme instead of devising PPEs to test the Waters09 IBE private keys? To our surprise, we have the following:  \n\nClaim 1.1 (Informal). The public parameters and private keys for the Waters09 IBE [46] are not PPE testable, under the DBDH and Decision Linear assumptions.  \n\nWaters proves this IBE system secure under the DBDH and Decision Linear assumptions. However, as part of his dual system security proof, he argues that under these assumptions no polynomial-time adversary can distinguish a real private key (generated by the key generation algorithm) from a “semi-functional” private key (used in the proof of security). In his construction, there is no overlap between the real and semi-functional key spaces. Thus, to be PPE testable, there must exist a PPE testing set that accepts all real private keys, but rejects all semi-functional keys. However, Waters argues that, under DBDH and Decision Linear, there is no efficient algorithm capable of making this bifurcation.  \n\nThis is certainly a curious counterexample to the thinking that this problem would be easy, and it was not the only curious example we discovered (see the discussion of the Boyen-Waters anonymous IBE and the Dodis VRF in Section 5.6). However, we were encouraged by our results that show that the vast majority of our test cases were PPE testable and, moreover, that our searching algorithm was able to find them in usually a matter of seconds. We describe how we systemically search for a PPE testing set in Section 4.  \n\n# 1.2 Applications of PPE Testability  \n\nPairing-based schemes are prevalent for their efficiency and functionality. There are a host of applications where one wants to verify some Untrusted pairing elements with respect to a set of Trusted elements. For starters, this is the basic goal of a signature scheme where the purported signature is Untrusted and the verification key and message are Trusted. Likewise, in a verifiable random function, one is given a function output with a proof that are Untrusted and one needs to verify them with respect to a Trusted public key. One can see expanding this to verifying anonymous credentials, e-cash and more.  \n\nThere are also several interesting applications for this when using identity-based encryption (IBE) schemes. First, per Naor’s observation in [22], any identity-based encryption (IBE) scheme gives rise to a signature scheme, where the verification key is the public parameters and the signature on message $m$ is a private key for identity $m$ . Naor’s suggested verification procedure is to encrypt a random message under identity $m$ and then try to decrypt it using the purported signature as the private key. This randomized and rather inefficient verification procedure is often replaced in practice by a direct verification of the signature elements using a set of pairing-product equations. The signature schemes derived from IBEs with PPEverification often possess a “structure-preserving” feature that make them particularly useful and efficient as a building block in larger systems, such as anonymous credentials.  \n\nA second example is the Blind IBE used to build adaptive oblivious transfer schemes by Green and Hohenberger [32]. In their oblivious transfer scheme, the Sender acts as the master authority in an IBE scheme and encrypts each message $m_{i}$ under identity $i$ . To retrieve a message $m_{j}$ , the Receiver engages in a “blind” key extraction protocol with the Sender, so that at the end of the protocol the Receiver obtains the private key for identity $j$ and the Sender does not learn $j$ . As part of this blind key extraction protocol, the Receiver uses a set of PPEs to verify the correctness of the private key for identity $j$ .  \n\nA third example is Accountable Authority $I B E$ introduced by Goyal [30] and expanded on by Goyal, Lu, Sahai and Waters [31], where should a decryption key or program for that user’s identity be found online, there exists a mechanism for the user to prove to a judge that it was the authority and not her that leaked this information. Again, since the user does not fully trust the authority that provides her with a private key, the authors require an efficient method (via a set of PPEs) to verify the well-formedness of the key she obtains.  \n\nFinally, this goal of having the user verify the private key given by a master authority translates over nicely as well to the ciphertext-policy attribute-based encryption setting, where the authority purports to give the user a key representing a set of attributes. As the complexity of the system increases (to ABE and beyond), the ability to derive the set of PPEs automatically becomes increasingly attractive.  \n\n# 1.3 Related Work  \n\nThe effort to automate cryptographic design and verification tasks has been gaining momentum and enjoying much success in the last few years.  \n\nIn 2014, Barthe, Fagerholm, et al. [13] put forward the GGA tool for automatically analyzing (bounded) cryptographic assumptions in the generic group model. This tool was extended to unbounded assumptions by Ambrona, Barthe and Schmidt [10]. Shortly thereafter, Ambrona, Barthe, Gay and Wee [9] showed how to apply this computer-aided reasoning to the design of complex cryptographic constructions, such as attribute-based encryption systems. We use the GGA tool as one piece of the AutoPPE tool.  \n\nIn other related work, Barthe, Fagerholm, Fiore, Scedrov, Schmidt and Tibouchi [14] built an automated tool to design optimal structure-preserving signatures in Type II pairing groups. As they state [14], their “tool can generate automatically and exhaustively all valid structure-preserving signatures within a userspecified search space, and analyze their (bounded) security in the generic group model.” Interestingly, some of the logic they employ in their synthesis algorithm closely resembles our Rule 1 presented in Section 4.2.1.  \n\nThe AutoPPE tool is designed to be interoperable with several existing open source automation tools, built by a community of authors, such as: AutoBatch [7, 8] (for batching the verification of PPEs), AutoStrong [6] (for compiling a signature scheme secure under the standard definition into one that is strongly secure), AutoGroup+ [6, 5] (for translating a Type-I pairing scheme into a Type-III pairing scheme; we also note some nice work on alternative methods for this translation including IPConv [2, 3], although these are not available as open source at this time), AutoG&P [17] (for automatically proving security of cryptographic constructions based on pairing-based assumptions), and AutoLWE [15] (for semi-automatically proving security of cryptographic constructions based on the Learning with Errors assumption).  \n\nMost of the above examples are for the public key setting, although there have also been elegant automation results for blockciphers [41] and authenticated encryption [35] as well.  \n\nThere is also a large-body of impressive work on machine-based cryptographic proof verification, such as Cryptoverif [18], CertiCrypt [16], EasyCrypt [12] and other tools, e.g. [11]. Tying these two bodies of work together, Barthe et al. [17] provided a tool that translates the proofs output by AutoG&P into a format verifiable by EasyCrypt and similarly Akinyele et al. [4] showed that the proofs output by AutoBatch can be automatically verified by EasyCrypt.  \n\n# 2 Preliminaries  \n\nWe define the algebraic setting and notation used in throughout this work.  \n\n# 2.1 Pairings  \n\nLet $\\mathbb{G}_{1}$ , $\\mathbb{G}_{2}$ and $\\mathbb{G}_{T}$ be groups of prime order $p^{1}$ . A map $e:\\mathbb{G}_{1}\\times\\mathbb{G}_{2}\\to\\mathbb{G}_{T}$ is an admissible pairing (also called a bilinear map) if it satisfies the following three properties:  \n\n1. Bilinearity: for all $g_{1}\\in\\mathbb{G}_{1}$ , $g_{2}\\in\\mathbb{G}_{2}$ , and $a,b\\in\\mathbb{Z}_{p}$ , it holds that $e(g_{1}^{a},g_{2}^{b})=e(g_{1}^{b},g_{2}^{a})=e(g_{1},g_{2})^{a b}$ .   \n2. Non-degeneracy: if $g_{1}$ and $g_{2}$ are generators of $\\mathbb{G}_{1}$ and $\\mathbb{G}_{2}$ , resp., then $e(g_{1},g_{2})$ is a generator of $\\mathbb{G}_{T}$ .   \n3. Efficiency: there exists an efficient method that given any $g_{1}\\in\\mathfrak{G}_{1}$ and $g_{2}\\in\\mathfrak{G}_{2}$ , computes $e(g_{1},g_{2})$ .  \n\nA pairing generator PGen is an algorithm that on input a security parameter $1^{\\lambda}$ , outputs the parameters for a pairing group $(p,g_{1},g_{2},g_{T},\\mathbb{G}_{1},\\mathbb{G}_{2},\\mathbb{G}_{T},e)$ such that $\\mathbb{G}_{1}$ , $\\mathbb{G}_{2}$ and $\\mathbb{G}_{T}$ are groups of order $p\\in\\Theta(2^{\\lambda})$ where $g_{1}$ generates $\\mathbb{G}_{1}$ , $g_{2}$ generates $\\mathbb{G}_{2}$ and $e:\\mathbb{G}_{1}\\times\\mathbb{G}_{2}\\to\\mathbb{G}_{T}$ is an admissible pairing. The above pairing is called an asymmetric or Type-III pairing. In Type-II pairings, there exists an efficient isomorphism $\\psi$ from $\\mathbb{G}_{1}$ to ${\\mathfrak{G}}_{2}$ or such an isomorphism $\\phi$ from $\\mathbb{G}_{2}$ to $\\mathbb{G}_{1}$ but not both. In symmetric or Type-I pairings, efficient isomorphisms $\\psi$ and $\\phi$ both exist, and thus we can consider it as though $\\mathbb{G}_{1}=\\mathbb{G}_{2}$ . In this work, we support any of these types of pairings. We will typically refer to Type III pairings in our text, since they are general and typically the most efficient choice for implementation, but our software tool in Section 5 can handle any type.  \n\nGiven pairing parameters $(p,g_{1},g_{2},g_{T},\\mathbb{G}_{1},\\mathbb{G}_{2},\\mathbb{G}_{T},e)$ , we follow prior definitions [33] to define a pairing product equation over variables $Z,\\{X_{i}\\}_{i=1}^{m},\\{Y_{i}\\}_{i=1}^{n}$ as an equation of the form  \n\n$$\nZ\\cdot\\prod_{i=1}^{n}e(A_{i},Y_{i})\\cdot\\prod_{i=1}^{m}e(X_{i},B_{i})\\cdot\\prod_{i=1}^{m}\\prod_{j=1}^{n}e(X_{i},Y_{j})^{\\gamma_{i j}}=1,\n$$  \n\nwhere $A_{i},X_{i}\\in\\mathbb{G}_{1},B_{i},Y_{i}\\in\\mathbb{G}_{2},Z\\in\\mathbb{G}_{T},\\gamma_{i j}\\in\\mathbb{Z}_{p}$ .  \n\nWe sometimes rearrange the terms of a PPE to improve readability. The identity element for all groups $\\mathbb{G}_{1},\\mathbb{G}_{2},\\mathbb{G}_{T}$ (which we here treat as multiplicative groups) will be defined as 1.  \n\n# 2.2 Notation  \n\nWe let $[1,n]$ be shorthand for the set $\\{1,\\ldots,n\\}$ . We use $\\mathbf{v}$ to denote a vector and $\\mathbf{v}_{i}$ to denote the $i$ -th element. For a vector $\\mathbf{v}$ of length $n$ and a subset $U\\subseteq[1,n]$ , we denote $\\mathbf{v}^{U}$ as the set of elements $v_{i}$ for $i=1,\\dots,n$ where $i\\in U$ . Similarly ${\\bf v}^{\\overline{{U}}}$ denotes the subset of elements $\\mathbf{v}_{i}$ for $i=1,\\dots,n$ where $i\\not\\in U$ . Let us denote the set of pairing group identifiers $\\{1,2,T\\}$ by $\\mathcal{L}$ . Let $x,y$ be polynomials over variables in $\\left(u_{1},\\ldots,u_{n}\\right)$ , then by $x=y$ , we mean that $x$ and $y$ are equivalent polynomials.  \n\n# 3 Defining PPE Testing Concepts  \n\nLet us now formalize our focus problem from the introduction.  \n\nDefinition 3.1 (PPE Problem Instance). $A$ pairing product equation (PPE) problem instance $\\amalg$ consists of  \n\n• pairing parameters $\\mathcal{G}=(p,g_{1},g_{2},g_{T},\\mathbb{G}_{1},\\mathbb{G}_{2},\\mathbb{G}_{T},e)$ ,   \n• positive integers $n,m$ ,   \n• multivariate rational polynomials $\\mathbf{f}=(f_{1},\\dots,f_{m})$ over $n$ variables in $\\mathbb{Z}_{p}$ denoted $\\mathbf{u}=(u_{1},\\ldots,u_{n})$ , • a sequence of pairing group identifiers in $\\mathcal{T}=\\{1,2,T\\}$ denoted $\\pmb{\\alpha}=(\\alpha_{1},\\dots,\\alpha_{m})$ ,   \n• a set Fixed $\\subseteq[1,n]$ and   \n• a set Trusted $\\subseteq[1,m]$ .  \n\nwith the restriction that if $i\\in$ Trusted, then $f_{i}$ is a multivariate rational polynomial over the set of variables in uFixed.  \n\nThe pairing parameters above can also indicate the type of pairing group (e.g., I, II or III). We remark that one can intuitively view the elements indicated by the Trusted set as a set of trusted (e.g., public) parameters and the set of elements not in Trusted as some elements one wants to verify with respect to the Trusted set (e.g., an IBE/ABE private key). The polynomials representing these elements are comprised of a set of variables; those variables in Fixed can be thought of as being chosen at public parameter setup time, while those variables not in Fixed correspond to values chosen later (e.g., during private IBE/ABE key generation). We sometimes denote the set of polynomials not in Trusted by untrusted set, and the set of variables not in Fixed by unfixed variables.  \n\nDefinition 3.2 (PPE Challenge). Let $\\Pi=({\\mathcal{G}},n,m,\\mathbf{f},\\alpha$ , Fixed, Trusted) be a PPE problem instance as in Definition 3.1. Let $\\mathbf{F}=(F_{1},\\dots,F_{m})$ be comprised of pairing group elements, where each $F_{i}$ is in group $\\mathbb{G}_{\\alpha_{i}}$ . We say that $\\mathbf{F}$ is $a$ challenge to PPE instance $\\Pi$ . We define classifications for this challenge as follows:  \n\n• $\\mathbf{F}=(F_{1},\\dots,F_{m})$ is a YES challenge if there exists an assignment to variables $\\mathbf{u}=(u_{1},\\ldots,u_{n})\\in\\mathbb{Z}_{p}^{n}$ such that for all $i$ , $F_{i}=g_{\\alpha_{i}}^{f_{i}(\\mathbf{u})}$ .   \n• $\\mathbf{F}=(F_{1},\\dots,F_{m})$ is a NO challenge if it is not $a$ YES challenge and there exists an assignment to $\\mathbf{u}=(u_{1},\\ldots,u_{n})\\in\\mathbb{Z}_{p}^{n}$ such that for all $i\\in$ Trusted, $F_{i}=g_{\\alpha_{i}}^{f_{i}(\\mathbf{u})}$ .   \n• $\\mathbf{F}=(F_{1},\\dots,F_{m})$ is an INVALID challenge if it is neither a YES nor NO challenge.  \n\nWe view a YES challenge as being a valid trusted/untrusted (e.g., public key/private key) pair, i.e., one that could have come from the distribution dictated by the instance parameters. We view a NO challenge as having trusted information according to the instance distribution, but where the untrusted elements to be verified do not fall into their proper distribution space. In an INVALID challenge, the supposedly trusted elements are not drawn from the proper distribution (e.g., the public parameters are not correct), and therefore, we make no attempt to verify with this challenge.  \n\nDefinition 3.3 (PPE Testable and Testing Set). A PPE problem instance $\\Pi$ is said to be PPE testable if and only if there exists a set of pairing product equations $T$ such that each equation in $T$ is simultaneously satisfied for all YES challenges and for all NO challenges, at least one equation in $T$ is not satisfied. (There are no conditions on the behavior for INVALID challenges.) For any PPE problem instance Π, we call such a set of pairing product equations $T$ $a$ testing set. A testing set for a PPE problem instance need not be unique.  \n\nOur subsequent goal will be to search for a testing set for a given PPE problem instance. In Section 1, we discussed how some natural constructions of cryptosystems exhibit pubic parameter and private key pairs that are PPE testable problem instances, whereas other natural examples (e.g., encryption systems based on dual system techniques) are provably not PPE testable.  \n\n# 4 Searching for a PPE Testing Set  \n\nRecall from the introduction our high-level algorithm to search for a testing set $Q$ of a PPE problem. The input is a PPE problem $\\mathrm{II}$ and there are two possible types of outputs. Either it will output that $\\amalg$ is PPE testable and, to confirm this, it will produce one testing set $Q$ or it will output the special response unknown. In the latter case, no determination about whether $\\Pi$ is PPE testable or not can be concluded. This algorithm has one-sided correctness, where the guarantee for this algorithm is that if it outputs that $\\mathrm{II}$ has testing set $Q$ , this will be true.  \n\nThe algorithm proceeds in a sequence of steps, where in each step it (attempts to) “reduce the complexity” of its input, by adding a polynomial $f_{i}$ to the set Trusted and possibly adding a variable $u_{i}$ to the set Fixed. We establish rules for when an item can be moved into one of these sets, how this movement contributes to the search for $Q$ and argue that these rules preserve the PPE testability of the input problem. At the end, if we can obtain $\\mathsf{T r u s t e d}=[1,m]$ , then we will have found a testing set. At any time before the end, if none of the movement rules can be applied, the algorithm aborts and outputs unknown.  \n\n# 4.1 Review on Computing Completion Lists for a List of Polynomials  \n\nOur rules will make use of completion lists in the pairing setting as described by Barthe et al. [13]. Consider any list $\\mathbf{f}~=~[f_{1},\\dots,f_{k}]$ of polynomials along with a sequence of identifiers $\\alpha_{1},\\cdots,\\alpha_{k}$ , where $\\alpha_{i}\\in\\mathcal{I}=$ $\\{1,2,T\\}$ for all $i\\leq k$ . For any $j\\in\\mathcal{I}$ , let $\\mathbf{t}_{i}=\\{f_{j}:\\alpha_{j}=i\\}$ . We now recall the notion of completion $\\mathsf{C L}(\\mathbf{f})=\\{\\mathbf{s}_{1},\\mathbf{s}_{2},\\mathbf{s}_{T}\\}$ of the list $\\mathbf{f}$ of polynomials with respect to a group setting [13]. Intuitively, $\\operatorname{CL}(\\mathbf{f})$ is the list of all polynomials that can be computed by an adversary by applying pairing and isomorphism operations, when he has access to the elements in group $\\mathbb{G}_{i}$ corresponding to the polynomials in $\\mathbf{t}_{i}$ for $i\\in I$  \n\n![](/tmp/output/99_20250326144295/images/963fabb6e274fb7541e5080119398aff3ee0dbec93fcaa7c1733af6094933e3d.jpg)  \n\nFigure 1: Algorithm to find reception list of a list of polynomials  \n\nWe now describe an algorithm to compute the completion CL(f), which is taken from [13] and handles pairing groups. The algorithm proceeds in two steps. In the first step, it computes the reception lists $\\{{\\bf r}_{i}\\}_{i\\in\\mathbb{Z}}$ . The elements of the reception lists are monomials over variables $w_{i,j}$ for $i\\in\\mathcal{I}$ , $j\\in|\\mathbf{t}_{i}|$ and are computed as shown in Figure 1.  \n\nThe monomials characterize which products of elements in $\\mathbf{t}$ the adversary can compute by applying pairing operations. The result of the first step is independent of the elements in the lists $\\mathbf{t}$ and only depends on the lengths of the lists. In the second step, it computes the actual polynomials from the reception lists as  \n\n$$\n\\mathbf{s}_{i}=[m_{1}(\\mathbf{t}),\\ldots,m_{|\\mathbf{r}_{i}|}(\\mathbf{t})]{\\mathrm{~for~}}[m_{1},\\ldots,m_{|\\mathbf{r}_{i}|}]=\\mathbf{r}_{i},\n$$  \n\nwhere every $m_{k}$ is a monomial over the variables $w_{i,j}$ and $m_{k}({\\bf t})$ denotes the result of evaluating the monomial $m_{k}$ by substituting $w_{i,j}$ with $\\mathbf{t}_{i}[j]$ for $i\\in\\mathcal{I}$ and $j\\in|\\mathbf{t}_{i}|$ .  \n\n# 4.2 Rules for “Reducing” the Complexity of a PPE Problem Instance  \n\nWe now describe two rules for reducing the complexity of a PPE instance, whereby we mean reducing the number of items left to verify, i.e., corresponding to the elements represented by polynomials not in the set Trusted.  \n\n# 4.2.1 Rule 1: Move element to Trusted with all Fixed variables  \n\nRule 1 is described in Figure 2. We note that similar logic to this was previously employed in an automated tool focused on synthesizing optimal structure-preserving signature schemes in Type II pairing groups by Barthe, Fagerholm, Fiore, Scedrov, Schmidt and Tibouchi [14]. We generalize for our related, but different goal. Given a PPE problem $\\Pi=({\\mathcal{G}},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ ,Fixed, Trusted) and an index $k\\in[1,m]$ , Rule 1 can possibly be applied if $k\\not\\in$ Trusted and the polynomial $f_{k}\\in{\\bf f}$ consists only of variables $u_{i}\\in\\mathbf{u}$ where $i\\in$ Fixed (these conditions are necessary, but not sufficient).  \n\nLet $(C,\\Pi^{\\prime})={\\mathsf{R u l e1}}(\\Pi,k)$ for an input on which the rule is successfully applied. Let the set of all possible testing sets of PPE problems $\\Pi$ and $\\Pi^{\\prime}$ be denoted $Q_{\\Pi}$ and $Q_{\\Pi^{\\prime}}$ respectively. We now prove that a testing set for $\\Pi$ can be derived from any testing set in $Q_{\\Pi^{\\prime}}$ ; we call this the correctness of Rule 1. Informally, Rule 1 will not flip a PPE problem from non-testable to testable.  \n\n![](/tmp/output/99_20250326144295/images/331be5872a99668973358c4383efb4d65a2b64c0a39dce3c1c3a56cb01226a8e.jpg)  \nFigure 2: Procedure for moving certain elements with all Fixed variables to Trusted.  \n\nLemma 4.1 (Correctness of Rule 1). Let $\\Pi=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,$ Fixed,Trusted) be a PPE problem instance as in Definition 3.1 and let $k\\in[1,m]$ . Suppose $\\bot\\neq(C,\\Pi^{\\prime})=\\mathsf{R u l e1}(\\Pi,k)$ . Then:  \n\n1. $\\Pi^{\\prime}$ is a PPE problem instance as in Definition 3.1 and   \n2. for every testing set $T\\in Q_{\\Pi^{\\prime}}$ , it holds that $(T\\cup\\{C\\})\\in Q_{\\Pi}$ .  \n\nProof. We have that $\\Pi=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,\\mathsf{F i x e}$ d, Trusted). Since $\\mathtt{R u l e1}(\\Pi,k){=}~(C,\\Pi^{\\prime}){\\neq}\\bot$ , we know that the rule was successfully applied, where $\\Pi^{\\prime}=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ , Fixed,Trusted $\\cup\\left\\{k\\right\\})$ .  \n\nCondition 1: we observe that $\\Pi^{\\prime}$ will also satisfy Definition 3.1, where the only non-trivial observation is that we must show that if $i\\in{\\mathsf{T r u s t e d}}\\cup\\{k\\}$ , then $f_{i}$ is a multivariate polynomial over the set of variables in $\\mathbf{u}^{\\mathsf{F i x e d}}$ is maintained. This follows from the fact that a necessary condition for Rule1 to move $k$ to Trusted is that fk only has variables in uFixed.  \n\nCondition 2: let $T$ be any testing set for $\\Pi^{\\prime}$ . By Definition 3.3, $T$ is a set of pairing product equations such that each equation in $\\{T_{1},\\dots,T_{w}\\}=T$ is simultaneously satisfied for all YES challenges, and at least one equation in $T$ is not satisfied for all NO challenges. Recall there are no conditions on the behavior for INVALID challenges.  \n\nWe now argue by contradiction that if $T\\cup\\{C\\}$ is not a testing set for $\\mathrm{II}$ , then $T$ cannot be a testing set for $\\Pi^{\\prime}$ . Since $T\\cup\\{C\\}$ is not a testing set for $\\amalg$ , then either:  \n\n• Case 1: There exists a YES challenge $\\mathbf{F}$ for $\\mathrm{II}$ such that at least one equation in $T\\cup\\{C\\}$ is not satisfied, or   \n• Case 2: There exists a NO challenge $\\mathbf{F}$ for $\\mathrm{II}$ such that all equations in $T\\cup\\{C\\}$ are simultaneously satisfied.  \n\nWe now analyze each of these cases.  \n\nIn Case 1, we know that at least one equation in $T\\cup\\{C\\}$ is not satisfied by challenge $\\mathbf{F}$ . We take this in two subcases. First, suppose that $T$ contains an unsatisfied equation. This means that $\\mathbf{F}$ is also a YES challenge for $\\Pi^{\\prime}$ (it can use the same settings for the variables), but for which one equation of $T$ is not satisfied. This contradicts the starting assumption that $T$ was a testing set for $\\Pi^{\\prime}$ . Second, suppose that all equations of $T$ are satisfied, but that the equation $C$ is not. By definition of being a YES challenge, we know there exists an assignment to the variables $\\mathbf{u}$ such that $F_{i}=g_{\\alpha_{i}}^{f_{i}(\\mathbf{u})}$ gfαii(u) for all i. Equation C tests that Fk is equal to gαii , thus this equation being false contradicts the fact the $\\mathbf{F}$ was a YES challenge.  \n\nIn Case 2, since $\\mathbf{F}$ is a NO challenge for $\\amalg$ where all equations in $T\\cup\\{C\\}$ are simultaneously satisfied, then $\\mathbf{F}$ is also a NO challenge for $\\Pi^{\\prime}$ where all equations in $T$ are simultaneously satisfied. We argue this as follows. By Definition 3.2 of a NO challenge for $\\mathrm{II}$ , there exists an assignment to $\\mathbf{u}=(u_{1},\\ldots,u_{n})\\in\\mathbb{Z}_{p}^{n}$ such that for all i ∈ Trusted, Fi = gfαkk(u) . To convert this to a NO challenge for $\\Pi^{\\prime}$ , we also need to show that $F_{k}=g_{\\alpha_{k}}^{f_{k}(\\mathbf{u})}$ for this same assignment $\\mathbf{u}$ . This follows from the fact that PPE $C$ is satisfied by this challenge and that $C$ explicitly tests that is computed this way, possibly with respect to an equivalent polynomial $\\begin{array}{r}{f_{k}\\equiv\\sum_{j=1}^{\\left|\\mathbf{s}_{T}\\right|}a_{j}\\cdot\\mathbf{s}_{T}[j]}\\end{array}$ . Now since $\\mathbf{F}$ is NO challenge for $\\Pi^{\\prime}$ , it remains to see how it performs with respect to the set $\\check{T}$ . However, since all equations in $T\\cup\\{C\\}$ are satisfied by this challenge $\\mathbf{F}$ , then all equations in $T$ are as well. This contradicts the original assumption that $T$ was a testing set for $\\Pi^{\\prime}$ .  \n\n4.2.2 Rule 2: Move element to Trusted by fixing an un-Fixed variable of form $v\\cdot u_{j}^{d}$  \n\nRule 2 is described in Figure 3. Given a PPE problem $\\boldsymbol{\\Pi}=(\\mathbb{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ , Fixed, Trusted) and indices $j\\in[1,n]$ and $k\\in[1,m]$ , Rule 2 can possibly be applied if $j\\not\\in{\\mathsf{F i x e d}}$ , $k\\not\\in$ Trusted and the polynomial $f_{k}\\in{\\bf f}$ is of the form $c\\cdot u_{j}^{d}+h$ , where the variable $u_{j}\\in\\mathbf{u}$ , the polynomial $h$ contains only variables in $\\mathbf{u}^{\\mathsf{F i x e d}}$ , constant $c\\in\\mathbb{Z}_{p}^{\\ast}$ , and constant $d\\in\\mathbb{Z}_{p}$ s.t. $d$ is relatively prime to $p-1$ .  \n\nDescription of Rule 2   \nInput: A PPE problem $\\Pi=({\\mathcal{G}},n,m,\\mathbf{f},\\mathbf{u},\\alpha.$ , Fixed, Trusted) and integers $j\\in[1,n]$ and $k\\in[1,m]$ .   \nOutput: A PPE problem $\\Pi^{\\prime}$ or $\\perp$ (meaning could not apply the rule).   \nSteps of Rule2(Π): 1. If polynomial $f_{k}\\in{\\bf f}$ is of the form $c\\cdot u_{j}^{d}+h$ , where · j Fixed, ·k  Trusted, • the variable $u_{j}\\in\\mathbf{u}$ , • the polynomial $h$ contains only variables in $\\mathbf{u}^{\\mathsf{F i x e d}}$ , • the constant $c\\in\\mathbb{Z}_{p}^{\\ast}$ and • the constant $d\\in\\mathbb{Z}_{p}$ is relatively prime to $p-1$ then output the PPE problem $\\Pi^{\\prime}=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,\\mathsf{F i x e d\\cup}\\{j\\},\\mathsf{T r u s t e d\\cup}\\{k\\}).$ 2. Otherwise, output $\\perp$ .  \n\nFigure 3: Procedure for moving certain elements to Trusted by fixing an un-Fixed variable  \n\nLet $\\perp\\ne\\Pi^{\\prime}=\\mathsf{R u l e}2(\\Pi,j,k)$ . We now prove that a testing set for $\\Pi^{\\prime}$ is also a testing set for $\\Pi$ , which ensures that Rule 2 does not “flip” a non-testable PPE problem into a testable one.  \n\nLemma 4.2 (Correctness of Rule 2). Let $\\Pi=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,\\mathsf{F i x e c}$ , Trusted) be a PPE problem instance as in Definition 3.1, $j\\in[1,n]$ and $k\\in[1,m]$ . Suppose $\\perp\\ne\\Pi^{\\prime}=\\mathsf{R u l e}2(\\Pi,j,k)$ . Then:  \n\n1. $\\Pi^{\\prime}$ is a PPE problem instance as in Definition 3.1 and   \n2. for every testing set $T\\in Q_{\\Pi^{\\prime}}$ , it holds that $T\\in Q_{\\Pi}$ .  \n\nProof. We have that $\\Pi=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ ,Fixed, Trusted). Since $\\mathsf{R u l e1}(\\Pi,j,k){=}\\Pi^{\\prime}\\neq\\bot$ , we know that the rule was successfully applied, where $\\Pi^{\\prime}=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,\\mathsf{F i x e d\\cup}\\{j\\}$ , Trusted $\\cup\\left\\{k\\right\\}.$ ).  \n\nCondition 1: we observe that $\\Pi^{\\prime}$ will also satisfy Definition 3.1, where the only non-trivial observation is that we must show that if $i\\in{\\mathsf{T r u s t e d}}\\cup\\{k\\}$ , then $f_{i}$ is a multivariate polynomial over the set of variables in $\\mathbf{u}^{\\mathsf{F i x e d\\cup}\\{j\\}}$ . This follows from the fact that a necessary condition for Rule2 to move $k$ to Trusted is that $f_{k}$ only has variables in $\\mathbf{u}^{\\mathsf{F i x e d\\cup}\\{j\\}}$ and that $j$ is simultaneously moved to Fixed.  \n\nCondition 2: the PPE problems $\\Pi$ and $\\Pi^{\\prime}$ differ only in their last two items: the Fixed and Trusted sets, where the $\\Pi^{\\prime}$ sets have the additional elements $\\{j\\}$ and $\\{k\\}$ respectively. As the definition of an YES challenge has no dependence on Trusted and Fixed sets, each YES challenge for $\\amalg$ is also an YES challenge for $\\Pi^{\\prime}$ and vice versa. Since $T$ is a testing set for $\\Pi^{\\prime}$ , each equation in $T$ is simultaneously satisfied for all YES challenges of $\\Pi^{\\prime}$ , and therefore satisfied for all YES challenges of $\\Pi$ .  \n\nSimilarly, we argue that any NO challenge for $\\Pi$ is also a NO challenge for $\\Pi^{\\prime}$ , meaning that at least one equation in $T$ is not satisfied in both cases. Consider any NO challenge $\\mathbf{F}$ for the PPE problem $\\mathrm{II}$ . By definition, $\\mathbf{F}$ is not a YES challenge for $\\Pi$ (or, by the above, for $\\Pi^{\\prime}$ ), and there exists an assignment of $\\mathbf{u}^{\\ast}\\in\\mathbb{Z}_{p}^{n}$ such that gfαii(u∗) ∀i ∈ Trusted.  \n\nWe want to show that Fk = gfαkk(u∗). Since Rule2(Π, j, k) ̸=⊥, we know that the polynomial fk was of the form $c\\cdot u_{j}^{d}+h$ , according to the constraints of Rule2, where $j\\not\\in$ Fixed. Thus, for this setting of $F_{k}$ in the challenge $\\mathbf{F}$ , there exists only one setting of the variable $u_{j}\\in\\mathbb{Z}_{p}$ that is consistent with $F_{k}$ being derived via the polynomial $f_{k}$ and the settings of $u_{i}\\in\\mathbf{u}^{*}$ for all $i\\in{\\mathsf{F i x e d}}$ . Let $F_{k}=g^{y}$ for some $y\\in\\mathbb{Z}_{p}$ . Then we have that:  \n\n$$\nu_{j}=(\\frac{y-h}{c}\\mod p)^{1/d\\mod(p-1)}.\n$$  \n\nThere is a unique solution to the above since $d$ is relatively prime to $p-1$ . Recall that $h$ is derived over the set of variables in uFixed.  \n\nBy Definition 3.1 of a PPE Problem, we have that if $i\\in$ Trusted, then $f_{i}$ is a multivariate polynomial over the set of variables in $\\mathbf{u}^{\\mathsf{F i x e d}}$ . We observe that Rule2 preserves this condition by only moving a polynomial’s index to Trusted if it over the set of variables in $\\mathbf{u}^{\\mathsf{F i x e d}}$ and the variable $u_{j}$ which it simultaneously moves to Fixed.  \n\nThus, for the same setting of variables $\\mathbf{u}^{\\ast}\\in\\mathbb{Z}_{p}^{n}$ , it holds that gfαii(u∗) ∀i ∈ (Trusted ∪ {k}). This allows us to conclude that if $T\\in Q_{\\Pi^{\\prime}}$ , then $T\\in Q_{\\Pi}$ .  \n\n# 4.3 Applying the Rules  \n\nWe now show how to apply these rules in our main searching algorithm. When QSearch(Π) returns a testing set $Q$ , we conclude $\\Pi$ is PPE testable. When the message unknown is returned, the algorithm failed to find a testing set. It does not, however, allow us to conclude anything about Π’s PPE testability.  \n\nAt a high-level, for input $\\Pi$ , if all elements are represented as Trusted, then QSearch(Π) returns the trivial testing set $\\varnothing$ and the PPE problem is trivially PPE testable. Otherwise, the algorithm attempts to apply Rule1, which seeks to move an un-Trusted element into Trusted via a PPE $C$ that can test it with respect to the other Trusted elements. If Rule1 can be applied, then the algorithm recurses on the PPE problem with one fewer un-Trusted elements and, if a testing set $Q^{\\prime}$ for this “smaller” problem is found, it outputs the joint testing set $C\\cup Q^{\\prime}$ . If Rule1 cannot be applied, then the search algorithm tries to apply Rule2, which seeks to move an un-Trusted element into Trusted via fixing an un-Fixed variable. If Rule2 can be applied, then the algorithm again recurses on the smaller instance and, if a testing set for this instance is found, it outputs it likewise. We claim that while the order in which we attempt to apply Rule1 doesn’t matter, the order for Rule2 possibly might; thus, our implementations will sometimes randomize the search order of the indices for this step (as opposed to the numerically increasing order we present here). We can explore other implementation options, such as first applying Rule2 to variables with the smallest $d$ constants, etc.  \n\nEfficiency of QSearch. We now discuss about the asymptotic complexity of the QSearch algorithm. First observe that the size of the Trusted set increases by one with each recursive call to QSearch. Consequently, the function QSearch is called recursively at most $m$ times. During each call, all the untrusted polynomials are scanned to check if any rule is applicable. For the purpose of this analysis, let us denote the size of a polynomial to be the total number of additions and multiplications involved in the normal form of the polynomial (e.g., the size of $x^{2}y z+3z^{3}y^{3}$ is 5). Therefore, multiplying 2 polynomials of size $s_{1}$ and $s_{2}$ takes $O(s_{1}s_{2})$ time. Let the the maximum size of all polynomials f in the input be $s$ . Executing Rule1 involves computing completion lists followed by checking if $\\mathbf{0}$ lies in span of certain polynomials. We know that  \n\n![](/tmp/output/99_20250326144295/images/bffbce4a0833c71d841c3238b5ada9e5c642ce8f230027be01e3e24ed934d221.jpg)  \n\nFigure 4: Recursive procedure for searching for a PPE Testing Set computing completion lists of $m$ polynomials involves $O(m^{2})$ polynomial multiplications taking $O(m^{2}\\cdot s^{2})$ time. Checking if 0 lies in span of $O(m^{2})$ polynomials (number of polynomials in completion lists) involves solving a system of $O(m^{2}\\cdot s)$ linear equations (upper bound on number of monomials in completion list) each of size $O(m^{2})$ , which takes at most $O((m^{2}\\cdot s)^{\\omega})$ time, where $n^{\\omega}$ is the complexity of multiplying two $n\\times n$ matrices. (Current best known value of $\\omega$ is 2.3728639 [27]). Consequently, the time taken to execute Rule1 on an untrusted polynomial is $O(m^{2\\omega}\\cdot s^{\\omega})$ . The time taken to execute Rule2 on a polynomial $f$ is only linear in terms of the size of $f$ . Consequently, every recursive call takes $O(m^{2\\omega+1}\\cdot s^{\\omega})$ time to check for both the rules on $m$ polynomials. As QSearch involves at most $m$ recursive calls, the total time taken is at most $O(m^{2\\omega+2}\\cdot s^{\\omega})$ .  \n\nTheorem 4.1 (Correctness of Testing Set from Algorithm in Figure 4). Let $\\Pi=({\\mathcal{G}},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ , Fixed, Trusted) be a PPE problem instance as in Definition 3.1. Let $Q=\\mathsf{Q S e a r c h}(\\Pi)$ . If $Q\\neq$ unknown, then $Q$ is a testing set for $\\mathrm{II}$ .  \n\nProof. We want to show that if QSearch $(\\Pi)=Q\\neq$ unknown, then $Q$ is a testing set for $\\Pi$ . We do this by induction.  \n\nBase Case: When $\\mathsf{T r u s t e d}=[1,m]$ , then ${\\mathsf{Q S e a r c h}}(\\Pi)=\\emptyset$ . In this case, all elements are Trusted; thus all PPE challenges for $\\Pi$ are either YES or INVALID challenges. The emptyset $\\varnothing$ trivially satisfies Definition 3.3.  \n\nInduction Step: For any $\\Pi^{\\prime}=({\\mathcal{G}},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ , Fixed′, Trusted′) where Trusted $\\mathrm{~\\'~}\\subseteq[1,m]$ and $Q^{\\prime}$ is a testing set for $\\Pi^{\\prime}$ , we prove that:  \n\n1. If $\\Pi^{\\prime\\prime}=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha,\\mathsf{F i x e d}^{\\prime},$ , Trusted′′ = Trusted′ \\ {k}) and $(C,\\Pi^{\\prime})=\\mathtt{R u l e1}(\\Pi^{\\prime\\prime},k)$ , for some $k\\in$ $[1,m]$ , then $Q^{\\prime\\prime}=C\\cup Q^{\\prime}$ is a testing set for $\\Pi^{\\prime\\prime}$ .   \n2. If $\\Pi^{\\prime\\prime}=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u},\\alpha$ , $\\mathsf{F i x e d}^{\\prime\\prime}=\\mathsf{F i x e d}^{\\prime}\\setminus\\{j\\}$ , Trusted′′ = Trusted ${}^{\\prime}\\setminus\\{k\\})$ and $(C,\\Pi^{\\prime})=\\mathsf{R u l e}2(\\Pi^{\\prime\\prime},k)$ , for some $k\\in[1,m]$ , then ${Q^{\\prime\\prime}=Q^{\\prime}}$ is a testing set for $\\Pi^{\\prime\\prime}$ .  \n\nWe have two cases.  \n\nCase 1: Since $Q^{\\prime}$ is a testing set for $\\Pi^{\\prime}$ , then we know it tests all $\\ell=m-\\vert\\mathsf{T r u s t e d^{\\prime}}\\vert$ untrusted elements in $\\Pi^{\\prime}$ . We need to argue that $Q^{\\prime\\prime}=C\\cup Q^{\\prime}$ tests all $\\ell+1=m-\\vert{\\sf T r u s t e d}^{\\prime\\prime}\\vert=m-\\vert{\\sf T r u s t e d}^{\\prime}\\vert+1$ untrusted elements in $\\Pi^{\\prime\\prime}$ . In particular, the sole element they differ in is the element represented by index $k$ . By inspection of Rule1, we see that the PPE $C$ allows for testing this element $k$ . Thus, if $Q^{\\prime}$ is a set of PPEs testing all untrusted elements except $k$ and $C$ is a PPE testing element $k$ , then together they form a testing set for all untrusted elements in $\\Pi^{\\prime\\prime}$ .  \n\nCase 2: Since $Q^{\\prime}$ is a testing set for $\\Pi^{\\prime}$ , then we know it tests all $\\ell=m-\\left|\\mathsf{T r u s t e d}^{\\prime}\\right|$ untrusted elements in $\\Pi^{\\prime}$ . We need to argue that ${Q}^{\\prime\\prime}={Q}^{\\prime}$ tests all $\\ell+1=m-\\vert\\mathsf{T r u s t e d}^{\\prime\\prime}\\vert=m-\\vert\\mathsf{T r u s t e d}^{\\prime}\\vert+1$ untrusted elements in $\\Pi^{\\prime\\prime}$ . In particular, the sole element they differ in is the element represented by index $k$ . Since Rule2 was successfully applied, element $k$ was moved to the trusted set, because it contained an unfixed variable, whose value was fixed by the move. Since the variable was unfixed but is now being fixed by the move, no PPE is required to test this element as a trusted element. All future uses of this variable will now be tested against its fixed value. Thus, no new PPEs are required for the execution of Rule2 and ${Q^{\\prime\\prime}=Q^{\\prime}}$ is a testing set for $\\Pi^{\\prime\\prime}$ .  \n\nArgument Summary: Since we have shown that QSearch $(\\Pi)\\neq$ unknown returns a testing set properly for the base case where all elements are trusted and we have shown the inductive step that for all $\\Pi^{\\prime},\\Pi^{\\prime\\prime}$ which differ by only one trusted element, according to the relationships of Rule1 or Rule2 called by QSearch, that QSearch correctly derives a testing set for $\\Pi^{\\prime\\prime}$ from a testing set for $\\Pi^{\\prime}$ , then by the principle of induction we have shown that any testing set output by QSearch is correct.  \n\nCorollary 4.1 (Correctness of PPE Testability from Algorithm in Figure 4). Let $\\Pi=(\\mathcal{G},n,m,\\mathbf{f},\\mathbf{u}_{},$ , $\\pmb{\\mathscr{C}}$ , Fixed, Trusted) be a PPE problem instance as in Definition 3.1. Let $Q={\\mathsf{Q S e a r c h}}(\\Pi)$ . If $Q\\neq$ unknown, then Π is PPE Testable.  \n\nProof. Follows directly from Theorem 4.1 and Definition 3.3.  \n\n# 5 Implementation and Case Studies  \n\nWe now describe a new software tool, called AutoPPE, which implements the PPE searching algorithm presented in Figure 4. We ran AutoPPE on a number of IBE, ABE, VRF, signature schemes, and other type of pairing-based public/private parameters, including some that are PPE testable and some that are provably not PPE testable. We report on the design, results and performance of AutoPPE in this section.  \n\n# 5.1 AutoPPE Implementation  \n\nWe implemented the AutoPPE tool using Ocaml version 4.02.3. We utilized the parsing tools and data structures (to store polynomials) from the Generic Group Analyzer (GGA)2. We used the SageMath package $^3$ to solve systems of linear equations. We implemented the remaining logic ourselves.  \n\nAutoPPE takes as input pairing information (such as the Type I, II or III), a set of fixed/unfixed variables, and a set of trusted/untrusted polynomials along with their group identifiers. (While this is a slightly different format than we used in Definition 3.1, we stress that it is the same information.) In addition, the tool optionally takes as input information that allows the tool to help the user encode some cryptosystem parameters as a PPE problem instance. In particular, all trusted and untrusted elements (represented by polynomials) are bilinear group elements in $\\mathbb{G}_{1},\\mathbb{G}_{2}$ or $\\mathbb{G}_{T}$ and Definition 3.1 does not allow including an element in $\\mathbb{Z}_{p}$ in either set. However, since it is not uncommon for schemes to contain elements in the $\\mathbb{Z}_{p}$ domain as part of their public or private parameters, we implemented a workaround for those schemes as described in Section 5.2. The tool runs the Figure 4 search algorithm, with a few optimizations detailed in Section 5.5 and a few limitations detailed in Section 5.6, and outputs either a set of PPEs or the special symbol unknown. After obtaining PPEs by running QSearch algorithm, the tool further runs an algorithm similar to [7] to produce equivalent PPEs which are more efficient to check.  \n\n![](/tmp/output/99_20250326144295/images/a99827210b14cb85bdba91dd7b4eb3683e67ca3d352dc2550f70246eb68b833b.jpg)  \n\nFigure 5: The workflow of the AutoPPE tool which follows the logic in Figure 4. It takes as input an intial PPE problem instance along with some additional information (i.e., variables in $\\mathbb{Z}_{p}$ ) that help the user encode a given pairing-based scheme into a proper PPE problem instance, as we explain in the text. The tool utilizes and adapted portions of existing tools such as the Generic Group Analyzer (GGA) for handling polynomials and completion sets and the SageMath package for solving systems of linear equations. The output is either a set of Pairing Product Equations (PPEs), indicating that the instance is PPE Testable, or the special symbol unknown.  \n\nThe source code for AutoPPE comprises about 3K lines of Ocaml code, and the input description of each pairing based scheme we tested consists of less than 10 lines of code. The ease of converting a given pairing based scheme into the input format for AutoPPE makes the tool highly practical and useful. We plan to make AutoPPE publicly available as open source code at https://github.com/JHUISI/auto-tools.  \n\n# 5.2 Encoding “Well-formedness” of Cryptosystem Parameters as a PPE Testability Problem  \n\nIn this subsection, we describe how to look at the public-private parameters of a pairing-based cryptosystem and then encode this as a PPE problem instance. Typically, the objective is to test that the private parameters are “well formed” with respect to the public parameters, where the definition of being “well formed” depends on the application. Let’s take identity-based encryption (IBE) as our starting example. For an IBE scheme IBE = (Setup,KeyGen, Enc, Dec), there are a number of different applications (see the discussion in Section 1) where one wants an efficient deterministic procedure (based on PPEs) that takes as input the public parameters pp, an identity id and a purported private key $S$ , and verifies whether $S$ is a possible output of the KeyGen algorithm with respect to pp and id. Recall that the critical point of our work is discovering whether a scheme’s parameters can be verified in this way or not.  \n\nWe now formulate the problem of determining well-formedness of a pairing-based IBE secret key as an instance of the PPE Testability problem. Suppose for the given IBE scheme on group structure $\\mathcal{G}$ , the public key is of the form $(g_{\\alpha_{1}}^{f_{1}},\\dots,g_{\\alpha_{k}}^{f_{k}})$ and the secret key for an identity is of the form $(g_{\\alpha_{k+1}}^{f_{k+1}},\\dots,g_{\\alpha_{m}}^{f_{m}})$ , where $\\alpha_{i}\\in\\{1,2,T\\}\\forall i$ , $\\{f_{1},\\ldots,f_{k}\\}$ are polynomials on variables $\\{u_{1},u_{2},\\ldots,u_{t}\\}$ and $\\{f_{k+1},~\\ldots,f_{m}\\}$ are polynomials on variables $\\{u_{1},u_{2},...u_{n}\\}$ . We formulate the corresponding PPE problem as $({\\mathcal{G}},n,m,\\mathbf{u}=$ $\\{u_{1},u_{2},\\ldots,u_{n}\\},\\mathbf{f}=\\{f_{1},f_{2},\\ldots,f_{m}\\},\\alpha=\\{\\alpha_{1},\\alpha_{2},\\ldots,\\alpha_{m}\\}$ , Truste $\\mathsf{a}=[1,k],\\mathsf{F i x e d}=[1,t])$ .  \n\nAlthough this encoding seems quite simple, many IBE constructions deviate from this form in several ways. We now describe several insights into converting a given IBE scheme into the above form.  \n\n(1) When multiple group elements are sampled randomly in a scheme, we first normalize the scheme by using a single generator for each group and replacing every random sampling of a group element by $g^{v}$ , where the $g$ is generator of the group and $v$ is a fresh variable randomly sampled from $\\mathbb{Z}_{p}$ .  \n\n(2) Many constructions such as Boyen-Waters [24] and Waters dual system [47] include identity id or $H a s h({\\sf i d})$ as part of the private key. In general, constructions which include variables $\\{v_{1},v_{2},\\ldots,v_{s}\\}$ in $\\mathbb{Z}_{p}$ as part of public/secret key can be reformulated into a PPE problem instance with the following modifications. Expand the Trusted set by including $f_{i}\\cdot\\mathsf{p o l y}(\\mathsf{v}_{1},\\mathsf{v}_{2},\\ldots,\\mathsf{v}_{5})$ with group identitifer $\\alpha_{i}$ for every trusted polynomial $f_{i}$ and every polynomial poly() of degree at most $d$ . We also include the variables $\\{v_{1},v_{2},\\ldots,v_{s}\\}$ as part of the Fixed set. The parameter $d$ can be easily configured in the tool. We used $d=1$ for our case studies and observed that it is sufficient for all the schemes that we tested.  \n\n(3) The Boneh-Franklin [22] and Gentry-Silverberg [29] constructions use a hash function $H$ that hashes identities to a group element. In this case, we reformulate the scheme by replacing $H(\\mathrm{id})$ with $g_{\\alpha}^{h}$ for an appropriate $\\alpha\\in\\mathcal{I}$ and a fresh variable $h$ .  \n\n(4) The Boneh-Boyen [20] construction hashes identity into bit string $H(\\mathsf{i d})=h_{1}||h_{2}||h_{3}||\\cdot\\cdot\\cdot||h_{k}$ , where $k$ is the length of the bit string. The Waters/Naccache [42] construction hashes identity into blocks of bit strings $H(\\mathsf{i d})=h_{1}||h_{2}||h_{3}||\\cdot\\cdot\\cdot||h_{k}$ , where each $h_{i}$ is a bit string block and $k$ is the number of blocks in $H(\\mathrm{id})$ . In either case, we first reformulate the problem by considering each $h_{i}$ as a separate variable in $\\mathbb{Z}_{p}$ and including it as a part of the secret key. We then reduce it to the PPE Testability problem as described earlier. Note that, this method results in a significant blowup in the number of polynomials in the input and can be tested efficiently only for modest values of $n$ . However, the output PPEs can be manually extended to higher values of $n$ by identifying a pattern.  \n\nUsing the above encoding approaches, we tested 8 pairing-based IBE schemes for well-formedness of the private key and our tool was able to quickly output a testing set for all of the schemes which are testable.  \n\nWe now look beyond IBE schemes. A signature scheme $\\mathsf{S I G}=(\\mathsf{S e t u p},\\mathsf{S i g n},\\mathsf{V e r i f y})$ is said to be wellformed if there exists an efficient deterministic procedure to verify that a given signature is a valid (possible) signature w.r.t. given message and public key. Similarly, a Verifiable Random Function (VRF) scheme VRF $=$ (Setup, Eval, Verify) is said to be well-formed if there exists an efficient deterministic method to test that a given VRF output and proof are valid w.r.t. given verification key and input. Analogously, a Ciphertext-Policy Attribute Based Encryption (CP-ABE) scheme ABE = (Setup, KeyGen, Enc, Dec) is said to have well-formed secret key if there exists an efficient deterministic way to check that a given ABE secret key is valid w.r.t. given public key and attributes. Testing whether a given pairing-based Signature/VRF/CPABE scheme is well-formed can be reformulated as a PPE testability problem analogous to the IBE case described above.  \n\n# 5.3 A Detailed Example for the Waters05 IBE  \n\nBefore presenting all our cases studies in Section 5.4, we’d like to walk the reader through one detailed example. Let us consider the Waters05 IBE scheme [45] with the Naccache Optimization [42]. We would like to check if the private key for an identity is PPE Testable given the public parameters and the identity. As mentioned in the introduction, an IBE scheme with “private key” PPE Testability immediately implies a signature scheme with deterministic verification. Moreover, an IBE scheme with “private key” PPE Testability, and a few other properties, admits an adaptive oblivious transfer scheme [32]. For the sake of completeness, we recall this popular construction in Appendix A.  \n\nThe input file for the tool is presented in Figure 6. For space reasons, we choose to illustrate this with a toy example of 4 as the identity block size; in practice one would likely use 8 or 32.4 The pairing information is specified using the line maps $\\mathtt{G1*G1->G T}$ , which denotes a Type I pairing. Alternately, a Type II pairing could be specified by maps $\\mathtt{G1}*\\mathtt{G2}\\mathrm{-}>\\mathtt{G T}$ , isos $\\mathrm{G1}\\mathrm{-}>\\mathrm{G2}$ , and a Type III pairing could be specified by maps $\\mathtt{G1*G2->G T}$ . In order to test the for well-formedness of an IBE private key, the public parameter elements (Trusted set) along with their group identifiers are specified by trusted polys [ ] in $\\mathtt{G_{-}}$ , and the private key elements for an identity (Trusted set) along with their group identifiers are specified using untrusted polys[ ] in G . Every polynomial should be specified along with a unique identifier which will be used to output the PPEs in a compact form. The variables sampled in the Setup phase (Fixed set) are specified using fixed vars [ ]. and the variables sampled during the KeyGen phase (Fixed set) are specified using unfixed vars [ ]. The IBE construction hashes identity id into blocks of bit strings, which can be treated as elements in $\\mathbb{Z}_{p}$ for our purposes. Each of the blocks is identified by a separate variable, and are specified using Zp vars [ ]. Comments in the input file can be specified using $(*\\ldots*)$ .  \n\n![](/tmp/output/99_20250326144295/images/26c4f74c571639e186ab2edaa0d95fbdb3e2d3871d4a7b4b0a1fe44471ebab15.jpg)  \nFigure 6: Input file for Waters05 IBE scheme with Nacacche Optimization.  \n\nThe output of the tool on the above input is presented in Figure 7. The tool first converts the input specification to a PPE instance by multiplying every variable specified in $\\mathtt{Z p\\mathrm{-vars\\left[-\\right]}}$ with every trusted polynomial and including them in trusted set. This expands the Trusted set from 9 polynomials (including the identity polynomials internally added by our tool) to 45 polynomials which are printed in the output. The tool later on applies the QSearch algorithm and outputs the PPEs in terms of the unique identifiers specified for each polynomial. Note that the tool also optimizes the PPEs to minimize the number of pairings used in the PPE. Further optimization can be achieved using AutoBatch tool [7, 8]5 which can batch many PPEs into few PPEs.  \n\n# 5.4 Case Studies  \n\nWe evaluated AutoPPE on various types of pairing-based schemes using a MacBook Pro 2015 laptop with 2.7GHz Intel Core i5 processor and 8GB 1867MHz DDR3 RAM. We present the results along with average execution times over 10 runs in Table 1. In Appendix B, we include more details about the input and output of AutoPPE on some test schemes. We observe that the tool outputs a testing set for most of the standard schemes which are testable within a few seconds.  \n\nWe note that in our implementation, we simplify checking whether the constant $d$ is relatively prime to $p-1$ in Rule2, by checking whether $d$ is a small prime ( $d\\in\\{1,3,5,7,11\\}_{.}$ ). We made this simplification is because none of the schemes we encountered include a polynomial with degree $d>2$ for an unfixed variable.  \n\nIn order to mimic the schemes presented in the papers as they are, we tested most of the schemes in the Type I setting. To demonstrate the flexibility of the tool, we also translated several of these schemes into the Type III setting6). The Waters dual system IBE [47] is not PPE testable (see Section 1) and our tool (correctly) output unknown (see the full output in Appendix B). The Boyen-Waters anonymous IBE [24] and the Dodis VRF [26] appear not to be PPE testable (see Section 5.6) and our tool also output unknown for these.  \n\nThe introduction motivated this problem by showing a connection between PPE testability for an IBE scheme and its suitability for use in blind and/or accountable authority IBE systems. We remark that we tested several such IBE schemes as part of our case study, including Boneh-Boyen [20], Waters [45] and Naccache [42] (which were employed in [32] to leverage this property to build OT).  \n\n<html><body><table><tr><td>Scheme</td><td>Pairing</td><td>Type</td><td>PPE Testability</td><td>Tool's Output</td><td>Execution Time</td></tr><tr><td>Boneh-Franklin01 ([22])</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>1.90s</td></tr><tr><td>Gentry-Silverberg02 ([29])</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>2.94s</td></tr><tr><td>Boneh-Boyen04a ([19]) (l = 160)</td><td>Type I</td><td>HIBE</td><td>Testable</td><td>Testable</td><td>5.55s</td></tr><tr><td>Boneh-Boyen04b ([20]) (|H(id)| = 16)</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>9.23s</td></tr><tr><td>Waters05 ([45]) (|H(id)| = 160)*</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>6.91s</td></tr><tr><td>Waters05 ([45]) (|H(id)| = 16)</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>3.87s</td></tr><tr><td>Naccache05 ([42]) (B(H(id)) = 8)</td><td>Type I</td><td>IBE</td><td>Testable</td><td>Testable</td><td>1.69s</td></tr><tr><td>Naccache05 ([42]) (B(H(id)) = 8)</td><td>Type III</td><td>IBE</td><td>Testable</td><td>Testable</td><td>1.66s</td></tr><tr><td>BBG05 ([21]) (l = 8)</td><td>Type I</td><td>HIBE</td><td>Testable</td><td>Testable</td><td>10.96s</td></tr><tr><td>Boyen-Waters06 ([24])</td><td>Type I</td><td>Anon-IBE</td><td>see Section 5.6</td><td>Unknown</td><td>0.0008s</td></tr><tr><td>Waters09 ([47])</td><td>Type I</td><td>IBE</td><td>Not Testable</td><td>Unknown</td><td>1.57s</td></tr><tr><td>BLS01 ([23])</td><td>Type I</td><td>Signature</td><td>Testable</td><td>Testable</td><td>1.69s</td></tr><tr><td>CL04 Scheme A ([25])</td><td>Type I</td><td>Signature</td><td>Testable</td><td>Testable</td><td>3.12s</td></tr><tr><td>CL04 Scheme B ([25])</td><td>Type I</td><td>Signature</td><td>Testable</td><td>Testable</td><td>6.21s</td></tr><tr><td>CL04 Scheme B ([25])</td><td>Type III</td><td>Signature</td><td>Testable</td><td>Testable</td><td>6.53s</td></tr><tr><td>CL04 Scheme C ([25]) (B(msg) = 8)</td><td>Type I</td><td>Signature</td><td>Testable</td><td>Testable</td><td>25.81s</td></tr><tr><td>Dodis03 ([26]) (|IC(c)| = 6)</td><td>Type I</td><td>VRF</td><td>see Section 5.6</td><td>Unknown</td><td>0.18s</td></tr><tr><td>Dodis03 ([26]) (|C(c)| = 6)</td><td>Type III</td><td>VRF</td><td>seeSection 5.6</td><td>Unknown</td><td>0.12s</td></tr><tr><td>Lys02 ([40]) (|C(∞)| = 5)</td><td>Type I</td><td>VRF</td><td>Testable</td><td>Testable</td><td>8.78s</td></tr><tr><td>Lys02 ([40]) (IC(∞)| = 5)</td><td>Type III</td><td>VRF</td><td>Testable</td><td>Testable</td><td>9.10s</td></tr><tr><td>Jager15 ([37]) (|H(x)| = 4)</td><td>Type I</td><td>VRF</td><td>Testable</td><td>Testable</td><td>9.11s</td></tr><tr><td>Jager15 ([37]) (|H(∞)| = 4)</td><td>Type III</td><td>VRF</td><td>Testable</td><td>Testable</td><td>9.98s</td></tr><tr><td>RW13 ([44]) (α = 60)</td><td>Type I</td><td>CP-ABE</td><td>Testable</td><td>Testable</td><td>222.75s</td></tr><tr><td>RW13 ([44]) (α = 60)</td><td>Type III</td><td>CP-ABE</td><td>Testable</td><td>Testable</td><td>222.43s</td></tr><tr><td>100-DDH</td><td>Type I</td><td>Custom</td><td>Testable</td><td>Testable</td><td>1.77s</td></tr><tr><td>100-DBDH</td><td>Type I</td><td>Custom</td><td>Not Testable</td><td>Unknown</td><td>0.16s</td></tr></table></body></html>  \n\nTable 1: The output of AutoPPE on various PPE testability problems. Here, $\\it l$ represents the number of delegation levels in a HIBE scheme, $|H(\\operatorname{id})|$ denotes the length of the hash of identity id, $B(H(\\operatorname{id}))$ denotes the number of blocks in the hash of identity id, $\\scriptstyle B({\\mathsf{m s g}})$ denotes the number of blocks in message msg, $|C(x)|$ denotes the length of encoding of input $x$ , $|H(x)|$ denotes the length of encoding of input $x$ and $a$ denotes the number of attributes. ”\\*” indicates that an optimized encoding mechanism is used to account for elements in $\\mathbb{Z}_{p}$ . The execution time is mentioned in seconds.  \n\n![](/tmp/output/99_20250326144295/images/4aec9a81993c0e986956135ca17d54d5ca2db747bdfc25ad5dc232a3a67c1af7.jpg)  \nFigure 7: Output of AutoPPE for Waters05 IBE scheme with Nacacche Optimization  \n\nIn the [26, 40, 37] VRF schemes, the input is encoded as a bit string, which is treated as a vector of $\\mathbb{Z}_{p}$ variables by our tool. We observe that the size of the polynomials in these schemes grow exponentially in size with respect to the length of encoding of the input. Consequently, we tested these schemes only with a short length encoding. However, for these schemes, we observe that the PPEs have a clear pattern which can be extrapolated to input encodings of arbitrary length.  \n\nIn Section 5.2, we described a method to encode schemes which output elements in $\\mathbb{Z}_{p}$ as part of their trusted or untrusted parameters as a PPE Testability problem. The naive method of reformulating such schemes blows up the size of the trusted set and is inefficient when there are large number of elements in $\\mathbb{Z}_{p}$ . However, for a few problems we can improve the run time by including only a subset of these additional polynomials in the trusted set; one can always try a smaller set first and then expand the input iteratively. We demonstrate this using the Waters05 [45] example, which hashes identities to 160 bit strings and as a result would blow up the size of the trusted set to $O(160^{2})$ polynomials when encoded naively. We improve upon the naive method by including only 480 polynomials in the trusted set and thereby achieving significantly faster run times.  \n\nWe also tested our tool on a few custom examples with $100+$ elements in them. In the 100-DDH example with Type I pairings, the trusted set contains polynomials $\\{a_{1},a_{2},\\cdot\\cdot\\cdot,a_{50}\\}$ in group $\\mathbb{G}_{1}$ , the untrusted set contains polynomials $\\{a_{51},a_{52},\\cdot\\cdot\\cdot,a_{100},b\\}$ in group $\\mathbb{G}_{1}$ and the polynomial $(a_{1}+a_{2}+\\cdot\\cdot\\cdot+a_{100})*b$ in group $\\mathbb{G}_{T}$ . Clearly, this problem can be tested using the PPE ${\\sf g}_{\\sf T}{}^{(a_{1}+a_{2}+\\cdots+a_{100})*b}=e\\bigl(\\prod_{i=1}^{100}{\\sf g}_{1}{}^{a_{i}},{\\sf g}_{1}{}^{b}\\bigr)$ . In the 100- DBDH example with Type I pairings, the trusted set contains $\\{a_{1},a_{2},\\cdot\\cdot\\cdot,a_{50}\\}$ in group $\\mathbb{G}_{1}$ , the untrusted set contains polynomials $\\{a_{51},a_{52},\\cdot\\cdot\\cdot,a_{100},b,c\\}$ in group $\\mathbb{G}_{1}$ and the polynomial $(a_{1}+a_{2}+\\cdot\\cdot\\cdot+a_{100})*b*c$ in group $\\mathbb{G}_{T}$ . This problem is not PPE Testable under the Decisional Bilinear Diffie-Hellman (DBDH) assumption as it involves deciding a DBDH instance.  \n\n# 5.5 Optimizations  \n\nThe QSearch algorithm discussed in Figure 4 has a high time complexity. It is particularly unacceptable for problems which include lot of elements in the $\\mathbb{Z}_{p}$ domain due to the blowup of the size of the trusted set and thereby the size of completion list. We therefore implemented a few optimizations which drastically improve the run time. We first observe that the completion lists get updated only by a few elements every time a polynomial is added to the trusted set. Consequently, the algorithm computes the completion list in an incremental manner instead of computing it from scratch each it checks for Rule 1. The completion list is updated each time a polynomial is added to the trusted set.  \n\nWe further optimized the algorithm to find coefficients in Rule 1 by removing a subset of the polynomials that trivially have zero coefficient. When applying Rule 1, suppose a polynomial $g$ in $\\mathbf{s}_{T}\\cup\\{f_{k}\\}$ has a monomial which is not present in any other polynomial, then trivially the coefficient $g$ is zero when expressing $\\mathbf{0}$ as a span of polynomials in $\\mathbf{s}_{T}\\cup\\{f_{k}\\}$ .  \n\n# 5.6 Limitations and Open Problems  \n\nThis work represents a meaningful first step in defining, understanding and automating the PPE testability of many well-known pairing cryptosystems. We now remark on some limitations of the tool that are exciting areas for future research.  \n\n(1) Beyond Prime Order Pairings. First, we restrict ourselves to pairing-based constructions with prime order groups. It would be interesting to extend the tool to composite-order pairings, e.g., [38, 39], and even RSA-based constructions. In constructions based on composite-order pairings, elements are sampled from various subgroups. Verifying the validity of untrusted terms involves testing whether the terms are in their designated subgroups. Our current model of representing a term with a polynomial may not be enough to verify such relationships.  \n\n(2) Rational Polynomials and More. Second, the tool doesn’t work on schemes, such as Gentry’s IBE [28] or Boneh-Boyen [19], which have group elements with exponents as rational polynomials (e.g., $g^{\\frac{1}{p(\\cdot)}}$ ) or schemes, such as Hohenberger-Waters [36], with polynomials with variable degree (e.g., $g^{x^{y}}$ ). While the GGA tool on which we built AutoPPE also does not handle rational polynomials, emerging new work by Ambrona et al. [9] does. While inspirational, it does not directly apply here. They work in the average case setting, and thus can ignore the negligible probability that an element is “undefined” (e.g., $g^{\\frac{1}{p(\\cdot)}}$ where $p(\\cdot)$ evaluates to zero on a randomly chosen input). Here we focus on the worst case setting – where a powerful adversary such as an IBE Master Authority might try to pass off an ill-formed private key to a user using any such loophole – and we need a set of PPEs that can catch any ill-formed key.  \n\n(3) Dependent Variables. Third, the algorithm works only on schemes which sample all the variables independently. For example, the framework doesn’t capture schemes which use hard-core predicate bits, schemes which use hash functions in complex ways (such as having $g^{x}$ with $x=H(m||S)$ , where $S$ is another group element) [34] or schemes which sample two orthogonal vectors [43]. However, this seems to be more a limitation of what can be tested with PPEs rather than a limitation of this particular tool.  \n\n(4) Unbounded PPEs. Fourth, our scheme considers only constructions with concrete numbers of elements and parameters. One might consider extending this to the unbounded setting, as was done for the generic group assumption setting by Ambrona et al. [10].  \n\n(5) On Relaxing PPE Testability. Our automation outputs unknown on the Dodis VRF [26], which might seem surprising, since one might predict that the PPEs of the VRF verification algorithm would form a PPE testing set. In the case of this VRF, it does not appear to be PPE testable, even though its verification scheme is correct. Our Rule 1 does not apply because the PRF output ${\\cal F}\\in\\mathbb{G}_{1}$ being verified must be paired with an element $g_{2}^{a}\\in\\mathbb{G}_{2}$ (as opposed to just $g_{2}$ ) to be compared against other established values such as an $A\\in\\mathbb{G}_{T}$ , i.e., an equation is checked of the form $e(F,g_{2}^{a})=A$ . The problem is that if $a=0$ and $A=1$ , then $e(F,g_{2}^{a})=A$ for any value of $F$ , thus verifying nothing about $F$ . In the scheme, $a$ is chosen at random by a trusted party and thus it will be zero with only negligible probability, making this a non-issue for scheme security. However, our current formulation of PPE challenges and testability, in Definitions 3.2 and 3.3 respectively, requires that a testing set outputs the correct response for all challenges – which for this scheme would include ones where $a=0$ , but in this case the VRF’s verification equations do not appear to be sufficient. An interesting open direction could be to consider ways to relax our PPE testability notion to capture schemes such as Dodis [26], where one may be able to test most, but not all possible inputs.  \n\n(6) PPE Circuits. Lastly, and perhaps of most surprise to us, we look at what we learned from the Boyen-Waters anonymous IBE [24] example. This one again falls into a gray area – where we aren’t actually sure if it is PPE testable or not. However, we found evidence leading us to believe that this scheme is: (a) not PPE testable under Definition 3.3, but is (b) potentially testable under a broader definition that would encompass outputting a “PPE circuit” rather than a conjunction of PPEs.  \n\nLet’s dig in further. In an anonymous IBE system, the anonymity requirement is that the ciphertext does not reveal the identity of the recipient. Thus, there does not seem be anything inherent in such a system (unlike with dual system encryption) that would make the private key not be PPE testable. The AutoPPE tool outputs unknown for this case, and it quickly rejects it by finding that it cannot apply Rule2.  \n\nIn Definition 3.3, we define testing sets to be a conjunction of PPEs. As a result, we needed to restrict the applicability of Rule2 only to polynomials of the form $f(\\cdot)=f_{1}(\\cdot)\\cdot u^{d}+f_{2}(\\cdot)$ , where $f_{1}$ is a non-zero constant. In case $f_{1}$ is a non-constant polynomial on fixed variables, the untrusted polynomial $f$ could be verified and moved to the trusted set, if we include conditional logic such as $(F_{1}=I)\\implies(F=F_{2})$ in the testing set. Here, $F$ is the formal challenge variable corresponding to the untrusted polynomial $f$ , $I$ is an identity element, $F_{1}$ and $F_{2}$ are expressions on formal variables corresponding to trusted polynomials which evaluate to $g_{\\alpha}^{f_{1}}$ and $g_{\\alpha}^{f_{2}}$ respectively for an appropriate group identifier $\\alpha$ . Essentially, the rule states that in case $f_{1}$ evaluates to 0 for the given challenge, then $f$ should evaluate to the value of $f_{2}$ . In order to include such a rule, we will need to modify the definition of testing set to be a propositional logic on PPEs (or PPE circuit, rather than conjunction of PPEs). Extending the notion of PPE testability would appear to make the Boyen-Waters IBE scheme [24] PPE Testable, as its private key contains two polynomials of the form $f_{1}(\\cdot)\\cdot u_{i}+f_{2}(\\cdot)$ , where $u_{i}$ is an unfixed variable and $f_{1}$ is a polynomial on fixed variables. After both of these $u_{i}$ variables are fixed (via some extended Rule2), all variables are fixed, and it seems feasible to test the remaining polynomials with respect to the trusted set. Developing the theory and logic for PPE Circuits is an exciting future direction.  \n\n# 6 Conclusion  \n\nThe ability to verify the well-formedness of a group of pairing elements (e.g., a private key) with respect to a set of trusted (e.g., public) parameters, by applying a set of pairing product equations, has numerous cryptographic applications. These include the design of basic and structure-preserving signature schemes, building oblivious transfer schemes from “blind” IBE, finding new verifiable random functions and keeping the IBE or ABE authority “accountable” to the user.  \n\nIn this work, we provided original observations demonstrating that it is not always easy for a human to determine whether or not a public-private parameter pair can be verified using a set of PPEs. Many IBE schemes (e.g., [22, 29, 20, 45]) have PPE-testable parameters, but some IBE schemes in the literature, such as those based on dual-system encryption [47], provably do not.  \n\nTo aid humans wishing to use PPE testability in their cryptographic design, we devised a set of rules for how to systematically search for a PPE testing set. We proved the correctness of this algorithm in Section 4.3 and provided an implementation of it, as AutoPPE, in Section 5. Tested on over two dozen schemes, the correctness and performance of the tool were solid. This allows researchers to move the discovery of PPE testing equations into the growing realm of cryptographic design tasks that can now be automated. This is one more important step in the larger goal of improving the speed and accuracy of the cryptographic design process via computer automation.  \n\n# Acknowledgments  \n\nThe authors are grateful to Brent Waters for valuable technical discussions and also to the anonymous reviewers of CCS 2019 for helpful feedback, especially for pointing out the connection to the automated analysis of structured-preserving signatures in [14].  \n\n# References  \n\n[1] Masayuki Abe, Melissa Chase, Bernardo David, Markulf Kohlweiss, Ryo Nishimaki, and Miyako Ohkubo. Constant-size structure-preserving signatures: Generic constructions and simple assumptions. Cryptology ePrint Archive, Report 2012/285, 2012. https://eprint.iacr.org/2012/285.   \n[2] Masayuki Abe, Jens Groth, Miyako Ohkubo, and Takeya Tango. Converting cryptographic schemes from symmetric to asymmetric bilinear groups. In Advances in Cryptology - CRYPTO, pages 241–260. Springer, 2014.   \n[3] Masayuki Abe, Fumitaka Hoshino, and Miyako Ohkubo. Design in type-i, run in type-iii: Fast and scalable bilinear-type conversion using integer programming. In Advances in Cryptology - CRYPTO, pages 387–415. Springer, 2016.   \n[4] Joseph A. Akinyele, Gilles Barthe, Benjamin Grégoire, Benedikt Schmidt, and Pierre-Yves Strub. Certified synthesis of efficient batch verifiers. In IEEE 27th Computer Security Foundations Symposium, pages 153–165. IEEE Computer Society, 2014.   \n[5] Joseph A. Akinyele, Christina Garman, and Susan Hohenberger. Automating fast and secure translations from Type-I to Type-III pairing schemes. In ACM SIGSAC Conference on Computer and Communications Security, pages 1370–1381. ACM, 2015.   \n[6] Joseph A. Akinyele, Matthew Green, and Susan Hohenberger. Using SMT solvers to automate design tasks for encryption and signature schemes. In ACM SIGSAC Conference on Computer and Communications Security, pages 399–410. ACM, 2013.   \n[7] Joseph A. Akinyele, Matthew Green, Susan Hohenberger, and Matthew W. Pagano. Machine-generated algorithms, proofs and software for the batch verification of digital signature schemes. In the ACM Conference on Computer and Communications Security, pages 474–487. ACM, 2012.   \n[8] Joseph A. Akinyele, Matthew Green, Susan Hohenberger, and Matthew W. Pagano. Machine-generated algorithms, proofs and software for the batch verification of digital signature schemes. Journal of Computer Security, 22(6):867–912, 2014.   \n[9] Miguel Ambrona, Gilles Barthe, Romain Gay, and Hoeteck Wee. Attribute-based encryption in the generic group model: Automated proofs and new constructions. In Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security, pages 647–664. ACM, 2017.   \n[10] Miguel Ambrona, Gilles Barthe, and Benedikt Schmidt. Automated unbounded analysis of cryptographic constructions in the generic group model. In Advances in Cryptology - EUROCRYPT, pages 822–851. Springer, 2016.   \n[11] Gilles Barthe, Juan Manuel Crespo, Yassine Lakhnech, and Benedikt Schmidt. Mind the gap: Modular machine-checked proofs of one-round key exchange protocols. In Advances in Cryptology - EUROCRYPT, pages 689–718. Springer, 2015.   \n[12] Gilles Barthe, Francois Dupressoir, Benjamin Gregoire, Alley Stoughton, and Pierre-Yves Strub. Easycrypt: Computer-aided cryptographic proofs, 2018. https://www.easycrypt.info/trac/.   \n[13] Gilles Barthe, Edvard Fagerholm, Dario Fiore, John C. Mitchell, Andre Scedrov, and Benedikt Schmidt. Automated analysis of cryptographic assumptions in generic group models. In Advances in Cryptology - CRYPTO, pages 95–112. Springer, 2014.   \n[14] Gilles Barthe, Edvard Fagerholm, Dario Fiore, Andre Scedrov, Benedikt Schmidt, and Mehdi Tibouchi. Strongly-optimal structure preserving signatures from type II pairings: Synthesis and lower bounds. In Public-Key Cryptography - PKC 2015, pages 355–376, 2015.   \n[15] Gilles Barthe, Xiong Fan, Joshua Gancher, Benjamin Gregoire, Charlie Jacomme, and Elaine Shi. Symbolic proofs for lattice-based cryptography. In Proceedings of the 2018 ACM SIGSAC Conference on Computer and Communications Security, CCS, pages 538–555. ACM, 2018.   \n[16] Gilles Barthe, Benjamin Grégoire, and Santiago Zanella Beguelin. Formal certification of code-based cryptographic proofs. In Proceedings of the 36th ACM SIGPLAN-SIGACT Symposium on Principles of Programming Languages, pages 90–101. ACM, 2009.   \n[17] Gilles Barthe, Benjamin Gregoire, and Benedikt Schmidt. Automated proofs of pairing-based cryptography. In Proceedings of the 22nd ACM SIGSAC Conference on Computer and Communications Security, pages 1156–1168. ACM, 2015.   \n[18] Bruno Blanchet. A computationally sound mechanized prover for security protocols. In 2006 IEEE Symposium on Security and Privacy, pages 140–154. IEEE Computer Society, 2006.   \n[19] Dan Boneh and Xavier Boyen. Efficient selective-id secure identity-based encryption without random oracles. In Advances in Cryptology - EUROCRYPT, pages 223–238. Springer, 2004.   \n[20] Dan Boneh and Xavier Boyen. Secure identity based encryption without random oracles. In CRYPTO, pages 443–459. Springer, 2004.   \n[21] Dan Boneh, Xavier Boyen, and Eu-Jin Goh. Hierarchical identity based encryption with constant size ciphertext. In Advances in Cryptology - EUROCRYPT 2005, pages 440–456, 2005.   \n[22] Dan Boneh and Matthew K. Franklin. Identity-based encryption from the weil pairing. In Advances in Cryptology - CRYPTO, pages 213–229. Springer, 2001.   \n[23] Dan Boneh, Ben Lynn, and Hovav Shacham. Short signatures from the weil pairing. In ASIACRYPT, pages 514–532. Springer, 2001.   \n[24] Xavier Boyen and Brent Waters. Anonymous hierarchical identity-based encryption (without random oracles). In Advances in Cryptology - CRYPTO, pages 290–307. Springer, 2006.   \n[25] Jan Camenisch and Anna Lysyanskaya. Signature schemes and anonymous credentials from bilinear maps. In Advances in Cryptology - CRYPTO, pages 56–72. Springer, 2004.   \n[26] Yevgeniy Dodis. Efficient construction of (distributed) verifiable random functions. In Public Key Cryptography - PKC, pages 1–17. Springer, 2003.   \n[27] Francois Le Gall. Powers of tensors and fast matrix multiplication. CoRR, abs/1401.7714, 2014.   \n[28] Craig Gentry. Practical identity-based encryption without random oracles. In Advances in Cryptology - EUROCRYPT, pages 445–464. Springer, 2006.   \n[29] Craig Gentry and Alice Silverberg. Hierarchical id-based cryptography. In Advances in Cryptology - ASIACRYPT, pages 548–566. Springer, 2002.   \n[30] Vipul Goyal. Reducing trust in the PKG in identity based cryptosystems. In Advances in Cryptology - CRYPTO, pages 430–447. Springer, 2007.   \n[31] Vipul Goyal, Steve Lu, Amit Sahai, and Brent Waters. Black-box accountable authority identity-based encryption. In Proceedings of the 2008 ACM Conference on Computer and Communications Security, pages 427–436. ACM, 2008.   \n[32] Matthew Green and Susan Hohenberger. Blind identity-based encryption and simulatable oblivious transfer. In Advances in Cryptology - ASIACRYPT, pages 265–282. Springer, 2007.   \n[33] Jens Groth and Amit Sahai. Efficient non-interactive proof systems for bilinear groups. In EUROCRYPT, pages 415–432. Springer, 2008.   \n[34] Florian Hess. Efficient identity based signature schemes based on pairings. In Selected Areas in Cryptography, pages 310–324. Springer, 2002.   \n[35] Viet Tung Hoang, Jonathan Katz, and Alex J. Malozemoff. Automated analysis and synthesis of authenticated encryption schemes. In Proceedings of the 22nd ACM SIGSAC Conference on Computer and Communications Security, pages 84–95. ACM, 2015.   \n[36] Susan Hohenberger and Brent Waters. Constructing verifiable random functions with large input spaces. In EUROCRYPT, pages 656–672. Springer, 2010.   \n[37] Tibor Jager. Verifiable random functions from weaker assumptions. In Theory of Cryptography - 12th Theory of Cryptography Conference, TCC, pages 121–143. Springer, 2015.   \n[38] Allison B. Lewko and Brent Waters. Unbounded HIBE and attribute-based encryption. In Advances in Cryptology - EUROCRYPT 2011, pages 547–567. Springer, 2011.   \n[39] Allison B. Lewko and Brent Waters. New proof methods for attribute-based encryption: Achieving full security through selective techniques. In CRYPTO, pages 180–198. Springer, 2012.   \n[40] Anna Lysyanskaya. Unique signatures and verifiable random functions from the DH-DDH separation. In Advances in Cryptology - CRYPTO, pages 597–612. Springer, 2002.   \n[41] Alex J. Malozemoff, Jonathan Katz, and Matthew D. Green. Automated analysis and synthesis of blockcipher modes of operation. In IEEE 27th Computer Security Foundations Symposium, pages 140–152. IEEE Computer Society, 2014.   \n[42] David Naccache. Secure and Practical identity-based encryption. IACR Cryptology ePrint Archive, 2005.   \n[43] Tatsuaki Okamoto and Katsuyuki Takashima. Fully secure unbounded inner-product and attributebased encryption. In Advances in Cryptology - ASIACRYPT, pages 349–366. Springer, 2012.   \n[44] Yannis Rouselakis and Brent Waters. Practical constructions and new proof methods for large universe attribute-based encryption. In 2013 ACM SIGSAC Conference on Computer and Communications Security, CCS, pages 463–474. ACM, 2013.   \n[45] Brent Waters. Efficient identity-based encryption without random oracles. In EUROCRYPT, pages 114–127. Springer, 2005.   \n[46] Brent Waters. Dual system encryption: Realizing fully secure ibe and hibe under simple assumptions. In CRYPTO, pages 619–636. Springer, 2009.   \n[47] Brent Waters. Dual system encryption: Realizing fully secure ibe and hibe under simple assumptions. In CRYPTO, pages 619–636. Springer, 2009.  \n\n# A Waters05 IBE Scheme  \n\nIn this section, we recall the public parameters and the private keys from the Waters05 IBE scheme with Naccache’s optimization [45, 42]. A part of the text has been taken verbatim from [42]. Let $\\mathbb{G}_{1}$ be a group of prime order $p$ , and $g$ be a group generator for $\\mathbb{G}_{1}$ . Let $e$ be an admissible bilinear map $e:\\mathbb{G}_{1}*\\mathbb{G}_{1}\\to\\mathbb{G}_{T}$ . Identities will be represented as $n$ dimensional vectors $v=(v_{1},\\ldots,v_{n})$ where each $v_{i}$ is an $\\it l$ -bit integer. The integers $n$ and $\\textit{l}$ are parameters unrelated to $p$ , and $n^{\\prime}=n\\cdot l$ is the output length of a collision-resistant hash function $H:\\{0,1\\}^{*}\\to\\{0,1\\}^{n^{\\prime}}$ .  \n\n${\\mathsf{S e t u p}}(1^{\\lambda})$ : Sample $\\alpha$ , $\\beta$ , $u$ and each element of an $n$ -dimensional vector $U=\\left(u_{i}\\right)$ uniformly at random from $\\mathbb{Z}_{p}$ . Set $g_{1}=g^{\\alpha}$ , $g_{2}=g^{\\beta}$ , $z=g^{u}$ and $z_{i}~=~g^{u_{i}}$ for each $i\\in[n]$ . The public parameters pk are $g,g_{1},g_{2},z,\\{z_{i}:i\\in[n]\\}$ . The master secret key msk is $g_{2}^{\\alpha}$ .  \n\n$\\mathsf{K e y G e n(m s k,\\boldsymbol{v})}$ : Let $v=(v_{1},\\ldots,v_{n})\\in(\\{0,1\\}^{\\iota})^{n}$ be an identity. Sample $r$ be uniformly at random in $\\mathbb{Z}_{p}$ . The private key $\\mathsf{s k}_{v}$ for identity $\\upsilon$ is constructed as  \n\n$$\n{\\sf s k}_{v}=\\Bigl(g_{2}^{\\alpha}\\cdot\\Bigl(z\\cdot\\prod_{i=1}^{n}z_{i}^{v_{i}}\\Bigr)^{r},g^{r}\\Bigr).\n$$  \n\n# B More Case Study Examples  \n\nIn this section, we present the PPEs output by the tool on few of the standard schemes.  \n\n# B.1 Boneh-Boyen 04a (BB1) HIBE scheme  \n\nIn this section, we recall the public parameters and the private keys of the BB1 HIBE [19] scheme.  \n\n$\\mathsf{S e t u p}(1^{\\lambda},1^{\\ell})$ : To generate system parameters for an HIBE of maximum depth $\\ell$ , select a random generator $g$ in $\\mathbb{G}_{1}$ , and random $\\alpha,x,h_{1},h_{2},...,h_{\\ell}\\gets\\mathbb{Z}_{p}$ , and set $g_{1}=g^{\\alpha},g_{2}=g^{x},H_{i}=g^{h_{i}}$ for all $i\\in[\\ell]$ . The public parameters and the master secret key are given by ${\\mathsf{p p}}=\\left(g,g_{1},g_{2},H_{1},\\ldots,H_{\\ell}\\right)$ and $\\mathrm{msk}=g_{2}^{\\alpha}$ .  \n\n$\\mathsf{K e y G e n}(\\mathsf{s k}_{\\mathsf{i d}|j-1},\\mathsf{i d})$ : To generate the private key $\\mathsf{s k}_{\\mathrm{id}}$ for an identity $\\mathsf{i d}=(\\mathsf{i d}_{1},\\ldots,\\mathsf{i d}_{j})\\in\\mathbb{Z}_{p}^{j}$ of depth $j\\leq\\ell$ . Pick random $r_{1},\\ldots,r_{j}\\gets\\mathbb{Z}_{p}$ and output  \n\n$$\n{\\sf s k}_{\\mathrm{id}}=\\Big(g_{2}^{\\alpha}\\cdot\\prod_{k=1}^{j}(g_{1}^{i d_{k}}\\cdot H_{k})^{r_{k}},g^{r_{1}},\\ldots,g^{r_{j}}\\Big).\n$$  \n\nNote that the private key for id can be generated just given a private key for id $|j-1=(\\mathsf{i d}_{1},\\ldots,\\mathsf{i d}_{j-1})\\in\\mathbb{Z}_{p}^{j-1}$ as required.  \n\nThe input file for the BB1 HIBE scheme when the maximum depth $\\ell=3$ is presented in Figure 8. The output by the tool is described in Figure 9.  \n\n#  \n\nInput File for BB1 HIBE $^{\\mathrm{*}}\\mathrm{BB1}$ HIBE scheme when number of levels is fixed to be $^{3^{*}}$ ) maps $\\mathrm{G}1^{\\ast}\\mathrm{G}1\\rightarrow\\mathrm{GT}$ . fixed vars [alpha, x, h1, h2, h3]. unfixed vars [r1, r2, r3]. trusted polys [F $_-$ alpha, F2=x, F3=h1, F4=h2, F5=h3] in G1. $\\mathrm{Zp}$ vars [id1, id2, id3]. untrusted polys [F6=x\\*alpha + (alpha\\*id1+h1)\\*r1 $^+$ (alpha\\*id2+h2)\\*r2 + (alpha\\*id3+h3)\\*r3, F7=r1, F8=r2, F9=r3] in G1.  \n\n![](/tmp/output/99_20250326144295/images/e08f7351445dea97ac7c6743f2202b3b76e10e996148c0788de72da066dc680e.jpg)  \n\n# B.2 Camenisch-Lysyanskaya Signature Scheme  \n\nIn this section, we recall the Camenisch-Lysyanskaya Signature Scheme B [25] signature scheme adapted to   \ntype-III pairings. We note that the original scheme described in the paper is in type-I setting. The setup   \nand signing procedures of the scheme proceeds as follows. ${\\mathsf{S e t u p}}(1^{\\lambda})$ : Select a random generator $g_{1}$ in group $\\mathbb{G}_{1}$ and $g_{2}$ in group $\\mathbb{G}_{2}$ . Sample random values   \n$x,y,z\\gets\\mathbb{Z}_{p}$ . Set $X=g_{1}^{x},Y=g_{1}^{y},Z=g_{1}^{z}$ . Set verification key and secret key as ${\\mathsf{v k}}=(g_{1},g_{2},X,Y,Z)$ and   \n${\\sf s k}=(x,y,z)$ . $\\mathtt{S i g n(v k,s k,m s g)}$ : Parse input message as $\\mathfrak{m s g}=(m,r)\\in\\mathbb{Z}_{p}^{2}$ , and secret key as ${\\sf s k}=(x,y,z)$ and   \nverification key as ${\\sf v k}=(g_{1},g_{2},X,Y,Z)$ . Sample a random $a\\leftarrow\\mathbb{Z}_{p}$ . Set $A=g_{2}^{a z}$ , $b=g_{2}^{a y}$ , $B=g_{2}^{a z y}$ ,   \n$c=g_{2}^{a x+a x y m+a z x y r}$ . Output signature $\\sigma=(g_{2}^{a},A,b,B,c)$ . The input file for CL04 Signature Scheme B [25] is presented in Figure 10. The output of the tool is   \npresented in Figure 11.  \n\n# B.3 Waters09 IBE Scheme  \n\nIn this section, we recall Waters09 HIBE scheme. The setup and key generation algorithms of the scheme proceeds as follows.  \n\n# Input File for CL04 Signature  \n\nmaps $\\mathrm{G}1^{\\ast}\\mathrm{G}2\\rightarrow\\mathrm{GT}$ .   \nfixed vars [x, y, z].   \nunfixed vars [a].   \n$\\mathrm{Zp}$ vars [m, r]. (\\*message\\*)   \ntrusted polys $[\\mathrm{F}1=\\mathrm{x}$ , $\\mathrm{F2}=\\mathrm{y}$ , $\\mathrm{F3}=\\mathrm{z}$ ] in G1. (\\*verification key\\*)   \nuntrusted polys $[\\mathrm{F4}=\\mathrm{a^{*}z}$ , $\\mathrm{F5}=\\mathrm{a^{*}y}$ , $\\mathrm{F6=a^{*}z^{*}y}$ , $\\mathrm{F}7=\\mathrm{a}^{*}(\\mathrm{x}+\\mathrm{x}^{*}\\mathrm{y}^{*}\\mathrm{m})+\\mathrm{a}^{*}\\mathrm{z}^{*}\\mathrm{x}^{*}\\mathrm{y}^{*}\\mathrm{r}$ , F8 = a] in G2. (\\*signature\\*)  \n\n# Figure 10: Input file for CL04 signature scheme B.  \n\n![](/tmp/output/99_20250326144295/images/b5f20f6e5c14dcdecc3c66d355c5ed4cefd0cac8e2d96ab0f4a8648fdf11b359.jpg)  \n\nFigure 11: Output of the tool for CL04 Signtature Scheme B.  \n\n${\\mathsf{S e t u p}}(1^{\\lambda})$ : Sample a random generator $g\\gets\\mathbb{G}_{1}$ , and then sample random elements $v,v_{1},v_{2},w,u,h,a_{1},a_{2},b,\\alpha\\gets$ $\\mathbb{Z}_{p}$ . Set $V=g^{v},V_{1}=g^{v_{1}},V_{2}=g^{v_{2}},W=g^{w},U=g^{u},H=g^{h},\\mathrm{T}_{1}=g^{v+v_{1}a_{1}},\\mathrm{T}_{2}=g^{v+v_{2}a_{2}}$ . It then public parameters  \n\n$$\n\\mathsf{p p}=(g^{b},g^{a_{1}},g^{a_{2}},g^{b\\cdot a_{1}},g^{b\\cdot a_{2}},\\mathrm{T_{1}},\\mathrm{T_{2}},\\mathrm{T_{1}^{b}},\\mathrm{T_{2}^{b}},W,U,H,e(g,g)^{\\alpha\\cdot a_{1}\\cdot b}).\n$$  \n\nIt sets master secret key $\\mathsf{m s k}=(g,g^{\\alpha},g^{\\alpha\\cdot a_{1}},V,V_{1},V_{2})$ . KeyGen(msk, id): Sample random elements $r_{1},r_{2},z_{1},z_{2},{\\tt t a g}_{k}\\gets\\mathbb{Z}_{p}$ . Let $r=r_{1}+r_{2}$ . Output secret key  \n\n$$\n\\begin{array}{r l}&{{\\sf s k_{i d}}=\\bigg(g^{\\alpha\\cdot a_{1}}\\cdot V^{r},g^{-\\alpha}V_{1}^{r}g^{z_{1}},(g^{b})^{-z_{1}},V_{2}^{r}g^{z_{2}},(g^{b})^{-z_{2}},g^{r_{2}b},g^{r_{1}}\\bigg.,}\\ &{~\\bigg.(U^{\\Y\\mathrm{id}}W^{\\sf t a g_{k}}H)^{r_{1}},{\\sf t a g}_{k}\\bigg).}\\end{array}\n$$  \n\nThe input file for Waters09 IBE scheme [46] is presented in Figure 12. The output by the tool is presented in Figure 14. Note that, this scheme is provably not PPE Testable, and hence our tool outputs unknown.  \n\n# Input File for Waters09 IBE  \n\nmaps G1 \\* G1 - $>$ GT.   \nfixed vars [a1, a2, b, alpha, $\\mathbf{v}$ , v1, v2, w, u, h].   \nunfixed vars [r1, r2, z1, z2].   \nZp vars [id, tag].   \ntrusted polys [F1 = b, $\\mathrm{F2}=\\mathrm{a1}$ , $\\mathrm{F3}=\\mathrm{a2}$ , $\\mathrm{F4}=\\mathrm{b^{*}a1}$ , $\\mathrm{F5=b^{*}a_{2}}$ , $\\mathrm{F6}=\\mathrm{v+v}1^{\\ast}\\mathrm{a}1$ , F7 = v+v2\\*a2, $\\mathrm{F}8=\\mathrm{b}^{*}(\\mathrm{v}{+}\\mathrm{v}1^{*}\\mathrm{a}1)$ , $\\mathrm{F}9=$ $\\mathbf{b}^{*}(\\mathbf{v}{+}\\mathbf{v}2^{*}\\mathbf{a}2)$ , $\\mathrm{F10=w}$ , $\\mathrm{{F11}=u}$ , $\\mathrm{F12=h}$ ] in G1.   \ntrusted polys $[\\mathrm{F13=alpha^{\\ast}a1^{\\ast}b}]$ in GT.   \nuntrusted polys $[\\mathrm{F14}=\\mathrm{alpha^{\\ast}a1+v^{\\ast}(r1+r2)}$ , $\\mathrm{F15=-alpha+v1^{*}(r1+r2)+z1}$ , $\\mathrm{F16=-b^{*}z1}$ , $\\mathrm{F17}=\\mathrm{v2^{*}(r1+r2)+z2}$ , $\\mathrm{F}18=$ - $\\mathbf{\\sigma}_{\\mathrm{b}}\\ast_{\\mathbf{z}2}$ , $\\mathrm{F19=r2^{*}b}$ , $\\mathrm{{F}20=r\\mathrm{{1}}}$ , $\\mathrm{F21}=(\\mathrm{u^{*}i d}+\\mathrm{w^{*}t a g}+\\mathrm{h})^{*}\\mathrm{r1}]$ in G1.  \n\nFigure 12: Input file for Waters09 IBE scheme.  \n\n#  \n\n<html><body><table><tr><td>InputFileforRW13CP-ABE</td></tr><tr><td>(*RouselakisWaters CP-ABEconstruction withk=4*)</td></tr><tr><td>maps G1 * G1->GT.</td></tr><tr><td>fixed-vars [u, h, w, v, alpha].</td></tr><tr><td>unfixed_vars [r,rl,r2, r3, r4].</td></tr><tr><td>(*public key*)</td></tr><tr><td>trusted-polys [F1 = u,F2 = h,F3 = w,F4 = v] in G1.</td></tr><tr><td>trusted_polys [F5 = alpha]in GT.</td></tr><tr><td>Zp-vars [a1,a2,a3,a4].(*attributes*) (*Secret key*)</td></tr><tr><td>untrusted-polys [F6 = alpha + w*r,F7 = r] in G1.</td></tr><tr><td>[=6-*+1*)=]so</td></tr><tr><td>untrusted-polys [F9 =(u*a2 + h)*r2 -v*r,F10 = r2] in G1.</td></tr><tr><td>untrusted-polys[F10 =(u*a3+ h)*r3-v*r,F11 = r3]in G1.</td></tr><tr><td>untrusted-polys[F12 =(u*a4 + h)*r4-v*r,F13 =r4]in G1.</td></tr></table></body></html>  \n\n# Figure 13: Input file for RW13 CP-ABE scheme.  \n\n# B.4 Rouselakis-Waters CP-ABE Scheme  \n\nIn this section, we recall Rouselakis-Waters CP-ABE scheme [44]. The setup and key generation algorithms of the scheme proceeds as follows.  \n\n${\\mathsf{S e t u p}}(1^{\\lambda})$ : The algorithm picks a random generator $g\\gets\\mathbb{G}_{1}$ , samples $u,h,w,v,\\alpha\\gets\\mathbb{Z}_{p}$ and sets $U=$ $g^{u},H=g^{h},W=g^{w},V=g^{v}$ . It outputs public parameters pp = (g, U, H $\\mathsf{p p}=(g,U,H,W,V,e(g,g)^{\\alpha})$ W, V, e(g, g) ) and msk = α . ${\\mathsf{K e y G e n}}(\\mathsf{m s k},S=\\{a_{1},a_{2},\\ldots,a_{k}\\}\\subseteq\\mathbb{Z}_{p})$ : Initially, the key generation algorithm picks $k+1$ random exponents $r,r_{1},r_{2},\\ldots,r_{k}\\gets\\mathbb{Z}_{p}$ . Then it computes $K_{0}=g^{\\alpha}\\cdot W^{r}$ , $K_{1}=g^{r}$ , and for every $i\\in[k]$ it computes $K_{i,2}=g^{r_{i}}$ and $K_{i,3}=(U^{a_{i}}H)^{r_{i}}V^{-r}$ . The secret key output is ${\\sf s k}=(S,K_{0},K_{1},\\{K_{i,2},K_{i,3}\\}\\forall i\\in[k])$ . The input file for Rouselakis-Waters CP-ABE scheme [44] when the number of attributes is fixed to be 4 is presented in Figure 13. The output by the tool is presented in Figure 15.  \n\n# Output of the tool for Waters09 IBE  \n\n$\\mathrm{F0}=\\mathrm{1}$ in G1 $\\mathrm{F0}=\\mathrm{1}$ in GT $\\mathrm{F}1=\\mathrm{b}$ in G1 $\\mathrm{F2}=\\mathrm{a1}$ in G1 $\\mathrm{F3}=\\mathrm{a2}$ in G1   \n$\\mathrm{F4}=\\mathrm{a1^{*}b}$ in G1 $\\mathrm{F5=a2^{*}b}$ in G1 $\\mathrm{F6=v+a1^{*}v1}$ in G1 $\\mathrm{F}7=\\mathrm{v}+\\mathrm{a}2^{\\ast}\\mathrm{v}2$ in G1   \n$\\mathrm{F8}=\\mathrm{b^{*}v}+\\mathrm{a1^{*}b^{*}v1}$ in G1 $\\mathrm{F9=b^{*}v+a2^{*}b^{*}v2}$ in G1   \n$\\mathrm{F10=w}$ in G1 $\\mathrm{F}11=\\mathrm{u}$ in G1 $\\mathrm{F12=h}$ in G1 $\\mathrm{F13=a1^{*}a l p h a^{*}b}$ in GT   \n$\\mathrm{{F14}=a1^{*}a l p h a+r1^{*}v+r2^{*}v}$ in G1 $\\mathrm{{F15}=-\\mathrm{{1^{*}a l p h a+z1+r\\mathrm{{1^{*}v1+r\\mathrm{{2^{*}v1}}}}}}}$ in G1   \n$\\mathrm{F16=-1^{*}b^{*}z1}$ in G1 $\\mathrm{F17=z2+r1^{*}v2+r2^{*}v2}$ in G1 $\\mathrm{F18=-1^{*}b^{*}z2}$ in G1   \n$\\mathrm{F19=b^{*}r2}$ in G1 $\\mathrm{F}20=\\mathrm{r}1$ in G1 $\\mathrm{F21}=\\mathrm{h^{*}r1}+\\mathrm{id^{*}r1^{*}u}+\\mathrm{r}\\mathrm{1^{*}t a g^{*}w}$ in G1   \n$\\mathrm{F22}=\\mathrm{id}$ in G1 $\\mathrm{F23}=\\mathrm{tag~in~G1}$ $\\mathrm{F24}=\\mathrm{id}$ in GT $\\mathrm{F25=tag}$ in GT   \n$\\mathrm{F26}=\\mathrm{b}^{\\ast}\\mathrm{id}$ in G1 $\\mathrm{F27}=\\mathrm{b^{*}t a g}$ in G1 $\\mathrm{F28=a1^{*}i d}$ in G1 $\\mathrm{F29}=\\mathrm{a1^{*}t a g}$ in G1   \n$\\mathrm{F30=a2^{*}i d}$ in G1 $\\mathrm{F31}=\\mathrm{a2^{*}t a g}$ in G1 $\\mathrm{F32}=\\mathrm{a1^{*}b^{*}i d}$ in G1   \n$\\mathrm{F33}=\\mathrm{a1^{*}b^{*}t a g}$ in G1 $\\mathrm{F34=a2^{*}b^{*}i d}$ in G1 $\\mathrm{F35=a2^{*}b^{*}t a g}$ in G1   \n$\\mathrm{F36}=\\mathrm{id^{*}v}+\\mathrm{a1^{*}i d^{*}v1}$ in G1 $\\mathrm{F37}=\\mathrm{tag^{*}v}+\\mathrm{a1^{*}t a g^{*}v1}$ in G1   \n$\\mathrm{F38=id^{*}v+a2^{*}i d^{*}v2}$ in G1 $\\mathrm{F39=tag^{*}v+a2^{*}t a g^{*}v2}$ in G1   \n$\\mathrm{F40=b^{*}i d^{*}v+a\\mathbf{1}^{*}b^{*}i d^{*}v\\mathbf{1}}$ in G1 $\\mathrm{F41=b^{*}t a g^{*}v+a1^{*}b^{*}t a g^{*}v1}$ in G1   \n$\\mathrm{F42=b^{*}i d^{*}v+a2^{*}b^{*}i d^{*}v2}$ in G1 $\\mathrm{F43=b^{*}t a g^{*}v+a2^{*}b^{*}t a g^{*}v2}$ in G1   \n$\\mathrm{F44}=\\mathrm{id^{*}w}$ in G1 $\\mathrm{F45=tag^{*}w}$ in G1 $\\mathrm{F46}=\\mathrm{id^{*}u}$ in G1 $\\mathrm{F47}=\\mathrm{tag^{*}u}$ in G1   \n$\\mathrm{F48=h^{*}i d}$ in G1 $\\mathrm{F49}=\\mathrm{h^{\\ast}t a g}$ in G1 $\\mathrm{F50=a1^{*}a l p h a^{*}b^{*}i d}$ in GT $\\mathrm{F51=a1^{*}a l p h a^{*}b^{*}t a g}$ in GT   \nProcessing untrusted polynomial $\\mathrm{F16=-1^{*}b^{*}z1}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F18=-1^{*}b^{*}z2}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F19=b^{*}r2}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F20=r1}$ by rule2   \nF20 moved to trusted set and r1 moved to fixed set by rule 2   \nProcessing untrusted polynomial $\\mathrm{{F14}=a1^{*}a l p h a+r1^{*}v+r2^{*}v}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F16=-1^{*}b^{*}z1}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F18=-1^{*}b^{*}z2}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F19=b^{*}r2}$ by rule2 Rule not applied   \nProcessing untrusted polynomial F21 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F}21,\\mathrm{F}0)=\\mathrm{e}(\\mathrm{F}12,\\mathrm{F}20)*\\mathrm{e}(\\mathrm{F}20,\\mathrm{F}45)*\\mathrm{e}(\\mathrm{F}20,\\mathrm{F}46)$   \nOptimized PPE $\\mathrm{e(F21,F0)=e(F12^{*}F45^{*}F46,F20)}$   \nF21 moved to trusted set by rule 1   \nProcessing untrusted polynomial $\\mathrm{{F14}=a1^{*}a l p h a+r1^{*}v+r2^{*}v}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F16=-1^{*}b^{*}z1}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F18=-1^{*}b^{*}z2}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F19=b^{*}r2}$ by rule2 Rule not applied   \nExecution time $:$ 2.644071s   \nUntrusted set : F14, F15, F16, F17, F18, F19   \nPPE $\\mathrm{~\\ensuremath{'s}~:~e(F21,F0)=e(F12^{*}F45^{*}F46,F20)}$   \nOutput : Unknown  \n\nFigure 14: Output of the tool for Waters09 IBE scheme.  \n\n# Output of the tool for RW13 CP-ABE  \n\n$\\mathrm{F}(\\mathrm{)=1}$ in G1 $\\mathrm{F0}=\\mathrm{1}$ in GT $\\mathrm{F}1=\\mathrm{u}$ in G1 $\\mathrm{F2}=\\mathrm{h}$ in G1 $\\mathrm{F3}=\\mathrm{w}$ in G1 $\\mathrm{F4}=\\mathrm{v}$ in G1   \n$\\mathrm{F}5=$ alpha in GT F6 = alpha + r\\*w in G1 $\\mathrm{F}7=\\mathrm{r}$ in G1   \n$\\mathrm{F8}=\\mathrm{h^{*}r1}-\\mathrm{r^{*}v}+\\mathrm{a1^{*}r1^{*}u}$ in G1 $\\mathrm{F}9=\\mathrm{r}1$ in G1 $\\mathrm{F}10=\\mathrm{h}^{*}\\mathrm{r}2\\textrm{-}\\mathrm{r}^{*}\\mathrm{v}+\\mathrm{a}2^{*}\\mathrm{r}2^{*}\\mathrm{u}$ in G1 $\\mathrm{F}11=\\mathrm{r}2$ in G1   \n$\\mathrm{F12}=\\mathrm{h^{*}r3}-\\mathrm{r^{*}v}+\\mathrm{a3^{*}r3^{*}u}$ in G1 $\\mathrm{F}13=\\mathrm{r}3$ in G1 $\\mathrm{F}14=\\mathrm{h}^{*}\\mathrm{r}4-\\mathrm{r}^{*}\\mathrm{v}+\\mathrm{a}4^{*}\\mathrm{r}4^{*}\\mathrm{u}$ in G1 $\\mathrm{F15=r4}$ in G1   \n${\\mathrm{F}}16={\\mathrm{a}}1$ in G1 $\\mathrm{F17=a2}$ in G1 $\\mathrm{F18=a3}$ in G1 $\\mathrm{F19=a4}$ in G1   \n$\\mathrm{F20=a1}$ in GT $\\mathrm{{F}21=a2}$ in GT $\\mathrm{F22=a3}$ in GT $\\mathrm{F23}=\\mathrm{a4}$ in GT   \n$\\mathrm{F24=a1^{*}u}$ in G1 $\\mathrm{F25=a2^{*}u}$ in G1 $\\mathrm{F26=a3^{*}u}$ in G1 $\\mathrm{F27=a4^{*}u}$ in G1   \n$\\mathrm{F28=a1^{*}h}$ in G1 $\\mathrm{F29=a2^{*}h}$ in G1 $\\mathrm{F30=a3^{*}h}$ in G1 $\\mathrm{F31}=\\mathrm{a4^{*}h}$ in G1   \n$\\mathrm{F32=a1^{*}w}$ in G1 $\\mathrm{F33=a2^{*}w}$ in G1 $\\mathrm{F34=a3^{*}\\mathrm{\\Delta}}$ w in G1 $\\mathrm{F35=a4^{*}w}$ in G1   \n${\\mathrm{F}}36={\\mathrm{a}}1^{*}{\\mathrm{v}}$ in G1 $\\mathrm{F37=a2^{*}v}$ in G1 $\\mathrm{F38=a3^{*}v}$ in G1 $\\mathrm{F39}=\\mathrm{a4^{*}v}$ in G1   \n$\\mathrm{F40=a1^{*}}$ alpha in GT $\\mathrm{F41}=\\mathrm{a2}^{\\ast}$ alpha in GT $\\mathrm{F42=a3^{*}}$ alpha in GT $\\mathrm{F43}=\\mathrm{a4^{*}}$ alpha in GT   \nProcessing untrusted polynomial $\\mathrm{{F6}=a l p h a+r^{*}w}$ by rule2. Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F}7=\\mathrm{r}$ by rule2   \nF7 moved to trusted set and r moved to fixed set by rule 2   \nProcessing untrusted polynomial $\\mathrm{F8}=\\mathrm{h^{*}r1-r^{*}v+a1^{*}r1^{*}u}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F}9=\\mathrm{r}1$ by rule2   \nF9 moved to trusted set and r1 moved to fixed set by rule 2   \nProcessing untrusted polynomial $\\mathrm{F}10=\\mathrm{h}^{*}\\mathrm{r}2\\cdot\\mathrm{r}^{*}\\mathrm{v}+\\mathrm{a}2^{*}\\mathrm{r}2^{*}\\mathrm{u}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{{F}11=r2}$ by rule2   \nF11 moved to trusted set and r2 moved to fixed set by rule 2   \nProcessing untrusted polynomial $\\mathrm{F12}=\\mathrm{h^{*}r3}-\\mathrm{r^{*}v}+\\mathrm{a3^{*}r3^{*}u}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F13}=\\mathrm{r3}$ by rule2   \nF13 moved to trusted set and r3 moved to fixed set by rule 2   \nProcessing untrusted polynomial $\\mathrm{F14}=\\mathrm{h^{*}r4}-\\mathrm{r^{*}v}+\\mathrm{a4^{*}r4^{*}u}$ by rule2 Rule not applied   \nProcessing untrusted polynomial $\\mathrm{F15=r4}$ by rule2   \nF15 moved to trusted set and $_{\\mathrm{r4}}$ moved to fixed set by rule 2   \nProcessing untrusted polynomial F6 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F6},\\mathrm{F0})=\\mathrm{F5~^{*}~}\\mathrm{e}(\\mathrm{F3},\\mathrm{F7})$   \nOptimized PPE $\\mathrm{e}(\\mathrm{F6},\\mathrm{F0})=\\mathrm{F5^{\\ast}e}(\\mathrm{F3},\\mathrm{F7})$   \nF6 moved to trusted set by rule 1   \nProcessing untrusted polynomial F8 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F8},\\mathrm{F0})=\\mathrm{e}(\\mathrm{F2},\\mathrm{F9})~^{\\ast}~(\\mathrm{e}(\\mathrm{F4},\\mathrm{F7}))\\hat{\\mathrm{\\Omega}}-1~^{\\ast}~\\mathrm{e}(\\mathrm{F9},\\mathrm{F24})$   \nOptimized PPE $\\mathbf{e}(\\mathrm{F8},\\mathrm{F0})=(\\mathbf{e}(\\mathrm{F4},\\mathrm{F7}))\\hat{\\mathbf{\\xi}}-\\mathbf{1}^{*}\\mathbf{e}(\\mathrm{F2}^{*}\\mathrm{F}24,\\mathrm{F9})$   \nF8 moved to trusted set by rule 1   \nProcessing untrusted polynomial F10 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F}10,\\mathrm{F}0)=\\mathrm{e}(\\mathrm{F}2,\\mathrm{F}11)^{\\ast}(\\mathrm{e}(\\mathrm{F}4,\\mathrm{F}7))\\hat{\\textbf{\\i}}1^{\\ast}\\mathrm{e}(\\mathrm{F}11,\\mathrm{F}25)$   \nOptimized PPE $\\mathrm{e}(\\mathrm{F}10,\\mathrm{F}0)=(\\mathrm{e}(\\mathrm{F}4,\\mathrm{F}7))\\hat{\\Omega}-1^{\\ast}\\mathrm{e}(\\mathrm{F}2^{\\ast}\\mathrm{F}25,\\mathrm{F}11)$   \nF10 moved to trusted set by rule 1   \nProcessing untrusted polynomial F12 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F}12,\\mathrm{F}0)=\\mathrm{e}(\\mathrm{F}2,\\mathrm{F}13)^{\\ast}(\\mathrm{e}(\\mathrm{F}4,\\mathrm{F}7))\\widehat{\\mathrm{\\Omega}}_{-1}\\mathrm{~\\ast~\\mathrm{e}(\\mathrm{F}13,\\mathrm{F}26)~}$   \nOptimized PPE $\\mathbf{e}(\\mathrm{F}12,\\mathrm{F}0)=(\\mathbf{e}(\\mathrm{F}4,\\mathrm{F}7))\\hat{\\mathbf{\\xi}}_{-1}{*}_{\\mathrm{e}}(\\mathrm{F}2^{*}\\mathrm{F}26,\\mathrm{F}13)$   \nF12 moved to trusted set by rule 1   \nProcessing untrusted polynomial F14 by rule1   \nNaive PPE $\\mathrm{e}(\\mathrm{F}14,\\mathrm{F}0)=\\mathrm{e}(\\mathrm{F}2,\\mathrm{F}15)~^{\\ast}~(\\mathrm{e}(\\mathrm{F}4,\\mathrm{F}7))\\hat{\\mathrm{\\Omega}}_{-1}~^{\\ast}~\\mathrm{e}(\\mathrm{F}15,\\mathrm{F}27)$   \nOptimized PPE $\\operatorname{ze}(\\operatorname{F14,F0})=(\\operatorname{e}(\\operatorname{F4,F7}))\\widehat{\\cdot}-1^{*}\\operatorname{e}(\\operatorname{F2^{*}F27,F15})$   \nF14 moved to trusted set by rule 1   \nExecution time : 12.837395s   \nPPEs : $\\mathbf{e}(\\mathrm{F}14,\\mathrm{F}0)=(\\mathbf{e}(\\mathrm{F}4,\\mathrm{F}7))\\hat{\\mathbf{\\xi}}-\\mathbf{1}^{*}\\mathbf{e}(\\mathrm{F}2^{*}\\mathrm{F}27,\\mathrm{F}15)$ , $\\mathrm{{e(F12,F0)=(\\mathrm{{e(F4,F7))\\hat{\\Pi}-1^{*}e(F2^{*}F26,F13),~e(\\bar{\\Pi}}}}}$ F10,F0) = (e(F4,F7))ˆ-   \n$1^{*}\\mathrm{e}(\\mathrm{F}2^{*}\\mathrm{F}25,\\mathrm{F}11)$ , $\\mathbf{e}(\\mathrm{F8},\\mathrm{F0})=(\\mathbf{e}(\\mathrm{F4},\\mathrm{F7}))\\hat{\\mathbf{\\xi}}-\\mathbf{1}^{*}\\mathbf{e}(\\mathrm{F2}^{*}\\mathrm{F}24,\\mathrm{F9})$ , e(F6,F0) = F5\\*e(F3,F7)  \n\nOuptut : PPE Testable :)  "}