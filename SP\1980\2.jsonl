{"text": "# ONE-TIME PADS ARE KEY SAFEGUARDING SCHEMES, NOT CRYPTOSYSTEMS. FAST KEY SAFEGUARDING SCHEMES (THRESHOLD SCHEMES) EXIST.  \n\nG. R. Blakley  \n\nDepartment of Mathematics TexasA&M University Co1lege Station, Tx 77843  \n\n# ABSTRACT  \n\nCommon sense, <PERSON> [KA67] and <PERSON> [BR79] all argue that there are no unbreakable cryptosystems. What, then, is to be made of the -- provably [D179a, Pp. 399-400] unbreakable -- Vernam one-time pad? The somewhat surprising answer is that it is not a cryptosystem at all, but rather a key safeguarding scheme [BL79] used, as all such schemes can be, in the courier mode. This suggests that proofs of invulnerability of key safeguarding schemes, what <PERSON><PERSON> [SH79] calls threshold schemes, are as natural as proofs of difficulty of breaking cryptosystems are unnatural (perhaps impossible). Indeed, such an approach sets the Vernam one-time pad securely into context. Both the projective geometric threshold scheme [BL79] and the Lagrange interpolation threshold scheme [SH79] profit from being generalized from the field of integers modulo some prime P. to arbitrary Galois fields. In particular, their computer implementations are particularly  \n\nfelicitous in some fields with $2^{\\tt n}$ elements.  \n\nNeither the longevity nor the loyalty of sentinels can be taken completely for granted, as Juvenal's question [RA69, p. 110]  \n\nsuggests. So the proverbial \"prudent man\" who wishes to have an important secret k (which wil1 be called a key) kept by sentinels should determine a nonnegative integer a, the abnegation number, which exceeds any likely number of sentinel deaths during the time the secret is to be kept.He should then determine a nonnegative integer b, the betrayal number,which exceeds any likely number of sentinel disloyalties during the time the secret is to be kept. He should then figure out a way to make $\\texttt{a+b+1}$ messages, called shadows of the key, and to give one of these shadows to each of a + b + 1 sentinels. His procedure should have the property that if any $\\texttt{b+1}$ sentinels get together to compare shadows, they should be able to reconstruct the key easily. But if only b sentinels get together the information possessed by b sentinels should suggest every possible key (every secret the prudent man might conceivably know) with approximately equal probability.  \n\nA threshold scheme T to conceal keys belonging a keyset K despite a abnegations and b betrayals has three parts: a concealer relation; a revealer function; and a proof of security. In mathematical terminology let K be a finite set, let a and b be nonnegative integers, and let $\\textbf{T}=\\textbf{T}(\\mathtt{K},\\mathtt{a},\\mathtt{b})$ be such a threshold scheme.Then $\\textbf{T}={\\bf\\Phi}({\\bf c},{\\bf r},{\\bf p})$ is a list of three things.  \n\nThe concealer c is a one-to-many relation $(\\overrightarrow{2}\\cdot\\overrightarrow{e}$ . set of ordered pairs). The first entry of any ordered pair belonging tocis a member $\\textbf{k}$ of K.  The second entry is a list  \n\n$$\n\\mathtt{s}=(\\mathtt{S}(1),\\mathtt{S}(2),\\dots,\\mathtt{S}(a+\\mathtt{b}+1))\n$$  \n\nof $\\texttt{a+b+1}$ pairwise distinct mathematical objects, called shadows of $\\textbf{k}$ . The revealer r is a function whose domain consists of all lists of $\\texttt{b+1}$ pairwise distinct shadows of memberskof $\\mathtt{\\textbf{K}}$ . The revealer function $\\pmb{\\tau}$ has the property that  \n\n$$\n\\mathbf{r}((\\mathbf{S}(\\mathbf{i}(1)),\\mathbf{S}(\\mathbf{i}(2)),\\ldots,\\mathbf{S}(\\mathbf{i}(\\mathbf{b}\\mathbf{+}1))))=\\mathbf{k}\n$$  \n\nwhenever  \n\n$$\n\\left(\\mathbf{S}\\left(\\mathbf{i}\\left(1\\right)\\right),\\mathbf{S}\\left(\\mathbf{i}\\left(2\\right)\\right),\\ldots,\\mathbf{S}\\left(\\mathbf{i}\\left(\\mathbf{b}\\mathbf{+}1\\right)\\right)\\right)\n$$  \n\nis a sublist of a list  \n\n$$\n\\mathtt{s}=(\\mathtt{S}(\\mathtt{1}),\\mathtt{S}(\\mathtt{2}),\\dots,\\mathtt{S}(\\mathtt{a}+\\mathtt{b}+\\mathtt{1}))\n$$  \n\nwhich is the second entry of an ordered pair $(\\pmb{\\mathrm{k}},\\pmb{\\mathrm{s}})$ belonging to the concealer c. The proof is to.the following effect.Let  \n\n$$\n\\mathbf{t}~=~\\left(\\mathbf{S}\\left(1\\right),\\mathbf{S}\\left(2\\right),\\ldots,\\mathbf{S}\\left(\\mathbf{b}\\right)\\right)\n$$  \n\nbe any list of b pairwise distinct shadows of members kof $\\pmb{\\mathrm{\\bfK}}$ .‘Let $\\pmb{\\mathrm{k}}^{\\star}$ and $\\mathbf{k}^{\\star\\star}$ be members of K. Then there are almost as many shadows $\\mathtt{s}^{\\star}$ of $\\mathbf{k}^{\\star}$ which have t as a sublist as there are shadows $\\mathtt{s}^{**}$ of $\\mathbf{k}^{\\star\\star}$ which have t as a sublist.  \n\nTo repeat the idea now, the concealer is a device which produces vastly many different lists of $\\texttt{a+b+1}$ pairwise distinct shadows for each key $\\textbf{k}$ . The revealer is a device which unerringly reconstructs a key k if it is given b $\\yen1$ pairwise distinct entries of any list of $\\texttt{a+b+1}$ shadows of $\\textbf{k}$ . The proof states that knowledge of only b entries of a list of shadows of a key k is worthless in a very strong sense. Every key ${\\bf k}^{\\ast}$ in the keyset K has as many (well, almost as many) lists of shadows containing those b shadows as the key k does. In summary, it is easy to reconstruct k from b $^\\mathrm{~\\bf~+~}\\mathsf{\\textbf{1}}$ of its shadows and impossible to reconstruct k from b of its shadows.  \n\nTo implement a threshold scheme it suffices to decide on the. value of a, then decide on the value of b, then construct the concealer relation C wisely, given the nature of the keyset K. After that one selects at random a key $\\textbf{k}\\in\\textbf{K}$ andmakes a random choice of a pair $(\\mathbf{k},\\mathbf{s})$ belonging to c. The shadows on the list  \n\n$$\n\\mathtt{s}=\\left(\\mathtt{S}(1),\\mathtt{S}(2),\\dots,\\mathtt{S}(\\mathtt{a}+\\mathtt{b}+1)\\right)\n$$  \n\nare distributed to ${\\textbf{a+b+1}}$ sentinels, each of whom is charged with guarding his shadow. At this juncture it is possible to retire and let r and $\\mathfrak{p}$ do the work of reconstruction when enough $(2,e)$ at least $\\mathbf{b}+\\mathbf{1})$ shadows are available, or proving Security when too few (i.e. no more than b) are.  \n\n# 2.Examples of threshold schemes.  \n\nAre there any threshold schemes? Yes, some of great antiquity.If $\\mathfrak{b}=0$ then merely let  \n\n$$\n{\\bf s}({\\bf i})={\\bf k}\n$$  \n\nfor every positive integer $\\texttt{i\\le a+b+1=a+1}$ In other words let every one of the8 $\\downarrow+\\downarrow$ shadows of the key k be a copy of $\\textbf{k}$ . Let every sentinel have a key, in other words, because the prudent man anticipates no betrayals. The proof in this case is perfectly straightforward. No list of 0 shadows of k gives any information about k because no list of 0 things contains information. The proof is rigorous, though vacuous. This is the world's most widely employed threshold schemes. Every member of the household has a copy of the key because the prudent man acts as if no betrayals will occur.  \n\nThe other extreme occurs when the prudent man believes that no abnegations will occur, i.e. that no sentinel's key shadow will be destroyed for as long as the key is valuable. This is the case $\\mathbf{a}=0$ , and a threshold scheme for this case is now over sixty years old [KA67, pp. 394-403]. Let  \n\n$$\n\\texttt{K}=\\{0,1,2,3,\\ldots,2^{\\mathbb{N}}-\\mathbf{1}\\}\n$$  \n\nfor some positive integer $\\aleph$ , and call K the set of all $\\aleph\\cdot$ -bit numbers. The prudent man chooses the key k from $\\pmb{\\mathrm{\\Tilde{K}}}$ in a manner he thinks to be mysterious enough to appear random to other people. He writes $\\mathbf{k}$ as an N-bit word. He chooses b other members S(l),...,S(b) of K in a way which also seems to him to appear random to others. He lets  \n\n$$\n{\\sf S}\\left({\\sf b}+1\\right)={\\sf S}\\left(1\\right)~{\\sf X O R}~{\\sf S}\\left(2\\right)~{\\sf X O R}~\\ldots~{\\sf X O R}~{\\sf S}\\left({\\sf b}\\right)~{\\sf X O R}~{\\sf k},\n$$  \n\nwhere xoR is bitwise exclusive or without carry. Itfollowsthat  \n\nHe distributes the shadows ${\\mathfrak{s}}(1),{\\mathfrak{s}}(2),\\ldots,{\\mathfrak{s}}({\\mathfrak{b}}+1)$ tob $^{\\mathrm{~\\scriptsize~+~1~}}$ sentinels. Suppose that only b sentinels try to reconstruct $\\mathbf{k}$ .Since the exclusive or operator xoR is associative and commutative it follows that we can assume the shadows are  \n\n$$\n{\\sf S}\\left(2\\right),\\ldots,{\\sf S}\\left({\\sf b}\\right),{\\sf S}\\left({\\sf b}+1\\right)\n$$  \n\nThe sentinels can produce  \n\n$$\n{\\tt Y}={\\tt S}\\left(2\\right)~{\\tt X O R}~{\\tt S}\\left(3\\right)~{\\tt X O R}~\\ldots~{\\tt X O R}~{\\tt S}\\left({\\tt b}+1\\right).\n$$  \n\nBut S(l)was chosen in a pseudorandom manner. Hence no information on  \n\n$$\n\\texttt{k}=\\texttt{Y X O R S}(\\texttt{l})\n$$  \n\nis available. This a = 0 example is important, and will be discussed again, especially the simplestsubcase ${\\mathfrak{a}}=0$ $\\texttt{b}=\\texttt{1}$  \n\nHow does the prudent man act if both a and $\\mathfrak{d}$ are positive? In other words what threshold schemes are available to somebody who acknowledges that sentinels both betray and die?  \n\nThe first one [BL79] is geometrical. A sum-- mary of its workings is as follows. Let $\\mathfrak{q}$ be a $\\textbf{b+1}$ dimensional projective space over the field $\\mathbf{z}/(\\mathbf{p})\\mathbf{\\Sigma}=\\mathbf{GF}(\\mathbf{p})$ of integers modulo some large prime $\\mathfrak{P}$ aboutthesame sizeasthelargest memberofa keyset $\\texttt{K}=\\{0,1,2,\\ldots,2^{\\mathrm{N}}-1\\}$ . There is a natural way to insert almost every key $\\textbf{k}\\in\\textbf{K}$ into a projective point $\\mathbf{q}$ of $\\mathfrak{q}$ in such a fashion that it can easily be retrieved from $\\mathbf{q}$ .If a key $\\textbf{k}$ has been inserted into a projective point $\\textbf{\\textsf{q}}\\in{\\textsf{q}}$ in this fashion then the shadows (namelybdimensional projective subspaces)  \n\n$$\n{\\tt S(1)},{\\tt S(2)},\\ldots,{\\tt S(a+b+1)}\n$$  \n\nare produced in the following manner.For each positive integer i，a\"randomly chosen\"b dimensional projective subspace S(i) of $\\mathfrak{q}$ containing qis produced. It follows from the random orientation of thebdimensional projective subspaces ${\\mathfrak{s}}(1),{\\mathfrak{s}}(2),\\ldots,{\\mathfrak{s}}({\\mathfrak{a}}+{\\mathfrak{b}}+1)$ of the $\\mathbf{b}+1$ dimen-- sional projective space Q (and this can be guaranteed by appropriate vigilance in choosing such subspaces)that everycollection of $\\mathbf{b}+\\mathbf{\\lambda}1$ ofthem intersect only in the point q of $\\mathfrak{Q}$ from which k can easily be extracted. It also follows that every collection of b or fewer of them give rise to an intersection (itself also a projective subspace H of Q) which is at least as big as a projective line and which, in consequence of the way in which keys are inserted into points, is hopelessly ambiguous as to what the key k is. More exactly, this projective subspace H is such that every member k of K is approximately as likely as every other member $\\mathbf{\\epsilon}_{\\mathbf{k}}\\star$ f $\\mathbf{\\deltaK}$ to have generated it.  \n\nThe second one [SH79], due to A. Shamir, is algebraic. It makes use of a bth degree polynomial function  f  from the field ${\\tt G F}(\\mathfrak{p})$ of integers modulo a large prime p to itself. Each of the shadows S(i)is an ordered pair  \n\n$$\n\\left(\\mathbf{a}(\\mathbf{\\1}),\\mathbf{f}\\left(\\mathbf{a}(\\mathbf{i})\\right)\\right)\\in\\mathsf{G F}\\left(\\mathfrak{p}\\right)\\times\\mathsf{G F}\\left(\\mathfrak{p}\\right).\n$$  \n\nNo two first entries coincide and no first entry is zero.The key is $\\textbf{k}=\\textbf{f}(0)$ . If somebody has $\\texttt{b+1}$ shadows he knows the values of 、f at $\\textbf{b+1}$ places and can infer f (whence f(o))from the Lagrange interpolation formula [H07l, pp. 124-126]. But if he has only b shadows then, for every ${\\textbf{g}}\\in{\\textbf{G F}}({\\textbf{p}})$ , there is exactly one bth degree polynomial function f with these shadows and with ${\\mathfrak{f}}(0)~=~{\\mathfrak{g}}$ .‘It follows that no information whatever is shed on f(o) by these shadows.  \n\nSecurity proofs for geometric or interpolation threshold schemes do not depend on the structure of the underlying field. Shamir has noted that, in a sense, they do not depend on its size either. A 1000 bit key k  can be shared among ${\\texttt{a+b+1}}$ guards with equal security by giving each guard a 1000 bit shadow of k from a threshold scheme over $\\mathtt{G E}(2^{1000})$ , or by giving each guard a list of 125 byte size shadows from 125 successive applications of a thresho1d scheme over GF(256) to the successive bytes of $\\mathbf{\\epsilon}_{{\\bf k}}$ ，or even by giving each guard a list of 1000 single bit shadows from 1000  \n\nimplementations of a threshold scheme over GF(2) to the successive bits of k. On practical grounds the first and last extremes will usually be rejectedinfavorofrepeateduseofathreshold  \n\nschemein $\\mathtt{G F}(2^{\\mathtt{N}})$ for some choice of N appropriate to the problem at hand.Section 4 below addressesthis in more detail.  \n\n# 3.Threshold schemes in courier mode  \n\nWhen shown the idea of threshold schemes R.D. Dixon[D179b] pointed out that they could be operated in courier mode to transmit secret information with proven security. His idea was as follows. Both messages and keys are usually numbers, strings of bits in fact.So let the message m be treated like a key. Let $\\mathbf{n}=\\mathbf{k}$ in a threshold scheme. Then dispatch a succession of courierstotheintended recipient.The first courier carries the first shadow, S(l). The second carries S(2). And so on. By choosing b large the sender can guard against disloyalty $(i,e,$ betrayal to the opposition of the contents of his shadow) on the part of a few couriers. Some messages will be destroyed, so a must also be fairly large. But the couriers can be surveilled and, if it looks as though too many shadows are being revealed to the opposition, the dispatches can cease before $\\mathbf{b}+1$ disloyal couriers have left.In this case the process is merely restarted with a new implementation of the threshold scheme. If the process is not stopped this way the receiver can signal, in the clear, when $\\mathbf{b}+\\mathbf{1}$ shadows have arrived. At this point he reconstitutes the message and the sender stops dispatching couriers.Note that some of the shadows can be sent over public channels, but not all of them.  \n\nThe price paid for securityis that each shadow is about the size of the message. This is the basic distinction between a cryptosystem and a threshold scheme in courier mode. The former requires that the secret key information be conveyed by courier.After that a volume of encrypted traffic much larger than the key can be sent over public channels. The latter requires that, for every message, at least one shadow (which is as long as the message) be sent by courier. Sometimes several shadows must go by courier.  \n\nVarious types of couriers are possible. One promising idea is to have a courier be a brief laser beam transmission along a line of sight between two stations or a short spread spectrum transmission. A few betrayals (i.e. interceptions) need not compromise a transmission if the value of b in the threshold scheme has been chosen large enough. In a 1ike vein, there is no harm in a few garbled or jammed transmissions if ais chosen large enough. Laser transmissions, coupled with threshold schemes, may usher in an era of transmissions by nonbroadcast but nevertheless wireless means. In such a milieu message security can sometimes be absolutely assured without recourse to conventional cryptography. The militar; implications of such a communications environment might be significant, effecting a decline in the fortunes of cryptanalysis. This would reverse a trend [KA67, pp. 298-299] dating back to the beginnings of the broadcast environment spawned by radio in the last century.  \n\nStill another type of courier is a roll of magnetic tape. Extensive and important corporate records can be broken into shadows and each shadow written on such a courier.The couriers (tapes) can be dispersed and stored in smallsafes whichat least report incursions when they are not strong enough to resist them.This is at once less expensive and more secure than some current practices whichrelyinsteadon duplicatedmassive vaults requiring multiple keys to open.It suffices to chooseblarge enough to exceed any likely number of safecrackings over the period of storage and to chooseaso large that it exceeds the number of likely tape losses due to fire or other disasters which might affect asafeor itscontents.  \n\nVernam's one-time pad [D179a,P. 399-400] is not a cryptosystem at all, but a threshold scheme in courier mode.Indeed it is obviously the subcase $\\widehat{\\sf a}=0$ $\\mathbf{b}~=~\\mathbf{1}$ ofthe secondthreshold scheme in Section 2 above.The reasons why this escaped notice for so long are many. The concept of threshold scheme seems not to have been formulated much before 1979.Also the \"courier\" in the one-time pad scheme was almcst invisible.  The sender and the receiver each carry a copy of the pad $(i,e$ .the shadow S(l)) with them for a long time before the messagekis composed and the transmission  \n\n$$\n{\\bf S}(2)={\\bf k}\\tt{X O R}{\\bf S}(1)\n$$  \n\nis sent over public channels.It comes as a shock to realize that one of them was the courier, carrying S(l)to himself.  \n\nThereorientationof thought implicit in this description is well worth it. With the defection of the one-time pad from the ranks of cryptosystems to those of threshold schemes, we see more starkly that no cryptosystems are provably secure. Whether one feels. that this is the nature of the beast and that Brassard's [BR79] result is a harbinger of things to come, or one hopes for some types of progress in provable security, one must in any case be pleased that a misleading analogy has been cleared away andthat the one-time padhas movedout of its anomalous position into a context in which it makes sense.  \n\n4. Threshold schemes in $\\mathtt{G E}(2^{\\mathtt{n}})$ are better than threshold schemes in GF(p),where p is an odd prime.  \n\nBoth the projective geometric and the Lagrange interpolation threshold scheme can be speeded up  \n\nmarkedly by implementation in $\\mathtt{G F}(2^{\\mathtt{N}})$ for many[Z168] choices of n, namely those $\\pmb{\\uppi}$ such that  \n\n$$\n\\mathtt{p}(\\mathbf{x})=\\mathbf{x}^{\\mathtt{n}}+\\mathbf{x}+\\mathtt{1}\n$$  \n\nis irreducible over $\\mathtt{G F}(2)$  \n\nFor suchnthe members of $\\mathtt{G F}(2^{\\mathtt{n}})$ canbe viewed as polynomials under ordinary polynomial addition, and under ordinary $(i,e$ Cauchy [KN54, Pp. 147-148]) multiplication fo1lowed by reduction in degree, to degree $\\texttt{n-1}$ or less, modulo  \n\n$$\n{\\begin{array}{l}{\\mathbf{a}~=~\\mathbf{a}(0)~+~\\mathbf{a}(1)\\mathbf{x}~+~\\ldots~+~\\mathbf{a}({\\bar{n}}{-}1)\\mathbf{x}^{{\\bar{n}}-1}}\\ {\\mathbf{b}~=~\\mathbf{b}(0)~+~\\mathbf{b}(1)\\mathbf{x}~+~\\ldots~+~\\mathbf{b}({\\bar{n}}{-}1)\\mathbf{x}^{{\\bar{n}}-1}}\\end{array}}\n$$  \n\nAnother way to view polynomials with coefficients equal to zero or one is as computer words. Thus there is no harm in writing  \n\n$$\n\\begin{array}{r l}{\\mathtt{a}=}&{\\left(\\mathtt{a}(0),\\mathtt{a}(1),\\mathtt{,...,a}(\\mathtt{n}\\mathrm{-1})\\right)}\\ {\\mathtt{b}=}&{\\left(\\mathtt{b}(0),\\mathtt{b}(1),\\mathtt{,...,b}(\\mathtt{n}\\mathrm{-1})\\right).}\\end{array}\n$$  \n\nThen  \n\n$$\n\\begin{array}{r l}{\\mathbf a+\\mathbf b=\\{\\mathbf a(0)}&{\\oplus\\mathbf b(0)\\}+\\{\\mathbf a(1)\\oplus\\mathbf b(1)\\}\\mathbf x}\\ {+\\mathbf\\alpha\\dots+\\{\\mathbf a(\\mathbf n-1)\\mathbf\\alpha\\oplus\\mathbf b(\\mathbf n-1)\\}\\mathbf x^{\\mathbf n-1}}\\ {=}&{\\mathbf{(a(0)}\\oplus\\mathbf b(0),\\mathbf a(1)\\oplus\\mathbf b(1),\\dots,\\mathbf a(\\mathbf n)\\oplus\\mathbf b(\\mathbf n))}\\end{array}\n$$  \n\nwhere the sums $\\oplus$ are modulo 2addition in GF(2). If the product  \n\n$$\n{\\bf a}\\star{\\bf b}={\\sf d}(0)+{\\sf d}(1){\\bf x}+\\ldots+{\\sf d}({\\bf n}{-}1){\\bf x}^{{\\bf n}{-}1}\n$$  \n\nis evaluated in ${\\mathfrak{G F}}(2^{\\bar{\\mathfrak{n}}})$ . then the usual Cauchy mul  \n\n$$\n\\begin{array}{r l}&{\\mathrm{tellsizet~to~cond~contate~int}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{tellsit~the~to~contate}}\\ &{\\quad+\\mathrm{t e l l s i t~t h e~t o~c o n\n$$  \n\nwhere the fundamental reduction modulo $\\mathtt{p}(\\mathbf{x})$ leads readily to the definitions  \n\n$$\n\\begin{array}{l}{{\\tt S(n,0,a,b)~=~0}}\\ {{\\tt S(n,i,a,b)~=~a(1)~\\wedge~b(n-1)~\\oplus~a(1+1)~\\wedge~b(n-2)~\\cdots~}}\\ {{\\tt S a(n-1)~\\wedge~b(1)}}\\ {{\\tt S(n,n,a,b)~=~0}}\\ {{\\tt C(j,a,b)~=~a(0)~\\wedge~b(j)~\\oplus~a(1)~\\wedge~b(j-1)~\\oplus~\\cdots~}}\\ {{\\tt\\Phi~a(i)~\\wedge~b(0)~}}\\end{array}\n$$  \n\nfor every $\\textrm{\\scriptsize f}\\{0,1,\\ldots,\\mathtt{n-1}\\}$ and every $\\texttt{i}\\in\\{1,2,\\ldots,\\mathtt{n}{-}1\\}$ .\"A11 $\\oplus$ sums are modulo 2 addition in GF(2)and al1 $\\wedge$ products are modulo 2multiplication in GF(2). In other words $\\oplus$ is the logical ecclusive or operation xoR acting on bits, and $\\wedge$ is the logical and operation AND acting on bits.  \n\nA simple example is GF(4). In this case ifwelet  \n\n$$\n\\begin{array}{r c l}{\\overline{{0}}~=~}&{(0,0)}\\ {\\overline{{1}}~=~}&{(1,0)}\\ {\\omega~{=}~}&{(0,1)}\\ {\\overline{{\\omega}}~{=}~}&{(1,1)}\\end{array}\n$$  \n\nwe find that the sum and product rules  \n\n$$\n{\\begin{array}{r l}{\\left(\\mathbf{a}\\left(1\\right),\\mathbf{a}\\left(2\\right)\\right)}&{+\\left(\\mathbf{b}\\left(1\\right),\\mathbf{b}\\left(2\\right)\\right)}&{=}\\ &{=\\left(\\mathbf{a}\\left(1\\right)\\quad\\oplus\\mathbf{b}\\left(1\\right),\\mathbf{a}\\left(2\\right)\\cdot\\mathbf{\\dot{\\sigma}}\\oplus\\mathbf{b}\\left(2\\right)\\right)}\\ &{\\left(\\mathbf{a}\\left(1\\right),\\mathbf{a}\\left(2\\right)\\right)\\quad\\star\\left(\\mathbf{b}\\left(1\\right),\\mathbf{b}\\left(2\\right)\\right)=}\\ &{={\\begin{array}{r l}{\\left(\\mathbf{a}\\left(0\\right)\\wedge\\mathbf{b}\\left(0\\right)\\quad\\oplus\\mathbf{\\dot{\\sigma}}\\left(\\mathbf{a}\\left(1\\right),\\mathbf{\\dot{\\sigma}}\\right)}&{}\\ {\\mathbf{a}\\left(0\\right)\\wedge\\mathbf{b}\\left(1\\right)\\quad\\oplus\\mathbf{a}\\left(1\\right)\\wedge\\mathbf{b}\\left(0\\right)\\quad\\oplus\\mathbf{a}\\left(1\\right)\\wedge\\mathbf{b}\\left(1\\right)\\oplus0}\\end{array}}\\right)}\\ &{={\\begin{array}{r l}{\\left(\\mathbf{a}\\left(0\\right)\\wedge\\mathbf{b}\\left(0\\right)\\quad\\oplus\\mathbf{a}\\left(1\\right)\\wedge\\mathbf{b}\\left(1\\right),}\\ {\\mathbf{a}\\left(0\\right)\\wedge\\mathbf{b}\\left(1\\right)\\quad\\oplus\\mathbf{a}\\left(1\\right)\\wedge\\mathbf{b}\\left(0\\right)\\quad\\oplus\\mathbf{a}\\left(1\\right)\\wedge\\mathbf{b}\\left(1\\right)\\right)}\\end{array}}}\\end{array}}\n$$  \n\ngive rise to the composition tables  \n\n<html><body><table><tr><td>+</td><td></td><td>1</td><td>m</td><td>一 3</td></tr><tr><td>0</td><td></td><td>1</td><td>m</td><td>m</td></tr><tr><td>1</td><td>1</td><td>0</td><td>一 m</td><td>m</td></tr><tr><td>3</td><td>m</td><td>m</td><td>0</td><td>1</td></tr><tr><td>← m</td><td>m</td><td>m</td><td>1</td><td>0</td></tr><tr><td>* 1</td><td>0</td><td>1</td><td>W</td><td>一</td></tr><tr><td>10</td><td></td><td>0</td><td>0</td><td></td></tr><tr><td>-1</td><td>0</td><td>1</td><td>m</td><td>m</td></tr><tr><td>m</td><td></td><td>m</td><td>m</td><td>1</td></tr><tr><td>一 m</td><td>10</td><td>m</td><td>1</td><td>m</td></tr></table></body></html>  \n\nThey are readily interpreted as ordinary complex multiplication, modulo 2 complex addition, and the $\\scriptstyle2z={\\overline{{0}}}$ 1  \n\nwhere $\\omega=-\\overline{{{1}}}/2+(3/2)\\mathbf{i}$ is a cube root of unity with complex conjugate $\\overline{{\\omega}}$  \n\nThe advantage of the foregoing representation is now clear. Every C and every S is merely a Cauchy sum or a shifted Cauchy sum in the field GF(2) of 2 elements. It can thereforebeevaluated in a few quick steps on a computer.  \n\n1. Fetch the word ${\\bf a}={\\bf\\ \\ {a(0),a(1),\\dots,a(n-1)\\Big)}}.$   \n2. Fetch the word $\\b{\\triangleright}=\\bigl(\\b{\\triangleright}\\left(0\\right),\\b{\\triangleright}\\left(1\\right),\\ldots,\\b{\\triangleright}\\left(\\b{\\triangleright}-\\b{1}\\right)\\bigr)$   \n3. From b form the reversed word $\\mathbf{r}_{\\mathbf{b}}~=~\\left(\\mathtt{b}\\left(\\mathtt{n}{-}1\\right),\\mathtt{b}\\left(\\mathtt{n}{-}2\\right),\\mathtt{\\ldots},\\mathtt{b}\\left(1\\right),\\mathtt{b}\\left(0\\right)\\right)$   \n4. Perform an i position right shift on the wordrbto get the word $\\mathtt{s r b}=\\left(0,0,\\ldots,0,\\mathtt{b}\\left(\\mathtt{n}-1\\right),\\mathtt{b}\\left(\\mathtt{n}-2\\right),\\mathtt{c}\\ldots,\\mathtt{b}\\left(\\mathtt{i}\\right)\\right).$   \n5.Formtheword $\\begin{array}{r l r l}{{\\bf\\lambda^{A N D}}}&{{\\sf s r b}}&{=}&{\\left(0,0,\\ldots,0,\\mathbf{a}(\\mathbf{i})\\wedge\\mathbf{b}\\left(\\mathbf{n}-\\mathbf{1}\\right),\\right.}\\ &{}&{\\left.\\mathbf{a}\\left(\\mathbf{i}+\\mathbf{1}\\right)\\wedge\\mathbf{b}\\left(\\mathbf{n}-2\\right),\\ldots,\\mathbf{a}\\left(\\mathbf{n}-\\mathbf{1}\\right)\\wedge\\mathbf{b}\\left(\\mathbf{i}\\right)\\right)}\\end{array}$ by taking a logical and.   \n6. Form the bit ${\\begin{array}{r l}{\\sum\\left(\\mathbf{a}\\mathrm{\\boldmath~AND~}\\mathbf{s}\\mathbf{r}\\mathbf{b}\\right)}&{=\\mathbf{a}\\left(\\mathbf{i}\\right)\\wedge\\mathbf{b}\\left(\\mathbf{n}-\\mathbf{1}\\right)\\oplus\\mathbf{a}\\left(\\mathbf{i}+\\mathbf{1}\\right)\\wedge\\mathbf{b}\\left(\\mathbf{n}-2\\right)\\oplus}\\ &{\\qquad\\cdot\\cdot\\mathbf{\\nabla}\\oplus\\mathbf{a}\\left(\\mathbf{n}-\\mathbf{1}\\right)\\wedge\\mathbf{b}\\left(\\mathbf{i}\\right)}\\end{array}}$  \n\nby iterated modulo 2 addition (i.e. iterated logical ecclusive or) of the bits of aANDsrb.  \n\nThese are fast operations. Obviously the  \n\n$\\mathtt{G F}(2^{\\mathtt{n}})$ Su1m  \n\n$$\n\\begin{array}{r l}{\\mathbf{a}+\\mathbf{b}=}&{}\\ {=}&{\\left(\\mathbf{a}\\left(0\\right)\\quad\\oplus\\mathbf{b}\\left(0\\right),\\mathbf{a}\\left(1\\right)\\quad\\oplus\\mathbf{b}\\left(1\\right),\\ldots,\\mathbf{a}\\left(\\mathbf{n}\\mathbf{-1}\\right)\\quad\\oplus\\mathbf{b}\\left(\\mathbf{n}\\mathbf{-1}\\right)\\right)}\\end{array}\n$$  \n\nis formed (even faster than the ${\\mathfrak{G F}}(2^{\\mathfrak{n}})$ product) by forming the 1ogical ecclusive or operation a XoR b on the words a and b.It is easy to verifythat $\\mathtt{G F}(2^{\\mathtt{n}})$ multiplications are much faster than multiplications modulo a large prime, even with the speedup now possible [BL8o] by virtue of elimination of the long division from modular multiplication. If a \"fetch word reversed\" machine operation is available the multiply timein  \n\n$\\mathtt{G F}(2^{\\mathtt{n}})$ can be cut further.  \n\nSubtraction coincides with addition in $\\mathtt{G F}(2^{\\mathtt{n}})$ since  \n\n$$\n{\\textbf{a}}\\oplus{\\textbf{b}}\\oplus{\\textbf{b}}={\\textbf{a}}\n$$  \n\nfor anya, $\\texttt{b}\\in\\texttt{G F}(2^{\\bar{\\texttt{n}}})$ . But division does not coincide with multiplication. Hence it is important to know how long it takes to calculate reciprocals in ${\\mathfrak{G F}}(2^{\\mathfrak{n}})$  \n\nThe multiplicative group of nonzero elements $\\mathtt{G F}(2^{\\mathtt{n}})$ is known [PA66, p. 256] to be a cyclic group of order $2^{\\tt n}\\mathrm{~-~}1$ . It follows from Lagrange's Theorem [PA66, P. 104] that  \n\n$$\n\\mathbf{a}^{2^{\\bar{\\mathbf{n}}}-1}=\\mathbf{\\lambda_{1}}\n$$  \n\nfor every nonzero a $\\textsf{\\in G F}(2^{\\mathfrak{n}})$ . Consequently it is true that  \n\n$$\n\\mathbf{a}^{-1}=\\mathbf{a}^{2^{\\mathrm{n}}-2}=(\\mathbf{a}^{2})^{2^{\\mathrm{n-1}}-1} \n$$  \n\nfor every nonzero a $\\mathtt{G F}(2^{\\mathtt{n}})$ .  This circle of ideas is exactly as strong as Fermat's Theorem [LE56, $\\mathbb{P}\\cdot42]$ .In fact, it should probably be called Fermat's Theorem. For prime $\\pmb{\\mathbb{p}}$ near $2^{\\tt n}$ there are approximately as many multiplications necessary to invert an element of $\\mathtt{G F}(\\mathtt{p})$ as to invert an element of $\\mathtt{G F}(2^{\\mathtt{n}})$ , when the Fermat's Theorem approach is used. The interesting question is whether. ${\\mathfrak{G F}}(2^{\\bar{\\mathfrak{n}}})$ admits of faster computer reciprocation schemes when $\\pmb{\\uppi}$ is a positive integer and the field $\\mathtt{G F}(2^{\\mathtt{n}})$ can be represented as polynomials modulo $\\texttt{p}(\\mathbf{x})=\\textbf{x}^{\\mathbf{n}}+\\textbf{x}+\\textbf{1}$ : It may be possible to extend work of G. Davida [DA72] to cover this case.  \n\nhave produced a fast implementation of GF(2127) arithmetic along the lines indicated above [BE79]. The exponent 127 is one of 21 positive integer exponents n less than l000 such that $\\mathtt{p}(\\mathbf{x})$ is irreducible [ZI68] over GF(2).  \n\n# 5. The fate of the prudent man.  \n\nThere is no use producing_ a threshold scheme if the prudent man of Section l knows the key. He has to go. But not violently. A layoff will suffice. His prudent machine replacement will. be a device which takes inputs a and b and which, after generating a key k by some satisfactorily pseudorandom process, produces the shadows  \n\n$$\n{\\mathfrak{s}}(1),{\\mathfrak{s}}(2),\\ldots,{\\mathfrak{s}}({\\mathfrak{a}}+{\\mathfrak{b}}+1)\n$$  \n\naccording to some threshold scheme.It then forgets k and waits a specified short period of time to cast the shadows upon (i.e. transfer shadow information to) other devices, the sentinels. It should have counters telling how often it has cast each type of shadow during that short period of time. After that period has elapsed it should also forget all shadows. Its counters should be checked to verify that it has only cast one copy of each type of shadow before forgetfulness set in. Perhaps it is desirable to have some plug compatibility scheme, which implies as many types of sentinels as of shadows(namely $\\texttt{a+b+1)}$ ，andtomake each sentinel impervious to further inputs from the time a shadow is cast upon it until it is reinitialized by some manufacturer-approved reset mechanism. Obviously casting a shadow should be done in a way believed to be private. Optical, rather than electrical or acoustical, coupling between devices is thus desirable because of the possibility of telltale radiation during the latter processes.  \n\nSentinels can then go to whatever encrypt/decrypt devices are used and, in turn, cast their shadows upon them. Again, counters and optical coupling are desirable.  \n\nUnder some circumstances (such as when a threshold scheme is in courier mode and some shadows are transmitted over public channels) sen-- tinels should be able to cast their shadows in human-readable form. But, under others, sentinels should only be able to cast their shadows into encrypt/decrypt devices.  \n\nOn the other hand, the prudent machine delegated the task of key and shadow generation, as well as any encrypt/decrypt unit, should be incapable of divulging shadow or key information (apart from the necessity of having the prudent machine cast one copy of each shadow upon some sentinel.) Sentinels, unlike the prudent machine, should be made to remember shadows indefinitely until some manufacturer-approved reset mechanism reinitializes them sothat another shadow can be cast upon them.  \n\nThe idea behind this scheme is that no person or group of people, however plausible their credentials,should ever be able to ascertain the key except, of course, when the threshold scheme is in courier mode and the key is a message. It should be impossible even to get a copy of the key into a given encrypt/decrypt unit without having, over a period of time, gained at least temporary access to at least b + 1 sentinels or the shadows they carry. Hence an archive capable of reviewing mes-- sage traffic in a given key k might sometimes have to retain at least b + 1 of the sentinels carrying shadows of $\\textbf{k}$  \n\nAt any rate the old business principle [KR79, $\\mathbf{p}\\cdot\\mathbf{\\nabla}^{801}$ that procedures must be arranged so that theft must involve collusion has been carried far here. b + 1 individuals must cooperate to produce keys or emplace them in an encrypt/decrypt head. Obviously the probability of such cooperation being in aid of legitimate purposes rises as b rises.  \n\n# 6.Recent developments. Acknowledgement.  \n\nFinally,it is worth noting that earlier this month the author has received a. remarkable paper [AS8o] by C. Asmuth and J. Bloom.  It propounds a new threshold scheme faster than the projective geometric and the Lagrange interpolation threshold scheme.It subsumes the latter into a family of diverse threshold schemes of varying kinds. Perhaps more importantly, their paper is the first to give a solution to the shoddy shadow problem -- formulated during a discussion involving Asmuth, D. Hensley and the author in his 1980 graduate cryptology course.The shoddy shadow problem can be propounded as follows.  \n\nWhen some couriers report their shadows incorrectly,whether by accident or design, the key reconstruction problem becomes bothersome.Asmuth and Bloom note that -- in the case $\\mathbf{\\hat{b}}=19$ ? $\\textbf{a}=\\textbf{10}\\--$ the probability of choosing a collection of $\\mathbf{b}+\\mathbf{1}=20$ correct shadows out of 30 is less than 0.0004 when 6 shadows are known to be incorrectly reported. They show how to weed out defective shadows in advance of an attempt to reconstruct a key.  \n\nThe author's research was supported in part by NSF Grant MCS7908516.  \n\n# REFERENCES  \n\nAS80 C. Asmuth and J. Bloom, A modular approach to key safeguarding. Preprint. Department of Mathematics, Texas A&M University (1980).  \n\nBE79 S. Berkovits, J. Kowalchuk and B. Schanning, Implementing public key scheme, IEEE Communications Magazine, Vol. 17, no. 3, May (1979), Pp. 2-3.  \n\nBL79 G. R. Blakley, Safeguarding cryptographic keys, Proceedings of the National Computer Conference, 1979, American Federation of Information Processing Societies -- Conference Proceedings; Vo1. 48 (1979)，PP. 313-317.  \n\nBL80 G. R. Blakley, A computer algorithm for calculating the product AB modulo M, Preprint. Department of Mathematics, Texas A&M University (1980).  \n\nBR79 G. Brassard, A note on the complexity of cryptography, IEEE Transactions on Information Theory, Vo1.IT-256 (1979), Pp. 232-233.  \n\nDA72 G.I. Davida, Inverse elements of a Galois field, Electronics Letters, Vo1.8,no. 21,19 0ctober (1972).  \n\nDI79a W. Diffie and M. Hellman, Privacy and authentication: an introduction to cryptography, Proceedings of the IEEE, Vo1. 67 (1979),Pp. 397-427.  \n\nDI79b R. D. Dixon, Private communication (1979).  \n\nH07l K. Hoffman and R. Kunze, Linear Algebra, Second Edition, Prentice-Hall, Englewood Cliffs, New Jersey (1971).  \n\nKA67D. Kahn, The Codebreakers, MacMi1lan, New York (1967).  \n\nKN54K. KnopP, Theory and Application of Infinite Series, Blackie and Son, London (1954).  \n\nKR79 L. I. Krauss and A. MacGahan, Computer Fraud and Countermeasures, Prentice-Hall, Englewood Cliffs, New Jersey (1979).  \n\nLE56 W. J.LeVeque, Topics in Number Theory, Volume 1, Addison-Wesley, Reading, Massachusetts (1956),  \n\nPA66 H. Paley and P. M. Weichsel, A first course in Abstract Algebra, Holt, Rinehart and Winston, New York (1966).  \n\nRA69 G.G.Ramsey (translator), Juvenal and Persius, Loeb Classical Library, Volume 91, Harvard University Press (1969).  \n\nSH79 A. Shamir, How to share a secret, Communications of the ACM, Vol. 22 (1979), Pp. 612-613.  \n\nZI68 N. Zierler and J. Brillhart, On primitive trinomials (mod 2), Information and Control, Vol. 13 (1968),PP. 541-554.  "}