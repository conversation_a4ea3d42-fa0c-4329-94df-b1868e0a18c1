{"text": "# The Latent\t\r  Community\t\r  Model\t\r  for Detec6ng\t\r  Sybil\t\r  A;a<PERSON>,\t\r  <PERSON> {zc7,\t\r  cmj4}\t\r  @ rice.edu  \n\n# Outline  \n\nSybil\t\r  aFack  \n\nI\t\r  I  \n\nCurrent\t\r  soluIons  \n\nOur approach  \n\n# Sybil AFack  \n\nAn\t\r   aFacker\t\r   creates\t\r   mulIple\t\r   fake idenIIes (Sybils)\t\r  to\t\r  gain\t\r  influence\t\r  in\t\r  the\t\r  open\t\r  system  \n\n# Examples  \n\nRecommendaIon\t\r  system,\t\r  i.e.,\t\r  Drugstore   \nEmail\t\r  system,\t\r  (spam\t\r  email)   \nWeb\t\r  spam   \nDistributed\t\r  Hash\t\r  Table\t\r  (DHT)   \nCommunicaIon\t\r  System\t\r  (Tor)  \n\n# Rice University: A Campus Tour  \n\nNigelCoxworth 41 videos Subscribe  \n\n# WALDEN UNIVERSITY  \n\n![](/tmp/output/8_20250327021589/images/d6f4aef3afecd1db8884c2d8a76ecaf68e18a5c593ec40134e9bf1b93b60c2f8.jpg)  \n\n![](/tmp/output/8_20250327021589/images/596962aaf0169f83870422d38906a1a8e3a809dff21c53c419b414844008fcd5.jpg)  \n\nCalling All Engineers  \n\n![](/tmp/output/8_20250327021589/images/b6949bad87db54180e1518d2b167d8fb036b8b13b415af8344ef6044b6d3a3f6.jpg)  \n\nRice University Virtual Tour  \n\n![](/tmp/output/8_20250327021589/images/7980d70efc92d9eb15bc094fd9c69b413c41870c7a8ae63704f5a53889c3b5d6.jpg)  \n\nRice University Tips and Tricks -Are Grades Rea...  \n\n![](/tmp/output/8_20250327021589/images/07e7edc1206ca6ebb65e8d956db0312035a185f8ac366b043699f6f2a1e7967a.jpg)  \n\nRice University - Orientation Week (O-Week) Out...  \n\n![](/tmp/output/8_20250327021589/images/4ce5dd53a4bd791c480de8b1591f5925329c5df104bae2ceedcd93606a63f849.jpg)  \n\n# Uploader Comments (NigelCoxworth)  \n\nVery nice video. Well put together - very professional. If you are professional that is not intended as insult I am merely ignorant. At homecoming 2006 I was a senior. It was a particularly poignant year for me full of hope, wonder, fear, regret, and sorrow. It was a struggle to hold back my tears watching my Alma mater portrayed so delicately.Thanks.-Paul Hanszen Colleqe 2007  \n\nRice Student Association  \n\n![](/tmp/output/8_20250327021589/images/3ddfa1a811141ac68e64fff3f85145160398268ee82bd25bfa1ad942c98986ec.jpg)  \n\n# Outline  \n\nSybil\t\r  aFack  \n\nCurrent\t\r  soluIons  \n\nOur\t\r  approach  \n\n# Current SoluIons  \n\nPrevenIon  \n\nDetecIon  \n\n# SoluIons PrevenIon  \n\nChallenge/Response\t\r  Mechanisms  \n\nComputaIonal\t\r  Puzzle\t\r  or\t\r  CAPTCHA  \n\nCredenIals  \n\nE.g.,\t\r  social\t\r  security\t\r  number,\t\r  driver\t\r  license, banking\t\r  account  \n\n# SoluIons /DetecIon  \n\nTrust/ReputaIon  \n\nAmazon’s\t\r  seller\t\r  raIng\t\r  system be\t\r  subjected\t\r  to\t\r  Whitewashing\t\r  aFack IP/IP-­‐Clustered be\t\r  subjected\t\r  to\t\r  Botnet Machine\t\r  Learning –  Features\t\r  like\t\r  invitaIon\t\r  frequency,\t\r  requests,\t\r  etc PageRank\t\r  and\t\r  HITS –  Trusted\t\r  pages  \n\n# Using Social networks  \n\n![](/tmp/output/8_20250327021589/images/f07f0d555901c2df98d45b3254426af658c8f8e70d8cbcbd1d2c4ca5fd2fad3c.jpg)  \n\nBoFleneck\t\r  Cut Fast\t\r  Mixing Property  \n\nSybilGuard\t\r  [SIGCOMM’06],\t\r  SybilLimit\t\r  [Oakland’08], SybilInfer\t\r  [NDSS’09],\t\r  SumUp\t\r  [NSDI’09], DSybil [ Oakland ‘09],  GateKeeper [Infocom’ 10]  \n\n# Outline  \n\nSybil\t\r  aFack  \n\nCurrent\t\r  soluIons  \n\nOur approach  \n\n# Our Approach  \n\nRather\t\r   than\t\r   assuming\t\r   the\t\r   forms\t\r   of\t\r   aFacks,\t\r   we instead\t\r   learn\t\r   a\t\r   staIsIcal\t\r   generaIve\t\r   model for the underlying network, called the latent community\t\r  (LC)\t\r  model.  \n\n# What\t\r  is\t\r  GeneraIve Model\t\r  ?  \n\ngenera6ve\t\r   model is\t\r   a\t\r   model\t\r   that\t\r   describes the\t\r   sequence\t\r   of\t\r   distribuIons\t\r   that generates our\t\r  observable\t\r  data.  \n\n# Example  \n\n# Regression  \n\n$y\\sim N o r m(a*x+b,\\sigma^{2})$  \n\ny: income,\t\r  x\t\r  :\t\r  age Given\t\r  an\t\r  x,\t\r  you\t\r  can\t\r  esImate: V and\t\r  its\t\r  probability  \n\n![](/tmp/output/8_20250327021589/images/92676c9f405bb8e7411a88d2eb1f2c1ea5627743880200ad31b90af9d633aaea.jpg)  \n\n# What\t\r  is\t\r  LC\t\r  Model?  \n\nLC\t\r  is\t\r  a\t\r  StaIsIcal generaIve\t\r  model  \n\nNode 一 Edge Community Latent\t\r  posiIons  \n\n![](/tmp/output/8_20250327021589/images/9c9931a2dc5cbb875bea9d04f8a5b73ec85b5959e0ec61c148897efeb01f222f.jpg)  \n\n# LC model  \n\nBayesian\t\r  network\t\r  of\t\r  LC model  \n\n–  π:\t\r  the\t\r  fracIon\t\r  of\t\r  nodes\t\r  in each\t\r  community. δ:\t\r  the\t\r  internal\t\r  edge\t\r  density   \n–  µ:\t\r  the\t\r  posiIons\t\r  in\t\r  Euclidean space.   \n–  c:\t\r  the\t\r  membership\t\r  of\t\r  nodes   \nη:\t\r  a\t\r  scaling\t\r  factor  \n\n![](/tmp/output/8_20250327021589/images/d4fb7a17ddefee82e8e485557b0554d57415897704f5da2f6479c0815b3feff6.jpg)  \n\n# An Example  \n\n![](/tmp/output/8_20250327021589/images/94cf080236c5af4c61818113e9ca53ecd204c2b3944e63152465cc2e4653baab.jpg)  \n\n# Learning Process  \n\n![](/tmp/output/8_20250327021589/images/87b83a5d8c9c83a82f4c3799290250fa0431cb4ac6d9959a81472fc338595991.jpg)  \n\n# Second Example  \n\n![](/tmp/output/8_20250327021589/images/c0a466073998a97d1d1e2947cdb6e83a8a074130462d4ee5dabb71533d1f3667.jpg)  \n\n2010\t\r  American\t\r  college\t\r  football\t\r  schedule  \n\n# LC-­‐based\t\r  Sybil\t\r  Detector\t\r  (1/2)  \n\n![](/tmp/output/8_20250327021589/images/7109c80537480f04cb9a55363a1c75044edd127412ef032b617ae4ae1bd71774.jpg)  \n\n# LC-­‐based\t\r  Sybil\t\r  detector\t\r  (2/2)  \n\nTwo\t\r  kinds\t\r  of communiIes  \n\nAssumpIons:  \n\nSeeds Nodes\t\r  in\t\r  the\t\r  same community have same properIes.  \n\n![](/tmp/output/8_20250327021589/images/042bd718c62bcd90ab38421552a4cf3de03ac2d230a5d39ed45a3482cff73516.jpg)  \n\n# Bayesian\t\r  Inference\t\r  Engine\t\r  for learning\t\r  algorithm  \n\nLearning\t\r  Algorithm  \n\nGibbs\t\r  Sampling  \n\nChoose\t\r  iniIal\t\r  values.   \n2. Iterate\t\r  over\t\r  each\t\r  parameter, and\t\r  sample\t\r  values.   \n3. Aggregate\t\r  the\t\r  distribuIon of\t\r  target\t\r  parameter.  \n\n# LC\t\r  Model\t\r  on\t\r  Digg\t\r  (1/4)  \n\n# Digg  \n\nFollowing\t\r  or\t\r  being followed\t\r  by\t\r  others Digg\t\r  or\t\r  Bury\t\r  others MoIvaIon\t\r  for\t\r  Sybils  \n\n594,\t\r  426 nodes   \n5,\t\r  066,\t\r  988\t\r  edges  \n\n![](/tmp/output/8_20250327021589/images/0324b15e4c4b65a1831d231c44d34028b0fc4867574313b64299a043a3650039.jpg)  \n\n# LC\t\r  Model\t\r  on\t\r  Digg\t\r  (2/4)  \n\nConfiguraIons  \n\n100\t\r  clusters “Kevin\t\r  Rose” as\t\r  a\t\r  seed 200\t\r  cycles Sybil\t\r  communiIes Community\t\r  3\t\r  and\t\r  4 –  δ:\t\r  \t\r  0.40\t\r  and\t\r  0.55 –  n:\t\r  311 and\t\r  299  \n\n![](/tmp/output/8_20250327021589/images/e038b62af29f734b67424244588a1c1de3b1eb363475671c94c82b94322be065.jpg)  \n\n# LC\t\r  Model\t\r  on\t\r  Digg\t\r  (3/4)  \n\n![](/tmp/output/8_20250327021589/images/5fbbce73c0786b0807fb14f9d45f9837ac7a891f89e3e4c8a4f3f7570aec1b01.jpg)  \n\nThe\t\r  relaIve\t\r  edge\t\r  density\t\r  among\t\r  Digg\t\r  communiIes  \n\n# LC\t\r  Model\t\r  on Digg\t\r  (4/4)  \n\n![](/tmp/output/8_20250327021589/images/4c2e157470e3c861d2e50ec5a486a2a0d9ddbde738bf26af49fc7e95a2f1c297.jpg)  \n\nThe\t\r  creaIon\t\r  Ime\t\r  of\t\r  edges\t\r  in\t\r  Digg\t\r  communiIes  \n\n# LC-­‐based\t\r  Sybil\t\r  Detector\t\r  (1/2)  \n\nCompared\t\r  Algorithm  \n\n–  SybilInfer\t\r  and\t\r  GateKeeper  \n\nSimulate\t\r  Sybil\t\r  aFacks  \n\nAFackers,\t\r  vicIms,\t\r  seeds,\t\r  aFack\t\r  topologies  \n\nDatasets  \n\n<html><body><table><tr><td>Dataset</td><td>Node</td><td>Edge</td><td>Directed</td></tr><tr><td>Irvine Community</td><td>1899</td><td>13, 820</td><td>True</td></tr><tr><td>Wikipedia Vote</td><td>7115</td><td>100, 762</td><td>True</td></tr><tr><td>Gnutella</td><td>8717</td><td>31,525</td><td>False</td></tr><tr><td>Email-Enron</td><td>36, 692</td><td>367, 662</td><td>False</td></tr></table></body></html>  \n\n# LC-­‐based\t\r  AutomaIc\t\r  Sybil\t\r  Detector\t\r  (2/2)  \n\n![](/tmp/output/8_20250327021589/images/c488896a76cf7d3eca75849e7d37684a29bb4c244806b6de621e5cee7bcae4f7.jpg)  \n\nGeneral\t\r  result\t\r  for\t\r  comparison  \n\n# Discussion  \n\nAlgorithm\t\r  Complexity  \n\nO(k\\*n)\t\r  (when\t\r  the\t\r  number\t\r  of\t\r  communiIes\t\r  is k) 200\t\r  cycles\t\r  lead\t\r  to\t\r  result  \n\nLC\t\r  model\t\r  in\t\r  other\t\r  literatures  \n\nContent\t\r  distribuIon\t\r  in\t\r  clusters Geographical\t\r  applicaIons  \n\n# Conclusion  \n\nLC\t\r  model\t\r  shows\t\r  good\t\r  performance\t\r  on\t\r  the Sybil\t\r  problem  \n\nWeakness  \n\nTree-­‐topology\t\r  aFack\t\r  or\t\r  Sparse\t\r  aFack. It\t\r  is\t\r  not\t\r  a\t\r  distributed\t\r  algorithm. It\t\r  is\t\r  not\t\r  used\t\r  in\t\r  applicaIons\t\r  without\t\r  social networks.  \n\nQ\t\r  &\t\r  A  \n\nThank\t\r  You  "}